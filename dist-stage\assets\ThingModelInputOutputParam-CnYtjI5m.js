import{aC as E,M as v,c as P}from"./index-Byekp3Iv.js";import{_ as z}from"./Dialog.vue_vue_type_style_index_0_lang-DhV_HFpU.js";import{T as D}from"./ThingModelProperty-DupfDGdB.js";import{D as n,a as A}from"./config-NlBZD0Kw.js";import{f as G,V as H,h as R,aj as $,k as q,ak as B}from"./form-designer-C0ARe9Dh.js";import{k as J,r as u,l as T,m as c,G as h,H as p,$ as K,v as C,F as Q,z as i,E as m,u as t,h as W,A as X,y as Y}from"./form-create-B86qX0W_.js";import"./ThingModelEnumDataSpecs-DFyLC04r.js";import"./ThingModelNumberDataSpecs-VnS8Qs6I.js";const Z={class:"btn"},ee=P(J({name:"ThingModelInputOutputParam",__name:"ThingModelInputOutputParam",props:{modelValue:{},direction:{}},emits:["update:modelValue"],setup(N,{emit:x}){const k=N,o=E(k,"modelValue",x),s=u(!1),L=u("\u65B0\u589E\u53C2\u6570"),V=u(!1),y=u(),d=u({dataType:n.INT,property:{dataType:n.INT,dataSpecs:{dataType:n.INT}}}),_=a=>{s.value=!0,w(),v(a)||(d.value={identifier:a.identifier,name:a.name,description:a.description,property:{dataType:a.dataType,dataSpecs:a.dataSpecs,dataSpecsList:a.dataSpecsList}})},g=async()=>{v(o.value)&&(o.value=[]),await y.value.validate();try{const a=t(d),e={identifier:a.identifier,name:a.name,description:a.description,dataType:a.property.dataType,paraOrder:0,direction:k.direction,dataSpecs:a.property.dataSpecs&&Object.keys(a.property.dataSpecs).length>1?a.property.dataSpecs:void 0,dataSpecsList:v(a.property.dataSpecsList)?void 0:a.property.dataSpecsList},r=o.value.findIndex(f=>f.identifier===a.identifier);r>-1?o.value[r]=e:o.value.push(e)}finally{s.value=!1}},w=()=>{var a;d.value={dataType:n.INT,property:{dataType:n.INT,dataSpecs:{dataType:n.INT}}},(a=y.value)==null||a.resetFields()};return(a,e)=>{const r=G,f=H,S=q,b=$,O=R,U=z,j=B;return c(),T(h,null,[(c(!0),T(h,null,K(t(o),(l,I)=>(c(),T("div",{key:I,class:"w-1/1 param-item flex justify-between px-10px mb-10px"},[C("span",null,"\u53C2\u6570\u540D\u79F0\uFF1A"+Q(l.name),1),C("div",Z,[p(r,{link:"",type:"primary",onClick:F=>_(l)},{default:i(()=>e[6]||(e[6]=[m("\u7F16\u8F91")])),_:2},1032,["onClick"]),p(f,{direction:"vertical"}),p(r,{link:"",type:"danger",onClick:F=>(M=>{o.value.splice(M,1)})(I)},{default:i(()=>e[7]||(e[7]=[m("\u5220\u9664")])),_:2},1032,["onClick"])])]))),128)),p(r,{link:"",type:"primary",onClick:e[0]||(e[0]=l=>_(null))},{default:i(()=>e[8]||(e[8]=[m("+\u65B0\u589E\u53C2\u6570")])),_:1}),p(U,{modelValue:t(s),"onUpdate:modelValue":e[5]||(e[5]=l=>W(s)?s.value=l:null),title:t(L),"append-to-body":""},{footer:i(()=>[p(r,{disabled:t(V),type:"primary",onClick:g},{default:i(()=>e[9]||(e[9]=[m("\u786E \u5B9A")])),_:1},8,["disabled"]),p(r,{onClick:e[4]||(e[4]=l=>s.value=!1)},{default:i(()=>e[10]||(e[10]=[m("\u53D6 \u6D88")])),_:1})]),default:i(()=>[X((c(),Y(O,{ref_key:"paramFormRef",ref:y,model:t(d),rules:t(A),"label-width":"100px"},{default:i(()=>[p(b,{label:"\u53C2\u6570\u540D\u79F0",prop:"name"},{default:i(()=>[p(S,{modelValue:t(d).name,"onUpdate:modelValue":e[1]||(e[1]=l=>t(d).name=l),placeholder:"\u8BF7\u8F93\u5165\u529F\u80FD\u540D\u79F0"},null,8,["modelValue"])]),_:1}),p(b,{label:"\u6807\u8BC6\u7B26",prop:"identifier"},{default:i(()=>[p(S,{modelValue:t(d).identifier,"onUpdate:modelValue":e[2]||(e[2]=l=>t(d).identifier=l),placeholder:"\u8BF7\u8F93\u5165\u6807\u8BC6\u7B26"},null,8,["modelValue"])]),_:1}),p(D,{modelValue:t(d).property,"onUpdate:modelValue":e[3]||(e[3]=l=>t(d).property=l),"is-params":""},null,8,["modelValue"])]),_:1},8,["model","rules"])),[[j,t(V)]])]),_:1},8,["modelValue","title"])],64)}}}),[["__scopeId","data-v-4ff132ad"]]);export{ee as default};
