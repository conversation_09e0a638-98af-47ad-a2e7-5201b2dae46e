import{a as $,d as q}from"./index-Byekp3Iv.js";import{_ as D}from"./Dialog.vue_vue_type_style_index_0_lang-DhV_HFpU.js";import{U as V}from"./index-DmG1voq9.js";import{f as O}from"./index-BWtAgQb-.js";import{h as Y}from"./index-CU_lrk1T.js";import{h as B,aj as J,k as K,x as L,Q as W,ak as X,f as Z}from"./form-designer-C0ARe9Dh.js";import{k as ee,r as t,P as ae,e as le,y as p,m as i,z as d,A as ue,u,H as o,l as C,G as T,$ as F,E as S,h as se}from"./form-create-B86qX0W_.js";const de=ee({name:"UserTenantForm",__name:"UserTenantForm",emits:["success"],setup(oe,{expose:j,emit:z}){const{t:c}=$(),_=q(),n=t(!1),y=t(""),m=t(!1),b=t(""),l=t({id:void 0,userId:void 0,userName:void 0,tenantId:void 0,roleIds:[]}),A=ae({}),f=t();j({open:async(s,e,v,r)=>{if(n.value=!0,y.value=c("action."+s),b.value=s,H(),e){m.value=!0;try{l.value=await V.getUserTenant(e)}finally{m.value=!1}}v!==void 0&&(l.value.userId=v),r!==void 0&&(l.value.userName=r),M(l.value.tenantId,l.value.roleIds),k(l.value.tenantId)}});const E=z,G=async()=>{await f.value.validate(),m.value=!0;try{const s=l.value;b.value==="create"?(await V.createUserTenant(s),_.success(c("common.createSuccess"))):(await V.updateUserTenant(s),_.success(c("common.updateSuccess"))),n.value=!1,E("success")}finally{m.value=!1}},H=()=>{var s;l.value={id:void 0,userId:void 0,userName:void 0,tenantId:void 0,roleIds:[]},(s=f.value)==null||s.resetFields()},I=t(new Map),M=(s,e)=>{I.value.set(s,e)},U=t([]),h=t([]),k=async s=>{s!==void 0&&(h.value=await Y(s),l.value.roleIds=I.value.get(s)||[])};return le(async()=>{U.value=await O()}),(s,e)=>{const v=K,r=J,w=W,N=L,P=B,g=Z,Q=D,R=X;return i(),p(Q,{title:u(y),modelValue:u(n),"onUpdate:modelValue":e[7]||(e[7]=a=>se(n)?n.value=a:null)},{footer:d(()=>[o(g,{onClick:G,type:"primary",disabled:u(m)},{default:d(()=>e[8]||(e[8]=[S("\u786E \u5B9A")])),_:1},8,["disabled"]),o(g,{onClick:e[6]||(e[6]=a=>n.value=!1)},{default:d(()=>e[9]||(e[9]=[S("\u53D6 \u6D88")])),_:1})]),default:d(()=>[ue((i(),p(P,{ref_key:"formRef",ref:f,model:u(l),rules:u(A),"label-width":"100px"},{default:d(()=>[o(r,{label:"\u7528\u6237\u7F16\u53F7",prop:"userId"},{default:d(()=>[o(v,{modelValue:u(l).userId,"onUpdate:modelValue":e[0]||(e[0]=a=>u(l).userId=a),placeholder:"\u8BF7\u8F93\u5165\u7528\u6237\u540D\u79F0",disabled:""},null,8,["modelValue"])]),_:1}),o(r,{label:"\u7528\u6237\u7F16\u53F7",prop:"userName"},{default:d(()=>[o(v,{modelValue:u(l).userName,"onUpdate:modelValue":e[1]||(e[1]=a=>u(l).userName=a),placeholder:"\u8BF7\u8F93\u5165\u7528\u6237\u540D\u79F0",disabled:""},null,8,["modelValue"])]),_:1}),o(r,{label:"\u79DF\u6237\u4FE1\u606F",prop:"tenantId"},{default:d(()=>[o(N,{modelValue:u(l).tenantId,"onUpdate:modelValue":e[2]||(e[2]=a=>u(l).tenantId=a),placeholder:"\u8BF7\u9009\u62E9\u79DF\u6237",onChange:e[3]||(e[3]=a=>k(a))},{default:d(()=>[(i(!0),C(T,null,F(u(U),a=>(i(),p(w,{label:a.name,value:a.id,key:a.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),o(r,{label:"\u89D2\u8272\u4FE1\u606F",prop:"roleIds"},{default:d(()=>[o(N,{modelValue:u(l).roleIds,"onUpdate:modelValue":e[4]||(e[4]=a=>u(l).roleIds=a),placeholder:"\u8BF7\u9009\u62E9\u89D2\u8272",multiple:"",onChange:e[5]||(e[5]=a=>{return x=a,void I.value.set(l.value.tenantId,x);var x})},{default:d(()=>[(i(!0),C(T,null,F(u(h),a=>(i(),p(w,{label:a.name,value:a.id,key:a.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])),[[R,u(m)]])]),_:1},8,["title","modelValue"])}}});export{de as _};
