import{W as F,h as Z,D as T,_ as H}from"./index-Byekp3Iv.js";import{_ as S}from"./index.vue_vue_type_script_setup_true_lang-BeMNDf6p.js";import{_ as q}from"./DictTag.vue_vue_type_script_lang-DdZ_pRVv.js";import{_ as L}from"./ContentWrap.vue_vue_type_script_setup_true_lang-D3mNNG_R.js";import{k as X,r as i,P as j,e as A,l as k,m as s,G as z,H as e,z as t,u as l,$ as G,y as d,Z as K,E as n,A as Q,F as x}from"./form-create-B86qX0W_.js";import{d as W}from"./formatTime-HVkyL6Kg.js";import{h as $,aj as J,x as O,Q as ee,k as ae,F as le,f as te,_ as re,a6 as pe,Z as oe,ak as se}from"./form-designer-C0ARe9Dh.js";const ne=X({name:"UserExperienceRecordList",__name:"UserExperienceRecordList",props:{userId:{type:Number,required:!0}},setup(I){const c=i(!0),g=i(0),y=i([]),p=j({pageNo:1,pageSize:10,userId:null,bizId:null,bizType:null,title:null,description:null,experience:null,totalExperience:null,createTime:[]}),w=i(),m=async()=>{c.value=!0;try{const _=await(async r=>await F.get({url:"/member/experience-record/page",params:r}))(p);y.value=_.list,g.value=_.total}finally{c.value=!1}},f=()=>{p.pageNo=1,m()},V=()=>{w.value.resetFields(),f()};return A(()=>{p.userId=I.userId,m()}),(_,r)=>{const N=ee,P=O,u=J,R=ae,U=le,E=H,h=te,M=$,v=L,o=re,b=pe,Y=q,D=oe,B=S,C=se;return s(),k(z,null,[e(v,null,{default:t(()=>[e(M,{class:"-mb-15px",model:l(p),ref_key:"queryFormRef",ref:w,inline:!0,"label-width":"68px"},{default:t(()=>[e(u,{label:"\u4E1A\u52A1\u7C7B\u578B",prop:"bizType"},{default:t(()=>[e(P,{modelValue:l(p).bizType,"onUpdate:modelValue":r[0]||(r[0]=a=>l(p).bizType=a),placeholder:"\u8BF7\u9009\u62E9\u4E1A\u52A1\u7C7B\u578B",clearable:"",class:"!w-240px"},{default:t(()=>[(s(!0),k(z,null,G(l(Z)(l(T).MEMBER_EXPERIENCE_BIZ_TYPE),a=>(s(),d(N,{key:a.value,label:a.label,value:a.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(u,{label:"\u6807\u9898",prop:"title"},{default:t(()=>[e(R,{modelValue:l(p).title,"onUpdate:modelValue":r[1]||(r[1]=a=>l(p).title=a),placeholder:"\u8BF7\u8F93\u5165\u6807\u9898",clearable:"",onKeyup:K(f,["enter"]),class:"!w-240px"},null,8,["modelValue"])]),_:1}),e(u,{label:"\u521B\u5EFA\u65F6\u95F4",prop:"createTime"},{default:t(()=>[e(U,{modelValue:l(p).createTime,"onUpdate:modelValue":r[2]||(r[2]=a=>l(p).createTime=a),"value-format":"YYYY-MM-DD HH:mm:ss",type:"daterange","start-placeholder":"\u5F00\u59CB\u65E5\u671F","end-placeholder":"\u7ED3\u675F\u65E5\u671F","default-time":[new Date("1 00:00:00"),new Date("1 23:59:59")],class:"!w-240px"},null,8,["modelValue","default-time"])]),_:1}),e(u,null,{default:t(()=>[e(h,{onClick:f},{default:t(()=>[e(E,{icon:"ep:search",class:"mr-5px"}),r[5]||(r[5]=n(" \u641C\u7D22"))]),_:1}),e(h,{onClick:V},{default:t(()=>[e(E,{icon:"ep:refresh",class:"mr-5px"}),r[6]||(r[6]=n(" \u91CD\u7F6E"))]),_:1})]),_:1})]),_:1},8,["model"])]),_:1}),e(v,null,{default:t(()=>[Q((s(),d(D,{data:l(y),stripe:!0,"show-overflow-tooltip":!0},{default:t(()=>[e(o,{label:"\u7F16\u53F7",align:"center",prop:"id",width:"150px"}),e(o,{label:"\u83B7\u5F97\u65F6\u95F4",align:"center",prop:"createTime",formatter:l(W)},null,8,["formatter"]),e(o,{label:"\u7ECF\u9A8C",align:"center",prop:"experience",width:"150px"},{default:t(a=>[a.row.experience>0?(s(),d(b,{key:0,class:"ml-2",type:"success",effect:"dark"},{default:t(()=>[n(" +"+x(a.row.experience),1)]),_:2},1024)):(s(),d(b,{key:1,class:"ml-2",type:"danger",effect:"dark"},{default:t(()=>[n(x(a.row.experience),1)]),_:2},1024))]),_:1}),e(o,{label:"\u603B\u7ECF\u9A8C",align:"center",prop:"totalExperience",width:"150px"},{default:t(a=>[e(b,{class:"ml-2",effect:"dark"},{default:t(()=>[n(x(a.row.totalExperience),1)]),_:2},1024)]),_:1}),e(o,{label:"\u6807\u9898",align:"center",prop:"title",width:"150px"}),e(o,{label:"\u63CF\u8FF0",align:"center",prop:"description"}),e(o,{label:"\u4E1A\u52A1\u7F16\u53F7",align:"center",prop:"bizId",width:"150px"}),e(o,{label:"\u4E1A\u52A1\u7C7B\u578B",align:"center",prop:"bizType",width:"150px"},{default:t(a=>[e(Y,{type:l(T).MEMBER_EXPERIENCE_BIZ_TYPE,value:a.row.bizType},null,8,["type","value"])]),_:1})]),_:1},8,["data"])),[[C,l(c)]]),e(B,{total:l(g),page:l(p).pageNo,"onUpdate:page":r[3]||(r[3]=a=>l(p).pageNo=a),limit:l(p).pageSize,"onUpdate:limit":r[4]||(r[4]=a=>l(p).pageSize=a),onPagination:m},null,8,["total","page","limit"])]),_:1})],64)}}});export{ne as _};
