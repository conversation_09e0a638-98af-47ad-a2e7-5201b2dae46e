import{a as re,d as ie,au as O,D as L,f as I,c as se}from"./index-Byekp3Iv.js";import{_ as me}from"./Dialog.vue_vue_type_style_index_0_lang-DhV_HFpU.js";import{R as D}from"./index-ByxWixVk.js";import{W as Q}from"./index-BC-xKroV.js";import{T as ne}from"./index-DYTTLVZU.js";import{U as pe}from"./index-C4fKXT07.js";import ce from"./ReportLossDetailForm-CyaG6V6W.js";import{h as ve,aj as Te,k as ye,x as fe,Q as we,F as he,ak as be,a0 as Ve,$ as ge,f as ke}from"./form-designer-C0ARe9Dh.js";import{k as _e,r as s,P as Ne,y as h,m as T,z as i,A as Ue,H as d,u as t,l as W,G as A,$ as P,v as b,h as V,F as xe,E as B,n as Ie}from"./form-create-B86qX0W_.js";const De={class:"duration-input-group"},Ce={class:"text-gray-500"},He=se(_e({name:"ReportOrderForm",__name:"ReportOrderForm",emits:["success"],setup(qe,{expose:G,emit:$}){const{t:g}=re(),k=ie(),v=s(!1),C=s(""),n=s(!1),_=s(""),N=s(null),w=s(""),U=s("reportLoss"),y=s(),e=s({id:void 0,workId:void 0,workNo:void 0,type:void 0,reportCode:void 0,startTime:void 0,endTime:void 0,costTime:void 0,costHeadcount:void 0,line:void 0,quantity:void 0,piece:void 0,batchNo:void 0,temperature:void 0,humidity:void 0,remark:void 0}),p=s("0"),c=s("0"),j=Ne({type:[{required:!0,message:"\u4EFB\u52A1\u7C7B\u578B\u4E0D\u80FD\u4E3A\u7A7A",trigger:"change"}],startTime:[{required:!0,message:"\u5F00\u59CB\u65F6\u95F4\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],endTime:[{required:!0,message:"\u7ED3\u675F\u65F6\u95F4\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"},{validator:(u,a,r)=>{a&&e.value.startTime&&a<e.value.startTime?r(new Error("\u7ED3\u675F\u65F6\u95F4\u4E0D\u80FD\u65E9\u4E8E\u5F00\u59CB\u65F6\u95F4")):r()},trigger:"blur"}],quantity:[{required:!0,message:"\u6570\u91CF\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}]}),f=s(),H=async u=>{if(!u)return"";try{const a=await pe.getUnit(u);return(a==null?void 0:a.name)||""}catch{return""}},z=u=>u.getTime()>Date.now(),K=u=>!!e.value.startTime&&u.getTime()<e.value.startTime,Y=u=>{const a=u.replace(/[^\d]/g,"");p.value=a},J=u=>{let a=u.replace(/[^\d]/g,"");parseInt(a)>59&&(a="59"),c.value=a},q=()=>{const u=60*(parseInt(p.value)||0)+(parseInt(c.value)||0);e.value.costTime=u},E=u=>{const a=Math.floor(u/60),r=u%60;p.value=a.toString(),c.value=r.toString()},x=()=>{if(e.value.startTime&&e.value.endTime){const u=new Date(e.value.startTime),a=new Date(e.value.endTime).getTime()-u.getTime();if(a>0){const r=Math.round(a/6e4);e.value.costTime=r,E(r)}else e.value.costTime=0,p.value="0",c.value="0";e.value.line&&M()}else e.value.costTime=0,p.value="0",c.value="0";Ie(()=>{f.value&&f.value.validateField("endTime")})},M=async()=>{try{if(!e.value.line||!e.value.startTime||!e.value.endTime)return;const u={line:e.value.line,startTime:I(e.value.startTime,"yyyy-MM-dd HH:mm:ss"),endTime:I(e.value.endTime,"yyyy-MM-dd HH:mm:ss")},a=await ne.getTempHumiditySimpleInfo(u);a&&(e.value.temperature=a.temperature,e.value.humidity=a.humidity)}catch{k.warning("\u83B7\u53D6\u6E29\u6E7F\u5EA6\u6570\u636E\u5931\u8D25\uFF0C\u8BF7\u624B\u52A8\u8F93\u5165")}},X=async u=>{u?e.value.startTime&&e.value.endTime&&await M():(e.value.temperature=void 0,e.value.humidity=void 0)};G({open:async(u,a,r)=>{if(v.value=!0,C.value=g("action."+u),_.value=u,ae(),r&&u==="create"){n.value=!0;try{const l=await Q.getWorkOrder(r);N.value=l,e.value.workId=l.id,e.value.workNo=l.workNo,e.value.line=l.scheduleLine||l.actualLine,e.value.quantity=l.scheduleQuantity,e.value.piece=l.schedulePiece,e.value.costHeadcount=l.scheduleHeadcount,e.value.batchNo=I(new Date,"yyyyMMdd"),e.value.type="2",l.scheduleStartTime?e.value.startTime=new Date(l.scheduleStartTime).getTime():l.actualStartTime&&(e.value.startTime=new Date(l.actualStartTime).getTime()),l.scheduleEndTime?e.value.endTime=new Date(l.scheduleEndTime).getTime():l.actualEndTime&&(e.value.endTime=new Date(l.actualEndTime).getTime()),x(),l.productUnit&&(w.value=await H(l.productUnit))}catch{}finally{n.value=!1}}if(a){n.value=!0;try{const l=await D.getReportOrder(a);if(e.value=l,l.costTime){const m=parseInt(l.costTime,10)||0;e.value.costTime=m,E(m)}if(e.value.workId){const m=await Q.getWorkOrder(e.value.workId);N.value=m,m.productUnit&&(w.value=await H(m.productUnit))}}finally{n.value=!1}}}});const Z=$,ee=async()=>{await f.value.validate(),y.value&&await y.value.validate(),n.value=!0;try{let u=[];if(y.value){const r=y.value.getData();r&&r.length>0&&(u=r.map(l=>({id:l.id,materialId:l.materialId,materialName:l.materialName,materialCode:l.materialCode,spec:l.spec,lossQuantity:l.lossQuantity,lossUnit:l.lossUnit,remark:l.remark||""})))}const a={id:e.value.id??0,workId:e.value.workId??0,workNo:e.value.workNo??"",type:e.value.type??"",reportCode:e.value.reportCode??"",startTime:e.value.startTime??Date.now(),endTime:e.value.endTime??Date.now(),costTime:(e.value.costTime??0).toString(),costHeadcount:Number(e.value.costHeadcount??0),line:e.value.line??"",quantity:Number(e.value.quantity??0),piece:Number(e.value.piece??0),batchNo:e.value.batchNo??"",temperature:Number(e.value.temperature??0),humidity:Number(e.value.humidity??0),remark:e.value.remark??"",slotNo:0,lossDetails:u};_.value==="create"?(await D.createReportOrder(a),k.success(g("common.createSuccess"))):(await D.updateReportOrder(a),k.success(g("common.updateSuccess"))),v.value=!1,Z("success")}finally{n.value=!1}},ae=()=>{var u;e.value={id:void 0,workId:void 0,workNo:void 0,type:void 0,reportCode:void 0,startTime:void 0,endTime:void 0,costTime:void 0,costHeadcount:void 0,line:void 0,quantity:void 0,piece:void 0,batchNo:void 0,temperature:void 0,humidity:void 0,remark:void 0},N.value=null,w.value="",(u=f.value)==null||u.resetFields()};return(u,a)=>{const r=ye,l=Te,m=we,R=fe,S=he,le=ve,te=ge,oe=Ve,F=ke,ue=me,de=be;return T(),h(ue,{title:t(C),modelValue:t(v),"onUpdate:modelValue":a[17]||(a[17]=o=>V(v)?v.value=o:null),width:"60%"},{footer:i(()=>[d(F,{onClick:ee,type:"primary",disabled:t(n)},{default:i(()=>a[20]||(a[20]=[B("\u786E \u5B9A")])),_:1},8,["disabled"]),d(F,{onClick:a[16]||(a[16]=o=>v.value=!1)},{default:i(()=>a[21]||(a[21]=[B("\u53D6 \u6D88")])),_:1})]),default:i(()=>[Ue((T(),h(le,{ref_key:"formRef",ref:f,model:t(e),rules:t(j),"label-width":"100px",inline:!0},{default:i(()=>[d(l,{label:"\u751F\u4EA7\u7F16\u53F7",prop:"workNo"},{default:i(()=>[d(r,{modelValue:t(e).workNo,"onUpdate:modelValue":a[0]||(a[0]=o=>t(e).workNo=o),placeholder:"\u8BF7\u8F93\u5165\u751F\u4EA7\u7F16\u53F7",class:"!w-240px",disabled:""},null,8,["modelValue"])]),_:1}),d(l,{label:"\u4EFB\u52A1\u7C7B\u578B",prop:"type"},{default:i(()=>[d(R,{modelValue:t(e).type,"onUpdate:modelValue":a[1]||(a[1]=o=>t(e).type=o),placeholder:"\u8BF7\u9009\u62E9\u4EFB\u52A1\u7C7B\u578B",class:"!w-240px"},{default:i(()=>[(T(!0),W(A,null,P(t(O)(t(L).MFG_WORK_TYPE),o=>(T(),h(m,{key:o.value,label:o.label,value:o.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),d(l,{label:"\u62A5\u5DE5\u7F16\u53F7",prop:"reportCode"},{default:i(()=>[d(r,{modelValue:t(e).reportCode,"onUpdate:modelValue":a[2]||(a[2]=o=>t(e).reportCode=o),placeholder:"\u4FDD\u5B58\u81EA\u52A8\u751F\u6210",clearable:"",class:"!w-240px",disabled:""},null,8,["modelValue"])]),_:1}),d(l,{label:"\u5F00\u59CB\u65F6\u95F4",prop:"startTime"},{default:i(()=>[d(S,{modelValue:t(e).startTime,"onUpdate:modelValue":a[3]||(a[3]=o=>t(e).startTime=o),type:"datetime","value-format":"x",placeholder:"\u9009\u62E9\u5F00\u59CB\u65F6\u95F4",clearable:"",class:"!w-240px",disabledDate:z,onChange:x},null,8,["modelValue"])]),_:1}),d(l,{label:"\u7ED3\u675F\u65F6\u95F4",prop:"endTime"},{default:i(()=>[d(S,{modelValue:t(e).endTime,"onUpdate:modelValue":a[4]||(a[4]=o=>t(e).endTime=o),type:"datetime","value-format":"x",placeholder:"\u9009\u62E9\u7ED3\u675F\u65F6\u95F4",class:"!w-240px",disabledDate:K,onChange:x},null,8,["modelValue"])]),_:1}),d(l,{label:"\u7528\u65F6",prop:"costTime"},{default:i(()=>[b("div",De,[d(r,{modelValue:t(p),"onUpdate:modelValue":a[5]||(a[5]=o=>V(p)?p.value=o:null),placeholder:"0",class:"duration-input",onInput:Y,onBlur:q},null,8,["modelValue"]),a[18]||(a[18]=b("span",{class:"duration-label"},"\u5C0F\u65F6",-1)),d(r,{modelValue:t(c),"onUpdate:modelValue":a[6]||(a[6]=o=>V(c)?c.value=o:null),placeholder:"0",class:"duration-input",onInput:J,onBlur:q},null,8,["modelValue"]),a[19]||(a[19]=b("span",{class:"duration-label"},"\u5206\u949F",-1))])]),_:1}),d(l,{label:"\u4EBA\u6570",prop:"costHeadcount"},{default:i(()=>[d(r,{modelValue:t(e).costHeadcount,"onUpdate:modelValue":a[7]||(a[7]=o=>t(e).costHeadcount=o),placeholder:"\u8BF7\u8F93\u5165\u4EBA\u6570",class:"!w-240px"},null,8,["modelValue"])]),_:1}),d(l,{label:"\u751F\u4EA7\u7EBF",prop:"line"},{default:i(()=>[d(R,{modelValue:t(e).line,"onUpdate:modelValue":a[8]||(a[8]=o=>t(e).line=o),placeholder:"\u8BF7\u9009\u62E9\u751F\u4EA7\u7EBF",clearable:"",class:"!w-240px",onChange:X},{default:i(()=>[(T(!0),W(A,null,P(t(O)(t(L).MANUFACTURE_LINE),o=>(T(),h(m,{key:o.value,label:o.label,value:o.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),d(l,{label:"\u6570\u91CF",prop:"quantity"},{default:i(()=>[d(r,{modelValue:t(e).quantity,"onUpdate:modelValue":a[9]||(a[9]=o=>t(e).quantity=o),placeholder:"\u8BF7\u8F93\u5165\u6570\u91CF",class:"!w-240px"},{suffix:i(()=>[b("span",Ce,xe(t(w)),1)]),_:1},8,["modelValue"])]),_:1}),d(l,{label:"\u4EF6\u6570",prop:"piece"},{default:i(()=>[d(r,{modelValue:t(e).piece,"onUpdate:modelValue":a[10]||(a[10]=o=>t(e).piece=o),placeholder:"\u8BF7\u8F93\u5165\u4EF6\u6570",class:"!w-240px"},null,8,["modelValue"])]),_:1}),d(l,{label:"\u6279\u53F7",prop:"batchNo"},{default:i(()=>[d(r,{modelValue:t(e).batchNo,"onUpdate:modelValue":a[11]||(a[11]=o=>t(e).batchNo=o),placeholder:"\u8BF7\u8F93\u5165\u6279\u53F7",class:"!w-240px"},null,8,["modelValue"])]),_:1}),d(l,{label:"\u6E29\u5EA6",prop:"temperature"},{default:i(()=>[d(r,{modelValue:t(e).temperature,"onUpdate:modelValue":a[12]||(a[12]=o=>t(e).temperature=o),placeholder:"\u8BF7\u8F93\u5165\u6E29\u5EA6",class:"!w-240px"},null,8,["modelValue"])]),_:1}),d(l,{label:"\u6E7F\u5EA6",prop:"humidity"},{default:i(()=>[d(r,{modelValue:t(e).humidity,"onUpdate:modelValue":a[13]||(a[13]=o=>t(e).humidity=o),placeholder:"\u8BF7\u8F93\u5165\u6E7F\u5EA6",class:"!w-240px"},null,8,["modelValue"])]),_:1}),d(l,{label:"\u5907\u6CE8",prop:"remark"},{default:i(()=>[d(r,{modelValue:t(e).remark,"onUpdate:modelValue":a[14]||(a[14]=o=>t(e).remark=o),placeholder:"\u8BF7\u8F93\u5165\u5907\u6CE8",class:"!w-240px"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])),[[de,t(n)]]),d(oe,{modelValue:t(U),"onUpdate:modelValue":a[15]||(a[15]=o=>V(U)?U.value=o:null),class:"mt-20px"},{default:i(()=>[d(te,{label:"\u62A5\u5DE5\u635F\u8017",name:"reportLoss"},{default:i(()=>[d(ce,{ref_key:"reportLossDetailFormRef",ref:y,"report-id":t(e).id,"work-id":t(e).workId,disabled:t(_)==="view"},null,8,["report-id","work-id","disabled"])]),_:1})]),_:1},8,["modelValue"])]),_:1},8,["title","modelValue"])}}}),[["__scopeId","data-v-a1308a66"]]);export{He as default};
