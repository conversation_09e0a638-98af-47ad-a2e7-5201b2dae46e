import{d as D,a as E,_ as G,aw as H}from"./index-Byekp3Iv.js";import{_ as F}from"./ContentWrap.vue_vue_type_script_setup_true_lang-D3mNNG_R.js";import{_ as W}from"./index.vue_vue_type_script_setup_true_lang-BeMNDf6p.js";import{g as X,d as Z}from"./index-EFvRhq4A.js";import{_ as q}from"./ReceivablePlanForm.vue_vue_type_script_setup_true_lang-B5SnYOuT.js";import{b as C}from"./formatTime-HVkyL6Kg.js";import{i as B,f as J,ak as K,_ as M,Z as O}from"./form-designer-C0ARe9Dh.js";import{k as Q,r as u,P as V,b as Y,af as $,l as ee,m as s,G as ae,H as a,z as c,E as f,A as g,u as l,y as b}from"./form-create-B86qX0W_.js";const te=Q({name:"CrmReceivablePlanList",__name:"ReceivablePlanList",props:{customerId:{},contractId:{}},emits:["createReceivable"],setup(N,{emit:R}){const t=N,w=D(),{t:S}=E(),v=u(!0),_=u(0),k=u([]),r=V({pageNo:1,pageSize:10,customerId:void 0,contractId:void 0}),d=async()=>{v.value=!0;try{t.customerId&&!t.contractId?r.customerId=t.customerId:t.customerId&&t.contractId&&(r.customerId=t.customerId,r.contractId=t.contractId);const n=await X(r);k.value=n.list,_.value=n.total}finally{v.value=!1}},y=u(),h=(n,e)=>{y.value.open(n,e,t.customerId,t.contractId)},P=R;return Y(()=>[t.customerId,t.contractId],n=>{n[0]&&(r.pageNo=1,r.customerId=void 0,r.contractId=void 0,d())},{immediate:!0,deep:!0}),(n,e)=>{const z=G,m=J,U=B,o=M,L=O,T=W,j=F,I=$("hasPermi"),A=K;return s(),ee(ae,null,[a(U,{justify:"end"},{default:c(()=>[a(m,{onClick:e[0]||(e[0]=i=>h("create",void 0))},{default:c(()=>[a(z,{class:"mr-5px",icon:"icon-park:income"}),e[3]||(e[3]=f(" \u521B\u5EFA\u56DE\u6B3E\u8BA1\u5212 "))]),_:1})]),_:1}),a(j,{class:"mt-10px"},{default:c(()=>[g((s(),b(L,{data:l(k),"show-overflow-tooltip":!0,stripe:!0},{default:c(()=>[a(o,{align:"center",label:"\u5BA2\u6237\u540D\u79F0",prop:"customerName",width:"150px"}),a(o,{align:"center",label:"\u5408\u540C\u7F16\u53F7",prop:"contractNo",width:"200px"}),a(o,{align:"center",label:"\u671F\u6570",prop:"period"}),a(o,{align:"center",label:"\u8BA1\u5212\u56DE\u6B3E(\u5143)",prop:"price",width:"120",formatter:l(H)},null,8,["formatter"]),a(o,{formatter:l(C),align:"center",label:"\u8BA1\u5212\u56DE\u6B3E\u65E5\u671F",prop:"returnTime",width:"180px"},null,8,["formatter"]),a(o,{align:"center",label:"\u63D0\u524D\u51E0\u5929\u63D0\u9192",prop:"remindDays",width:"150"}),a(o,{formatter:l(C),align:"center",label:"\u63D0\u9192\u65E5\u671F",prop:"remindTime",width:"180px"},null,8,["formatter"]),a(o,{label:"\u8D1F\u8D23\u4EBA",prop:"ownerUserName",width:"120"}),a(o,{align:"center",label:"\u5907\u6CE8",prop:"remark"}),a(o,{align:"center",fixed:"right",label:"\u64CD\u4F5C",width:"200px"},{default:c(i=>[g((s(),b(m,{link:"",type:"primary",onClick:x=>{return p=i.row,void P("createReceivable",p);var p},disabled:i.row.receivableId},{default:c(()=>e[4]||(e[4]=[f(" \u521B\u5EFA\u56DE\u6B3E ")])),_:2},1032,["onClick","disabled"])),[[I,["crm:receivable:create"]]]),g((s(),b(m,{link:"",type:"primary",onClick:x=>h("update",i.row.id)},{default:c(()=>e[5]||(e[5]=[f(" \u7F16\u8F91 ")])),_:2},1032,["onClick"])),[[I,["crm:receivable-plan:update"]]]),g((s(),b(m,{link:"",type:"danger",onClick:x=>(async p=>{try{await w.delConfirm(),await Z(p),w.success(S("common.delSuccess")),await d()}catch{}})(i.row.id)},{default:c(()=>e[6]||(e[6]=[f(" \u5220\u9664 ")])),_:2},1032,["onClick"])),[[I,["crm:receivable-plan:delete"]]])]),_:1})]),_:1},8,["data"])),[[A,l(v)]]),a(T,{limit:l(r).pageSize,"onUpdate:limit":e[1]||(e[1]=i=>l(r).pageSize=i),page:l(r).pageNo,"onUpdate:page":e[2]||(e[2]=i=>l(r).pageNo=i),total:l(_),onPagination:d},null,8,["limit","page","total"])]),_:1}),a(q,{ref_key:"formRef",ref:y,onSuccess:d},null,512)],64)}}});export{te as _};
