import{W as t}from"./index-Byekp3Iv.js";const a={getSeckillConfigPage:async i=>await t.get({url:"/promotion/seckill-config/page",params:i}),getSimpleSeckillConfigList:async()=>await t.get({url:"/promotion/seckill-config/list"}),getSeckillConfig:async i=>await t.get({url:"/promotion/seckill-config/get?id="+i}),createSeckillConfig:async i=>await t.post({url:"/promotion/seckill-config/create",data:i}),updateSeckillConfig:async i=>await t.put({url:"/promotion/seckill-config/update",data:i}),deleteSeckillConfig:async i=>await t.delete({url:"/promotion/seckill-config/delete?id="+i}),updateSeckillConfigStatus:async(i,e)=>{const o={id:i,status:e};return t.put({url:"/promotion/seckill-config/update-status",data:o})}};export{a as S};
