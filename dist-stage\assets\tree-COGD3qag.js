const m={id:"id",children:"children",pid:"pid"},y={children:"children",label:"name",value:"id",isLeaf:"leaf",emitPath:!1},h=r=>Object.assign({},m,r),A=(r,t={})=>{t=h(t);const{children:e}=t,n=[...r];for(let i=0;i<n.length;i++)n[i][e]&&n.splice(i+1,0,...n[i][e]);return n},v=(r,t,e={})=>{e=h(e);const n=[],i=[...r],s=new Set,{children:o}=e;for(;i.length;){const c=i[0];if(s.has(c))n.pop(),i.shift();else if(s.add(c),c[o]&&i.unshift(...c[o]),n.push(c),t(c))return n}return null},b=(r,t,e={})=>{const n=(e=h(e)).children;return function i(s){return s.map(o=>({...o})).filter(o=>(o[n]=o[n]&&i(o[n]),t(o)||o[n]&&o[n].length))}(r)},O=(r,t)=>r.map(e=>p(e,t)),p=(r,{children:t="children",conversion:e})=>{const n=Array.isArray(r[t])&&r[t].length>0,i=e(r)||{};return n?{...i,[t]:r[t].map(s=>p(s,{children:t,conversion:e}))}:{...i}},g=(r,t,e={})=>{r.forEach(n=>{const i=t(n,e)||n;n.children&&g(n.children,t,i)})},S=(r,t,e,n)=>{if(!Array.isArray(r))return[];const i=t||"id",s=e||"parentId",o="children",c={},a={},d=[];for(const l of r){const f=l[s];c[f]==null&&(c[f]=[]),a[l[i]]=l,c[f].push(l)}for(const l of r)a[l[s]]==null&&d.push(l);for(const l of d)u(l);function u(l){if(c[l[i]]!==null&&(l[o]=c[l[i]]),l[o])for(const f of l[o])u(f)}return d},$=(r,t,e,n,i)=>{t=t||"id",e=e||"parentId",i=i||Math.min(...r.map(c=>c[e]))||0;const s=JSON.parse(JSON.stringify(r)),o=s.filter(c=>{const a=s.filter(d=>c[t]===d[e]);return a.length>0&&(c.children=a),c[e]===i});return o!==""?o:r},I=(r,t)=>{if(r===void 0||!Array.isArray(r)||r.length===0)return"";const e=r.find(s=>s.id===t);if(e!==void 0)return e.name;let n="";function i(s){if(s===void 0||!Array.isArray(s)||s.length===0)return!1;for(const o of s){if(o.id===t)return n+=` / ${o.name}`,!0;if(o.children!==void 0&&o.children.length!==0&&(n+=` / ${o.name}`,i(o.children)))return!0}return!1}for(const s of r)if(n=`${s.name}`,i(s.children))break;return n};export{$ as a,O as b,v as c,y as d,g as e,b as f,A as g,S as h,I as t};
