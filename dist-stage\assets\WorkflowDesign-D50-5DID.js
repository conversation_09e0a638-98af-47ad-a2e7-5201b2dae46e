import{c as Bd}from"./index-Byekp3Iv.js";import{k as xl,r as Zn,e as Rd,K as Xd,l as Cn,m as Wt,N as Yd,B as Kd,i as Wd,b as jd,af as qd,y as Gi,C as Fd,v as kn,H as _n,u as Ut,A as Gd,z as Bn,E as bo,G as $l,$ as Cl,F as kl,h as Ud}from"./form-create-B86qX0W_.js";import{t as Jd}from"./index-BJA3gMrI.js";import{f as Qd,x as e1,Q as t1,k as n1,aD as o1,a7 as r1,aG as i1}from"./form-designer-C0ARe9Dh.js";var a1=Object.defineProperty,_l=e=>{throw TypeError(e)},kt=(e,t,n)=>((o,r,i)=>r in o?a1(o,r,{enumerable:!0,configurable:!0,writable:!0,value:i}):o[r]=i)(e,typeof t!="symbol"?t+"":t,n),Ui=(e,t,n)=>t.has(e)||_l("Cannot "+n),rt=(e,t,n)=>(Ui(e,t,"read from private field"),n?n.call(e):t.get(e)),xo=(e,t,n)=>t.has(e)?_l("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(e):t.set(e,n),Rr=(e,t,n,o)=>(Ui(e,t,"write to private field"),t.set(e,n),n),Sl;typeof window<"u"&&((Sl=window.__svelte??(window.__svelte={})).v??(Sl.v=new Set)).add("5");let $o=!1;$o=!0;const Ji="[!",Co={},_t=Symbol(),El="http://www.w3.org/2000/svg",Go=32,Qi=64,Xr=128,fn=256,ea=512,ht=1024,ko=2048,_o=4096,Rn=8192,ta=16384,So=65536,Pl=1<<20,Xn=Symbol("$state"),na=Symbol("legacy props"),l1=Symbol("");var Uo=Array.isArray,s1=Array.prototype.indexOf,oa=Array.from,Yr=Object.keys,Jo=Object.defineProperty,Sn=Object.getOwnPropertyDescriptor,Ml=Object.getOwnPropertyDescriptors,u1=Object.prototype,c1=Array.prototype,ra=Object.getPrototypeOf;function Qo(e){return typeof e=="function"}const st=()=>{};function d1(e){return e()}function er(e){for(var t=0;t<e.length;t++)e[t]()}const f1=typeof requestIdleCallback>"u"?e=>setTimeout(e,1):requestIdleCallback;let tr=[],nr=[];function Vl(){var e=tr;tr=[],er(e)}function Hl(){var e=nr;nr=[],er(e)}function or(e){tr.length===0&&queueMicrotask(Vl),tr.push(e)}function zl(){tr.length>0&&Vl(),nr.length>0&&Hl()}function Nl(e){return e===this.v}function ia(e,t){return e!=e?t==t:e!==t||e!==null&&typeof e=="object"||typeof e=="function"}function aa(e){return!ia(e,this.v)}function St(e,t){return{f:0,v:e,reactions:null,equals:Nl,rv:0,wv:0}}function Yn(e){return Ll(St(e))}function rr(e,t=!1){var n;const o=St(e);return t||(o.equals=aa),$o&&Be!==null&&Be.l!==null&&((n=Be.l).s??(n.s=[])).push(o),o}function se(e,t=!1){return Ll(rr(e,t))}function Ll(e){return je!==null&&!Jt&&2&je.f&&(pn===null?function(t){pn=t}([e]):pn.push(e)),e}function te(e,t){return je!==null&&!Jt&&ei()&&18&je.f&&(pn===null||!pn.includes(e))&&function(){throw new Error("https://svelte.dev/e/state_unsafe_mutation")}(),la(e,t)}function la(e,t){return e.equals(t)||(e.v,e.v=t,e.wv=Kl(),Dl(e,ko),ei()&&Xe!==null&&Xe.f&ht&&!(96&Xe.f)&&(Pn===null?function(n){Pn=n}([e]):Pn.push(e))),t}function Ol(e,t=1){var n=p(e),o=t===1?n++:n--;return te(e,n),o}function Dl(e,t){var n=e.reactions;if(n!==null)for(var o=ei(),r=n.length,i=0;i<r;i++){var a=n[i],l=a.f;l&ko||!o&&a===Xe||(Qt(a,t),1280&l&&(2&l?Dl(a,_o):Jr(a)))}}function ze(e){var t=2050,n=je!==null&&2&je.f?je:null;return Xe===null||n!==null&&n.f&fn?t|=fn:Xe.f|=Pl,{ctx:Be,deps:null,effects:null,equals:Nl,f:t,fn:e,reactions:null,rv:0,v:null,wv:0,parent:n??Xe}}function ye(e){const t=ze(e);return t.equals=aa,t}function Tl(e){var t=e.effects;if(t!==null){e.effects=null;for(var n=0;n<t.length;n+=1)jt(t[n])}}function Al(e){var t=function(n){var o,r=Xe;Wn(function(i){for(var a=i.parent;a!==null;){if(!(2&a.f))return a;a=a.parent}return null}(n));try{Tl(n),o=jl(n)}finally{Wn(r)}return o}(e);Qt(e,(jn||e.f&fn)&&e.deps!==null?_o:ht),e.equals(t)||(e.v=t,e.wv=Kl())}let Oe,Ve=!1;function Lt(e){Ve=e}function mt(e){if(e===null)throw Co;return Oe=e}function vn(){return mt(gn(Oe))}function K(e){if(Ve){if(gn(Oe)!==null)throw Co;Oe=e}}function He(e=1){if(Ve){for(var t=e,n=Oe;t--;)n=gn(n);Oe=n}}function sa(){for(var e=0,t=Oe;;){if(t.nodeType===8){var n=t.data;if(n==="]"){if(e===0)return t;e-=1}else(n==="["||n===Ji)&&(e+=1)}var o=gn(t);t.remove(),t=o}}function Et(e,t=null,n){if(typeof e!="object"||e===null||Xn in e)return e;const o=ra(e);if(o!==u1&&o!==c1)return e;var r,i=new Map,a=Uo(e),l=St(0);return a&&i.set("length",St(e.length)),new Proxy(e,{defineProperty(u,s,c){(!("value"in c)||c.configurable===!1||c.enumerable===!1||c.writable===!1)&&function(){throw new Error("https://svelte.dev/e/state_descriptors_fixed")}();var d=i.get(s);return d===void 0?(d=St(c.value),i.set(s,d)):te(d,Et(c.value,r)),!0},deleteProperty(u,s){var c=i.get(s);if(c===void 0)s in u&&i.set(s,St(_t));else{if(a&&typeof s=="string"){var d=i.get("length"),f=Number(s);Number.isInteger(f)&&f<d.v&&te(d,f)}te(c,_t),Il(l)}return!0},get(u,s,c){var d;if(s===Xn)return e;var f=i.get(s),g=s in u;if(f===void 0&&(!g||(d=Sn(u,s))!=null&&d.writable)&&(f=St(Et(g?u[s]:_t,r)),i.set(s,f)),f!==void 0){var m=p(f);return m===_t?void 0:m}return Reflect.get(u,s,c)},getOwnPropertyDescriptor(u,s){var c=Reflect.getOwnPropertyDescriptor(u,s);if(c&&"value"in c){var d=i.get(s);d&&(c.value=p(d))}else if(c===void 0){var f=i.get(s),g=f==null?void 0:f.v;if(f!==void 0&&g!==_t)return{enumerable:!0,configurable:!0,value:g,writable:!0}}return c},has(u,s){var c;if(s===Xn)return!0;var d=i.get(s),f=d!==void 0&&d.v!==_t||Reflect.has(u,s);return(d!==void 0||Xe!==null&&(!f||(c=Sn(u,s))!=null&&c.writable))&&(d===void 0&&(d=St(f?Et(u[s],r):_t),i.set(s,d)),p(d)===_t)?!1:f},set(u,s,c,d){var f,g=i.get(s),m=s in u;if(a&&s==="length")for(var v=c;v<g.v;v+=1){var y=i.get(v+"");y!==void 0?te(y,_t):v in u&&(y=St(_t),i.set(v+"",y))}g===void 0?(!m||(f=Sn(u,s))!=null&&f.writable)&&(te(g=St(void 0),Et(c,r)),i.set(s,g)):(m=g.v!==_t,te(g,Et(c,r)));var w=Reflect.getOwnPropertyDescriptor(u,s);if(w!=null&&w.set&&w.set.call(d,c),!m){if(a&&typeof s=="string"){var h=i.get("length"),C=Number(s);Number.isInteger(C)&&C>=h.v&&te(h,C+1)}Il(l)}return!0},ownKeys(u){p(l);var s=Reflect.ownKeys(u).filter(f=>{var g=i.get(f);return g===void 0||g.v!==_t});for(var[c,d]of i)d.v!==_t&&!(c in u)&&s.push(c);return s},setPrototypeOf(){(function(){throw new Error("https://svelte.dev/e/state_prototype_fixed")})()}})}function Il(e,t=1){te(e,e.v+t)}var Pt,Zl,Bl,Rl;function ua(){if(Pt===void 0){Pt=window,Zl=/Firefox/.test(navigator.userAgent);var e=Element.prototype,t=Node.prototype;Bl=Sn(t,"firstChild").get,Rl=Sn(t,"nextSibling").get,e.__click=void 0,e.__className=void 0,e.__attributes=null,e.__styles=null,e.__e=void 0,Text.prototype.__t=void 0}}function En(e=""){return document.createTextNode(e)}function yt(e){return Bl.call(e)}function gn(e){return Rl.call(e)}function W(e,t){if(!Ve)return yt(e);var n=yt(Oe);if(n===null)n=Oe.appendChild(En());else if(t&&n.nodeType!==3){var o=En();return n==null||n.before(o),mt(o),o}return mt(n),n}function ke(e,t){if(!Ve){var n=yt(e);return n instanceof Comment&&n.data===""?gn(n):n}return Oe}function Z(e,t=1,n=!1){let o=Ve?Oe:e;for(var r;t--;)r=o,o=gn(o);if(!Ve)return o;var i=o==null?void 0:o.nodeType;if(n&&i!==3){var a=En();return o===null?r==null||r.after(a):o.before(a),mt(a),a}return mt(o),o}function ca(e){e.textContent=""}let Kr=!1,Wr=!1,jr=null,ao=!1,da=!1;function Xl(e){da=e}let ir=[],je=null,Jt=!1;function Kn(e){je=e}let Xe=null;function Wn(e){Xe=e}let pn=null,wt=null,Ot=0,Pn=null,Yl=1,qr=0,jn=!1;function Kl(){return++Yl}function Eo(e){var t,n=e.f;if(n&ko)return!0;if(n&_o){var o=e.deps,r=!!(n&fn);if(o!==null){var i,a,l=!!(n&ea),u=r&&Xe!==null&&!jn,s=o.length;if(l||u){var c=e,d=c.parent;for(i=0;i<s;i++)a=o[i],(l||(t=a==null?void 0:a.reactions)==null||!t.includes(c))&&(a.reactions??(a.reactions=[])).push(c);l&&(c.f^=ea),u&&d!==null&&!(d.f&fn)&&(c.f^=fn)}for(i=0;i<s;i++)if(Eo(a=o[i])&&Al(a),a.wv>e.wv)return!0}(!r||Xe!==null&&!jn)&&Qt(e,ht)}return!1}function Fr(e,t,n,o){if(Kr){if(n===null&&(Kr=!1),function(r){return!(r.f&ta||r.parent!==null&&r.parent.f&Xr)}(t))throw e}else n!==null&&(Kr=!0),function(r,i){for(var a=i;a!==null;){if(a.f&Xr)try{return void a.fn(r)}catch{a.f^=Xr}a=a.parent}throw Kr=!1,r}(e,t)}function Wl(e,t,n=!0){var o=e.reactions;if(o!==null)for(var r=0;r<o.length;r++){var i=o[r];2&i.f?Wl(i,t,!1):t===i&&(n?Qt(i,ko):i.f&ht&&Qt(i,_o),Jr(i))}}function jl(e){var t,n=wt,o=Ot,r=Pn,i=je,a=jn,l=pn,u=Be,s=Jt,c=e.f;wt=null,Ot=0,Pn=null,jn=!!(c&fn)&&(Jt||!ao||je===null),je=96&c?null:e,pn=null,ns(e.ctx),Jt=!1,qr++;try{var d=(0,e.fn)(),f=e.deps;if(wt!==null){var g;if(Gr(e,Ot),f!==null&&Ot>0)for(f.length=Ot+wt.length,g=0;g<wt.length;g++)f[Ot+g]=wt[g];else e.deps=f=wt;if(!jn)for(g=Ot;g<f.length;g++)((t=f[g]).reactions??(t.reactions=[])).push(e)}else f!==null&&Ot<f.length&&(Gr(e,Ot),f.length=Ot);if(ei()&&Pn!==null&&!Jt&&f!==null&&!(6146&e.f))for(g=0;g<Pn.length;g++)Wl(Pn[g],e);return i!==null&&qr++,d}finally{wt=n,Ot=o,Pn=r,je=i,jn=a,pn=l,ns(u),Jt=s}}function v1(e,t){let n=t.reactions;if(n!==null){var o=s1.call(n,e);if(o!==-1){var r=n.length-1;r===0?n=t.reactions=null:(n[o]=n[r],n.pop())}}n===null&&2&t.f&&(wt===null||!wt.includes(t))&&(Qt(t,_o),768&t.f||(t.f^=ea),Tl(t),Gr(t,0))}function Gr(e,t){var n=e.deps;if(n!==null)for(var o=t;o<n.length;o++)v1(e,n[o])}function Ur(e){var t=e.f;if(!(t&ta)){Qt(e,ht);var n=Xe,o=Be,r=ao;Xe=e,ao=!0;try{16&t?function(a){for(var l=a.first;l!==null;){var u=l.next;l.f&Go||jt(l),l=u}}(e):Jl(e),Ul(e);var i=jl(e);e.teardown=typeof i=="function"?i:null,e.wv=Yl,e.deps}catch(a){Fr(a,e,n,o||e.ctx)}finally{ao=r,Xe=n}}}function g1(){try{(function(){throw new Error("https://svelte.dev/e/effect_update_depth_exceeded")})()}catch(e){if(jr===null)throw e;Fr(e,jr,null)}}function ql(){var e=ao;try{var t=0;for(ao=!0;ir.length>0;){t++>1e3&&g1();var n=ir,o=n.length;ir=[];for(var r=0;r<o;r++){var i=n[r];i.f&ht||(i.f^=ht),p1(h1(i))}}}finally{Wr=!1,ao=e,jr=null}}function p1(e){var t=e.length;if(t!==0)for(var n=0;n<t;n++){var o=e[n];if(!(24576&o.f))try{Eo(o)&&(Ur(o),o.deps===null&&o.first===null&&o.nodes_start===null&&(o.teardown===null?Ql(o):o.fn=null))}catch(r){Fr(r,o,null,o.ctx)}}}function Jr(e){Wr||(Wr=!0,queueMicrotask(ql));for(var t=jr=e;t.parent!==null;){var n=(t=t.parent).f;if(96&n){if(!(n&ht))return;t.f^=ht}}ir.push(t)}function h1(e){for(var t=[],n=e.first;n!==null;){var o=n.f,r=!!(o&Go);if(!(r&&o&ht||o&Rn)){if(4&o)t.push(n);else if(r)n.f^=ht;else{var i=je;try{je=n,Eo(n)&&Ur(n)}catch(u){Fr(u,n,null,n.ctx)}finally{je=i}}var a=n.first;if(a!==null){n=a;continue}}var l=n.parent;for(n=n.next;n===null&&l!==null;)n=l.next,l=l.parent}return t}function x(e){for(zl();ir.length>0;)Wr=!0,ql(),zl()}function p(e){var t=!!(2&e.f);if(je===null||Jt){if(t&&e.deps===null&&e.effects===null){var n=e,o=n.parent;o!==null&&!(o.f&fn)&&(n.f^=fn)}}else{pn!==null&&pn.includes(e)&&function(){throw new Error("https://svelte.dev/e/state_unsafe_local_read")}();var r=je.deps;e.rv<qr&&(e.rv=qr,wt===null&&r!==null&&r[Ot]===e?Ot++:wt===null?wt=[e]:(!jn||!wt.includes(e))&&wt.push(e))}return t&&Eo(n=e)&&Al(n),e.v}function hn(e){var t=Jt;try{return Jt=!0,e()}finally{Jt=t}}const m1=-7169;function Qt(e,t){e.f=e.f&m1|t}function oe(e){if(typeof e=="object"&&e&&!(e instanceof EventTarget)){if(Xn in e)fa(e);else if(!Array.isArray(e))for(let t in e){const n=e[t];typeof n=="object"&&n&&Xn in n&&fa(n)}}}function fa(e,t=new Set){if(!(typeof e!="object"||e===null||e instanceof EventTarget||t.has(e))){t.add(e),e instanceof Date&&e.getTime();for(let o in e)try{fa(e[o],t)}catch{}const n=ra(e);if(n!==Object.prototype&&n!==Array.prototype&&n!==Map.prototype&&n!==Set.prototype&&n!==Date.prototype){const o=Ml(n);for(let r in o){const i=o[r].get;if(i)try{i.call(e)}catch{}}}}}function Fl(e){Xe===null&&je===null&&function(){throw new Error("https://svelte.dev/e/effect_orphan")}(),je!==null&&je.f&fn&&Xe===null&&function(){throw new Error("https://svelte.dev/e/effect_in_unowned_derived")}(),da&&function(){throw new Error("https://svelte.dev/e/effect_in_teardown")}()}function lo(e,t,n,o=!0){var r=!!(e&Qi),i=Xe,a={ctx:Be,deps:null,nodes_start:null,nodes_end:null,f:e|ko,first:null,fn:t,last:null,next:null,parent:r?null:i,prev:null,teardown:null,transitions:null,wv:0};if(n)try{Ur(a),a.f|=32768}catch(u){throw jt(a),u}else t!==null&&Jr(a);if(!(n&&a.deps===null&&a.first===null&&a.nodes_start===null&&a.teardown===null&&!(a.f&(Pl|Xr)))&&!r&&o&&(i!==null&&function(u,s){var c=s.last;c===null?s.last=s.first=u:(c.next=u,u.prev=c,s.last=u)}(a,i),je!==null&&2&je.f)){var l=je;(l.effects??(l.effects=[])).push(a)}return a}function Gl(e){const t=lo(8,null,!1);return Qt(t,ht),t.teardown=e,t}function Po(e){if(Fl(),!(Xe!==null&&Xe.f&Go&&Be!==null&&!Be.m))return Dt(e);var t=Be;(t.e??(t.e=[])).push({fn:e,effect:Xe,reaction:je})}function Dt(e){return lo(4,e,!1)}function he(e,t){var n=Be,o={effect:null,ran:!1};n.l.r1.push(o),o.effect=Mo(()=>{e(),!o.ran&&(o.ran=!0,te(n.l.r2,!0),hn(t))})}function ut(){var e=Be;Mo(()=>{if(p(e.l.r2)){for(var t of e.l.r1){var n=t.effect;n.f&ht&&Qt(n,_o),Eo(n)&&Ur(n),t.ran=!1}e.l.r2.v=!1}})}function Mo(e){return lo(8,e,!0)}function Me(e,t=[],n=ze){const o=t.map(n);return so(()=>e(...o.map(p)))}function so(e,t=0){return lo(24|t,e,!0)}function Mn(e,t=!0){return lo(40,e,!0,t)}function Ul(e){var t=e.teardown;if(t!==null){const n=da,o=je;Xl(!0),Kn(null);try{t.call(null)}finally{Xl(n),Kn(o)}}}function Jl(e,t=!1){var n=e.first;for(e.first=e.last=null;n!==null;){var o=n.next;jt(n,t),n=o}}function jt(e,t=!0){var n=!1;if((t||524288&e.f)&&e.nodes_start!==null){for(var o=e.nodes_start,r=e.nodes_end;o!==null;){var i=o===r?null:gn(o);o.remove(),o=i}n=!0}Jl(e,t&&!n),Gr(e,0),Qt(e,ta);var a=e.transitions;if(a!==null)for(const u of a)u.stop();Ul(e);var l=e.parent;l!==null&&l.first!==null&&Ql(e),e.next=e.prev=e.teardown=e.ctx=e.deps=e.fn=e.nodes_start=e.nodes_end=null}function Ql(e){var t=e.parent,n=e.prev,o=e.next;n!==null&&(n.next=o),o!==null&&(o.prev=n),t!==null&&(t.first===e&&(t.first=o),t.last===e&&(t.last=n))}function Vo(e,t){var n=[];va(e,n,!0),es(n,()=>{jt(e),t&&t()})}function es(e,t){var n=e.length;if(n>0){var o=()=>--n||t();for(var r of e)r.out(o)}else t()}function va(e,t,n){if(!(e.f&Rn)){if(e.f^=Rn,e.transitions!==null)for(const i of e.transitions)(i.is_global||n)&&t.push(i);for(var o=e.first;o!==null;){var r=o.next;va(o,t,!!(o.f&So||o.f&Go)&&n),o=r}}}function ar(e){ts(e,!0)}function ts(e,t){if(e.f&Rn){e.f^=Rn,e.f&ht||(e.f^=ht),Eo(e)&&(Qt(e,ko),Jr(e));for(var n=e.first;n!==null;){var o=n.next;ts(n,!!(n.f&So||n.f&Go)&&t),n=o}if(e.transitions!==null)for(const r of e.transitions)(r.is_global||t)&&r.in()}}function Qr(e){throw new Error("https://svelte.dev/e/lifecycle_outside_component")}let Be=null;function ns(e){Be=e}function uo(e){return ga().get(e)}function Ho(e,t){return ga().set(e,t),t}function ve(e,t=!1,n){Be={p:Be,c:null,e:null,m:!1,s:e,x:null,l:null},$o&&!t&&(Be.l={s:null,u:null,r1:[],r2:St(!1)})}function ge(e){const t=Be;if(t!==null){e!==void 0&&(t.x=e);const a=t.e;if(a!==null){var n=Xe,o=je;t.e=null;try{for(var r=0;r<a.length;r++){var i=a[r];Wn(i.effect),Kn(i.reaction),Dt(i.fn)}}finally{Wn(n),Kn(o)}}Be=t.p,t.m=!0}return e||{}}function ei(){return!$o||Be!==null&&Be.l===null}function ga(e){return Be===null&&Qr(),Be.c??(Be.c=new Map(function(t){let n=t.p;for(;n!==null;){const o=n.c;if(o!==null)return o;n=n.p}return null}(Be)||void 0))}function y1(e){return e.endsWith("capture")&&e!=="gotpointercapture"&&e!=="lostpointercapture"}const w1=["beforeinput","click","change","dblclick","contextmenu","focusin","focusout","input","keydown","keyup","mousedown","mousemove","mouseout","mouseover","mouseup","pointerdown","pointermove","pointerout","pointerover","pointerup","touchend","touchmove","touchstart"];function b1(e){return w1.includes(e)}const x1={formnovalidate:"formNoValidate",ismap:"isMap",nomodule:"noModule",playsinline:"playsInline",readonly:"readOnly",defaultvalue:"defaultValue",defaultchecked:"defaultChecked",srcobject:"srcObject",novalidate:"noValidate",allowfullscreen:"allowFullscreen",disablepictureinpicture:"disablePictureInPicture",disableremoteplayback:"disableRemotePlayback"};function $1(e){return e=e.toLowerCase(),x1[e]??e}const C1=["touchstart","touchmove"];function k1(e){return C1.includes(e)}const _1=["textarea","script","style","title"];function S1(e,t){if(t){const n=document.body;e.autofocus=!0,or(()=>{document.activeElement===n&&e.focus()})}}let os=!1;const rs=new Set,pa=new Set;function is(e,t,n,o={}){function r(i){if(o.capture||lr.call(t,i),!i.cancelBubble)return function(a){var l=je,u=Xe;Kn(null),Wn(null);try{return a()}finally{Kn(l),Wn(u)}}(()=>n==null?void 0:n.call(this,i))}return e.startsWith("pointer")||e.startsWith("touch")||e==="wheel"?or(()=>{t.addEventListener(e,r,o)}):t.addEventListener(e,r,o),r}function Ze(e,t,n,o,r){var i={capture:o,passive:r},a=is(e,t,n,i);(t===document.body||t===window||t===document)&&Gl(()=>{t.removeEventListener(e,a,i)})}function ti(e){for(var t=0;t<e.length;t++)rs.add(e[t]);for(var n of pa)n(e)}function lr(e){var t,n=this,o=n.ownerDocument,r=e.type,i=((t=e.composedPath)==null?void 0:t.call(e))||[],a=i[0]||e.target,l=0,u=e.__root;if(u){var s=i.indexOf(u);if(s!==-1&&(n===document||n===window))return void(e.__root=n);var c=i.indexOf(n);if(c===-1)return;s<=c&&(l=s)}if((a=i[l]||e.target)!==n){Jo(e,"currentTarget",{configurable:!0,get:()=>a||o});var d=je,f=Xe;Kn(null),Wn(null);try{for(var g,m=[];a!==null;){var v=a.assignedSlot||a.parentNode||a.host||null;try{var y=a["__"+r];if(y!==void 0&&(!a.disabled||e.target===a))if(Uo(y)){var[w,...h]=y;w.apply(a,[e,...h])}else y.call(a,e)}catch(C){g?m.push(C):g=C}if(e.cancelBubble||v===n||v===null)break;a=v}if(g){for(let C of m)queueMicrotask(()=>{throw C});throw g}}finally{e.__root=n,delete e.currentTarget,Kn(d),Wn(f)}}}function ha(e){var t=document.createElement("template");return t.innerHTML=e,t.content}function Mt(e,t){var n=Xe;n.nodes_start===null&&(n.nodes_start=e,n.nodes_end=t)}function ae(e,t){var n,o=!!(1&t),r=!!(2&t),i=!e.startsWith("<!>");return()=>{if(Ve)return Mt(Oe,null),Oe;n===void 0&&(n=ha(i?e:"<!>"+e),o||(n=yt(n)));var a=r||Zl?document.importNode(n,!0):n.cloneNode(!0);return o?Mt(yt(a),a.lastChild):Mt(a,a),a}}function xe(e,t,n="svg"){var o,r=!e.startsWith("<!>"),i=!!(1&t),a=`<${n}>${r?e:"<!>"+e}</${n}>`;return()=>{if(Ve)return Mt(Oe,null),Oe;if(!o){var l=yt(ha(a));if(i)for(o=document.createDocumentFragment();yt(l);)o.appendChild(yt(l));else o=yt(l)}var u=o.cloneNode(!0);return i?Mt(yt(u),u.lastChild):Mt(u,u),u}}function Te(e=""){if(!Ve){var t=En(e+"");return Mt(t,t),t}var n=Oe;return n.nodeType!==3&&(n.before(n=En()),mt(n)),Mt(n,n),n}function Ue(){if(Ve)return Mt(Oe,null),Oe;var e=document.createDocumentFragment(),t=document.createComment(""),n=En();return e.append(t,n),Mt(t,n),e}function T(e,t){if(Ve)return Xe.nodes_end=Oe,void vn();e!==null&&e.before(t)}function Tt(e,t){var n=t==null?"":typeof t=="object"?t+"":t;n!==(e.__t??(e.__t=e.nodeValue))&&(e.__t=n,e.nodeValue=n+"")}function as(e,t){return ls(e,t)}function E1(e,t){ua(),t.intro=t.intro??!1;const n=t.target,o=Ve,r=Oe;try{for(var i=yt(n);i&&(i.nodeType!==8||i.data!=="[");)i=gn(i);if(!i)throw Co;Lt(!0),mt(i),vn();const a=ls(e,{...t,anchor:i});if(Oe===null||Oe.nodeType!==8||Oe.data!=="]")throw Co;return Lt(!1),a}catch(a){if(a===Co)return t.recover===!1&&function(){throw new Error("https://svelte.dev/e/hydration_failed")}(),ua(),ca(n),Lt(!1),as(e,t);throw a}finally{Lt(o),mt(r)}}const zo=new Map;function ls(e,{target:t,anchor:n,props:o={},events:r,context:i,intro:a=!0}){ua();var l=new Set,u=d=>{for(var f=0;f<d.length;f++){var g=d[f];if(!l.has(g)){l.add(g);var m=k1(g);t.addEventListener(g,lr,{passive:m});var v=zo.get(g);v===void 0?(document.addEventListener(g,lr,{passive:m}),zo.set(g,1)):zo.set(g,v+1)}}};u(oa(rs)),pa.add(u);var s=void 0,c=function(d){const f=lo(Qi,d,!0);return(g={})=>new Promise(m=>{g.outro?Vo(f,()=>{jt(f),m(void 0)}):(jt(f),m(void 0))})}(()=>{var d=n??t.appendChild(En());return Mn(()=>{i&&(ve({}),Be.c=i),r&&(o.$$events=r),Ve&&Mt(d,null),s=e(d,o)||{},Ve&&(Xe.nodes_end=Oe),i&&ge()}),()=>{var f;for(var g of l){t.removeEventListener(g,lr);var m=zo.get(g);--m==0?(document.removeEventListener(g,lr),zo.delete(g)):zo.set(g,m)}pa.delete(u),d!==n&&((f=d.parentNode)==null||f.removeChild(d))}});return ma.set(s,c),s}let ma=new WeakMap;function _e(e,t,[n,o]=[0,0]){Ve&&n===0&&vn();var r=e,i=null,a=null,l=_t,u=!1;const s=(d,f=!0)=>{u=!0,c(f,d)},c=(d,f)=>{if(l===(l=d))return;let g=!1;if(Ve&&o!==-1){if(n===0){const m=r.data;m==="["?o=0:m===Ji?o=1/0:(o=parseInt(m.substring(1)))!=o&&(o=l?1/0:-1)}!!l==o>n&&(mt(r=sa()),Lt(!1),g=!0,o=-1)}l?(i?ar(i):f&&(i=Mn(()=>f(r))),a&&Vo(a,()=>{a=null})):(a?ar(a):f&&(a=Mn(()=>f(r,[n+1,o]))),i&&Vo(i,()=>{i=null})),g&&Lt(!0)};so(()=>{u=!1,t(s),u||c(null,null)},n>0?So:0),Ve&&(r=Oe)}function ni(e,t){return t}function At(e,t,n,o,r,i=null){var a=e,l={flags:t,items:new Map,first:null};if(4&t){var u=e;a=Ve?mt(yt(u)):u.appendChild(En())}Ve&&vn();var s=null,c=!1,d=ye(()=>{var f=n();return Uo(f)?f:f==null?[]:oa(f)});so(()=>{var f=p(d),g=f.length;if(c&&g===0)return;c=g===0;let m=!1;if(Ve&&a.data===Ji!=(g===0)&&(mt(a=sa()),Lt(!1),m=!0),Ve){for(var v,y=null,w=0;w<g;w++){if(Oe.nodeType===8&&Oe.data==="]"){a=Oe,m=!0,Lt(!1);break}var h=f[w],C=o(h,w);v=ss(Oe,l,y,null,h,C,w,r,t,n),l.items.set(C,v),y=v}g>0&&mt(sa())}Ve||function(b,k,_,S,E,L,P){var D,M,H,O,V,z,N,A,X,B,Q=!!(8&E),ne=!!(3&E),ee=b.length,me=k.items,de=k.first,J=de,F=null,I=[],j=[];if(Q)for(B=0;B<ee;B+=1)N=b[B],A=L(N,B),(X=me.get(A))!==void 0&&((D=X.a)==null||D.measure(),(z??(z=new Set)).add(X));for(B=0;B<ee;B+=1)if(A=L(N=b[B],B),(X=me.get(A))!==void 0){if(ne&&P1(X,N,B,E),X.e.f&Rn&&(ar(X.e),Q&&((M=X.a)==null||M.unfix(),(z??(z=new Set)).delete(X))),X!==J){if(V!==void 0&&V.has(X)){if(I.length<j.length){var G,le=j[0];F=le.prev;var we=I[0],Pe=I[I.length-1];for(G=0;G<I.length;G+=1)us(I[G],le,_);for(G=0;G<j.length;G+=1)V.delete(j[G]);qn(k,we.prev,Pe.next),qn(k,F,we),qn(k,Pe,le),J=le,F=Pe,B-=1,I=[],j=[]}else V.delete(X),us(X,J,_),qn(k,X.prev,X.next),qn(k,X,F===null?k.first:F.next),qn(k,F,X),F=X;continue}for(I=[],j=[];J!==null&&J.k!==A;)J.e.f&Rn||(V??(V=new Set)).add(J),j.push(J),J=J.next;if(J===null)continue;X=J}I.push(X),F=X,J=X.next}else F=ss(J?J.e.nodes_start:_,k,F,F===null?k.first:F.next,N,A,B,S,E,P),me.set(A,F),I=[],j=[],J=F.next;if(J!==null||V!==void 0){for(var Ne=V===void 0?[]:oa(V);J!==null;)J.e.f&Rn||Ne.push(J),J=J.next;var ue=Ne.length;if(ue>0){var Y=4&E&&ee===0?_:null;if(Q){for(B=0;B<ue;B+=1)(H=Ne[B].a)==null||H.measure();for(B=0;B<ue;B+=1)(O=Ne[B].a)==null||O.fix()}(function($e,Ce,Se,Ae){for(var Ge=[],fe=Ce.length,U=0;U<fe;U++)va(Ce[U].e,Ge,!0);var re=fe>0&&Ge.length===0&&Se!==null;if(re){var Qe=Se.parentNode;ca(Qe),Qe.append(Se),Ae.clear(),qn($e,Ce[0].prev,Ce[fe-1].next)}es(Ge,()=>{for(var Fe=0;Fe<fe;Fe++){var tt=Ce[Fe];re||(Ae.delete(tt.k),qn($e,tt.prev,tt.next)),jt(tt.e,!re)}})})(k,Ne,Y,me)}}Q&&or(()=>{var $e;if(z!==void 0)for(X of z)($e=X.a)==null||$e.apply()}),Xe.first=k.first&&k.first.e,Xe.last=F&&F.e}(f,l,a,r,t,o,n),i!==null&&(g===0?s?ar(s):s=Mn(()=>i(a)):s!==null&&Vo(s,()=>{s=null})),m&&Lt(!0),p(d)}),Ve&&(a=Oe)}function P1(e,t,n,o){1&o&&la(e.v,t),2&o?la(e.i,n):e.i=n}function ss(e,t,n,o,r,i,a,l,u,s){var c=1&u?16&u?St(r):rr(r):r,d=2&u?St(a):a,f={i:d,v:c,k:i,a:null,e:null,prev:n,next:o};try{return f.e=Mn(()=>l(e,c,d,s),Ve),f.e.prev=n&&n.e,f.e.next=o&&o.e,n===null?t.first=f:(n.next=f,n.e.next=f.e),o!==null&&(o.prev=f,o.e.prev=f.e),f}finally{}}function us(e,t,n){for(var o=e.next?e.next.e.nodes_start:n,r=t?t.e.nodes_start:n,i=e.e.nodes_start;i!==o;){var a=gn(i);r.before(i),i=a}}function qn(e,t,n){t===null?e.first=n:(t.next=n,t.e.next=n&&n.e),n!==null&&(n.prev=t,n.e.prev=t&&t.e)}function cs(e,t,n,o,r){var i,a=e,l="";so(()=>{l!==(l=t()??"")?(i!==void 0&&(jt(i),i=void 0),l!==""&&(i=Mn(()=>{if(Ve){Oe.data;for(var u=vn(),s=u;u!==null&&(u.nodeType!==8||u.data!=="");)s=u,u=gn(u);if(u===null)throw Co;return Mt(Oe,s),void(a=mt(u))}var c=ha(l+"");Mt(yt(c),c.lastChild),a.before(c)}))):Ve&&vn()})}function ft(e,t,n,o,r){var i;Ve&&vn();var a=(i=t.$$slots)==null?void 0:i[n],l=!1;a===!0&&(a=t[n==="default"?"children":n],l=!0),a===void 0||a(e,l?()=>o:o)}function co(e,t,...n){var o,r=e,i=st;so(()=>{i!==(i=t())&&(o&&(jt(o),o=null),o=Mn(()=>i(r,...n)))},So),Ve&&(r=Oe)}function ds(e,t,n){Ve&&vn();var o,r,i=e;so(()=>{o!==(o=t())&&(r&&(Vo(r),r=null),o&&(r=Mn(()=>n(i,o))))},So),Ve&&(i=Oe)}function M1(e,t,n,o,r,i){let a=Ve;Ve&&vn();var l,u,s=null;Ve&&Oe.nodeType===1&&(s=Oe,vn());var c,d=Ve?Oe:e;so(()=>{const f=t()||null;var g=f==="svg"?El:null;f!==l&&(c&&(f===null?Vo(c,()=>{c=null,u=null}):f===u?ar(c):jt(c)),f&&f!==u&&(c=Mn(()=>{if(Mt(s=Ve?s:g?document.createElementNS(g,f):document.createElement(f),s),o){Ve&&function(v){return _1.includes(v)}(f)&&s.append(document.createComment(""));var m=Ve?yt(s):s.appendChild(En());Ve&&(m===null?Lt(!1):mt(m)),o(s,m)}Xe.nodes_end=s,d.before(s)})),(l=f)&&(u=l))},So),a&&(Lt(!0),mt(d))}function qe(e,t){or(()=>{var n=e.getRootNode(),o=n.host?n:n.head??n.ownerDocument.head;if(!o.querySelector("#"+t.hash)){const r=document.createElement("style");r.id=t.hash,r.textContent=t.code,o.appendChild(r)}})}function vt(e,t,n){Dt(()=>{var o=hn(()=>t(e,n==null?void 0:n())||{});if(n&&o!=null&&o.update){var r=!1,i={};Mo(()=>{var a=n();oe(a),r&&ia(i,a)&&(i=a,o.update(a))}),r=!0}if(o!=null&&o.destroy)return()=>o.destroy()})}function fs(e){var t,n,o="";if(typeof e=="string"||typeof e=="number")o+=e;else if(typeof e=="object")if(Array.isArray(e)){var r=e.length;for(t=0;t<r;t++)e[t]&&(n=fs(e[t]))&&(o&&(o+=" "),o+=n)}else for(n in e)e[n]&&(o&&(o+=" "),o+=n);return o}function mn(e){return typeof e=="object"?function(){for(var t,n,o=0,r="",i=arguments.length;o<i;o++)(t=arguments[o])&&(n=fs(t))&&(r&&(r+=" "),r+=n);return r}(e):e??""}const vs=[...` 	
\r\f\xA0\v\uFEFF`];function bt(e,t,n,o,r,i){var a=e.__className;if(Ve||a!==n){var l=function(c,d,f){var g=c==null?"":""+c;if(d&&(g=g?g+" "+d:d),f){for(var m in f)if(f[m])g=g?g+" "+m:m;else if(g.length)for(var v=m.length,y=0;(y=g.indexOf(m,y))>=0;){var w=y+v;y!==0&&!vs.includes(g[y-1])||w!==g.length&&!vs.includes(g[w])?y=w:g=(y===0?"":g.substring(0,y))+g.substring(w+1)}}return g===""?null:g}(n,o,i);(!Ve||l!==e.getAttribute("class"))&&(l==null?e.removeAttribute("class"):t?e.className=l:e.setAttribute("class",l)),e.__className=n}else if(i)for(var u in i){var s=!!i[u];(r==null||s!==!!r[u])&&e.classList.toggle(u,s)}return i}const sr=Symbol("class");function ur(e){if(Ve){var t=!1,n=()=>{if(!t){if(t=!0,e.hasAttribute("value")){var o=e.value;pe(e,"value",null),e.value=o}if(e.hasAttribute("checked")){var r=e.checked;pe(e,"checked",null),e.checked=r}}};e.__on_r=n,function(o){nr.length===0&&f1(Hl),nr.push(o)}(n),os||(os=!0,document.addEventListener("reset",o=>{Promise.resolve().then(()=>{var r;if(!o.defaultPrevented)for(const i of o.target.elements)(r=i.__on_r)==null||r.call(i)})},{capture:!0}))}}function ya(e,t){var n=e.__attributes??(e.__attributes={});n.value===(n.value=t??void 0)||e.value===t&&(t!==0||e.nodeName!=="PROGRESS")||(e.value=t??"")}function V1(e,t){t?e.hasAttribute("selected")||e.setAttribute("selected",""):e.removeAttribute("selected")}function pe(e,t,n,o){var r=e.__attributes??(e.__attributes={});Ve&&(r[t]=e.getAttribute(t),t==="src"||t==="srcset"||t==="href"&&e.nodeName==="LINK")||r[t]!==(r[t]=n)&&(t==="style"&&"__styles"in e&&(e.__styles={}),t==="loading"&&(e[l1]=n),n==null?e.removeAttribute(t):typeof n!="string"&&hs(e).includes(t)?e[t]=n:e.setAttribute(t,n))}function en(e,t,n,o,r=!1,i=!1,a=!1){let l=Ve&&i;l&&Lt(!1);var u=t||{},s=e.tagName==="OPTION";for(var c in t)c in n||(n[c]=null);n.class?n.class=mn(n.class):(o||n[sr])&&(n.class=null);var d=hs(e),f=e.__attributes??(e.__attributes={});for(const h in n){let C=n[h];if(s&&h==="value"&&C==null)e.value=e.__value="",u[h]=C;else if(h!=="class"){var g=u[h];if(C!==g){u[h]=C;var m=h[0]+h[1];if(m!=="$$"){if(m==="on"){const b={},k="$$"+h;let _=h.slice(2);var v=b1(_);if(y1(_)&&(_=_.slice(0,-7),b.capture=!0),!v&&g){if(C!=null)continue;e.removeEventListener(_,u[k],b),u[k]=null}if(C!=null)if(v)e[`__${_}`]=C,ti([_]);else{let S=function(E){u[h].call(this,E)};u[k]=is(_,e,S,b)}else v&&(e[`__${_}`]=void 0)}else if(h==="style"&&C!=null)e.style.cssText=C+"";else if(h==="autofocus")S1(e,!!C);else if(i||h!=="__value"&&(h!=="value"||C==null))if(h==="selected"&&s)V1(e,C);else{var y=h;r||(y=$1(y));var w=y==="defaultValue"||y==="defaultChecked";if(C!=null||i||w)w||d.includes(y)&&(i||typeof C!="string")?e[y]=C:typeof C!="function"&&pe(e,y,C);else if(f[h]=null,y==="value"||y==="checked"){let b=e;const k=t===void 0;if(y==="value"){let _=b.defaultValue;b.removeAttribute(y),b.defaultValue=_,b.value=b.__value=k?_:null}else{let _=b.defaultChecked;b.removeAttribute(y),b.defaultChecked=_,b.checked=!!k&&_}}else e.removeAttribute(h)}else e.value=e.__value=C;h==="style"&&"__styles"in e&&(e.__styles={})}}}else bt(e,e.namespaceURI==="http://www.w3.org/1999/xhtml",C,o,t==null?void 0:t[sr],n[sr]),u[h]=C,u[sr]=n[sr]}return l&&Lt(!0),u}var fo,cr,oi,wa,gs,ps=new Map;function hs(e){var t=ps.get(e.nodeName);if(t)return t;ps.set(e.nodeName,t=[]);for(var n,o=e,r=Element.prototype;r!==o;){for(var i in n=Ml(o))n[i].set&&t.push(i);o=ra(o)}return t}function nt(e,t,n,o){var r=e.__styles??(e.__styles={});r[t]!==n&&(r[t]=n,n==null?e.style.removeProperty(t):e.style.setProperty(t,n,""))}const ba=class{constructor(e){xo(this,wa),xo(this,fo,new WeakMap),xo(this,cr),xo(this,oi),Rr(this,oi,e)}observe(e,t){var n=rt(this,fo).get(e)||new Set;return n.add(t),rt(this,fo).set(e,n),((o,r,i)=>(Ui(o,r,"access private method"),i))(this,wa,gs).call(this).observe(e,rt(this,oi)),()=>{var o=rt(this,fo).get(e);o.delete(t),o.size===0&&(rt(this,fo).delete(e),rt(this,cr).unobserve(e))}}};fo=new WeakMap,cr=new WeakMap,oi=new WeakMap,wa=new WeakSet,gs=function(){return rt(this,cr)??Rr(this,cr,new ResizeObserver(e=>{for(var t of e)for(var n of(ba.entries.set(t.target,t),rt(this,fo).get(t.target)||[]))n(t)}))},kt(ba,"entries",new WeakMap);var H1=new ba({box:"border-box"});function ms(e,t,n){var o=H1.observe(e,()=>n(e[t]));Dt(()=>(hn(()=>n(e[t])),o))}function ys(e,t){return e===t||(e==null?void 0:e[Xn])===t}function Vn(e={},t,n,o){return Dt(()=>{var r,i;return Mo(()=>{r=i,i=[],hn(()=>{e!==n(...i)&&(t(e,...i),r&&ys(n(...r),e)&&t(null,...r))})}),()=>{or(()=>{i&&ys(n(...i),e)&&t(null,...i)})}}),e}function xa(e){return function(...t){return t[0].stopPropagation(),e==null?void 0:e.apply(this,t)}}function Le(e=!1){const t=Be,n=t.l.u;if(!n)return;let o=()=>oe(t.s);if(e){let r=0,i={};const a=ze(()=>{let l=!1;const u=t.s;for(const s in u)u[s]!==i[s]&&(i[s]=u[s],l=!0);return l&&r++,r});o=()=>p(a)}n.b.length&&function(r){Fl(),Mo(r)}(()=>{ws(t,o),er(n.b)}),Po(()=>{const r=hn(()=>n.m.map(d1));return()=>{for(const i of r)typeof i=="function"&&i()}}),n.a.length&&Po(()=>{ws(t,o),er(n.a)})}function ws(e,t){if(e.l.s)for(const n of e.l.s)p(n);t()}function De(e,t){var n,o=(n=e.$$events)==null?void 0:n[t.type],r=Uo(o)?o.slice():o==null?[]:[o];for(var i of r)i.call(this,t)}function tn(e){Be===null&&Qr(),$o&&Be.l!==null?function(t){var n=t.l;return n.u??(n.u={a:[],b:[],m:[]})}(Be).m.push(e):Po(()=>{const t=hn(e);if(typeof t=="function")return t})}function $a(e){Be===null&&Qr(),tn(()=>()=>hn(e))}function ri(){const e=Be;return e===null&&Qr(),(t,n,o)=>{var r;const i=(r=e.s.$$events)==null?void 0:r[t];if(i){const a=Uo(i)?i.slice():[i],l=function(u,s,{bubbles:c=!1,cancelable:d=!1}={}){return new CustomEvent(u,{detail:s,bubbles:c,cancelable:d})}(t,n,o);for(const u of a)u.call(e.x,l);return!l.defaultPrevented}return!0}}function Ca(e,t,n){if(e==null)return t(void 0),n&&n(void 0),st;const o=hn(()=>e.subscribe(t,n));return o.unsubscribe?()=>o.unsubscribe():o}const No=[];function qt(e,t){return{subscribe:be(e,t).subscribe}}function be(e,t=st){let n=null;const o=new Set;function r(a){if(ia(e,a)&&(e=a,n)){const l=!No.length;for(const u of o)u[1](),No.push(u,e);if(l){for(let u=0;u<No.length;u+=2)No[u][0](No[u+1]);No.length=0}}}function i(a){r(a(e))}return{set:r,update:i,subscribe:function(a,l=st){const u=[a,l];return o.add(u),o.size===1&&(n=t(r,i)||st),a(e),()=>{o.delete(u),o.size===0&&n&&(n(),n=null)}}}}function Fn(e,t,n){const o=!Array.isArray(e),r=o?[e]:e;if(!r.every(Boolean))throw new Error("derived() expects stores as input, got a falsy value");const i=t.length<2;return qt(n,(a,l)=>{let u=!1;const s=[];let c=0,d=st;const f=()=>{if(c)return;d();const m=t(o?s[0]:s,a,l);i?a(m):d=typeof m=="function"?m:st},g=r.map((m,v)=>Ca(m,y=>{s[v]=y,c&=~(1<<v),u&&f()},()=>{c|=1<<v}));return u=!0,f(),function(){er(g),d(),u=!1}})}function q(e){let t;return Ca(e,n=>t=n)(),t}let ii=!1,ka=Symbol();function ie(e,t,n){const o=n[t]??(n[t]={store:null,source:rr(void 0),unsubscribe:st});if(o.store!==e&&!(ka in n))if(o.unsubscribe(),o.store=e??null,e==null)o.source.v=void 0,o.unsubscribe=st;else{var r=!0;o.unsubscribe=Ca(e,i=>{r?o.source.v=i:te(o.source,i)}),r=!1}return e&&ka in n?q(e):p(o.source)}function ai(e,t){return e.set(t),t}function Je(){const e={};return[e,function(){Gl(()=>{for(var t in e)e[t].unsubscribe();Jo(e,ka,{enumerable:!1,value:!0})})}]}const z1={get(e,t){if(!e.exclude.includes(t))return e.props[t]},set:(e,t)=>!1,getOwnPropertyDescriptor(e,t){if(!e.exclude.includes(t)&&t in e.props)return{enumerable:!0,configurable:!0,value:e.props[t]}},has:(e,t)=>!e.exclude.includes(t)&&t in e.props,ownKeys:e=>Reflect.ownKeys(e.props).filter(t=>!e.exclude.includes(t))};function gt(e,t,n){return new Proxy({props:e,exclude:t},z1)}const N1={get(e,t){if(!e.exclude.includes(t))return p(e.version),t in e.special?e.special[t]():e.props[t]},set:(e,t,n)=>(t in e.special||(e.special[t]=$({get[t](){return e.props[t]}},t,4)),e.special[t](n),Ol(e.version),!0),getOwnPropertyDescriptor(e,t){if(!e.exclude.includes(t)&&t in e.props)return{enumerable:!0,configurable:!0,value:e.props[t]}},deleteProperty:(e,t)=>(e.exclude.includes(t)||(e.exclude.push(t),Ol(e.version)),!0),has:(e,t)=>!e.exclude.includes(t)&&t in e.props,ownKeys:e=>Reflect.ownKeys(e.props).filter(t=>!e.exclude.includes(t))};function et(e,t){return new Proxy({props:e,exclude:t,special:{},version:St(0)},N1)}const L1={get(e,t){let n=e.props.length;for(;n--;){let o=e.props[n];if(Qo(o)&&(o=o()),typeof o=="object"&&o!==null&&t in o)return o[t]}},set(e,t,n){let o=e.props.length;for(;o--;){let r=e.props[o];Qo(r)&&(r=r());const i=Sn(r,t);if(i&&i.set)return i.set(n),!0}return!1},getOwnPropertyDescriptor(e,t){let n=e.props.length;for(;n--;){let o=e.props[n];if(Qo(o)&&(o=o()),typeof o=="object"&&o!==null&&t in o){const r=Sn(o,t);return r&&!r.configurable&&(r.configurable=!0),r}}},has(e,t){if(t===Xn||t===na)return!1;for(let n of e.props)if(Qo(n)&&(n=n()),n!=null&&t in n)return!0;return!1},ownKeys(e){const t=[];for(let n of e.props){Qo(n)&&(n=n());for(const o in n)t.includes(o)||t.push(o)}return t}};function lt(...e){return new Proxy({props:e},L1)}function $(e,t,n,o){var r,i,a=!!(1&n),l=!$o||!!(2&n),u=!!(8&n),s=!!(16&n),c=!1;u?[i,c]=function(S){var E=ii;try{return ii=!1,[S(),ii]}finally{ii=E}}(()=>e[t]):i=e[t];var d,f=Xn in e||na in e,g=u&&(((r=Sn(e,t))==null?void 0:r.set)??(f&&t in e&&(S=>e[t]=S)))||void 0,m=o,v=!0,y=!1,w=()=>(y=!0,v&&(v=!1,m=s?hn(o):o),m);if(i===void 0&&o!==void 0&&(g&&l&&function(){throw new Error("https://svelte.dev/e/props_invalid_value")}(),i=w(),g&&g(i)),l)d=()=>{var S=e[t];return S===void 0?w():(v=!0,y=!1,S)};else{var h=(a?ze:ye)(()=>e[t]);h.f|=131072,d=()=>{var S=p(h);return S!==void 0&&(m=void 0),S===void 0?m:S}}if(!(4&n))return d;if(g){var C=e.$$legacy;return function(S,E){return arguments.length>0?((!l||!E||C||c)&&g(E?d():S),S):d()}}var b=!1,k=rr(i),_=ze(()=>{var S=d(),E=p(k);return b?(b=!1,E):k.v=S});return a||(_.equals=aa),function(S,E){if(arguments.length>0){const L=E?p(_):l&&u?Et(S):S;return _.equals(L)||(b=!0,te(k,L),y&&m!==void 0&&(m=L),hn(()=>p(_))),S}return p(_)}}var Gn,nn;class O1{constructor(t){var n;xo(this,Gn),xo(this,nn);var o=new Map,r=(a,l)=>{var u=rr(l);return o.set(a,u),u};const i=new Proxy({...t.props||{},$$events:{}},{get:(a,l)=>p(o.get(l)??r(l,Reflect.get(a,l))),has:(a,l)=>l===na||(p(o.get(l)??r(l,Reflect.get(a,l))),Reflect.has(a,l)),set:(a,l,u)=>(te(o.get(l)??r(l,u),u),Reflect.set(a,l,u))});Rr(this,nn,(t.hydrate?E1:as)(t.component,{target:t.target,anchor:t.anchor,props:i,context:t.context,intro:t.intro??!1,recover:t.recover})),(!((n=t==null?void 0:t.props)!=null&&n.$$host)||t.sync===!1)&&x(),Rr(this,Gn,i.$$events);for(const a of Object.keys(rt(this,nn)))a==="$set"||a==="$destroy"||a==="$on"||Jo(this,a,{get(){return rt(this,nn)[a]},set(l){rt(this,nn)[a]=l},enumerable:!0});rt(this,nn).$set=a=>{Object.assign(i,a)},rt(this,nn).$destroy=()=>{(function(a,l){const u=ma.get(a);u?(ma.delete(a),u(l)):Promise.resolve()})(rt(this,nn))}}$set(t){rt(this,nn).$set(t)}$on(t,n){rt(this,Gn)[t]=rt(this,Gn)[t]||[];const o=(...r)=>n.call(this,...r);return rt(this,Gn)[t].push(o),()=>{rt(this,Gn)[t]=rt(this,Gn)[t].filter(r=>r!==o)}}$destroy(){rt(this,nn).$destroy()}}let bs;function li(e,t,n,o){var r;const i=(r=n[e])==null?void 0:r.type;if(t=i==="Boolean"&&typeof t!="boolean"?t!=null:t,!o||!n[e])return t;if(o==="toAttribute")switch(i){case"Object":case"Array":return t==null?null:JSON.stringify(t);case"Boolean":return t?"":null;case"Number":return t??null;default:return t}else switch(i){case"Object":case"Array":return t&&JSON.parse(t);case"Boolean":default:return t;case"Number":return t!=null?+t:t}}function ce(e,t,n,o,r,i){let a=class extends bs{constructor(){super(e,n,r),this.$$p_d=t}static get observedAttributes(){return Yr(t).map(l=>(t[l].attribute||l).toLowerCase())}};return Yr(t).forEach(l=>{Jo(a.prototype,l,{get(){return this.$$c&&l in this.$$c?this.$$c[l]:this.$$d[l]},set(u){var s;u=li(l,u,t),this.$$d[l]=u;var c=this.$$c;c&&((s=Sn(c,l))!=null&&s.get?c[l]=u:c.$set({[l]:u}))}})}),o.forEach(l=>{Jo(a.prototype,l,{get(){var u;return(u=this.$$c)==null?void 0:u[l]}})}),e.element=a,a}function xt(e){if(typeof e=="string"||typeof e=="number")return""+e;let t="";if(Array.isArray(e))for(let n,o=0;o<e.length;o++)(n=xt(e[o]))!==""&&(t+=(t&&" ")+n);else for(let n in e)e[n]&&(t+=(t&&" ")+n);return t}Gn=new WeakMap,nn=new WeakMap,typeof HTMLElement=="function"&&(bs=class extends HTMLElement{constructor(e,t,n){super(),kt(this,"$$ctor"),kt(this,"$$s"),kt(this,"$$c"),kt(this,"$$cn",!1),kt(this,"$$d",{}),kt(this,"$$r",!1),kt(this,"$$p_d",{}),kt(this,"$$l",{}),kt(this,"$$l_u",new Map),kt(this,"$$me"),this.$$ctor=e,this.$$s=t,n&&this.attachShadow({mode:"open"})}addEventListener(e,t,n){if(this.$$l[e]=this.$$l[e]||[],this.$$l[e].push(t),this.$$c){const o=this.$$c.$on(e,t);this.$$l_u.set(t,o)}super.addEventListener(e,t,n)}removeEventListener(e,t,n){if(super.removeEventListener(e,t,n),this.$$c){const o=this.$$l_u.get(t);o&&(o(),this.$$l_u.delete(t))}}async connectedCallback(){if(this.$$cn=!0,!this.$$c){let t=function(r){return i=>{const a=document.createElement("slot");r!=="default"&&(a.name=r),T(i,a)}};if(await Promise.resolve(),!this.$$cn||this.$$c)return;const n={},o=function(r){const i={};return r.childNodes.forEach(a=>{i[a.slot||"default"]=!0}),i}(this);for(const r of this.$$s)r in o&&(r!=="default"||this.$$d.children?n[r]=t(r):(this.$$d.children=t(r),n.default=!0));for(const r of this.attributes){const i=this.$$g_p(r.name);i in this.$$d||(this.$$d[i]=li(i,r.value,this.$$p_d,"toProp"))}for(const r in this.$$p_d)!(r in this.$$d)&&this[r]!==void 0&&(this.$$d[r]=this[r],delete this[r]);this.$$c=(e={component:this.$$ctor,target:this.shadowRoot||this,props:{...this.$$d,$$slots:n,$$host:this}},new O1(e)),this.$$me=function(r){const i=lo(Qi,r,!0);return()=>{jt(i)}}(()=>{Mo(()=>{var r;this.$$r=!0;for(const i of Yr(this.$$c)){if((r=this.$$p_d[i])==null||!r.reflect)continue;this.$$d[i]=this.$$c[i];const a=li(i,this.$$d[i],this.$$p_d,"toAttribute");a==null?this.removeAttribute(this.$$p_d[i].attribute||i):this.setAttribute(this.$$p_d[i].attribute||i,a)}this.$$r=!1})});for(const r in this.$$l)for(const i of this.$$l[r]){const a=this.$$c.$on(r,i);this.$$l_u.set(i,a)}this.$$l={}}var e}attributeChangedCallback(e,t,n){var o;this.$$r||(e=this.$$g_p(e),this.$$d[e]=li(e,n,this.$$p_d,"toProp"),(o=this.$$c)==null||o.$set({[e]:this.$$d[e]}))}disconnectedCallback(){this.$$cn=!1,Promise.resolve().then(()=>{!this.$$cn&&this.$$c&&(this.$$c.$destroy(),this.$$me(),this.$$c=void 0)})}$$g_p(e){return Yr(this.$$p_d).find(t=>this.$$p_d[t].attribute===e||!this.$$p_d[t].attribute&&t.toLowerCase()===e)||e}});var D1={value:()=>{}};function si(){for(var e,t=0,n=arguments.length,o={};t<n;++t){if(!(e=arguments[t]+"")||e in o||/[\s.]/.test(e))throw new Error("illegal type: "+e);o[e]=[]}return new ui(o)}function ui(e){this._=e}function T1(e,t){for(var n,o=0,r=e.length;o<r;++o)if((n=e[o]).name===t)return n.value}function xs(e,t,n){for(var o=0,r=e.length;o<r;++o)if(e[o].name===t){e[o]=D1,e=e.slice(0,o).concat(e.slice(o+1));break}return n!=null&&e.push({name:t,value:n}),e}ui.prototype=si.prototype={constructor:ui,on:function(e,t){var n,o=this._,r=function(l,u){return l.trim().split(/^|\s+/).map(function(s){var c="",d=s.indexOf(".");if(d>=0&&(c=s.slice(d+1),s=s.slice(0,d)),s&&!u.hasOwnProperty(s))throw new Error("unknown type: "+s);return{type:s,name:c}})}(e+"",o),i=-1,a=r.length;if(!(arguments.length<2)){if(t!=null&&typeof t!="function")throw new Error("invalid callback: "+t);for(;++i<a;)if(n=(e=r[i]).type)o[n]=xs(o[n],e.name,t);else if(t==null)for(n in o)o[n]=xs(o[n],e.name,null);return this}for(;++i<a;)if((n=(e=r[i]).type)&&(n=T1(o[n],e.name)))return n},copy:function(){var e={},t=this._;for(var n in t)e[n]=t[n].slice();return new ui(e)},call:function(e,t){if((n=arguments.length-2)>0)for(var n,o,r=new Array(n),i=0;i<n;++i)r[i]=arguments[i+2];if(!this._.hasOwnProperty(e))throw new Error("unknown type: "+e);for(i=0,n=(o=this._[e]).length;i<n;++i)o[i].value.apply(t,r)},apply:function(e,t,n){if(!this._.hasOwnProperty(e))throw new Error("unknown type: "+e);for(var o=this._[e],r=0,i=o.length;r<i;++r)o[r].value.apply(t,n)}};var _a="http://www.w3.org/1999/xhtml";const $s={svg:"http://www.w3.org/2000/svg",xhtml:_a,xlink:"http://www.w3.org/1999/xlink",xml:"http://www.w3.org/XML/1998/namespace",xmlns:"http://www.w3.org/2000/xmlns/"};function ci(e){var t=e+="",n=t.indexOf(":");return n>=0&&(t=e.slice(0,n))!=="xmlns"&&(e=e.slice(n+1)),$s.hasOwnProperty(t)?{space:$s[t],local:e}:e}function A1(e){return function(){var t=this.ownerDocument,n=this.namespaceURI;return n===_a&&t.documentElement.namespaceURI===_a?t.createElement(e):t.createElementNS(n,e)}}function I1(e){return function(){return this.ownerDocument.createElementNS(e.space,e.local)}}function Cs(e){var t=ci(e);return(t.local?I1:A1)(t)}function Z1(){}function Sa(e){return e==null?Z1:function(){return this.querySelector(e)}}function B1(){return[]}function ks(e){return e==null?B1:function(){return this.querySelectorAll(e)}}function R1(e){return function(){return function(t){return t==null?[]:Array.isArray(t)?t:Array.from(t)}(e.apply(this,arguments))}}function _s(e){return function(){return this.matches(e)}}function Ss(e){return function(t){return t.matches(e)}}var X1=Array.prototype.find;function Y1(){return this.firstElementChild}var K1=Array.prototype.filter;function W1(){return Array.from(this.children)}function Es(e){return new Array(e.length)}function di(e,t){this.ownerDocument=e.ownerDocument,this.namespaceURI=e.namespaceURI,this._next=null,this._parent=e,this.__data__=t}function j1(e,t,n,o,r,i){for(var a,l=0,u=t.length,s=i.length;l<s;++l)(a=t[l])?(a.__data__=i[l],o[l]=a):n[l]=new di(e,i[l]);for(;l<u;++l)(a=t[l])&&(r[l]=a)}function q1(e,t,n,o,r,i,a){var l,u,s,c=new Map,d=t.length,f=i.length,g=new Array(d);for(l=0;l<d;++l)(u=t[l])&&(g[l]=s=a.call(u,u.__data__,l,t)+"",c.has(s)?r[l]=u:c.set(s,u));for(l=0;l<f;++l)s=a.call(e,i[l],l,i)+"",(u=c.get(s))?(o[l]=u,u.__data__=i[l],c.delete(s)):n[l]=new di(e,i[l]);for(l=0;l<d;++l)(u=t[l])&&c.get(g[l])===u&&(r[l]=u)}function F1(e){return e.__data__}function G1(e){return typeof e=="object"&&"length"in e?e:Array.from(e)}function U1(e,t){return e<t?-1:e>t?1:e>=t?0:NaN}function J1(e){return function(){this.removeAttribute(e)}}function Q1(e){return function(){this.removeAttributeNS(e.space,e.local)}}function e0(e,t){return function(){this.setAttribute(e,t)}}function t0(e,t){return function(){this.setAttributeNS(e.space,e.local,t)}}function n0(e,t){return function(){var n=t.apply(this,arguments);n==null?this.removeAttribute(e):this.setAttribute(e,n)}}function o0(e,t){return function(){var n=t.apply(this,arguments);n==null?this.removeAttributeNS(e.space,e.local):this.setAttributeNS(e.space,e.local,n)}}function Ps(e){return e.ownerDocument&&e.ownerDocument.defaultView||e.document&&e||e.defaultView}function r0(e){return function(){this.style.removeProperty(e)}}function i0(e,t,n){return function(){this.style.setProperty(e,t,n)}}function a0(e,t,n){return function(){var o=t.apply(this,arguments);o==null?this.style.removeProperty(e):this.style.setProperty(e,o,n)}}function Lo(e,t){return e.style.getPropertyValue(t)||Ps(e).getComputedStyle(e,null).getPropertyValue(t)}function l0(e){return function(){delete this[e]}}function s0(e,t){return function(){this[e]=t}}function u0(e,t){return function(){var n=t.apply(this,arguments);n==null?delete this[e]:this[e]=n}}function Ms(e){return e.trim().split(/^|\s+/)}function Ea(e){return e.classList||new Vs(e)}function Vs(e){this._node=e,this._names=Ms(e.getAttribute("class")||"")}function Hs(e,t){for(var n=Ea(e),o=-1,r=t.length;++o<r;)n.add(t[o])}function zs(e,t){for(var n=Ea(e),o=-1,r=t.length;++o<r;)n.remove(t[o])}function c0(e){return function(){Hs(this,e)}}function d0(e){return function(){zs(this,e)}}function f0(e,t){return function(){(t.apply(this,arguments)?Hs:zs)(this,e)}}function v0(){this.textContent=""}function g0(e){return function(){this.textContent=e}}function p0(e){return function(){var t=e.apply(this,arguments);this.textContent=t??""}}function h0(){this.innerHTML=""}function m0(e){return function(){this.innerHTML=e}}function y0(e){return function(){var t=e.apply(this,arguments);this.innerHTML=t??""}}function w0(){this.nextSibling&&this.parentNode.appendChild(this)}function b0(){this.previousSibling&&this.parentNode.insertBefore(this,this.parentNode.firstChild)}function x0(){return null}function $0(){var e=this.parentNode;e&&e.removeChild(this)}function C0(){var e=this.cloneNode(!1),t=this.parentNode;return t?t.insertBefore(e,this.nextSibling):e}function k0(){var e=this.cloneNode(!0),t=this.parentNode;return t?t.insertBefore(e,this.nextSibling):e}function _0(e){return function(){var t=this.__on;if(t){for(var n,o=0,r=-1,i=t.length;o<i;++o)n=t[o],e.type&&n.type!==e.type||n.name!==e.name?t[++r]=n:this.removeEventListener(n.type,n.listener,n.options);++r?t.length=r:delete this.__on}}}function S0(e,t,n){return function(){var o,r=this.__on,i=function(u){return function(s){u.call(this,s,this.__data__)}}(t);if(r){for(var a=0,l=r.length;a<l;++a)if((o=r[a]).type===e.type&&o.name===e.name)return this.removeEventListener(o.type,o.listener,o.options),this.addEventListener(o.type,o.listener=i,o.options=n),void(o.value=t)}this.addEventListener(e.type,i,n),o={type:e.type,name:e.name,value:t,listener:i,options:n},r?r.push(o):this.__on=[o]}}function Ns(e,t,n){var o=Ps(e),r=o.CustomEvent;typeof r=="function"?r=new r(t,n):(r=o.document.createEvent("Event"),n?(r.initEvent(t,n.bubbles,n.cancelable),r.detail=n.detail):r.initEvent(t,!1,!1)),e.dispatchEvent(r)}function E0(e,t){return function(){return Ns(this,e,t)}}function P0(e,t){return function(){return Ns(this,e,t.apply(this,arguments))}}di.prototype={constructor:di,appendChild:function(e){return this._parent.insertBefore(e,this._next)},insertBefore:function(e,t){return this._parent.insertBefore(e,t)},querySelector:function(e){return this._parent.querySelector(e)},querySelectorAll:function(e){return this._parent.querySelectorAll(e)}},Vs.prototype={add:function(e){this._names.indexOf(e)<0&&(this._names.push(e),this._node.setAttribute("class",this._names.join(" ")))},remove:function(e){var t=this._names.indexOf(e);t>=0&&(this._names.splice(t,1),this._node.setAttribute("class",this._names.join(" ")))},contains:function(e){return this._names.indexOf(e)>=0}};var Ls=[null];function It(e,t){this._groups=e,this._parents=t}function dr(){return new It([[document.documentElement]],Ls)}function Ft(e){return typeof e=="string"?new It([[document.querySelector(e)]],[document.documentElement]):new It([[e]],Ls)}function on(e,t){if(e=function(i){let a;for(;a=i.sourceEvent;)i=a;return i}(e),t===void 0&&(t=e.currentTarget),t){var n=t.ownerSVGElement||t;if(n.createSVGPoint){var o=n.createSVGPoint();return o.x=e.clientX,o.y=e.clientY,[(o=o.matrixTransform(t.getScreenCTM().inverse())).x,o.y]}if(t.getBoundingClientRect){var r=t.getBoundingClientRect();return[e.clientX-r.left-t.clientLeft,e.clientY-r.top-t.clientTop]}}return[e.pageX,e.pageY]}It.prototype=dr.prototype={constructor:It,select:function(e){typeof e!="function"&&(e=Sa(e));for(var t=this._groups,n=t.length,o=new Array(n),r=0;r<n;++r)for(var i,a,l=t[r],u=l.length,s=o[r]=new Array(u),c=0;c<u;++c)(i=l[c])&&(a=e.call(i,i.__data__,c,l))&&("__data__"in i&&(a.__data__=i.__data__),s[c]=a);return new It(o,this._parents)},selectAll:function(e){e=typeof e=="function"?R1(e):ks(e);for(var t=this._groups,n=t.length,o=[],r=[],i=0;i<n;++i)for(var a,l=t[i],u=l.length,s=0;s<u;++s)(a=l[s])&&(o.push(e.call(a,a.__data__,s,l)),r.push(a));return new It(o,r)},selectChild:function(e){return this.select(e==null?Y1:function(t){return function(){return X1.call(this.children,t)}}(typeof e=="function"?e:Ss(e)))},selectChildren:function(e){return this.selectAll(e==null?W1:function(t){return function(){return K1.call(this.children,t)}}(typeof e=="function"?e:Ss(e)))},filter:function(e){typeof e!="function"&&(e=_s(e));for(var t=this._groups,n=t.length,o=new Array(n),r=0;r<n;++r)for(var i,a=t[r],l=a.length,u=o[r]=[],s=0;s<l;++s)(i=a[s])&&e.call(i,i.__data__,s,a)&&u.push(i);return new It(o,this._parents)},data:function(e,t){if(!arguments.length)return Array.from(this,F1);var n=t?q1:j1,o=this._parents,r=this._groups;typeof e!="function"&&(e=function(k){return function(){return k}}(e));for(var i=r.length,a=new Array(i),l=new Array(i),u=new Array(i),s=0;s<i;++s){var c=o[s],d=r[s],f=d.length,g=G1(e.call(c,c&&c.__data__,s,o)),m=g.length,v=l[s]=new Array(m),y=a[s]=new Array(m);n(c,d,v,y,u[s]=new Array(f),g,t);for(var w,h,C=0,b=0;C<m;++C)if(w=v[C]){for(C>=b&&(b=C+1);!(h=y[b])&&++b<m;);w._next=h||null}}return(a=new It(a,o))._enter=l,a._exit=u,a},enter:function(){return new It(this._enter||this._groups.map(Es),this._parents)},exit:function(){return new It(this._exit||this._groups.map(Es),this._parents)},join:function(e,t,n){var o=this.enter(),r=this,i=this.exit();return typeof e=="function"?(o=e(o))&&(o=o.selection()):o=o.append(e+""),t!=null&&(r=t(r))&&(r=r.selection()),n==null?i.remove():n(i),o&&r?o.merge(r).order():r},merge:function(e){for(var t=e.selection?e.selection():e,n=this._groups,o=t._groups,r=n.length,i=o.length,a=Math.min(r,i),l=new Array(r),u=0;u<a;++u)for(var s,c=n[u],d=o[u],f=c.length,g=l[u]=new Array(f),m=0;m<f;++m)(s=c[m]||d[m])&&(g[m]=s);for(;u<r;++u)l[u]=n[u];return new It(l,this._parents)},selection:function(){return this},order:function(){for(var e=this._groups,t=-1,n=e.length;++t<n;)for(var o,r=e[t],i=r.length-1,a=r[i];--i>=0;)(o=r[i])&&(a&&4^o.compareDocumentPosition(a)&&a.parentNode.insertBefore(o,a),a=o);return this},sort:function(e){function t(d,f){return d&&f?e(d.__data__,f.__data__):!d-!f}e||(e=U1);for(var n=this._groups,o=n.length,r=new Array(o),i=0;i<o;++i){for(var a,l=n[i],u=l.length,s=r[i]=new Array(u),c=0;c<u;++c)(a=l[c])&&(s[c]=a);s.sort(t)}return new It(r,this._parents).order()},call:function(){var e=arguments[0];return arguments[0]=this,e.apply(null,arguments),this},nodes:function(){return Array.from(this)},node:function(){for(var e=this._groups,t=0,n=e.length;t<n;++t)for(var o=e[t],r=0,i=o.length;r<i;++r){var a=o[r];if(a)return a}return null},size:function(){let e=0;for(const t of this)++e;return e},empty:function(){return!this.node()},each:function(e){for(var t=this._groups,n=0,o=t.length;n<o;++n)for(var r,i=t[n],a=0,l=i.length;a<l;++a)(r=i[a])&&e.call(r,r.__data__,a,i);return this},attr:function(e,t){var n=ci(e);if(arguments.length<2){var o=this.node();return n.local?o.getAttributeNS(n.space,n.local):o.getAttribute(n)}return this.each((t==null?n.local?Q1:J1:typeof t=="function"?n.local?o0:n0:n.local?t0:e0)(n,t))},style:function(e,t,n){return arguments.length>1?this.each((t==null?r0:typeof t=="function"?a0:i0)(e,t,n??"")):Lo(this.node(),e)},property:function(e,t){return arguments.length>1?this.each((t==null?l0:typeof t=="function"?u0:s0)(e,t)):this.node()[e]},classed:function(e,t){var n=Ms(e+"");if(arguments.length<2){for(var o=Ea(this.node()),r=-1,i=n.length;++r<i;)if(!o.contains(n[r]))return!1;return!0}return this.each((typeof t=="function"?f0:t?c0:d0)(n,t))},text:function(e){return arguments.length?this.each(e==null?v0:(typeof e=="function"?p0:g0)(e)):this.node().textContent},html:function(e){return arguments.length?this.each(e==null?h0:(typeof e=="function"?y0:m0)(e)):this.node().innerHTML},raise:function(){return this.each(w0)},lower:function(){return this.each(b0)},append:function(e){var t=typeof e=="function"?e:Cs(e);return this.select(function(){return this.appendChild(t.apply(this,arguments))})},insert:function(e,t){var n=typeof e=="function"?e:Cs(e),o=t==null?x0:typeof t=="function"?t:Sa(t);return this.select(function(){return this.insertBefore(n.apply(this,arguments),o.apply(this,arguments)||null)})},remove:function(){return this.each($0)},clone:function(e){return this.select(e?k0:C0)},datum:function(e){return arguments.length?this.property("__data__",e):this.node().__data__},on:function(e,t,n){var o,r,i=function(d){return d.trim().split(/^|\s+/).map(function(f){var g="",m=f.indexOf(".");return m>=0&&(g=f.slice(m+1),f=f.slice(0,m)),{type:f,name:g}})}(e+""),a=i.length;if(!(arguments.length<2)){for(l=t?S0:_0,o=0;o<a;++o)this.each(l(i[o],t,n));return this}var l=this.node().__on;if(l){for(var u,s=0,c=l.length;s<c;++s)for(o=0,u=l[s];o<a;++o)if((r=i[o]).type===u.type&&r.name===u.name)return u.value}},dispatch:function(e,t){return this.each((typeof t=="function"?P0:E0)(e,t))},[Symbol.iterator]:function*(){for(var e=this._groups,t=0,n=e.length;t<n;++t)for(var o,r=e[t],i=0,a=r.length;i<a;++i)(o=r[i])&&(yield o)}};const M0={passive:!1},fr={capture:!0,passive:!1};function Pa(e){e.stopImmediatePropagation()}function Oo(e){e.preventDefault(),e.stopImmediatePropagation()}function Os(e){var t=e.document.documentElement,n=Ft(e).on("dragstart.drag",Oo,fr);"onselectstart"in t?n.on("selectstart.drag",Oo,fr):(t.__noselect=t.style.MozUserSelect,t.style.MozUserSelect="none")}function Ds(e,t){var n=e.document.documentElement,o=Ft(e).on("dragstart.drag",null);t&&(o.on("click.drag",Oo,fr),setTimeout(function(){o.on("click.drag",null)},0)),"onselectstart"in n?o.on("selectstart.drag",null):(n.style.MozUserSelect=n.__noselect,delete n.__noselect)}const fi=e=>()=>e;function Ma(e,{sourceEvent:t,subject:n,target:o,identifier:r,active:i,x:a,y:l,dx:u,dy:s,dispatch:c}){Object.defineProperties(this,{type:{value:e,enumerable:!0,configurable:!0},sourceEvent:{value:t,enumerable:!0,configurable:!0},subject:{value:n,enumerable:!0,configurable:!0},target:{value:o,enumerable:!0,configurable:!0},identifier:{value:r,enumerable:!0,configurable:!0},active:{value:i,enumerable:!0,configurable:!0},x:{value:a,enumerable:!0,configurable:!0},y:{value:l,enumerable:!0,configurable:!0},dx:{value:u,enumerable:!0,configurable:!0},dy:{value:s,enumerable:!0,configurable:!0},_:{value:c}})}function V0(e){return!e.ctrlKey&&!e.button}function H0(){return this.parentNode}function z0(e,t){return t??{x:e.x,y:e.y}}function N0(){return navigator.maxTouchPoints||"ontouchstart"in this}function Va(e,t,n){e.prototype=t.prototype=n,n.constructor=e}function Ts(e,t){var n=Object.create(e.prototype);for(var o in t)n[o]=t[o];return n}function vr(){}Ma.prototype.on=function(){var e=this._.on.apply(this._,arguments);return e===this._?this:e};var gr=.7,vi=1/gr,Do="\\s*([+-]?\\d+)\\s*",pr="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",yn="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",L0=/^#([0-9a-f]{3,8})$/,O0=new RegExp(`^rgb\\(${Do},${Do},${Do}\\)$`),D0=new RegExp(`^rgb\\(${yn},${yn},${yn}\\)$`),T0=new RegExp(`^rgba\\(${Do},${Do},${Do},${pr}\\)$`),A0=new RegExp(`^rgba\\(${yn},${yn},${yn},${pr}\\)$`),I0=new RegExp(`^hsl\\(${pr},${yn},${yn}\\)$`),Z0=new RegExp(`^hsla\\(${pr},${yn},${yn},${pr}\\)$`),As={aliceblue:15792383,antiquewhite:16444375,aqua:65535,aquamarine:8388564,azure:15794175,beige:16119260,bisque:16770244,black:0,blanchedalmond:16772045,blue:255,blueviolet:9055202,brown:10824234,burlywood:14596231,cadetblue:6266528,chartreuse:8388352,chocolate:13789470,coral:16744272,cornflowerblue:6591981,cornsilk:16775388,crimson:14423100,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:12092939,darkgray:11119017,darkgreen:25600,darkgrey:11119017,darkkhaki:12433259,darkmagenta:9109643,darkolivegreen:5597999,darkorange:16747520,darkorchid:10040012,darkred:9109504,darksalmon:15308410,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:16716947,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:11674146,floralwhite:16775920,forestgreen:2263842,fuchsia:16711935,gainsboro:14474460,ghostwhite:16316671,gold:16766720,goldenrod:14329120,gray:8421504,green:32768,greenyellow:11403055,grey:8421504,honeydew:15794160,hotpink:16738740,indianred:13458524,indigo:4915330,ivory:16777200,khaki:15787660,lavender:15132410,lavenderblush:16773365,lawngreen:8190976,lemonchiffon:16775885,lightblue:11393254,lightcoral:15761536,lightcyan:14745599,lightgoldenrodyellow:16448210,lightgray:13882323,lightgreen:9498256,lightgrey:13882323,lightpink:16758465,lightsalmon:16752762,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:11584734,lightyellow:16777184,lime:65280,limegreen:3329330,linen:16445670,magenta:16711935,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:12211667,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:13047173,midnightblue:1644912,mintcream:16121850,mistyrose:16770273,moccasin:16770229,navajowhite:16768685,navy:128,oldlace:16643558,olive:8421376,olivedrab:7048739,orange:16753920,orangered:16729344,orchid:14315734,palegoldenrod:15657130,palegreen:10025880,paleturquoise:11529966,palevioletred:14381203,papayawhip:16773077,peachpuff:16767673,peru:13468991,pink:16761035,plum:14524637,powderblue:11591910,purple:8388736,rebeccapurple:6697881,red:16711680,rosybrown:12357519,royalblue:4286945,saddlebrown:9127187,salmon:16416882,sandybrown:16032864,seagreen:3050327,seashell:16774638,sienna:10506797,silver:12632256,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:16775930,springgreen:65407,steelblue:4620980,tan:13808780,teal:32896,thistle:14204888,tomato:16737095,turquoise:4251856,violet:15631086,wheat:16113331,white:16777215,whitesmoke:16119285,yellow:16776960,yellowgreen:10145074};function Is(){return this.rgb().formatHex()}function Zs(){return this.rgb().formatRgb()}function hr(e){var t,n;return e=(e+"").trim().toLowerCase(),(t=L0.exec(e))?(n=t[1].length,t=parseInt(t[1],16),n===6?Bs(t):n===3?new Vt(t>>8&15|t>>4&240,t>>4&15|240&t,(15&t)<<4|15&t,1):n===8?gi(t>>24&255,t>>16&255,t>>8&255,(255&t)/255):n===4?gi(t>>12&15|t>>8&240,t>>8&15|t>>4&240,t>>4&15|240&t,((15&t)<<4|15&t)/255):null):(t=O0.exec(e))?new Vt(t[1],t[2],t[3],1):(t=D0.exec(e))?new Vt(255*t[1]/100,255*t[2]/100,255*t[3]/100,1):(t=T0.exec(e))?gi(t[1],t[2],t[3],t[4]):(t=A0.exec(e))?gi(255*t[1]/100,255*t[2]/100,255*t[3]/100,t[4]):(t=I0.exec(e))?Ys(t[1],t[2]/100,t[3]/100,1):(t=Z0.exec(e))?Ys(t[1],t[2]/100,t[3]/100,t[4]):As.hasOwnProperty(e)?Bs(As[e]):e==="transparent"?new Vt(NaN,NaN,NaN,0):null}function Bs(e){return new Vt(e>>16&255,e>>8&255,255&e,1)}function gi(e,t,n,o){return o<=0&&(e=t=n=NaN),new Vt(e,t,n,o)}function Ha(e,t,n,o){return arguments.length===1?function(r){return r instanceof vr||(r=hr(r)),r?new Vt((r=r.rgb()).r,r.g,r.b,r.opacity):new Vt}(e):new Vt(e,t,n,o??1)}function Vt(e,t,n,o){this.r=+e,this.g=+t,this.b=+n,this.opacity=+o}function Rs(){return`#${go(this.r)}${go(this.g)}${go(this.b)}`}function Xs(){const e=pi(this.opacity);return`${e===1?"rgb(":"rgba("}${vo(this.r)}, ${vo(this.g)}, ${vo(this.b)}${e===1?")":`, ${e})`}`}function pi(e){return isNaN(e)?1:Math.max(0,Math.min(1,e))}function vo(e){return Math.max(0,Math.min(255,Math.round(e)||0))}function go(e){return((e=vo(e))<16?"0":"")+e.toString(16)}function Ys(e,t,n,o){return o<=0?e=t=n=NaN:n<=0||n>=1?e=t=NaN:t<=0&&(e=NaN),new rn(e,t,n,o)}function Ks(e){if(e instanceof rn)return new rn(e.h,e.s,e.l,e.opacity);if(e instanceof vr||(e=hr(e)),!e)return new rn;if(e instanceof rn)return e;var t=(e=e.rgb()).r/255,n=e.g/255,o=e.b/255,r=Math.min(t,n,o),i=Math.max(t,n,o),a=NaN,l=i-r,u=(i+r)/2;return l?(a=t===i?(n-o)/l+6*(n<o):n===i?(o-t)/l+2:(t-n)/l+4,l/=u<.5?i+r:2-i-r,a*=60):l=u>0&&u<1?0:a,new rn(a,l,u,e.opacity)}function rn(e,t,n,o){this.h=+e,this.s=+t,this.l=+n,this.opacity=+o}function Ws(e){return(e=(e||0)%360)<0?e+360:e}function hi(e){return Math.max(0,Math.min(1,e||0))}function za(e,t,n){return 255*(e<60?t+(n-t)*e/60:e<180?n:e<240?t+(n-t)*(240-e)/60:t)}Va(vr,hr,{copy(e){return Object.assign(new this.constructor,this,e)},displayable(){return this.rgb().displayable()},hex:Is,formatHex:Is,formatHex8:function(){return this.rgb().formatHex8()},formatHsl:function(){return Ks(this).formatHsl()},formatRgb:Zs,toString:Zs}),Va(Vt,Ha,Ts(vr,{brighter(e){return e=e==null?vi:Math.pow(vi,e),new Vt(this.r*e,this.g*e,this.b*e,this.opacity)},darker(e){return e=e==null?gr:Math.pow(gr,e),new Vt(this.r*e,this.g*e,this.b*e,this.opacity)},rgb(){return this},clamp(){return new Vt(vo(this.r),vo(this.g),vo(this.b),pi(this.opacity))},displayable(){return-.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:Rs,formatHex:Rs,formatHex8:function(){return`#${go(this.r)}${go(this.g)}${go(this.b)}${go(255*(isNaN(this.opacity)?1:this.opacity))}`},formatRgb:Xs,toString:Xs})),Va(rn,function(e,t,n,o){return arguments.length===1?Ks(e):new rn(e,t,n,o??1)},Ts(vr,{brighter(e){return e=e==null?vi:Math.pow(vi,e),new rn(this.h,this.s,this.l*e,this.opacity)},darker(e){return e=e==null?gr:Math.pow(gr,e),new rn(this.h,this.s,this.l*e,this.opacity)},rgb(){var e=this.h%360+360*(this.h<0),t=isNaN(e)||isNaN(this.s)?0:this.s,n=this.l,o=n+(n<.5?n:1-n)*t,r=2*n-o;return new Vt(za(e>=240?e-240:e+120,r,o),za(e,r,o),za(e<120?e+240:e-120,r,o),this.opacity)},clamp(){return new rn(Ws(this.h),hi(this.s),hi(this.l),pi(this.opacity))},displayable(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl(){const e=pi(this.opacity);return`${e===1?"hsl(":"hsla("}${Ws(this.h)}, ${100*hi(this.s)}%, ${100*hi(this.l)}%${e===1?")":`, ${e})`}`}}));const js=e=>()=>e;function B0(e){return(e=+e)==1?qs:function(t,n){return n-t?function(o,r,i){return o=Math.pow(o,i),r=Math.pow(r,i)-o,i=1/i,function(a){return Math.pow(o+a*r,i)}}(t,n,e):js(isNaN(t)?n:t)}}function qs(e,t){var n=t-e;return n?function(o,r){return function(i){return o+i*r}}(e,n):js(isNaN(e)?t:e)}const Fs=function e(t){var n=B0(t);function o(r,i){var a=n((r=Ha(r)).r,(i=Ha(i)).r),l=n(r.g,i.g),u=n(r.b,i.b),s=qs(r.opacity,i.opacity);return function(c){return r.r=a(c),r.g=l(c),r.b=u(c),r.opacity=s(c),r+""}}return o.gamma=e,o}(1);function Un(e,t){return e=+e,t=+t,function(n){return e*(1-n)+t*n}}var Na=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,La=new RegExp(Na.source,"g");function R0(e,t){var n,o,r,i=Na.lastIndex=La.lastIndex=0,a=-1,l=[],u=[];for(e+="",t+="";(n=Na.exec(e))&&(o=La.exec(t));)(r=o.index)>i&&(r=t.slice(i,r),l[a]?l[a]+=r:l[++a]=r),(n=n[0])===(o=o[0])?l[a]?l[a]+=o:l[++a]=o:(l[++a]=null,u.push({i:a,x:Un(n,o)})),i=La.lastIndex;return i<t.length&&(r=t.slice(i),l[a]?l[a]+=r:l[++a]=r),l.length<2?u[0]?function(s){return function(c){return s(c)+""}}(u[0].x):function(s){return function(){return s}}(t):(t=u.length,function(s){for(var c,d=0;d<t;++d)l[(c=u[d]).i]=c.x(s);return l.join("")})}var mi,Gs=180/Math.PI,Us={translateX:0,translateY:0,rotate:0,skewX:0,scaleX:1,scaleY:1};function Js(e,t,n,o,r,i){var a,l,u;return(a=Math.sqrt(e*e+t*t))&&(e/=a,t/=a),(u=e*n+t*o)&&(n-=e*u,o-=t*u),(l=Math.sqrt(n*n+o*o))&&(n/=l,o/=l,u/=l),e*o<t*n&&(e=-e,t=-t,u=-u,a=-a),{translateX:r,translateY:i,rotate:Math.atan2(t,e)*Gs,skewX:Math.atan(u)*Gs,scaleX:a,scaleY:l}}function Qs(e,t,n,o){function r(i){return i.length?i.pop()+" ":""}return function(i,a){var l=[],u=[];return i=e(i),a=e(a),function(s,c,d,f,g,m){if(s!==d||c!==f){var v=g.push("translate(",null,t,null,n);m.push({i:v-4,x:Un(s,d)},{i:v-2,x:Un(c,f)})}else(d||f)&&g.push("translate("+d+t+f+n)}(i.translateX,i.translateY,a.translateX,a.translateY,l,u),function(s,c,d,f){s!==c?(s-c>180?c+=360:c-s>180&&(s+=360),f.push({i:d.push(r(d)+"rotate(",null,o)-2,x:Un(s,c)})):c&&d.push(r(d)+"rotate("+c+o)}(i.rotate,a.rotate,l,u),function(s,c,d,f){s!==c?f.push({i:d.push(r(d)+"skewX(",null,o)-2,x:Un(s,c)}):c&&d.push(r(d)+"skewX("+c+o)}(i.skewX,a.skewX,l,u),function(s,c,d,f,g,m){if(s!==d||c!==f){var v=g.push(r(g)+"scale(",null,",",null,")");m.push({i:v-4,x:Un(s,d)},{i:v-2,x:Un(c,f)})}else(d!==1||f!==1)&&g.push(r(g)+"scale("+d+","+f+")")}(i.scaleX,i.scaleY,a.scaleX,a.scaleY,l,u),i=a=null,function(s){for(var c,d=-1,f=u.length;++d<f;)l[(c=u[d]).i]=c.x(s);return l.join("")}}}var X0=Qs(function(e){const t=new(typeof DOMMatrix=="function"?DOMMatrix:WebKitCSSMatrix)(e+"");return t.isIdentity?Us:Js(t.a,t.b,t.c,t.d,t.e,t.f)},"px, ","px)","deg)"),Y0=Qs(function(e){return e!=null&&(mi||(mi=document.createElementNS("http://www.w3.org/2000/svg","g")),mi.setAttribute("transform",e),e=mi.transform.baseVal.consolidate())?Js((e=e.matrix).a,e.b,e.c,e.d,e.e,e.f):Us},", ",")",")");function eu(e){return((e=Math.exp(e))+1/e)/2}const K0=function e(t,n,o){function r(i,a){var l,u,s=i[0],c=i[1],d=i[2],f=a[0],g=a[1],m=a[2],v=f-s,y=g-c,w=v*v+y*y;if(w<1e-12)u=Math.log(m/d)/t,l=function(S){return[s+S*v,c+S*y,d*Math.exp(t*S*u)]};else{var h=Math.sqrt(w),C=(m*m-d*d+o*w)/(2*d*n*h),b=(m*m-d*d-o*w)/(2*m*n*h),k=Math.log(Math.sqrt(C*C+1)-C),_=Math.log(Math.sqrt(b*b+1)-b);u=(_-k)/t,l=function(S){var E=S*u,L=eu(k),P=d/(n*h)*(L*function(D){return((D=Math.exp(2*D))-1)/(D+1)}(t*E+k)-function(D){return((D=Math.exp(D))-1/D)/2}(k));return[s+P*v,c+P*y,d*L/eu(t*E+k)]}}return l.duration=1e3*u*t/Math.SQRT2,l}return r.rho=function(i){var a=Math.max(.001,+i),l=a*a;return e(a,l,l*l)},r}(Math.SQRT2,2,4);var yi,mr,To=0,yr=0,wr=0,wi=0,po=0,bi=0,br=typeof performance=="object"&&performance.now?performance:Date,tu=typeof window=="object"&&window.requestAnimationFrame?window.requestAnimationFrame.bind(window):function(e){setTimeout(e,17)};function Oa(){return po||(tu(W0),po=br.now()+bi)}function W0(){po=0}function xi(){this._call=this._time=this._next=null}function nu(e,t,n){var o=new xi;return o.restart(e,t,n),o}function ou(){po=(wi=br.now())+bi,To=yr=0;try{(function(){Oa(),++To;for(var e,t=yi;t;)(e=po-t._time)>=0&&t._call.call(void 0,e),t=t._next;--To})()}finally{To=0,function(){for(var e,t,n=yi,o=1/0;n;)n._call?(o>n._time&&(o=n._time),e=n,n=n._next):(t=n._next,n._next=null,n=e?e._next=t:yi=t);mr=e,Da(o)}(),po=0}}function j0(){var e=br.now(),t=e-wi;t>1e3&&(bi-=t,wi=e)}function Da(e){To||(yr&&(yr=clearTimeout(yr)),e-po>24?(e<1/0&&(yr=setTimeout(ou,e-br.now()-bi)),wr&&(wr=clearInterval(wr))):(wr||(wi=br.now(),wr=setInterval(j0,1e3)),To=1,tu(ou)))}function ru(e,t,n){var o=new xi;return t=t==null?0:+t,o.restart(r=>{o.stop(),e(r+t)},t,n),o}xi.prototype=nu.prototype={constructor:xi,restart:function(e,t,n){if(typeof e!="function")throw new TypeError("callback is not a function");n=(n==null?Oa():+n)+(t==null?0:+t),!this._next&&mr!==this&&(mr?mr._next=this:yi=this,mr=this),this._call=e,this._time=n,Da()},stop:function(){this._call&&(this._call=null,this._time=1/0,Da())}};var q0=si("start","end","cancel","interrupt"),F0=[];function $i(e,t,n,o,r,i){var a=e.__transition;if(a){if(n in a)return}else e.__transition={};(function(l,u,s){var c,d=l.__transition;function f(y){s.state=1,s.timer.restart(g,s.delay,s.time),s.delay<=y&&g(y-s.delay)}function g(y){var w,h,C,b;if(s.state!==1)return v();for(w in d)if((b=d[w]).name===s.name){if(b.state===3)return ru(g);b.state===4?(b.state=6,b.timer.stop(),b.on.call("interrupt",l,l.__data__,b.index,b.group),delete d[w]):+w<u&&(b.state=6,b.timer.stop(),b.on.call("cancel",l,l.__data__,b.index,b.group),delete d[w])}if(ru(function(){s.state===3&&(s.state=4,s.timer.restart(m,s.delay,s.time),m(y))}),s.state=2,s.on.call("start",l,l.__data__,s.index,s.group),s.state===2){for(s.state=3,c=new Array(C=s.tween.length),w=0,h=-1;w<C;++w)(b=s.tween[w].value.call(l,l.__data__,s.index,s.group))&&(c[++h]=b);c.length=h+1}}function m(y){for(var w=y<s.duration?s.ease.call(null,y/s.duration):(s.timer.restart(v),s.state=5,1),h=-1,C=c.length;++h<C;)c[h].call(l,w);s.state===5&&(s.on.call("end",l,l.__data__,s.index,s.group),v())}function v(){for(var y in s.state=6,s.timer.stop(),delete d[u],d)return;delete l.__transition}d[u]=s,s.timer=nu(f,0,s.time)})(e,n,{name:t,index:o,group:r,on:q0,tween:F0,time:i.time,delay:i.delay,duration:i.duration,ease:i.ease,timer:null,state:0})}function Ta(e,t){var n=an(e,t);if(n.state>0)throw new Error("too late; already scheduled");return n}function wn(e,t){var n=an(e,t);if(n.state>3)throw new Error("too late; already running");return n}function an(e,t){var n=e.__transition;if(!n||!(n=n[t]))throw new Error("transition not found");return n}function Ci(e,t){var n,o,r,i=e.__transition,a=!0;if(i){for(r in t=t==null?null:t+"",i)(n=i[r]).name===t?(o=n.state>2&&n.state<5,n.state=6,n.timer.stop(),n.on.call(o?"interrupt":"cancel",e,e.__data__,n.index,n.group),delete i[r]):a=!1;a&&delete e.__transition}}function G0(e,t){var n,o;return function(){var r=wn(this,e),i=r.tween;if(i!==n){for(var a=0,l=(o=n=i).length;a<l;++a)if(o[a].name===t){(o=o.slice()).splice(a,1);break}}r.tween=o}}function U0(e,t,n){var o,r;if(typeof n!="function")throw new Error;return function(){var i=wn(this,e),a=i.tween;if(a!==o){r=(o=a).slice();for(var l={name:t,value:n},u=0,s=r.length;u<s;++u)if(r[u].name===t){r[u]=l;break}u===s&&r.push(l)}i.tween=r}}function Aa(e,t,n){var o=e._id;return e.each(function(){var r=wn(this,o);(r.value||(r.value={}))[t]=n.apply(this,arguments)}),function(r){return an(r,o).value[t]}}function iu(e,t){var n;return(typeof t=="number"?Un:t instanceof hr?Fs:(n=hr(t))?(t=n,Fs):R0)(e,t)}function J0(e){return function(){this.removeAttribute(e)}}function Q0(e){return function(){this.removeAttributeNS(e.space,e.local)}}function ef(e,t,n){var o,r,i=n+"";return function(){var a=this.getAttribute(e);return a===i?null:a===o?r:r=t(o=a,n)}}function tf(e,t,n){var o,r,i=n+"";return function(){var a=this.getAttributeNS(e.space,e.local);return a===i?null:a===o?r:r=t(o=a,n)}}function nf(e,t,n){var o,r,i;return function(){var a,l,u=n(this);return u==null?void this.removeAttribute(e):(a=this.getAttribute(e))===(l=u+"")?null:a===o&&l===r?i:(r=l,i=t(o=a,u))}}function of(e,t,n){var o,r,i;return function(){var a,l,u=n(this);return u==null?void this.removeAttributeNS(e.space,e.local):(a=this.getAttributeNS(e.space,e.local))===(l=u+"")?null:a===o&&l===r?i:(r=l,i=t(o=a,u))}}function rf(e,t){var n,o;function r(){var i=t.apply(this,arguments);return i!==o&&(n=(o=i)&&function(a,l){return function(u){this.setAttributeNS(a.space,a.local,l.call(this,u))}}(e,i)),n}return r._value=t,r}function af(e,t){var n,o;function r(){var i=t.apply(this,arguments);return i!==o&&(n=(o=i)&&function(a,l){return function(u){this.setAttribute(a,l.call(this,u))}}(e,i)),n}return r._value=t,r}function lf(e,t){return function(){Ta(this,e).delay=+t.apply(this,arguments)}}function sf(e,t){return t=+t,function(){Ta(this,e).delay=t}}function uf(e,t){return function(){wn(this,e).duration=+t.apply(this,arguments)}}function cf(e,t){return t=+t,function(){wn(this,e).duration=t}}var df=dr.prototype.constructor;function au(e){return function(){this.style.removeProperty(e)}}var ff=0;function Hn(e,t,n,o){this._groups=e,this._parents=t,this._name=n,this._id=o}function lu(){return++ff}var zn=dr.prototype;Hn.prototype={constructor:Hn,select:function(e){var t=this._name,n=this._id;typeof e!="function"&&(e=Sa(e));for(var o=this._groups,r=o.length,i=new Array(r),a=0;a<r;++a)for(var l,u,s=o[a],c=s.length,d=i[a]=new Array(c),f=0;f<c;++f)(l=s[f])&&(u=e.call(l,l.__data__,f,s))&&("__data__"in l&&(u.__data__=l.__data__),d[f]=u,$i(d[f],t,n,f,d,an(l,n)));return new Hn(i,this._parents,t,n)},selectAll:function(e){var t=this._name,n=this._id;typeof e!="function"&&(e=ks(e));for(var o=this._groups,r=o.length,i=[],a=[],l=0;l<r;++l)for(var u,s=o[l],c=s.length,d=0;d<c;++d)if(u=s[d]){for(var f,g=e.call(u,u.__data__,d,s),m=an(u,n),v=0,y=g.length;v<y;++v)(f=g[v])&&$i(f,t,n,v,g,m);i.push(g),a.push(u)}return new Hn(i,a,t,n)},selectChild:zn.selectChild,selectChildren:zn.selectChildren,filter:function(e){typeof e!="function"&&(e=_s(e));for(var t=this._groups,n=t.length,o=new Array(n),r=0;r<n;++r)for(var i,a=t[r],l=a.length,u=o[r]=[],s=0;s<l;++s)(i=a[s])&&e.call(i,i.__data__,s,a)&&u.push(i);return new Hn(o,this._parents,this._name,this._id)},merge:function(e){if(e._id!==this._id)throw new Error;for(var t=this._groups,n=e._groups,o=t.length,r=n.length,i=Math.min(o,r),a=new Array(o),l=0;l<i;++l)for(var u,s=t[l],c=n[l],d=s.length,f=a[l]=new Array(d),g=0;g<d;++g)(u=s[g]||c[g])&&(f[g]=u);for(;l<o;++l)a[l]=t[l];return new Hn(a,this._parents,this._name,this._id)},selection:function(){return new df(this._groups,this._parents)},transition:function(){for(var e=this._name,t=this._id,n=lu(),o=this._groups,r=o.length,i=0;i<r;++i)for(var a,l=o[i],u=l.length,s=0;s<u;++s)if(a=l[s]){var c=an(a,t);$i(a,e,n,s,l,{time:c.time+c.delay+c.duration,delay:0,duration:c.duration,ease:c.ease})}return new Hn(o,this._parents,e,n)},call:zn.call,nodes:zn.nodes,node:zn.node,size:zn.size,empty:zn.empty,each:zn.each,on:function(e,t){var n=this._id;return arguments.length<2?an(this.node(),n).on.on(e):this.each(function(o,r,i){var a,l,u=function(s){return(s+"").trim().split(/^|\s+/).every(function(c){var d=c.indexOf(".");return d>=0&&(c=c.slice(0,d)),!c||c==="start"})}(r)?Ta:wn;return function(){var s=u(this,o),c=s.on;c!==a&&(l=(a=c).copy()).on(r,i),s.on=l}}(n,e,t))},attr:function(e,t){var n=ci(e),o=n==="transform"?Y0:iu;return this.attrTween(e,typeof t=="function"?(n.local?of:nf)(n,o,Aa(this,"attr."+e,t)):t==null?(n.local?Q0:J0)(n):(n.local?tf:ef)(n,o,t))},attrTween:function(e,t){var n="attr."+e;if(arguments.length<2)return(n=this.tween(n))&&n._value;if(t==null)return this.tween(n,null);if(typeof t!="function")throw new Error;var o=ci(e);return this.tween(n,(o.local?rf:af)(o,t))},style:function(e,t,n){var o=(e+="")=="transform"?X0:iu;return t==null?this.styleTween(e,function(r,i){var a,l,u;return function(){var s=Lo(this,r),c=(this.style.removeProperty(r),Lo(this,r));return s===c?null:s===a&&c===l?u:u=i(a=s,l=c)}}(e,o)).on("end.style."+e,au(e)):typeof t=="function"?this.styleTween(e,function(r,i,a){var l,u,s;return function(){var c=Lo(this,r),d=a(this),f=d+"";return d==null&&(this.style.removeProperty(r),f=d=Lo(this,r)),c===f?null:c===l&&f===u?s:(u=f,s=i(l=c,d))}}(e,o,Aa(this,"style."+e,t))).each(function(r,i){var a,l,u,s,c="style."+i,d="end."+c;return function(){var f=wn(this,r),g=f.on,m=f.value[c]==null?s||(s=au(i)):void 0;(g!==a||u!==m)&&(l=(a=g).copy()).on(d,u=m),f.on=l}}(this._id,e)):this.styleTween(e,function(r,i,a){var l,u,s=a+"";return function(){var c=Lo(this,r);return c===s?null:c===l?u:u=i(l=c,a)}}(e,o,t),n).on("end.style."+e,null)},styleTween:function(e,t,n){var o="style."+(e+="");if(arguments.length<2)return(o=this.tween(o))&&o._value;if(t==null)return this.tween(o,null);if(typeof t!="function")throw new Error;return this.tween(o,function(r,i,a){var l,u;function s(){var c=i.apply(this,arguments);return c!==u&&(l=(u=c)&&function(d,f,g){return function(m){this.style.setProperty(d,f.call(this,m),g)}}(r,c,a)),l}return s._value=i,s}(e,t,n??""))},text:function(e){return this.tween("text",typeof e=="function"?function(t){return function(){var n=t(this);this.textContent=n??""}}(Aa(this,"text",e)):function(t){return function(){this.textContent=t}}(e==null?"":e+""))},textTween:function(e){var t="text";if(arguments.length<1)return(t=this.tween(t))&&t._value;if(e==null)return this.tween(t,null);if(typeof e!="function")throw new Error;return this.tween(t,function(n){var o,r;function i(){var a=n.apply(this,arguments);return a!==r&&(o=(r=a)&&function(l){return function(u){this.textContent=l.call(this,u)}}(a)),o}return i._value=n,i}(e))},remove:function(){return this.on("end.remove",(e=this._id,function(){var t=this.parentNode;for(var n in this.__transition)if(+n!==e)return;t&&t.removeChild(this)}));var e},tween:function(e,t){var n=this._id;if(e+="",arguments.length<2){for(var o,r=an(this.node(),n).tween,i=0,a=r.length;i<a;++i)if((o=r[i]).name===e)return o.value;return null}return this.each((t==null?G0:U0)(n,e,t))},delay:function(e){var t=this._id;return arguments.length?this.each((typeof e=="function"?lf:sf)(t,e)):an(this.node(),t).delay},duration:function(e){var t=this._id;return arguments.length?this.each((typeof e=="function"?uf:cf)(t,e)):an(this.node(),t).duration},ease:function(e){var t=this._id;return arguments.length?this.each(function(n,o){if(typeof o!="function")throw new Error;return function(){wn(this,n).ease=o}}(t,e)):an(this.node(),t).ease},easeVarying:function(e){if(typeof e!="function")throw new Error;return this.each(function(t,n){return function(){var o=n.apply(this,arguments);if(typeof o!="function")throw new Error;wn(this,t).ease=o}}(this._id,e))},end:function(){var e,t,n=this,o=n._id,r=n.size();return new Promise(function(i,a){var l={value:a},u={value:function(){--r==0&&i()}};n.each(function(){var s=wn(this,o),c=s.on;c!==e&&((t=(e=c).copy())._.cancel.push(l),t._.interrupt.push(l),t._.end.push(u)),s.on=t}),r===0&&i()})},[Symbol.iterator]:zn[Symbol.iterator]};var vf={time:null,delay:0,duration:250,ease:function(e){return((e*=2)<=1?e*e*e:(e-=2)*e*e+2)/2}};function gf(e,t){for(var n;!(n=e.__transition)||!(n=n[t]);)if(!(e=e.parentNode))throw new Error(`transition ${t} not found`);return n}dr.prototype.interrupt=function(e){return this.each(function(){Ci(this,e)})},dr.prototype.transition=function(e){var t,n;e instanceof Hn?(t=e._id,e=e._name):(t=lu(),(n=vf).time=Oa(),e=e==null?null:e+"");for(var o=this._groups,r=o.length,i=0;i<r;++i)for(var a,l=o[i],u=l.length,s=0;s<u;++s)(a=l[s])&&$i(a,e,t,s,l,n||gf(a,t));return new Hn(o,this._parents,e,t)};const ki=e=>()=>e;function pf(e,{sourceEvent:t,target:n,transform:o,dispatch:r}){Object.defineProperties(this,{type:{value:e,enumerable:!0,configurable:!0},sourceEvent:{value:t,enumerable:!0,configurable:!0},target:{value:n,enumerable:!0,configurable:!0},transform:{value:o,enumerable:!0,configurable:!0},_:{value:r}})}function Nn(e,t,n){this.k=e,this.x=t,this.y=n}Nn.prototype={constructor:Nn,scale:function(e){return e===1?this:new Nn(this.k*e,this.x,this.y)},translate:function(e,t){return e===0&t===0?this:new Nn(this.k,this.x+this.k*e,this.y+this.k*t)},apply:function(e){return[e[0]*this.k+this.x,e[1]*this.k+this.y]},applyX:function(e){return e*this.k+this.x},applyY:function(e){return e*this.k+this.y},invert:function(e){return[(e[0]-this.x)/this.k,(e[1]-this.y)/this.k]},invertX:function(e){return(e-this.x)/this.k},invertY:function(e){return(e-this.y)/this.k},rescaleX:function(e){return e.copy().domain(e.range().map(this.invertX,this).map(e.invert,e))},rescaleY:function(e){return e.copy().domain(e.range().map(this.invertY,this).map(e.invert,e))},toString:function(){return"translate("+this.x+","+this.y+") scale("+this.k+")"}};var _i=new Nn(1,0,0);function su(e){for(;!e.__zoom;)if(!(e=e.parentNode))return _i;return e.__zoom}function Ia(e){e.stopImmediatePropagation()}function xr(e){e.preventDefault(),e.stopImmediatePropagation()}function hf(e){return!(e.ctrlKey&&e.type!=="wheel"||e.button)}function mf(){var e=this;return e instanceof SVGElement?(e=e.ownerSVGElement||e).hasAttribute("viewBox")?[[(e=e.viewBox.baseVal).x,e.y],[e.x+e.width,e.y+e.height]]:[[0,0],[e.width.baseVal.value,e.height.baseVal.value]]:[[0,0],[e.clientWidth,e.clientHeight]]}function uu(){return this.__zoom||_i}function yf(e){return-e.deltaY*(e.deltaMode===1?.05:e.deltaMode?1:.002)*(e.ctrlKey?10:1)}function wf(){return navigator.maxTouchPoints||"ontouchstart"in this}function bf(e,t,n){var o=e.invertX(t[0][0])-n[0][0],r=e.invertX(t[1][0])-n[1][0],i=e.invertY(t[0][1])-n[0][1],a=e.invertY(t[1][1])-n[1][1];return e.translate(r>o?(o+r)/2:Math.min(0,o)||Math.max(0,r),a>i?(i+a)/2:Math.min(0,i)||Math.max(0,a))}function cu(){var e,t,n,o=hf,r=mf,i=bf,a=yf,l=wf,u=[0,1/0],s=[[-1/0,-1/0],[1/0,1/0]],c=250,d=K0,f=si("start","zoom","end"),g=0,m=10;function v(M){M.property("__zoom",uu).on("wheel.zoom",_,{passive:!1}).on("mousedown.zoom",S).on("dblclick.zoom",E).filter(l).on("touchstart.zoom",L).on("touchmove.zoom",P).on("touchend.zoom touchcancel.zoom",D).style("-webkit-tap-highlight-color","rgba(0,0,0,0)")}function y(M,H){return(H=Math.max(u[0],Math.min(u[1],H)))===M.k?M:new Nn(H,M.x,M.y)}function w(M,H,O){var V=H[0]-O[0]*M.k,z=H[1]-O[1]*M.k;return V===M.x&&z===M.y?M:new Nn(M.k,V,z)}function h(M){return[(+M[0][0]+ +M[1][0])/2,(+M[0][1]+ +M[1][1])/2]}function C(M,H,O,V){M.on("start.zoom",function(){b(this,arguments).event(V).start()}).on("interrupt.zoom end.zoom",function(){b(this,arguments).event(V).end()}).tween("zoom",function(){var z=this,N=arguments,A=b(z,N).event(V),X=r.apply(z,N),B=O==null?h(X):typeof O=="function"?O.apply(z,N):O,Q=Math.max(X[1][0]-X[0][0],X[1][1]-X[0][1]),ne=z.__zoom,ee=typeof H=="function"?H.apply(z,N):H,me=d(ne.invert(B).concat(Q/ne.k),ee.invert(B).concat(Q/ee.k));return function(de){if(de===1)de=ee;else{var J=me(de),F=Q/J[2];de=new Nn(F,B[0]-J[0]*F,B[1]-J[1]*F)}A.zoom(null,de)}})}function b(M,H,O){return!O&&M.__zooming||new k(M,H)}function k(M,H){this.that=M,this.args=H,this.active=0,this.sourceEvent=null,this.extent=r.apply(M,H),this.taps=0}function _(M,...H){if(o.apply(this,arguments)){var O=b(this,H).event(M),V=this.__zoom,z=Math.max(u[0],Math.min(u[1],V.k*Math.pow(2,a.apply(this,arguments)))),N=on(M);if(O.wheel)(O.mouse[0][0]!==N[0]||O.mouse[0][1]!==N[1])&&(O.mouse[1]=V.invert(O.mouse[0]=N)),clearTimeout(O.wheel);else{if(V.k===z)return;O.mouse=[N,V.invert(N)],Ci(this),O.start()}xr(M),O.wheel=setTimeout(function(){O.wheel=null,O.end()},150),O.zoom("mouse",i(w(y(V,z),O.mouse[0],O.mouse[1]),O.extent,s))}}function S(M,...H){if(!n&&o.apply(this,arguments)){var O=M.currentTarget,V=b(this,H,!0).event(M),z=Ft(M.view).on("mousemove.zoom",function(B){if(xr(B),!V.moved){var Q=B.clientX-A,ne=B.clientY-X;V.moved=Q*Q+ne*ne>g}V.event(B).zoom("mouse",i(w(V.that.__zoom,V.mouse[0]=on(B,O),V.mouse[1]),V.extent,s))},!0).on("mouseup.zoom",function(B){z.on("mousemove.zoom mouseup.zoom",null),Ds(B.view,V.moved),xr(B),V.event(B).end()},!0),N=on(M,O),A=M.clientX,X=M.clientY;Os(M.view),Ia(M),V.mouse=[N,this.__zoom.invert(N)],Ci(this),V.start()}}function E(M,...H){if(o.apply(this,arguments)){var O=this.__zoom,V=on(M.changedTouches?M.changedTouches[0]:M,this),z=O.invert(V),N=O.k*(M.shiftKey?.5:2),A=i(w(y(O,N),V,z),r.apply(this,H),s);xr(M),c>0?Ft(this).transition().duration(c).call(C,A,V,M):Ft(this).call(v.transform,A,V,M)}}function L(M,...H){if(o.apply(this,arguments)){var O,V,z,N,A=M.touches,X=A.length,B=b(this,H,M.changedTouches.length===X).event(M);for(Ia(M),V=0;V<X;++V)N=[N=on(z=A[V],this),this.__zoom.invert(N),z.identifier],B.touch0?!B.touch1&&B.touch0[2]!==N[2]&&(B.touch1=N,B.taps=0):(B.touch0=N,O=!0,B.taps=1+!!e);e&&(e=clearTimeout(e)),O&&(B.taps<2&&(t=N[0],e=setTimeout(function(){e=null},500)),Ci(this),B.start())}}function P(M,...H){if(this.__zooming){var O,V,z,N,A=b(this,H).event(M),X=M.changedTouches,B=X.length;for(xr(M),O=0;O<B;++O)z=on(V=X[O],this),A.touch0&&A.touch0[2]===V.identifier?A.touch0[0]=z:A.touch1&&A.touch1[2]===V.identifier&&(A.touch1[0]=z);if(V=A.that.__zoom,A.touch1){var Q=A.touch0[0],ne=A.touch0[1],ee=A.touch1[0],me=A.touch1[1],de=(de=ee[0]-Q[0])*de+(de=ee[1]-Q[1])*de,J=(J=me[0]-ne[0])*J+(J=me[1]-ne[1])*J;V=y(V,Math.sqrt(de/J)),z=[(Q[0]+ee[0])/2,(Q[1]+ee[1])/2],N=[(ne[0]+me[0])/2,(ne[1]+me[1])/2]}else{if(!A.touch0)return;z=A.touch0[0],N=A.touch0[1]}A.zoom("touch",i(w(V,z,N),A.extent,s))}}function D(M,...H){if(this.__zooming){var O,V,z=b(this,H).event(M),N=M.changedTouches,A=N.length;for(Ia(M),n&&clearTimeout(n),n=setTimeout(function(){n=null},500),O=0;O<A;++O)V=N[O],z.touch0&&z.touch0[2]===V.identifier?delete z.touch0:z.touch1&&z.touch1[2]===V.identifier&&delete z.touch1;if(z.touch1&&!z.touch0&&(z.touch0=z.touch1,delete z.touch1),z.touch0)z.touch0[1]=this.__zoom.invert(z.touch0[0]);else if(z.end(),z.taps===2&&(V=on(V,this),Math.hypot(t[0]-V[0],t[1]-V[1])<m)){var X=Ft(this).on("dblclick.zoom");X&&X.apply(this,arguments)}}}return v.transform=function(M,H,O,V){var z=M.selection?M.selection():M;z.property("__zoom",uu),M!==z?C(M,H,O,V):z.interrupt().each(function(){b(this,arguments).event(V).start().zoom(null,typeof H=="function"?H.apply(this,arguments):H).end()})},v.scaleBy=function(M,H,O,V){v.scaleTo(M,function(){return this.__zoom.k*(typeof H=="function"?H.apply(this,arguments):H)},O,V)},v.scaleTo=function(M,H,O,V){v.transform(M,function(){var z=r.apply(this,arguments),N=this.__zoom,A=O==null?h(z):typeof O=="function"?O.apply(this,arguments):O,X=N.invert(A),B=typeof H=="function"?H.apply(this,arguments):H;return i(w(y(N,B),A,X),z,s)},O,V)},v.translateBy=function(M,H,O,V){v.transform(M,function(){return i(this.__zoom.translate(typeof H=="function"?H.apply(this,arguments):H,typeof O=="function"?O.apply(this,arguments):O),r.apply(this,arguments),s)},null,V)},v.translateTo=function(M,H,O,V,z){v.transform(M,function(){var N=r.apply(this,arguments),A=this.__zoom,X=V==null?h(N):typeof V=="function"?V.apply(this,arguments):V;return i(_i.translate(X[0],X[1]).scale(A.k).translate(typeof H=="function"?-H.apply(this,arguments):-H,typeof O=="function"?-O.apply(this,arguments):-O),N,s)},V,z)},k.prototype={event:function(M){return M&&(this.sourceEvent=M),this},start:function(){return++this.active==1&&(this.that.__zooming=this,this.emit("start")),this},zoom:function(M,H){return this.mouse&&M!=="mouse"&&(this.mouse[1]=H.invert(this.mouse[0])),this.touch0&&M!=="touch"&&(this.touch0[1]=H.invert(this.touch0[0])),this.touch1&&M!=="touch"&&(this.touch1[1]=H.invert(this.touch1[0])),this.that.__zoom=H,this.emit("zoom"),this},end:function(){return--this.active==0&&(delete this.that.__zooming,this.emit("end")),this},emit:function(M){var H=Ft(this.that).datum();f.call(M,this.that,new pf(M,{sourceEvent:this.sourceEvent,target:v,transform:this.that.__zoom,dispatch:f}),H)}},v.wheelDelta=function(M){return arguments.length?(a=typeof M=="function"?M:ki(+M),v):a},v.filter=function(M){return arguments.length?(o=typeof M=="function"?M:ki(!!M),v):o},v.touchable=function(M){return arguments.length?(l=typeof M=="function"?M:ki(!!M),v):l},v.extent=function(M){return arguments.length?(r=typeof M=="function"?M:ki([[+M[0][0],+M[0][1]],[+M[1][0],+M[1][1]]]),v):r},v.scaleExtent=function(M){return arguments.length?(u[0]=+M[0],u[1]=+M[1],v):[u[0],u[1]]},v.translateExtent=function(M){return arguments.length?(s[0][0]=+M[0][0],s[1][0]=+M[1][0],s[0][1]=+M[0][1],s[1][1]=+M[1][1],v):[[s[0][0],s[0][1]],[s[1][0],s[1][1]]]},v.constrain=function(M){return arguments.length?(i=M,v):i},v.duration=function(M){return arguments.length?(c=+M,v):c},v.interpolate=function(M){return arguments.length?(d=M,v):d},v.on=function(){var M=f.on.apply(f,arguments);return M===f?v:M},v.clickDistance=function(M){return arguments.length?(g=(M=+M)*M,v):Math.sqrt(g)},v.tapDistance=function(M){return arguments.length?(m=+M,v):m},v}su.prototype=Nn.prototype;const xf=()=>"Only child nodes can use a parent extent.",$f=(e,{id:t,sourceHandle:n,targetHandle:o})=>`Couldn't create edge for ${e} handle id: "${e==="source"?n:o}", edge id: ${t}.`,Cf=()=>"It seems that you are trying to drag a node that is not initialized. Please use onNodesChange as explained in the docs.",Si=[[Number.NEGATIVE_INFINITY,Number.NEGATIVE_INFINITY],[Number.POSITIVE_INFINITY,Number.POSITIVE_INFINITY]];var ho,du,Jn,Ei;(du=ho||(ho={})).Strict="strict",du.Loose="loose",function(e){e.Free="free",e.Vertical="vertical",e.Horizontal="horizontal"}(Jn||(Jn={})),function(e){e.Partial="partial",e.Full="full"}(Ei||(Ei={}));const Za={inProgress:!1,isValid:null,from:null,fromHandle:null,fromPosition:null,fromNode:null,to:null,toHandle:null,toPosition:null,toNode:null};var Ao,$r,Ee;(function(e){e.Bezier="default",e.Straight="straight",e.Step="step",e.SmoothStep="smoothstep",e.SimpleBezier="simplebezier"})(Ao||(Ao={})),function(e){e.Arrow="arrow",e.ArrowClosed="arrowclosed"}($r||($r={})),function(e){e.Left="left",e.Top="top",e.Right="right",e.Bottom="bottom"}(Ee||(Ee={}));const fu={[Ee.Left]:Ee.Right,[Ee.Right]:Ee.Left,[Ee.Top]:Ee.Bottom,[Ee.Bottom]:Ee.Top};function vu(e,t,n){if(!n)return;const o=[];e.forEach((r,i)=>{t!=null&&t.has(i)||o.push(r)}),o.length&&n(o)}function kf(e){return e===null?null:e?"valid":"invalid"}const Ba=e=>"id"in e&&"internals"in e&&!("source"in e)&&!("target"in e),Cr=(e,t=[0,0])=>{const{width:n,height:o}=Qn(e),r=e.origin??t,i=n*r[0],a=o*r[1];return{x:e.position.x-i,y:e.position.y-a}},kr=(e,t={})=>{if(e.size===0)return{x:0,y:0,width:0,height:0};let n={x:1/0,y:1/0,x2:-1/0,y2:-1/0};return e.forEach(o=>{if(t.filter===void 0||t.filter(o)){const r=Vi(o);n=Pi(n,r)}}),Mi(n)},gu=(e,t,[n,o,r]=[0,0,1],i=!1,a=!1)=>{const l={...Sr(t,[n,o,r]),width:t.width/r,height:t.height/r},u=[];for(const s of e.values()){const{measured:c,selectable:d=!0,hidden:f=!1}=s;if(a&&!d||f)continue;const g=c.width??s.width??s.initialWidth??null,m=c.height??s.height??s.initialHeight??null,v=_r(l,Zo(s)),y=(g??0)*(m??0),w=i&&v>0;(!s.internals.handleBounds||w||v>=y||s.dragging)&&u.push(s)}return u},Ra=(e,t)=>{const n=new Set;return e.forEach(o=>{n.add(o.id)}),t.filter(o=>n.has(o.source)||n.has(o.target))};function pu(e,t){const n=new Map,o=t!=null&&t.nodes?new Set(t.nodes.map(r=>r.id)):null;return e.forEach(r=>{r.measured.width&&r.measured.height&&(t!=null&&t.includeHiddenNodes||!r.hidden)&&(!o||o.has(r.id))&&n.set(r.id,r)}),n}async function hu({nodes:e,width:t,height:n,panZoom:o,minZoom:r,maxZoom:i},a){if(e.size===0)return Promise.resolve(!1);const l=kr(e),u=Ka(l,t,n,(a==null?void 0:a.minZoom)??r,(a==null?void 0:a.maxZoom)??i,(a==null?void 0:a.padding)??.1);return await o.setViewport(u,{duration:a==null?void 0:a.duration}),Promise.resolve(!0)}function _f({nodeId:e,nextPosition:t,nodeLookup:n,nodeOrigin:o=[0,0],nodeExtent:r,onError:i}){const a=n.get(e),l=a.parentId?n.get(a.parentId):void 0,{x:u,y:s}=l?l.internals.positionAbsolute:{x:0,y:0},c=a.origin??o;let d=r;if(a.extent!=="parent"||a.expandParent)l&&Bo(a.extent)&&(d=[[a.extent[0][0]+u,a.extent[0][1]+s],[a.extent[1][0]+u,a.extent[1][1]+s]]);else if(l){const g=l.measured.width,m=l.measured.height;g&&m&&(d=[[u,s],[u+g,s+m]])}else i==null||i("005",xf());const f=Bo(d)?mo(t,d,a.measured):t;return(a.measured.width===void 0||a.measured.height===void 0)&&(i==null||i("015",Cf())),{position:{x:f.x-u+(a.measured.width??0)*c[0],y:f.y-s+(a.measured.height??0)*c[1]},positionAbsolute:f}}async function mu({nodesToRemove:e=[],edgesToRemove:t=[],nodes:n,edges:o,onBeforeDelete:r}){const i=new Set(e.map(d=>d.id)),a=[];for(const d of n){if(d.deletable===!1)continue;const f=i.has(d.id),g=!f&&d.parentId&&a.find(m=>m.id===d.parentId);(f||g)&&a.push(d)}const l=new Set(t.map(d=>d.id)),u=o.filter(d=>d.deletable!==!1),s=Ra(a,u);for(const d of u)l.has(d.id)&&!s.find(f=>f.id===d.id)&&s.push(d);if(!r)return{edges:s,nodes:a};const c=await r({nodes:a,edges:s});return typeof c=="boolean"?c?{edges:s,nodes:a}:{edges:[],nodes:[]}:c}const Io=(e,t=0,n=1)=>Math.min(Math.max(e,t),n),mo=(e={x:0,y:0},t,n)=>({x:Io(e.x,t[0][0],t[1][0]-((n==null?void 0:n.width)??0)),y:Io(e.y,t[0][1],t[1][1]-((n==null?void 0:n.height)??0))});function yu(e,t,n){const{width:o,height:r}=Qn(n),{x:i,y:a}=n.internals.positionAbsolute;return mo(e,[[i,a],[i+o,a+r]],t)}const wu=(e,t,n)=>e<t?Io(Math.abs(e-t),1,t)/t:e>n?-Io(Math.abs(e-n),1,t)/t:0,bu=(e,t,n=15,o=40)=>[wu(e.x,o,t.width-o)*n,wu(e.y,o,t.height-o)*n],Pi=(e,t)=>({x:Math.min(e.x,t.x),y:Math.min(e.y,t.y),x2:Math.max(e.x2,t.x2),y2:Math.max(e.y2,t.y2)}),Xa=({x:e,y:t,width:n,height:o})=>({x:e,y:t,x2:e+n,y2:t+o}),Mi=({x:e,y:t,x2:n,y2:o})=>({x:e,y:t,width:n-e,height:o-t}),Zo=(e,t=[0,0])=>{var n,o;const{x:r,y:i}=Ba(e)?e.internals.positionAbsolute:Cr(e,t);return{x:r,y:i,width:((n=e.measured)==null?void 0:n.width)??e.width??e.initialWidth??0,height:((o=e.measured)==null?void 0:o.height)??e.height??e.initialHeight??0}},Vi=(e,t=[0,0])=>{var n,o;const{x:r,y:i}=Ba(e)?e.internals.positionAbsolute:Cr(e,t);return{x:r,y:i,x2:r+(((n=e.measured)==null?void 0:n.width)??e.width??e.initialWidth??0),y2:i+(((o=e.measured)==null?void 0:o.height)??e.height??e.initialHeight??0)}},xu=(e,t)=>Mi(Pi(Xa(e),Xa(t))),_r=(e,t)=>{const n=Math.max(0,Math.min(e.x+e.width,t.x+t.width)-Math.max(e.x,t.x)),o=Math.max(0,Math.min(e.y+e.height,t.y+t.height)-Math.max(e.y,t.y));return Math.ceil(n*o)},$u=e=>Ln(e.width)&&Ln(e.height)&&Ln(e.x)&&Ln(e.y),Ln=e=>!isNaN(e)&&isFinite(e),Sf=(e,t)=>{},Ya=(e,t=[1,1])=>({x:t[0]*Math.round(e.x/t[0]),y:t[1]*Math.round(e.y/t[1])}),Sr=({x:e,y:t},[n,o,r],i=!1,a=[1,1])=>{const l={x:(e-n)/r,y:(t-o)/r};return i?Ya(l,a):l},Cu=({x:e,y:t},[n,o,r])=>({x:e*r+n,y:t*r+o}),Ka=(e,t,n,o,r,i)=>{const a=t/(e.width*(1+i)),l=n/(e.height*(1+i)),u=Math.min(a,l),s=Io(u,o,r);return{x:t/2-(e.x+e.width/2)*s,y:n/2-(e.y+e.height/2)*s,zoom:s}},Hi=()=>{var e;return typeof navigator<"u"&&((e=navigator==null?void 0:navigator.userAgent)==null?void 0:e.indexOf("Mac"))>=0};function Bo(e){return e!==void 0&&e!=="parent"}function Qn(e){var t,n;return{width:((t=e.measured)==null?void 0:t.width)??e.width??e.initialWidth??0,height:((n=e.measured)==null?void 0:n.height)??e.height??e.initialHeight??0}}function ku(e){var t,n;return(((t=e.measured)==null?void 0:t.width)??e.width??e.initialWidth)!==void 0&&(((n=e.measured)==null?void 0:n.height)??e.height??e.initialHeight)!==void 0}function Wa(e,{snapGrid:t=[0,0],snapToGrid:n=!1,transform:o,containerBounds:r}){const{x:i,y:a}=On(e),l=Sr({x:i-((r==null?void 0:r.left)??0),y:a-((r==null?void 0:r.top)??0)},o),{x:u,y:s}=n?Ya(l,t):l;return{xSnapped:u,ySnapped:s,...l}}const ja=e=>({width:e.offsetWidth,height:e.offsetHeight}),Ef=["INPUT","SELECT","TEXTAREA"],_u=e=>"clientX"in e,On=(e,t)=>{var n,o;const r=_u(e),i=r?e.clientX:(n=e.touches)==null?void 0:n[0].clientX,a=r?e.clientY:(o=e.touches)==null?void 0:o[0].clientY;return{x:i-((t==null?void 0:t.left)??0),y:a-((t==null?void 0:t.top)??0)}},Su=(e,t,n,o,r)=>{const i=t.querySelectorAll(`.${e}`);return i&&i.length?Array.from(i).map(a=>{const l=a.getBoundingClientRect();return{id:a.getAttribute("data-handleid"),type:e,nodeId:r,position:a.getAttribute("data-handlepos"),x:(l.left-n.left)/o,y:(l.top-n.top)/o,...ja(a)}}):null};function zi(e,t){return e>=0?.5*e:25*t*Math.sqrt(-e)}function Eu({pos:e,x1:t,y1:n,x2:o,y2:r,c:i}){switch(e){case Ee.Left:return[t-zi(t-o,i),n];case Ee.Right:return[t+zi(o-t,i),n];case Ee.Top:return[t,n-zi(n-r,i)];case Ee.Bottom:return[t,n+zi(r-n,i)]}}function Pu({sourceX:e,sourceY:t,sourcePosition:n=Ee.Bottom,targetX:o,targetY:r,targetPosition:i=Ee.Top,curvature:a=.25}){const[l,u]=Eu({pos:n,x1:e,y1:t,x2:o,y2:r,c:a}),[s,c]=Eu({pos:i,x1:o,y1:r,x2:e,y2:t,c:a}),[d,f,g,m]=function({sourceX:v,sourceY:y,targetX:w,targetY:h,sourceControlX:C,sourceControlY:b,targetControlX:k,targetControlY:_}){const S=.125*v+.375*C+.375*k+.125*w,E=.125*y+.375*b+.375*_+.125*h;return[S,E,Math.abs(S-v),Math.abs(E-y)]}({sourceX:e,sourceY:t,targetX:o,targetY:r,sourceControlX:l,sourceControlY:u,targetControlX:s,targetControlY:c});return[`M${e},${t} C${l},${u} ${s},${c} ${o},${r}`,d,f,g,m]}function Mu({sourceX:e,sourceY:t,targetX:n,targetY:o}){const r=Math.abs(n-e)/2,i=n<e?n+r:n-r,a=Math.abs(o-t)/2;return[i,o<t?o+a:o-a,r,a]}function Pf({sourceNode:e,targetNode:t,selected:n=!1,zIndex:o=0,elevateOnSelect:r=!1}){if(!r)return o;const i=n||t.selected||e.selected,a=Math.max(e.internals.z||0,t.internals.z||0,1e3);return o+(i?a:0)}const Mf=({source:e,sourceHandle:t,target:n,targetHandle:o})=>`xy-edge__${e}${t||""}-${n}${o||""}`;function qa({sourceX:e,sourceY:t,targetX:n,targetY:o}){const[r,i,a,l]=Mu({sourceX:e,sourceY:t,targetX:n,targetY:o});return[`M ${e},${t}L ${n},${o}`,r,i,a,l]}const Vu={[Ee.Left]:{x:-1,y:0},[Ee.Right]:{x:1,y:0},[Ee.Top]:{x:0,y:-1},[Ee.Bottom]:{x:0,y:1}},Hu=(e,t)=>Math.sqrt(Math.pow(t.x-e.x,2)+Math.pow(t.y-e.y,2));function Vf({source:e,sourcePosition:t=Ee.Bottom,target:n,targetPosition:o=Ee.Top,center:r,offset:i}){const a=Vu[t],l=Vu[o],u={x:e.x+a.x*i,y:e.y+a.y*i},s={x:n.x+l.x*i,y:n.y+l.y*i},c=(({source:_,sourcePosition:S=Ee.Bottom,target:E})=>S===Ee.Left||S===Ee.Right?_.x<E.x?{x:1,y:0}:{x:-1,y:0}:_.y<E.y?{x:0,y:1}:{x:0,y:-1})({source:u,sourcePosition:t,target:s}),d=c.x!==0?"x":"y",f=c[d];let g,m,v=[];const y={x:0,y:0},w={x:0,y:0},[h,C,b,k]=Mu({sourceX:e.x,sourceY:e.y,targetX:n.x,targetY:n.y});if(a[d]*l[d]==-1){g=r.x??h,m=r.y??C;const _=[{x:g,y:u.y},{x:g,y:s.y}],S=[{x:u.x,y:m},{x:s.x,y:m}];v=a[d]===f?d==="x"?_:S:d==="x"?S:_}else{const _=[{x:u.x,y:s.y}],S=[{x:s.x,y:u.y}];if(v=d==="x"?a.x===f?S:_:a.y===f?_:S,t===o){const P=Math.abs(e[d]-n[d]);if(P<=i){const D=Math.min(i-1,i-P);a[d]===f?y[d]=(u[d]>e[d]?-1:1)*D:w[d]=(s[d]>n[d]?-1:1)*D}}if(t!==o){const P=d==="x"?"y":"x",D=a[d]===l[P],M=u[P]>s[P],H=u[P]<s[P];(a[d]===1&&(!D&&M||D&&H)||a[d]!==1&&(!D&&H||D&&M))&&(v=d==="x"?_:S)}const E={x:u.x+y.x,y:u.y+y.y},L={x:s.x+w.x,y:s.y+w.y};Math.max(Math.abs(E.x-v[0].x),Math.abs(L.x-v[0].x))>=Math.max(Math.abs(E.y-v[0].y),Math.abs(L.y-v[0].y))?(g=(E.x+L.x)/2,m=v[0].y):(g=v[0].x,m=(E.y+L.y)/2)}return[[e,{x:u.x+y.x,y:u.y+y.y},...v,{x:s.x+w.x,y:s.y+w.y},n],g,m,b,k]}function Ni({sourceX:e,sourceY:t,sourcePosition:n=Ee.Bottom,targetX:o,targetY:r,targetPosition:i=Ee.Top,borderRadius:a=5,centerX:l,centerY:u,offset:s=20}){const[c,d,f,g,m]=Vf({source:{x:e,y:t},sourcePosition:n,target:{x:o,y:r},targetPosition:i,center:{x:l,y:u},offset:s});return[c.reduce((v,y,w)=>{let h="";return h=w>0&&w<c.length-1?function(C,b,k,_){const S=Math.min(Hu(C,b)/2,Hu(b,k)/2,_),{x:E,y:L}=b;if(C.x===E&&E===k.x||C.y===L&&L===k.y)return`L${E} ${L}`;if(C.y===L)return`L ${E+S*(C.x<k.x?-1:1)},${L}Q ${E},${L} ${E},${L+S*(C.y<k.y?1:-1)}`;const P=C.x<k.x?1:-1;return`L ${E},${L+S*(C.y<k.y?-1:1)}Q ${E},${L} ${E+S*P},${L}`}(c[w-1],y,c[w+1],a):`${w===0?"M":"L"}${y.x} ${y.y}`,v+=h},""),d,f,g,m]}function zu(e){var t;return e&&!!(e.internals.handleBounds||(t=e.handles)!=null&&t.length)&&!!(e.measured.width||e.width||e.initialWidth)}function Nu(e){if(!e)return null;const t=[],n=[];for(const o of e)o.width=o.width??1,o.height=o.height??1,o.type==="source"?t.push(o):o.type==="target"&&n.push(o);return{source:t,target:n}}function Er(e,t,n=Ee.Left,o=!1){const r=((t==null?void 0:t.x)??0)+e.internals.positionAbsolute.x,i=((t==null?void 0:t.y)??0)+e.internals.positionAbsolute.y,{width:a,height:l}=t??Qn(e);if(o)return{x:r+a/2,y:i+l/2};switch((t==null?void 0:t.position)??n){case Ee.Top:return{x:r+a/2,y:i};case Ee.Right:return{x:r+a,y:i+l/2};case Ee.Bottom:return{x:r+a/2,y:i+l};case Ee.Left:return{x:r,y:i+l/2}}}function Lu(e,t){return e&&(t?e.find(n=>n.id===t):e[0])||null}function Fa(e,t){return e?typeof e=="string"?e:`${t?`${t}__`:""}${Object.keys(e).sort().map(n=>`${n}=${e[n]}`).join("&")}`:""}const Ga={nodeOrigin:[0,0],nodeExtent:Si,elevateNodesOnSelect:!0,defaults:{}},Hf={...Ga,checkEquality:!0};function Ua(e,t){const n={...e};for(const o in t)t[o]!==void 0&&(n[o]=t[o]);return n}function Ou(e,t,n,o){var r,i;const a=Ua(Hf,o),l=new Map(t),u=a!=null&&a.elevateNodesOnSelect?1e3:0;t.clear(),n.clear();for(const s of e){let c=l.get(s.id);if(a.checkEquality&&s===(c==null?void 0:c.internals.userNode))t.set(s.id,c);else{const d=Cr(s,a.nodeOrigin),f=Bo(s.extent)?s.extent:a.nodeExtent,g=mo(d,f,Qn(s));c={...a.defaults,...s,measured:{width:(r=s.measured)==null?void 0:r.width,height:(i=s.measured)==null?void 0:i.height},internals:{positionAbsolute:g,handleBounds:s.measured?c==null?void 0:c.internals.handleBounds:void 0,z:Du(s,u),userNode:s}},t.set(s.id,c)}s.parentId&&Ja(c,t,n,o)}}function Ja(e,t,n,o){const{elevateNodesOnSelect:r,nodeOrigin:i,nodeExtent:a}=Ua(Ga,o),l=e.parentId,u=t.get(l);if(!u)return;(function(v,y){if(!v.parentId)return;const w=y.get(v.parentId);w?w.set(v.id,v):y.set(v.parentId,new Map([[v.id,v]]))})(e,n);const s=r?1e3:0,{x:c,y:d,z:f}=function(v,y,w,h,C){const{x:b,y:k}=y.internals.positionAbsolute,_=Qn(v),S=Cr(v,w),E=Bo(v.extent)?mo(S,v.extent,_):S;let L=mo({x:b+E.x,y:k+E.y},h,_);v.extent==="parent"&&(L=yu(L,_,y));const P=Du(v,C),D=y.internals.z??0;return{x:L.x,y:L.y,z:D>P?D:P}}(e,u,i,a,s),{positionAbsolute:g}=e.internals,m=c!==g.x||d!==g.y;(m||f!==e.internals.z)&&t.set(e.id,{...e,internals:{...e.internals,positionAbsolute:m?{x:c,y:d}:g,z:f}})}function Du(e,t){return(Ln(e.zIndex)?e.zIndex:0)+(e.selected?t:0)}function zf(e,t,n,o,r,i){const a=o==null?void 0:o.querySelector(".xyflow__viewport");let l=!1;if(!a)return{changes:[],updatedInternals:l};const u=[],s=window.getComputedStyle(a),{m22:c}=new window.DOMMatrixReadOnly(s.transform),d=[];for(const f of e.values()){const g=t.get(f.id);if(!g)continue;if(g.hidden){t.set(g.id,{...g,internals:{...g.internals,handleBounds:void 0}}),l=!0;continue}const m=ja(f.nodeElement),v=g.measured.width!==m.width||g.measured.height!==m.height;if(m.width&&m.height&&(v||!g.internals.handleBounds||f.force)){const y=f.nodeElement.getBoundingClientRect(),w=Bo(g.extent)?g.extent:i;let{positionAbsolute:h}=g.internals;g.parentId&&g.extent==="parent"?h=yu(h,m,t.get(g.parentId)):w&&(h=mo(h,w,m));const C={...g,measured:m,internals:{...g.internals,positionAbsolute:h,handleBounds:{source:Su("source",f.nodeElement,y,c,g.id),target:Su("target",f.nodeElement,y,c,g.id)}}};t.set(g.id,C),g.parentId&&Ja(C,t,n,{nodeOrigin:r}),l=!0,v&&(u.push({id:g.id,type:"dimensions",dimensions:m}),g.expandParent&&g.parentId&&d.push({id:g.id,parentId:g.parentId,rect:Zo(C,r)}))}}if(d.length>0){const f=function(g,m,v,y=[0,0]){var w;const h=[],C=new Map;for(const b of g){const k=m.get(b.parentId);if(!k)continue;const _=((w=C.get(b.parentId))==null?void 0:w.expandedRect)??Zo(k),S=xu(_,b.rect);C.set(b.parentId,{expandedRect:S,parent:k})}return C.size>0&&C.forEach(({expandedRect:b,parent:k},_)=>{var S;const E=k.internals.positionAbsolute,L=Qn(k),P=k.origin??y,D=b.x<E.x?Math.round(Math.abs(E.x-b.x)):0,M=b.y<E.y?Math.round(Math.abs(E.y-b.y)):0,H=Math.max(L.width,Math.round(b.width)),O=Math.max(L.height,Math.round(b.height)),V=(H-L.width)*P[0],z=(O-L.height)*P[1];(D>0||M>0||V||z)&&(h.push({id:_,type:"position",position:{x:k.position.x-D+V,y:k.position.y-M+z}}),(S=v.get(_))==null||S.forEach(N=>{g.some(A=>A.id===N.id)||h.push({id:N.id,type:"position",position:{x:N.position.x+D,y:N.position.y+M}})})),(L.width<b.width||L.height<b.height||D||M)&&h.push({id:_,type:"dimensions",setAttributes:!0,dimensions:{width:H+(D?P[0]*D-V:0),height:O+(M?P[1]*M-z:0)}})}),h}(d,t,n,r);u.push(...f)}return{changes:u,updatedInternals:l}}function Tu(e,t,n,o,r,i){let a=r;const l=o.get(a)||new Map;o.set(a,l.set(n,t)),a=`${r}-${e}`;const u=o.get(a)||new Map;if(o.set(a,u.set(n,t)),i){a=`${r}-${e}-${i}`;const s=o.get(a)||new Map;o.set(a,s.set(n,t))}}function Au(e,t,n){e.clear(),t.clear();for(const o of n){const{source:r,target:i,sourceHandle:a=null,targetHandle:l=null}=o,u={edgeId:o.id,source:r,target:i,sourceHandle:a,targetHandle:l},s=`${r}-${a}--${i}-${l}`;Tu("source",u,`${i}-${l}--${r}-${a}`,e,r,a),Tu("target",u,s,e,i,l),t.set(o.id,o)}}function Iu(e,t){if(!e.parentId)return!1;const n=t.get(e.parentId);return!!n&&(!!n.selected||Iu(n,t))}function Zu(e,t,n){var o;let r=e;do{if((o=r==null?void 0:r.matches)!=null&&o.call(r,t))return!0;if(r===n)return!1;r=r==null?void 0:r.parentElement}while(r);return!1}function Qa({nodeId:e,dragItems:t,nodeLookup:n,dragging:o=!0}){var r,i,a;const l=[];for(const[s,c]of t){const d=(r=n.get(s))==null?void 0:r.internals.userNode;d&&l.push({...d,position:c.position,dragging:o})}if(!e)return[l[0],l];const u=(i=n.get(e))==null?void 0:i.internals.userNode;return[u?{...u,position:((a=t.get(e))==null?void 0:a.position)||u.position,dragging:o}:l[0],l]}function Nf({onNodeMouseDown:e,getStoreItems:t,onDragStart:n,onDrag:o,onDragStop:r}){let i={x:null,y:null},a=0,l=new Map,u=!1,s={x:0,y:0},c=null,d=!1,f=null,g=!1;return{update:function({noDragClassName:m,handleSelector:v,domNode:y,isSelectable:w,nodeId:h,nodeClickDistance:C=0}){function b({x:E,y:L},P){const{nodeLookup:D,nodeExtent:M,snapGrid:H,snapToGrid:O,nodeOrigin:V,onNodeDrag:z,onSelectionDrag:N,onError:A,updateNodePositions:X}=t();i={x:E,y:L};let B=!1,Q={x:0,y:0,x2:0,y2:0};if(l.size>1&&M){const ne=kr(l);Q=Xa(ne)}for(const[ne,ee]of l){if(!D.has(ne))continue;let me={x:E-ee.distance.x,y:L-ee.distance.y};O&&(me=Ya(me,H));let de=[[M[0][0],M[0][1]],[M[1][0],M[1][1]]];if(l.size>1&&M&&!ee.extent){const{positionAbsolute:I}=ee.internals,j=I.x-Q.x+M[0][0],G=I.x+ee.measured.width-Q.x2+M[1][0];de=[[j,I.y-Q.y+M[0][1]],[G,I.y+ee.measured.height-Q.y2+M[1][1]]]}const{position:J,positionAbsolute:F}=_f({nodeId:ne,nextPosition:me,nodeLookup:D,nodeExtent:de,nodeOrigin:V,onError:A});B=B||ee.position.x!==J.x||ee.position.y!==J.y,ee.position=J,ee.internals.positionAbsolute=F}if(B&&(X(l,!0),P&&(o||z||!h&&N))){const[ne,ee]=Qa({nodeId:h,dragItems:l,nodeLookup:D});o==null||o(P,l,ne,ee),z==null||z(P,ne,ee),h||N==null||N(P,ee)}}async function k(){if(!c)return;const{transform:E,panBy:L,autoPanSpeed:P,autoPanOnNodeDrag:D}=t();if(!D)return u=!1,void cancelAnimationFrame(a);const[M,H]=bu(s,c,P);(M!==0||H!==0)&&(i.x=(i.x??0)-M/E[2],i.y=(i.y??0)-H/E[2],await L({x:M,y:H})&&b(i,null)),a=requestAnimationFrame(k)}function _(E){var L;const{nodeLookup:P,multiSelectionActive:D,nodesDraggable:M,transform:H,snapGrid:O,snapToGrid:V,selectNodesOnDrag:z,onNodeDragStart:N,onSelectionDragStart:A,unselectNodesAndEdges:X}=t();d=!0,(!z||!w)&&!D&&h&&((L=P.get(h))!=null&&L.selected||X()),w&&z&&h&&(e==null||e(h));const B=Wa(E.sourceEvent,{transform:H,snapGrid:O,snapToGrid:V,containerBounds:c});if(i=B,l=function(Q,ne,ee,me){const de=new Map;for(const[J,F]of Q)if((F.selected||F.id===me)&&(!F.parentId||!Iu(F,Q))&&(F.draggable||ne&&typeof F.draggable>"u")){const I=Q.get(J);I&&de.set(J,{id:J,position:I.position||{x:0,y:0},distance:{x:ee.x-I.internals.positionAbsolute.x,y:ee.y-I.internals.positionAbsolute.y},extent:I.extent,parentId:I.parentId,origin:I.origin,expandParent:I.expandParent,internals:{positionAbsolute:I.internals.positionAbsolute||{x:0,y:0}},measured:{width:I.measured.width??0,height:I.measured.height??0}})}return de}(P,M,B,h),l.size>0&&(n||N||!h&&A)){const[Q,ne]=Qa({nodeId:h,dragItems:l,nodeLookup:P});n==null||n(E.sourceEvent,l,Q,ne),N==null||N(E.sourceEvent,Q,ne),h||A==null||A(E.sourceEvent,ne)}}f=Ft(y);const S=function(){var E,L,P,D,M=V0,H=H0,O=z0,V=N0,z={},N=si("start","drag","end"),A=0,X=0;function B(I){I.on("mousedown.drag",Q).filter(V).on("touchstart.drag",me).on("touchmove.drag",de,M0).on("touchend.drag touchcancel.drag",J).style("touch-action","none").style("-webkit-tap-highlight-color","rgba(0,0,0,0)")}function Q(I,j){if(!D&&M.call(this,I,j)){var G=F(this,H.call(this,I,j),I,j,"mouse");G&&(Ft(I.view).on("mousemove.drag",ne,fr).on("mouseup.drag",ee,fr),Os(I.view),Pa(I),P=!1,E=I.clientX,L=I.clientY,G("start",I))}}function ne(I){if(Oo(I),!P){var j=I.clientX-E,G=I.clientY-L;P=j*j+G*G>X}z.mouse("drag",I)}function ee(I){Ft(I.view).on("mousemove.drag mouseup.drag",null),Ds(I.view,P),Oo(I),z.mouse("end",I)}function me(I,j){if(M.call(this,I,j)){var G,le,we=I.changedTouches,Pe=H.call(this,I,j),Ne=we.length;for(G=0;G<Ne;++G)(le=F(this,Pe,I,j,we[G].identifier,we[G]))&&(Pa(I),le("start",I,we[G]))}}function de(I){var j,G,le=I.changedTouches,we=le.length;for(j=0;j<we;++j)(G=z[le[j].identifier])&&(Oo(I),G("drag",I,le[j]))}function J(I){var j,G,le=I.changedTouches,we=le.length;for(D&&clearTimeout(D),D=setTimeout(function(){D=null},500),j=0;j<we;++j)(G=z[le[j].identifier])&&(Pa(I),G("end",I,le[j]))}function F(I,j,G,le,we,Pe){var Ne,ue,Y,$e=N.copy(),Ce=on(Pe||G,j);if((Y=O.call(I,new Ma("beforestart",{sourceEvent:G,target:B,identifier:we,active:A,x:Ce[0],y:Ce[1],dx:0,dy:0,dispatch:$e}),le))!=null)return Ne=Y.x-Ce[0]||0,ue=Y.y-Ce[1]||0,function Se(Ae,Ge,fe){var U,re=Ce;switch(Ae){case"start":z[we]=Se,U=A++;break;case"end":delete z[we],--A;case"drag":Ce=on(fe||Ge,j),U=A}$e.call(Ae,I,new Ma(Ae,{sourceEvent:Ge,subject:Y,target:B,identifier:we,active:U,x:Ce[0]+Ne,y:Ce[1]+ue,dx:Ce[0]-re[0],dy:Ce[1]-re[1],dispatch:$e}),le)}}return B.filter=function(I){return arguments.length?(M=typeof I=="function"?I:fi(!!I),B):M},B.container=function(I){return arguments.length?(H=typeof I=="function"?I:fi(I),B):H},B.subject=function(I){return arguments.length?(O=typeof I=="function"?I:fi(I),B):O},B.touchable=function(I){return arguments.length?(V=typeof I=="function"?I:fi(!!I),B):V},B.on=function(){var I=N.on.apply(N,arguments);return I===N?B:I},B.clickDistance=function(I){return arguments.length?(X=(I=+I)*I,B):Math.sqrt(X)},B}().clickDistance(C).on("start",E=>{const{domNode:L,nodeDragThreshold:P,transform:D,snapGrid:M,snapToGrid:H}=t();c=(L==null?void 0:L.getBoundingClientRect())||null,g=!1,P===0&&_(E),i=Wa(E.sourceEvent,{transform:D,snapGrid:M,snapToGrid:H,containerBounds:c}),s=On(E.sourceEvent,c)}).on("drag",E=>{const{autoPanOnNodeDrag:L,transform:P,snapGrid:D,snapToGrid:M,nodeDragThreshold:H,nodeLookup:O}=t(),V=Wa(E.sourceEvent,{transform:P,snapGrid:D,snapToGrid:M,containerBounds:c});if((E.sourceEvent.type==="touchmove"&&E.sourceEvent.touches.length>1||h&&!O.has(h))&&(g=!0),!g){if(!u&&L&&d&&(u=!0,k()),!d){const z=V.xSnapped-(i.x??0),N=V.ySnapped-(i.y??0);Math.sqrt(z*z+N*N)>H&&_(E)}(i.x!==V.xSnapped||i.y!==V.ySnapped)&&l&&d&&(s=On(E.sourceEvent,c),b(V,E.sourceEvent))}}).on("end",E=>{if(d&&!g&&(u=!1,d=!1,cancelAnimationFrame(a),l.size>0)){const{nodeLookup:L,updateNodePositions:P,onNodeDragStop:D,onSelectionDragStop:M}=t();if(P(l,!1),r||D||!h&&M){const[H,O]=Qa({nodeId:h,dragItems:l,nodeLookup:L,dragging:!1});r==null||r(E.sourceEvent,l,H,O),D==null||D(E.sourceEvent,H,O),h||M==null||M(E.sourceEvent,O)}}}).filter(E=>{const L=E.target;return!E.button&&(!m||!Zu(L,`.${m}`,y))&&(!v||Zu(L,v,y))});f.call(S)},destroy:function(){f==null||f.on(".drag",null)}}}function Lf(e,t,n,o){var r,i;let a=[],l=1/0;const u=function(s,c,d){const f=[],g={x:s.x-d,y:s.y-d,width:2*d,height:2*d};for(const m of c.values())_r(g,Zo(m))>0&&f.push(m);return f}(e,n,t+250);for(const s of u){const c=[...((r=s.internals.handleBounds)==null?void 0:r.source)??[],...((i=s.internals.handleBounds)==null?void 0:i.target)??[]];for(const d of c){if(o.nodeId===d.nodeId&&o.type===d.type&&o.id===d.id)continue;const{x:f,y:g}=Er(s,d,d.position,!0),m=Math.sqrt(Math.pow(f-e.x,2)+Math.pow(g-e.y,2));m>t||(m<l?(a=[{...d,x:f,y:g}],l=m):m===l&&a.push({...d,x:f,y:g}))}}if(!a.length)return null;if(a.length>1){const s=o.type==="source"?"target":"source";return a.find(c=>c.type===s)??a[0]}return a[0]}function Bu(e,t,n,o,r,i=!1){var a,l,u;const s=o.get(e);if(!s)return null;const c=r==="strict"?(a=s.internals.handleBounds)==null?void 0:a[t]:[...((l=s.internals.handleBounds)==null?void 0:l.source)??[],...((u=s.internals.handleBounds)==null?void 0:u.target)??[]],d=(n?c==null?void 0:c.find(f=>f.id===n):c==null?void 0:c[0])??null;return d&&i?{...d,...Er(s,d,d.position,!0)}:d}function Ru(e,t){return e||(t!=null&&t.classList.contains("target")?"target":t!=null&&t.classList.contains("source")?"source":null)}const Xu=()=>!0;function Yu(e,{handle:t,connectionMode:n,fromNodeId:o,fromHandleId:r,fromType:i,doc:a,lib:l,flowId:u,isValidConnection:s=Xu,nodeLookup:c}){const d=i==="target",f=t?a.querySelector(`.${l}-flow__handle[data-id="${u}-${t==null?void 0:t.nodeId}-${t==null?void 0:t.id}-${t==null?void 0:t.type}"]`):null,{x:g,y:m}=On(e),v=a.elementFromPoint(g,m),y=v!=null&&v.classList.contains(`${l}-flow__handle`)?v:f,w={handleDomNode:y,isValid:!1,connection:null,toHandle:null};if(y){const h=Ru(void 0,y),C=y.getAttribute("data-nodeid"),b=y.getAttribute("data-handleid"),k=y.classList.contains("connectable"),_=y.classList.contains("connectableend");if(!C||!h)return w;const S={source:d?C:o,sourceHandle:d?b:r,target:d?o:C,targetHandle:d?r:b};w.connection=S;const E=k&&_&&(n===ho.Strict?d&&h==="source"||!d&&h==="target":C!==o||b!==r);w.isValid=E&&s(S),w.toHandle=Bu(C,h,b,c,n,!1)}return w}const Of={onPointerDown:function(e,{connectionMode:t,connectionRadius:n,handleId:o,nodeId:r,edgeUpdaterType:i,isTarget:a,domNode:l,nodeLookup:u,lib:s,autoPanOnConnect:c,flowId:d,panBy:f,cancelConnection:g,onConnectStart:m,onConnect:v,onConnectEnd:y,isValidConnection:w=Xu,onReconnectEnd:h,updateConnection:C,getTransform:b,getFromHandle:k,autoPanSpeed:_}){const S=(I=>{var j;return((j=I==null?void 0:I.getRootNode)==null?void 0:j.call(I))||(window==null?void 0:window.document)})(e.target);let E,L=0;const{x:P,y:D}=On(e),M=S==null?void 0:S.elementFromPoint(P,D),H=Ru(i,M),O=l==null?void 0:l.getBoundingClientRect();if(!O||!H)return;const V=Bu(r,H,o,u,t);if(!V)return;let z=On(e,O),N=!1,A=null,X=!1,B=null;function Q(){if(!c||!O)return;const[I,j]=bu(z,O,_);f({x:I,y:j}),L=requestAnimationFrame(Q)}const ne={...V,nodeId:r,type:H,position:V.position},ee=u.get(r),me={inProgress:!0,isValid:null,from:Er(ee,ne,Ee.Left,!0),fromHandle:ne,fromPosition:ne.position,fromNode:ee,to:z,toHandle:null,toPosition:fu[ne.position],toNode:null};C(me);let de=me;function J(I){if(!k()||!ne)return void F(I);const j=b();z=On(I,O),E=Lf(Sr(z,j,!1,[1,1]),n,u,ne),N||(Q(),N=!0);const G=Yu(I,{handle:E,connectionMode:t,fromNodeId:r,fromHandleId:o,fromType:a?"target":"source",isValidConnection:w,doc:S,lib:s,flowId:d,nodeLookup:u});B=G.handleDomNode,A=G.connection,X=function(we,Pe){let Ne=null;return Pe?Ne=!0:we&&!Pe&&(Ne=!1),Ne}(!!E,G.isValid);const le={...de,isValid:X,to:E&&X?Cu({x:E.x,y:E.y},j):z,toHandle:G.toHandle,toPosition:X&&G.toHandle?G.toHandle.position:fu[ne.position],toNode:G.toHandle?u.get(G.toHandle.nodeId):null};X&&E&&de.toHandle&&le.toHandle&&de.toHandle.type===le.toHandle.type&&de.toHandle.nodeId===le.toHandle.nodeId&&de.toHandle.id===le.toHandle.id&&de.to.x===le.to.x&&de.to.y===le.to.y||(C(le),de=le)}function F(I){(E||B)&&A&&X&&(v==null||v(A));const{inProgress:j,...G}=de,le={...G,toPosition:de.toHandle?de.toPosition:null};y==null||y(I,le),i&&(h==null||h(I,le)),g(),cancelAnimationFrame(L),N=!1,X=!1,A=null,B=null,S.removeEventListener("mousemove",J),S.removeEventListener("mouseup",F),S.removeEventListener("touchmove",J),S.removeEventListener("touchend",F)}m==null||m(e,{nodeId:r,handleId:o,handleType:H}),S.addEventListener("mousemove",J),S.addEventListener("mouseup",F),S.addEventListener("touchmove",J),S.addEventListener("touchend",F)},isValid:Yu},Li=e=>({x:e.x,y:e.y,zoom:e.k}),el=({x:e,y:t,zoom:n})=>_i.translate(e,t).scale(n),Ro=(e,t)=>e.target.closest(`.${t}`),Ku=(e,t)=>t===2&&Array.isArray(e)&&e.includes(2),tl=(e,t=0,n=()=>{})=>{const o=typeof t=="number"&&t>0;return o||n(),o?e.transition().duration(t).on("end",n):e},Wu=e=>{const t=e.ctrlKey&&Hi()?10:1;return-e.deltaY*(e.deltaMode===1?.05:e.deltaMode?1:.002)*t};function Df({domNode:e,minZoom:t,maxZoom:n,paneClickDistance:o,translateExtent:r,viewport:i,onPanZoom:a,onPanZoomStart:l,onPanZoomEnd:u,onDraggingChange:s}){const c={isZoomingOrPanning:!1,usedRightMouseButton:!1,prevViewport:{x:0,y:0,zoom:0},mouseButton:0,timerId:void 0,panScrollTimeout:void 0,isPanScrolling:!1},d=e.getBoundingClientRect(),f=cu().clickDistance(!Ln(o)||o<0?0:o).scaleExtent([t,n]).translateExtent(r),g=Ft(e).call(f);h({x:i.x,y:i.y,zoom:Io(i.zoom,t,n)},[[0,0],[d.width,d.height]],r);const m=g.on("wheel.zoom"),v=g.on("dblclick.zoom");function y(C,b){return g?new Promise(k=>{f==null||f.transform(tl(g,b==null?void 0:b.duration,()=>k(!0)),C)}):Promise.resolve(!1)}function w(){f.on("zoom",null)}async function h(C,b,k){const _=el(C),S=f==null?void 0:f.constrain()(_,b,k);return S&&await y(S),new Promise(E=>E(S))}return f.wheelDelta(Wu),{update:function({noWheelClassName:C,noPanClassName:b,onPaneContextMenu:k,userSelectionActive:_,panOnScroll:S,panOnDrag:E,panOnScrollMode:L,panOnScrollSpeed:P,preventScrolling:D,zoomOnPinch:M,zoomOnScroll:H,zoomOnDoubleClick:O,zoomActivationKeyPressed:V,lib:z,onTransformChange:N}){_&&!c.isZoomingOrPanning&&w();const A=!S||V||_?function({noWheelClassName:B,preventScrolling:Q,d3ZoomHandler:ne}){return function(ee,me){if(!Q&&ee.type==="wheel"&&!ee.ctrlKey||Ro(ee,B))return null;ee.preventDefault(),ne.call(this,ee,me)}}({noWheelClassName:C,preventScrolling:D,d3ZoomHandler:m}):function({zoomPanValues:B,noWheelClassName:Q,d3Selection:ne,d3Zoom:ee,panOnScrollMode:me,panOnScrollSpeed:de,zoomOnPinch:J,onPanZoomStart:F,onPanZoom:I,onPanZoomEnd:j}){return G=>{if(Ro(G,Q))return!1;G.preventDefault(),G.stopImmediatePropagation();const le=ne.property("__zoom").k||1;if(G.ctrlKey&&J){const Y=on(G),$e=Wu(G),Ce=le*Math.pow(2,$e);return void ee.scaleTo(ne,Ce,Y,G)}const we=G.deltaMode===1?20:1;let Pe=me===Jn.Vertical?0:G.deltaX*we,Ne=me===Jn.Horizontal?0:G.deltaY*we;!Hi()&&G.shiftKey&&me!==Jn.Vertical&&(Pe=G.deltaY*we,Ne=0),ee.translateBy(ne,-Pe/le*de,-Ne/le*de,{internal:!0});const ue=Li(ne.property("__zoom"));clearTimeout(B.panScrollTimeout),B.isPanScrolling||(B.isPanScrolling=!0,F==null||F(G,ue)),B.isPanScrolling&&(I==null||I(G,ue),B.panScrollTimeout=setTimeout(()=>{j==null||j(G,ue),B.isPanScrolling=!1},150))}}({zoomPanValues:c,noWheelClassName:C,d3Selection:g,d3Zoom:f,panOnScrollMode:L,panOnScrollSpeed:P,zoomOnPinch:M,onPanZoomStart:l,onPanZoom:a,onPanZoomEnd:u});if(g.on("wheel.zoom",A,{passive:!1}),!_){const B=function({zoomPanValues:ee,onDraggingChange:me,onPanZoomStart:de}){return J=>{var F,I,j;if((F=J.sourceEvent)!=null&&F.internal)return;const G=Li(J.transform);ee.mouseButton=((I=J.sourceEvent)==null?void 0:I.button)||0,ee.isZoomingOrPanning=!0,ee.prevViewport=G,((j=J.sourceEvent)==null?void 0:j.type)==="mousedown"&&me(!0),de&&(de==null||de(J.sourceEvent,G))}}({zoomPanValues:c,onDraggingChange:s,onPanZoomStart:l});f.on("start",B);const Q=function({zoomPanValues:ee,panOnDrag:me,onPaneContextMenu:de,onTransformChange:J,onPanZoom:F}){return I=>{var j,G;ee.usedRightMouseButton=!(!de||!Ku(me,ee.mouseButton??0)),(j=I.sourceEvent)!=null&&j.sync||J([I.transform.x,I.transform.y,I.transform.k]),F&&((G=I.sourceEvent)==null||!G.internal)&&(F==null||F(I.sourceEvent,Li(I.transform)))}}({zoomPanValues:c,panOnDrag:E,onPaneContextMenu:!!k,onPanZoom:a,onTransformChange:N});f.on("zoom",Q);const ne=function({zoomPanValues:ee,panOnDrag:me,panOnScroll:de,onDraggingChange:J,onPanZoomEnd:F,onPaneContextMenu:I}){return j=>{var G;if(((G=j.sourceEvent)==null||!G.internal)&&(ee.isZoomingOrPanning=!1,I&&Ku(me,ee.mouseButton??0)&&!ee.usedRightMouseButton&&j.sourceEvent&&I(j.sourceEvent),ee.usedRightMouseButton=!1,J(!1),F&&((le,we)=>le.x!==we.x||le.y!==we.y||le.zoom!==we.k)(ee.prevViewport,j.transform))){const le=Li(j.transform);ee.prevViewport=le,clearTimeout(ee.timerId),ee.timerId=setTimeout(()=>{F==null||F(j.sourceEvent,le)},de?150:0)}}}({zoomPanValues:c,panOnDrag:E,panOnScroll:S,onPaneContextMenu:k,onPanZoomEnd:u,onDraggingChange:s});f.on("end",ne)}const X=function({zoomActivationKeyPressed:B,zoomOnScroll:Q,zoomOnPinch:ne,panOnDrag:ee,panOnScroll:me,zoomOnDoubleClick:de,userSelectionActive:J,noWheelClassName:F,noPanClassName:I,lib:j}){return G=>{var le;const we=B||Q,Pe=ne&&G.ctrlKey;if(G.button===1&&G.type==="mousedown"&&(Ro(G,`${j}-flow__node`)||Ro(G,`${j}-flow__edge`)))return!0;if(!ee&&!we&&!me&&!de&&!ne||J||Ro(G,F)&&G.type==="wheel"||Ro(G,I)&&(G.type!=="wheel"||me&&G.type==="wheel"&&!B)||!ne&&G.ctrlKey&&G.type==="wheel")return!1;if(!ne&&G.type==="touchstart"&&((le=G.touches)==null?void 0:le.length)>1)return G.preventDefault(),!1;if(!we&&!me&&!Pe&&G.type==="wheel"||!ee&&(G.type==="mousedown"||G.type==="touchstart")||Array.isArray(ee)&&!ee.includes(G.button)&&G.type==="mousedown")return!1;const Ne=Array.isArray(ee)&&ee.includes(G.button)||!G.button||G.button<=1;return(!G.ctrlKey||G.type==="wheel")&&Ne}}({zoomActivationKeyPressed:V,panOnDrag:E,zoomOnScroll:H,panOnScroll:S,zoomOnDoubleClick:O,zoomOnPinch:M,userSelectionActive:_,noPanClassName:b,noWheelClassName:C,lib:z});f.filter(X),O?g.on("dblclick.zoom",v):g.on("dblclick.zoom",null)},destroy:w,setViewport:async function(C,b){const k=el(C);return await y(k,b),new Promise(_=>_(k))},setViewportConstrained:h,getViewport:function(){const C=g?su(g.node()):{x:0,y:0,k:1};return{x:C.x,y:C.y,zoom:C.k}},scaleTo:function(C,b){return g?new Promise(k=>{f==null||f.scaleTo(tl(g,b==null?void 0:b.duration,()=>k(!0)),C)}):Promise.resolve(!1)},scaleBy:function(C,b){return g?new Promise(k=>{f==null||f.scaleBy(tl(g,b==null?void 0:b.duration,()=>k(!0)),C)}):Promise.resolve(!1)},setScaleExtent:function(C){f==null||f.scaleExtent(C)},setTranslateExtent:function(C){f==null||f.translateExtent(C)},syncViewport:function(C){if(g){const b=el(C),k=g.property("__zoom");(k.k!==C.zoom||k.x!==C.x||k.y!==C.y)&&(f==null||f.transform(g,b,null,{sync:!0}))}},setClickDistance:function(C){const b=!Ln(C)||C<0?0:C;f==null||f.clickDistance(b)}}}var ju;(function(e){e.Line="line",e.Handle="handle"})(ju||(ju={}));var Tf=ae('<div role="button" tabindex="-1"><!></div>');function eo(e,t){ve(t,!1);const[n,o]=Je(),r=()=>ie(O,"$connectable",n),i=()=>ie(z,"$connectionMode",n),a=()=>ie(Ne,"$flowId",n),l=()=>ie(me,"$onedgecreate",n),u=()=>ie(B,"$viewport",n),s=()=>ie(ue,"$connection",n),c=()=>ie(j,"$edges",n),d=()=>ie(G,"$connectionLookup",n),f=se(),g=se(),m=se(),v=se(),y=se(),w=se(),h=se(),C=se();let b=$(t,"id",12,void 0),k=$(t,"type",12,"source"),_=$(t,"position",28,()=>Ee.Top),S=$(t,"style",12,void 0),E=$(t,"isValidConnection",12,void 0),L=$(t,"onconnect",12,void 0),P=$(t,"ondisconnect",12,void 0),D=$(t,"isConnectable",12,void 0),M=$(t,"class",12,void 0);const H=uo("svelteflow__node_id"),O=uo("svelteflow__node_connectable"),V=Ke(),{connectionMode:z,domNode:N,nodeLookup:A,connectionRadius:X,viewport:B,isValidConnection:Q,lib:ne,addEdge:ee,onedgecreate:me,panBy:de,cancelConnection:J,updateConnection:F,autoPanOnConnect:I,edges:j,connectionLookup:G,onconnect:le,onconnectstart:we,onconnectend:Pe,flowId:Ne,connection:ue}=V;function Y(fe){const U=_u(fe);(U&&fe.button===0||!U)&&Of.onPointerDown(fe,{handleId:p(m),nodeId:H,isTarget:p(f),connectionRadius:ie(X,"$connectionRadius",n),domNode:ie(N,"$domNode",n),nodeLookup:ie(A,"$nodeLookup",n),connectionMode:i(),lib:ie(ne,"$lib",n),autoPanOnConnect:ie(I,"$autoPanOnConnect",n),flowId:a(),isValidConnection:E()??ie(Q,"$isValidConnectionStore",n),updateConnection:F,cancelConnection:J,panBy:de,onConnect:re=>{var Qe;const Fe=l()?l()(re):re;Fe&&(ee(Fe),(Qe=ie(le,"$onConnectAction",n))==null||Qe(re))},onConnectStart:(re,Qe)=>{var Fe;(Fe=ie(we,"$onConnectStartAction",n))==null||Fe(re,{nodeId:Qe.nodeId,handleId:Qe.handleId,handleType:Qe.handleType})},onConnectEnd:(re,Qe)=>{var Fe;(Fe=ie(Pe,"$onConnectEndAction",n))==null||Fe(re,Qe)},getTransform:()=>[u().x,u().y,u().zoom],getFromHandle:()=>s().fromHandle})}let $e=se(null),Ce=se();he(()=>oe(k()),()=>{te(f,k()==="target")}),he(()=>(oe(D()),r()),()=>{te(g,D()!==void 0?D():r())}),he(()=>oe(b()),()=>{te(m,b()||null)}),he(()=>(oe(L()),oe(P()),c(),d(),oe(k()),oe(b())),()=>{(L()||P())&&(c(),te(Ce,d().get(`${H}-${k()}${b()?`-${b()}`:""}`)))}),he(()=>(p($e),p(Ce),oe(P()),oe(L())),()=>{if(p($e)&&!function(fe,U){if(!fe&&!U)return!0;if(!fe||!U||fe.size!==U.size)return!1;if(!fe.size&&!U.size)return!0;for(const re of fe.keys())if(!U.has(re))return!1;return!0}(p(Ce),p($e))){const fe=p(Ce)??new Map;vu(p($e),fe,P()),vu(fe,p($e),L())}te($e,p(Ce)??new Map)}),he(()=>s(),()=>{te(v,!!s().fromHandle)}),he(()=>(s(),oe(k()),p(m)),()=>{var fe,U,re;te(y,((fe=s().fromHandle)==null?void 0:fe.nodeId)===H&&((U=s().fromHandle)==null?void 0:U.type)===k()&&((re=s().fromHandle)==null?void 0:re.id)===p(m))}),he(()=>(s(),oe(k()),p(m)),()=>{var fe,U,re;te(w,((fe=s().toHandle)==null?void 0:fe.nodeId)===H&&((U=s().toHandle)==null?void 0:U.type)===k()&&((re=s().toHandle)==null?void 0:re.id)===p(m))}),he(()=>(i(),s(),oe(k()),p(m)),()=>{var fe,U,re;te(h,i()===ho.Strict?((fe=s().fromHandle)==null?void 0:fe.type)!==k():H!==((U=s().fromHandle)==null?void 0:U.nodeId)||p(m)!==((re=s().fromHandle)==null?void 0:re.id))}),he(()=>(p(w),s()),()=>{te(C,p(w)&&s().isValid)}),ut(),Le();var Se=Tf();let Ae;pe(Se,"data-nodeid",H),ft(W(Se),t,"default",{}),K(Se),Me(fe=>{pe(Se,"data-handleid",p(m)),pe(Se,"data-handlepos",_()),pe(Se,"data-id",`${a()??""}-${H??""}-${b()||""}-${k()??""}`),Ae=bt(Se,1,mn(fe),null,Ae,{valid:p(C),connectingto:p(w),connectingfrom:p(y),source:!p(f),target:p(f),connectablestart:p(g),connectableend:p(g),connectable:p(g),connectionindicator:p(g)&&(!p(v)||p(h))}),pe(Se,"style",S())},[()=>xt(["svelte-flow__handle",`svelte-flow__handle-${_()}`,"nodrag","nopan",_(),M()])],ye),Ze("mousedown",Se,Y),Ze("touchstart",Se,Y),T(e,Se);var Ge=ge({get id(){return b()},set id(fe){b(fe),x()},get type(){return k()},set type(fe){k(fe),x()},get position(){return _()},set position(fe){_(fe),x()},get style(){return S()},set style(fe){S(fe),x()},get isValidConnection(){return E()},set isValidConnection(fe){E(fe),x()},get onconnect(){return L()},set onconnect(fe){L(fe),x()},get ondisconnect(){return P()},set ondisconnect(fe){P(fe),x()},get isConnectable(){return D()},set isConnectable(fe){D(fe),x()},get class(){return M()},set class(fe){M(fe),x()}});return o(),Ge}ce(eo,{id:{},type:{},position:{},style:{},isValidConnection:{},onconnect:{},ondisconnect:{},isConnectable:{},class:{}},["default"],[],!0);var Af=ae("<!> <!>",1);function Oi(e,t){const n=et(t,["children","$$slots","$$events","$$legacy","$$host"]);et(n,["data","targetPosition","sourcePosition"]),ve(t,!1);let o=$(t,"data",28,()=>({label:"Node"})),r=$(t,"targetPosition",12,void 0),i=$(t,"sourcePosition",12,void 0);Le();var a=Af(),l=ke(a);const u=ye(()=>r()??Ee.Top);eo(l,{type:"target",get position(){return p(u)}});var s=Z(l),c=Z(s);const d=ye(()=>i()??Ee.Bottom);return eo(c,{type:"source",get position(){return p(d)}}),Me(()=>{var f;return Tt(s,` ${((f=o())==null?void 0:f.label)??""} `)}),T(e,a),ge({get data(){return o()},set data(f){o(f),x()},get targetPosition(){return r()},set targetPosition(f){r(f),x()},get sourcePosition(){return i()},set sourcePosition(f){i(f),x()}})}ce(Oi,{data:{},targetPosition:{},sourcePosition:{}},[],[],!0);var If=ae(" <!>",1);function qu(e,t){const n=et(t,["children","$$slots","$$events","$$legacy","$$host"]);et(n,["data","sourcePosition"]),ve(t,!1);let o=$(t,"data",28,()=>({label:"Node"})),r=$(t,"sourcePosition",12,void 0);Le(),He();var i=If(),a=ke(i),l=Z(a);const u=ye(()=>r()??Ee.Bottom);return eo(l,{type:"source",get position(){return p(u)}}),Me(()=>{var s;return Tt(a,`${((s=o())==null?void 0:s.label)??""} `)}),T(e,i),ge({get data(){return o()},set data(s){o(s),x()},get sourcePosition(){return r()},set sourcePosition(s){r(s),x()}})}ce(qu,{data:{},sourcePosition:{}},[],[],!0);var Zf=ae(" <!>",1);function Fu(e,t){const n=et(t,["children","$$slots","$$events","$$legacy","$$host"]);et(n,["data","targetPosition"]),ve(t,!1);let o=$(t,"data",28,()=>({label:"Node"})),r=$(t,"targetPosition",12,void 0);Le(),He();var i=Zf(),a=ke(i),l=Z(a);const u=ye(()=>r()??Ee.Top);return eo(l,{type:"target",get position(){return p(u)}}),Me(()=>{var s;return Tt(a,`${((s=o())==null?void 0:s.label)??""} `)}),T(e,i),ge({get data(){return o()},set data(s){o(s),x()},get targetPosition(){return r()},set targetPosition(s){r(s),x()}})}function Gu(e,t){const n=et(t,["children","$$slots","$$events","$$legacy","$$host"]);et(n,[])}function Uu(e,t,n){if(!t)return;const o=n?t.querySelector(n):t;o&&o.appendChild(e)}function Di(e,{target:t,domNode:n}){return Uu(e,n,t),{async update({target:o,domNode:r}){Uu(e,r,o)},destroy(){e.parentNode&&e.parentNode.removeChild(e)}}}ce(Fu,{data:{},targetPosition:{}},[],[],!0),ce(Gu,{},[],[],!0);var Bf=ae("<div><!></div>");function Ju(e,t){ve(t,!1);const[n,o]=Je(),{domNode:r}=Ke();Le();var i=Bf();ft(W(i),t,"default",{}),K(i),vt(i,(a,l)=>Di==null?void 0:Di(a,l),()=>({target:".svelte-flow__edgelabel-renderer",domNode:ie(r,"$domNode",n)})),T(e,i),ge(),o()}function Qu(){const{edgeLookup:e,selectionRect:t,selectionRectMode:n,multiselectionKeyPressed:o,addSelectedEdges:r,unselectNodesAndEdges:i,elementsSelectable:a}=Ke();return l=>{const u=q(e).get(l);u&&(u.selectable||q(a)&&typeof u.selectable>"u")&&(t.set(null),n.set(null),u.selected?u.selected&&q(o)&&i({nodes:[],edges:[u]}):r([l]))}}ce(Ju,{},["default"],[],!0);var Rf=ae('<div class="svelte-flow__edge-label" role="button" tabindex="-1"><!></div>');function ec(e,t){ve(t,!1);let n=$(t,"style",12,void 0),o=$(t,"x",12,void 0),r=$(t,"y",12,void 0);const i=Qu(),a=uo("svelteflow__edge_id");return Le(),Ju(e,{children:(l,u)=>{var s=Rf();ft(W(s),t,"default",{}),K(s),Me(()=>{pe(s,"style","pointer-events: all;"+n()),nt(s,"transform",`translate(-50%, -50%) translate(${o()??""}px,${r()??""}px)`)}),Ze("keyup",s,()=>{}),Ze("click",s,()=>{a&&i(a)}),T(l,s)},$$slots:{default:!0}}),ge({get style(){return n()},set style(l){n(l),x()},get x(){return o()},set x(l){o(l),x()},get y(){return r()},set y(l){r(l),x()}})}ce(ec,{style:{},x:{},y:{}},["default"],[],!0);var Xf=xe('<path fill="none" class="svelte-flow__edge-interaction"></path>'),Yf=xe('<path fill="none"></path><!><!>',1);function Pr(e,t){ve(t,!1);let n=$(t,"id",12,void 0),o=$(t,"path",12),r=$(t,"label",12,void 0),i=$(t,"labelX",12,void 0),a=$(t,"labelY",12,void 0),l=$(t,"labelStyle",12,void 0),u=$(t,"markerStart",12,void 0),s=$(t,"markerEnd",12,void 0),c=$(t,"style",12,void 0),d=$(t,"interactionWidth",12,20),f=$(t,"class",12,void 0),g=d()===void 0?20:d();Le();var m=Yf(),v=ke(m),y=Z(v),w=b=>{var k=Xf();pe(k,"stroke-opacity",0),pe(k,"stroke-width",g),Me(()=>pe(k,"d",o())),T(b,k)};_e(y,b=>{g&&b(w)});var h=Z(y),C=b=>{ec(b,{get x(){return i()},get y(){return a()},get style(){return l()},children:(k,_)=>{He();var S=Te();Me(()=>Tt(S,r())),T(k,S)},$$slots:{default:!0}})};return _e(h,b=>{r()&&b(C)}),Me(b=>{pe(v,"id",n()),pe(v,"d",o()),bt(v,0,mn(b)),pe(v,"marker-start",u()),pe(v,"marker-end",s()),pe(v,"style",c())},[()=>xt(["svelte-flow__edge-path",f()])],ye),T(e,m),ge({get id(){return n()},set id(b){n(b),x()},get path(){return o()},set path(b){o(b),x()},get label(){return r()},set label(b){r(b),x()},get labelX(){return i()},set labelX(b){i(b),x()},get labelY(){return a()},set labelY(b){a(b),x()},get labelStyle(){return l()},set labelStyle(b){l(b),x()},get markerStart(){return u()},set markerStart(b){u(b),x()},get markerEnd(){return s()},set markerEnd(b){s(b),x()},get style(){return c()},set style(b){c(b),x()},get interactionWidth(){return d()},set interactionWidth(b){d(b),x()},get class(){return f()},set class(b){f(b),x()}})}function Ti(e,t){const n=et(t,["children","$$slots","$$events","$$legacy","$$host"]);et(n,["label","labelStyle","style","markerStart","markerEnd","interactionWidth","sourceX","sourceY","sourcePosition","targetX","targetY","targetPosition"]),ve(t,!1);const o=se(),r=se(),i=se();let a=$(t,"label",12,void 0),l=$(t,"labelStyle",12,void 0),u=$(t,"style",12,void 0),s=$(t,"markerStart",12,void 0),c=$(t,"markerEnd",12,void 0),d=$(t,"interactionWidth",12,void 0),f=$(t,"sourceX",12),g=$(t,"sourceY",12),m=$(t,"sourcePosition",12),v=$(t,"targetX",12),y=$(t,"targetY",12),w=$(t,"targetPosition",12);return he(()=>(p(o),p(r),p(i),oe(f()),oe(g()),oe(v()),oe(y()),oe(m()),oe(w())),()=>{var h;h=Pu({sourceX:f(),sourceY:g(),targetX:v(),targetY:y(),sourcePosition:m(),targetPosition:w()}),te(o,h[0]),te(r,h[1]),te(i,h[2])}),ut(),Le(),Pr(e,{get path(){return p(o)},get labelX(){return p(r)},get labelY(){return p(i)},get label(){return a()},get labelStyle(){return l()},get markerStart(){return s()},get markerEnd(){return c()},get interactionWidth(){return d()},get style(){return u()}}),ge({get label(){return a()},set label(h){a(h),x()},get labelStyle(){return l()},set labelStyle(h){l(h),x()},get style(){return u()},set style(h){u(h),x()},get markerStart(){return s()},set markerStart(h){s(h),x()},get markerEnd(){return c()},set markerEnd(h){c(h),x()},get interactionWidth(){return d()},set interactionWidth(h){d(h),x()},get sourceX(){return f()},set sourceX(h){f(h),x()},get sourceY(){return g()},set sourceY(h){g(h),x()},get sourcePosition(){return m()},set sourcePosition(h){m(h),x()},get targetX(){return v()},set targetX(h){v(h),x()},get targetY(){return y()},set targetY(h){y(h),x()},get targetPosition(){return w()},set targetPosition(h){w(h),x()}})}function tc(e,t){const n=et(t,["children","$$slots","$$events","$$legacy","$$host"]);et(n,["label","labelStyle","style","markerStart","markerEnd","interactionWidth","sourceX","sourceY","sourcePosition","targetX","targetY","targetPosition"]),ve(t,!1);const o=se(),r=se(),i=se();let a=$(t,"label",12,void 0),l=$(t,"labelStyle",12,void 0),u=$(t,"style",12,void 0),s=$(t,"markerStart",12,void 0),c=$(t,"markerEnd",12,void 0),d=$(t,"interactionWidth",12,void 0),f=$(t,"sourceX",12),g=$(t,"sourceY",12),m=$(t,"sourcePosition",12),v=$(t,"targetX",12),y=$(t,"targetY",12),w=$(t,"targetPosition",12);return he(()=>(p(o),p(r),p(i),oe(f()),oe(g()),oe(v()),oe(y()),oe(m()),oe(w())),()=>{var h;h=Ni({sourceX:f(),sourceY:g(),targetX:v(),targetY:y(),sourcePosition:m(),targetPosition:w()}),te(o,h[0]),te(r,h[1]),te(i,h[2])}),ut(),Le(),Pr(e,{get path(){return p(o)},get labelX(){return p(r)},get labelY(){return p(i)},get label(){return a()},get labelStyle(){return l()},get markerStart(){return s()},get markerEnd(){return c()},get interactionWidth(){return d()},get style(){return u()}}),ge({get label(){return a()},set label(h){a(h),x()},get labelStyle(){return l()},set labelStyle(h){l(h),x()},get style(){return u()},set style(h){u(h),x()},get markerStart(){return s()},set markerStart(h){s(h),x()},get markerEnd(){return c()},set markerEnd(h){c(h),x()},get interactionWidth(){return d()},set interactionWidth(h){d(h),x()},get sourceX(){return f()},set sourceX(h){f(h),x()},get sourceY(){return g()},set sourceY(h){g(h),x()},get sourcePosition(){return m()},set sourcePosition(h){m(h),x()},get targetX(){return v()},set targetX(h){v(h),x()},get targetY(){return y()},set targetY(h){y(h),x()},get targetPosition(){return w()},set targetPosition(h){w(h),x()}})}function nc(e,t){const n=et(t,["children","$$slots","$$events","$$legacy","$$host"]);et(n,["label","labelStyle","style","markerStart","markerEnd","interactionWidth","sourceX","sourceY","targetX","targetY"]),ve(t,!1);const o=se(),r=se(),i=se();let a=$(t,"label",12,void 0),l=$(t,"labelStyle",12,void 0),u=$(t,"style",12,void 0),s=$(t,"markerStart",12,void 0),c=$(t,"markerEnd",12,void 0),d=$(t,"interactionWidth",12,void 0),f=$(t,"sourceX",12),g=$(t,"sourceY",12),m=$(t,"targetX",12),v=$(t,"targetY",12);return he(()=>(p(o),p(r),p(i),oe(f()),oe(g()),oe(m()),oe(v())),()=>{var y;y=qa({sourceX:f(),sourceY:g(),targetX:m(),targetY:v()}),te(o,y[0]),te(r,y[1]),te(i,y[2])}),ut(),Le(),Pr(e,{get path(){return p(o)},get labelX(){return p(r)},get labelY(){return p(i)},get label(){return a()},get labelStyle(){return l()},get markerStart(){return s()},get markerEnd(){return c()},get interactionWidth(){return d()},get style(){return u()}}),ge({get label(){return a()},set label(y){a(y),x()},get labelStyle(){return l()},set labelStyle(y){l(y),x()},get style(){return u()},set style(y){u(y),x()},get markerStart(){return s()},set markerStart(y){s(y),x()},get markerEnd(){return c()},set markerEnd(y){c(y),x()},get interactionWidth(){return d()},set interactionWidth(y){d(y),x()},get sourceX(){return f()},set sourceX(y){f(y),x()},get sourceY(){return g()},set sourceY(y){g(y),x()},get targetX(){return m()},set targetX(y){m(y),x()},get targetY(){return v()},set targetY(y){v(y),x()}})}function oc(e,t){const n=et(t,["children","$$slots","$$events","$$legacy","$$host"]);et(n,["label","labelStyle","style","markerStart","markerEnd","interactionWidth","sourceX","sourceY","sourcePosition","targetX","targetY","targetPosition"]),ve(t,!1);const o=se(),r=se(),i=se();let a=$(t,"label",12,void 0),l=$(t,"labelStyle",12,void 0),u=$(t,"style",12,void 0),s=$(t,"markerStart",12,void 0),c=$(t,"markerEnd",12,void 0),d=$(t,"interactionWidth",12,void 0),f=$(t,"sourceX",12),g=$(t,"sourceY",12),m=$(t,"sourcePosition",12),v=$(t,"targetX",12),y=$(t,"targetY",12),w=$(t,"targetPosition",12);return he(()=>(p(o),p(r),p(i),oe(f()),oe(g()),oe(v()),oe(y()),oe(m()),oe(w())),()=>{var h;h=Ni({sourceX:f(),sourceY:g(),targetX:v(),targetY:y(),sourcePosition:m(),targetPosition:w(),borderRadius:0}),te(o,h[0]),te(r,h[1]),te(i,h[2])}),ut(),Le(),Pr(e,{get path(){return p(o)},get labelX(){return p(r)},get labelY(){return p(i)},get label(){return a()},get labelStyle(){return l()},get markerStart(){return s()},get markerEnd(){return c()},get interactionWidth(){return d()},get style(){return u()}}),ge({get label(){return a()},set label(h){a(h),x()},get labelStyle(){return l()},set labelStyle(h){l(h),x()},get style(){return u()},set style(h){u(h),x()},get markerStart(){return s()},set markerStart(h){s(h),x()},get markerEnd(){return c()},set markerEnd(h){c(h),x()},get interactionWidth(){return d()},set interactionWidth(h){d(h),x()},get sourceX(){return f()},set sourceX(h){f(h),x()},get sourceY(){return g()},set sourceY(h){g(h),x()},get sourcePosition(){return m()},set sourcePosition(h){m(h),x()},get targetX(){return v()},set targetX(h){v(h),x()},get targetY(){return y()},set targetY(h){y(h),x()},get targetPosition(){return w()},set targetPosition(h){w(h),x()}})}ce(Pr,{id:{},path:{},label:{},labelX:{},labelY:{},labelStyle:{},markerStart:{},markerEnd:{},style:{},interactionWidth:{},class:{}},[],[],!0),ce(Ti,{label:{},labelStyle:{},style:{},markerStart:{},markerEnd:{},interactionWidth:{},sourceX:{},sourceY:{},sourcePosition:{},targetX:{},targetY:{},targetPosition:{}},[],[],!0),ce(tc,{label:{},labelStyle:{},style:{},markerStart:{},markerEnd:{},interactionWidth:{},sourceX:{},sourceY:{},sourcePosition:{},targetX:{},targetY:{},targetPosition:{}},[],[],!0),ce(nc,{label:{},labelStyle:{},style:{},markerStart:{},markerEnd:{},interactionWidth:{},sourceX:{},sourceY:{},targetX:{},targetY:{}},[],[],!0),ce(oc,{label:{},labelStyle:{},style:{},markerStart:{},markerEnd:{},interactionWidth:{},sourceX:{},sourceY:{},sourcePosition:{},targetX:{},targetY:{},targetPosition:{}},[],[],!0);const Kf=(e,t,n,o=[0,0],r=Si)=>{const{subscribe:i,set:a,update:l}=be([]);let u=e,s={},c=!0;const d=f=>(Ou(f,t,n,{elevateNodesOnSelect:c,nodeOrigin:o,nodeExtent:r,defaults:s,checkEquality:!1}),u=f,a(u),u);return d(u),{subscribe:i,set:d,update:f=>d(f(u)),setDefaultOptions:f=>{s=f},setOptions:f=>{c=f.elevateNodesOnSelect??c}}},Wf=(e,t,n,o)=>{const{subscribe:r,set:i,update:a}=be([]);let l=e,u={};const s=c=>{const d=u?c.map(f=>({...u,...f})):c;Au(t,n,d),l=d,i(l)};return s(l),{subscribe:r,set:s,update:c=>s(c(l)),setDefaultOptions:c=>{u=c}}},rc={input:qu,output:Fu,default:Oi,group:Gu},ic={straight:nc,smoothstep:tc,default:Ti,step:oc};function jf(e){const t=Fn([e.edges,e.nodes,e.nodeLookup,e.onlyRenderVisibleElements,e.viewport,e.width,e.height],([n,,o,r,i,a,l])=>r&&a&&l?n.filter(u=>{const s=o.get(u.source),c=o.get(u.target);return s&&c&&function({sourceNode:d,targetNode:f,width:g,height:m,transform:v}){const y=Pi(Vi(d),Vi(f));y.x===y.x2&&(y.x2+=1),y.y===y.y2&&(y.y2+=1);const w={x:-v[0]/v[2],y:-v[1]/v[2],width:g/v[2],height:m/v[2]};return _r(w,Mi(y))>0}({sourceNode:s,targetNode:c,width:a,height:l,transform:[i.x,i.y,i.zoom]})}):n);return Fn([t,e.nodes,e.nodeLookup,e.connectionMode,e.onerror],([n,,o,r,i])=>n.reduce((a,l)=>{const u=o.get(l.source),s=o.get(l.target);if(!u||!s)return a;const c=function(d){var f;const{sourceNode:g,targetNode:m}=d;if(!zu(g)||!zu(m))return null;const v=g.internals.handleBounds||Nu(g.handles),y=m.internals.handleBounds||Nu(m.handles),w=Lu((v==null?void 0:v.source)??[],d.sourceHandle),h=Lu(d.connectionMode===ho.Strict?(y==null?void 0:y.target)??[]:((y==null?void 0:y.target)??[]).concat((y==null?void 0:y.source)??[]),d.targetHandle);if(!w||!h)return(f=d.onError)==null||f.call(d,"008",$f(w?"target":"source",{id:d.id,sourceHandle:d.sourceHandle,targetHandle:d.targetHandle})),null;const C=(w==null?void 0:w.position)||Ee.Bottom,b=(h==null?void 0:h.position)||Ee.Top,k=Er(g,w,C),_=Er(m,h,b);return{sourceX:k.x,sourceY:k.y,targetX:_.x,targetY:_.y,sourcePosition:C,targetPosition:b}}({id:l.id,sourceNode:u,targetNode:s,sourceHandle:l.sourceHandle||null,targetHandle:l.targetHandle||null,connectionMode:r,onError:i});return c&&a.push({...l,zIndex:Pf({selected:l.selected,zIndex:l.zIndex,sourceNode:u,targetNode:s,elevateOnSelect:!1}),...c}),a},[]))}function qf(e){return Fn([e.nodeLookup,e.onlyRenderVisibleElements,e.width,e.height,e.viewport,e.nodes],([t,n,o,r,i])=>{const a=[i.x,i.y,i.zoom];return n?gu(t,{x:0,y:0,width:o,height:r},a,!0):Array.from(t.values())})}const Ai=Symbol();function ac({nodes:e,edges:t,width:n,height:o,fitView:r,nodeOrigin:i,nodeExtent:a}){const l=(({nodes:m=[],edges:v=[],width:y,height:w,fitView:h,nodeOrigin:C,nodeExtent:b})=>{const k=new Map,_=new Map,S=new Map,E=new Map,L=C??[0,0],P=b??Si;Ou(m,k,_,{nodeExtent:P,nodeOrigin:L,elevateNodesOnSelect:!1,checkEquality:!1}),Au(S,E,v);let D={x:0,y:0,zoom:1};if(h&&y&&w){const M=kr(k,{filter:H=>!(!H.width&&!H.initialWidth||!H.height&&!H.initialHeight)});D=Ka(M,y,w,.5,2,.1)}return{flowId:be(null),nodes:Kf(m,k,_,L,P),nodeLookup:qt(k),parentLookup:qt(_),edgeLookup:qt(E),visibleNodes:qt([]),edges:Wf(v,S,E),visibleEdges:qt([]),connectionLookup:qt(S),height:be(500),width:be(500),minZoom:be(.5),maxZoom:be(2),nodeOrigin:be(L),nodeDragThreshold:be(1),nodeExtent:be(P),translateExtent:be(Si),autoPanOnNodeDrag:be(!0),autoPanOnConnect:be(!0),fitViewOnInit:be(!1),fitViewOnInitDone:be(!1),fitViewOptions:be(void 0),panZoom:be(null),snapGrid:be(null),dragging:be(!1),selectionRect:be(null),selectionKeyPressed:be(!1),multiselectionKeyPressed:be(!1),deleteKeyPressed:be(!1),panActivationKeyPressed:be(!1),zoomActivationKeyPressed:be(!1),selectionRectMode:be(null),selectionMode:be(Ei.Partial),nodeTypes:be(rc),edgeTypes:be(ic),viewport:be(D),connectionMode:be(ho.Strict),domNode:be(null),connection:qt(Za),connectionLineType:be(Ao.Bezier),connectionRadius:be(20),isValidConnection:be(()=>!0),nodesDraggable:be(!0),nodesConnectable:be(!0),elementsSelectable:be(!0),selectNodesOnDrag:be(!0),markers:qt([]),defaultMarkerColor:be("#b1b1b7"),lib:qt("svelte"),onlyRenderVisibleElements:be(!1),onerror:be(Sf),ondelete:be(void 0),onedgecreate:be(void 0),onconnect:be(void 0),onconnectstart:be(void 0),onconnectend:be(void 0),onbeforedelete:be(void 0),nodesInitialized:be(!1),edgesInitialized:be(!1),viewportInitialized:be(!1),initialized:qt(!1)}})({nodes:e,edges:t,width:n,height:o,fitView:r,nodeOrigin:i,nodeExtent:a});function u(m,v){const y=q(l.panZoom);return y?y.scaleBy(m,v):Promise.resolve(!1)}function s(m){let v=!1;return m.forEach(y=>{y.selected&&(y.selected=!1,v=!0)}),v}function c(m){s((m==null?void 0:m.nodes)||q(l.nodes))&&l.nodes.set(q(l.nodes)),s((m==null?void 0:m.edges)||q(l.edges))&&l.edges.set(q(l.edges))}function d(m){const v=q(l.multiselectionKeyPressed);l.nodes.update(y=>y.map(w=>{const h=m.includes(w.id),C=v&&w.selected||h;return w.selected=C,w})),v||l.edges.update(y=>y.map(w=>(w.selected=!1,w)))}l.deleteKeyPressed.subscribe(async m=>{var v;if(m){const y=q(l.nodes),w=q(l.edges),h=y.filter(_=>_.selected),C=w.filter(_=>_.selected),{nodes:b,edges:k}=await mu({nodesToRemove:h,edgesToRemove:C,nodes:y,edges:w,onBeforeDelete:q(l.onbeforedelete)});(b.length||k.length)&&(l.nodes.update(_=>_.filter(S=>!b.some(E=>E.id===S.id))),l.edges.update(_=>_.filter(S=>!k.some(E=>E.id===S.id))),(v=q(l.ondelete))==null||v({nodes:b,edges:k}))}});const f=be(Za);function g(){f.set(Za)}return{...l,visibleEdges:jf(l),visibleNodes:qf(l),connection:Fn([f,l.viewport],([m,v])=>m.inProgress?{...m,to:Sr(m.to,[v.x,v.y,v.zoom])}:{...m}),markers:Fn([l.edges,l.defaultMarkerColor,l.flowId],([m,v,y])=>function(w,{id:h,defaultColor:C,defaultMarkerStart:b,defaultMarkerEnd:k}){const _=new Set;return w.reduce((S,E)=>([E.markerStart||b,E.markerEnd||k].forEach(L=>{if(L&&typeof L=="object"){const P=Fa(L,h);_.has(P)||(S.push({id:P,color:L.color||C,...L}),_.add(P))}}),S),[]).sort((S,E)=>S.id.localeCompare(E.id))}(m,{defaultColor:v,id:y})),initialized:(()=>{let m=!1;const v=q(l.nodes).length,y=q(l.edges).length;return Fn([l.nodesInitialized,l.edgesInitialized,l.viewportInitialized],([w,h,C])=>m||(m=v===0?C:y===0?C&&w:C&&w&&h,m))})(),syncNodeStores:m=>function(v,y){const w=v.set,h=y.set,C=q(v),b=q(y);let k=C.length===0&&b.length>0?b:C;v.set(k);const _=S=>{const E=w(S);return k=E,h(k),E};v.set=y.set=_,v.update=y.update=S=>_(S(k))}(l.nodes,m),syncEdgeStores:m=>function(v,y){const w=v.set,h=y.set;let C=q(y);v.set(C);const b=k=>{w(k),h(k),C=k};v.set=y.set=b,v.update=y.update=k=>b(k(C))}(l.edges,m),syncViewport:m=>((v,y,w)=>{if(!w)return;const h=q(v),C=y.set,b=w.set;let k=w?q(w):{x:0,y:0,zoom:1};y.set(k),y.set=_=>(C(_),b(_),k=_,_),w.set=_=>(h==null||h.syncViewport(_),C(_),b(_),k=_,_),y.update=_=>{y.set(_(k))},w.update=_=>{w.set(_(k))}})(l.panZoom,l.viewport,m),setNodeTypes:function(m){l.nodeTypes.set({...rc,...m})},setEdgeTypes:function(m){l.edgeTypes.set({...ic,...m})},addEdge:function(m){const v=q(l.edges);l.edges.set(((y,w)=>{if(!y.source||!y.target)return w;let h;return h=(C=>"id"in C&&"source"in C&&"target"in C)(y)?{...y}:{...y,id:Mf(y)},((C,b)=>b.some(k=>!(k.source!==C.source||k.target!==C.target||k.sourceHandle!==C.sourceHandle&&(k.sourceHandle||C.sourceHandle)||k.targetHandle!==C.targetHandle&&(k.targetHandle||C.targetHandle))))(h,w)?w:(h.sourceHandle===null&&delete h.sourceHandle,h.targetHandle===null&&delete h.targetHandle,w.concat(h))})(m,v))},updateNodePositions:(m,v=!1)=>{var y;const w=q(l.nodeLookup);for(const[h,C]of m){const b=(y=w.get(h))==null?void 0:y.internals.userNode;b&&(b.position=C.position,b.dragging=v)}l.nodes.update(h=>h)},updateNodeInternals:function(m){var v,y,w;const h=q(l.nodeLookup),C=q(l.parentLookup),{changes:b,updatedInternals:k}=zf(m,h,q(l.parentLookup),q(l.domNode),q(l.nodeOrigin));if(k){if(function(_,S,E){const L=Ua(Ga,E);for(const P of _.values())if(P.parentId)Ja(P,_,S,L);else{const D=Cr(P,L.nodeOrigin),M=Bo(P.extent)?P.extent:L.nodeExtent,H=mo(D,M,Qn(P));P.internals.positionAbsolute=H}}(h,C,{nodeOrigin:i,nodeExtent:a}),!q(l.fitViewOnInitDone)&&q(l.fitViewOnInit)){const _=q(l.fitViewOptions),S=function(E){const L=q(l.panZoom);if(!L)return!1;const P=pu(q(l.nodeLookup),E);return hu({nodes:P,width:q(l.width),height:q(l.height),minZoom:q(l.minZoom),maxZoom:q(l.maxZoom),panZoom:L},E),P.size>0}({..._,nodes:_==null?void 0:_.nodes});l.fitViewOnInitDone.set(S)}for(const _ of b){const S=(v=h.get(_.id))==null?void 0:v.internals.userNode;if(S)switch(_.type){case"dimensions":{const E={...S.measured,..._.dimensions};_.setAttributes&&(S.width=((y=_.dimensions)==null?void 0:y.width)??S.width,S.height=((w=_.dimensions)==null?void 0:w.height)??S.height),S.measured=E;break}case"position":S.position=_.position??S.position}}l.nodes.update(_=>_),q(l.nodesInitialized)||l.nodesInitialized.set(!0)}},zoomIn:function(m){return u(1.2,m)},zoomOut:function(m){return u(1/1.2,m)},fitView:m=>function(v){const y=q(l.panZoom),w=q(l.domNode);if(!y||!w)return Promise.resolve(!1);const{width:h,height:C}=ja(w);return hu({nodes:pu(q(l.nodeLookup),v),width:h,height:C,minZoom:q(l.minZoom),maxZoom:q(l.maxZoom),panZoom:y},v)}(m),setMinZoom:function(m){const v=q(l.panZoom);v&&(v.setScaleExtent([m,q(l.maxZoom)]),l.minZoom.set(m))},setMaxZoom:function(m){const v=q(l.panZoom);v&&(v.setScaleExtent([q(l.minZoom),m]),l.maxZoom.set(m))},setTranslateExtent:function(m){const v=q(l.panZoom);v&&(v.setTranslateExtent(m),l.translateExtent.set(m))},setPaneClickDistance:function(m){var v;(v=q(l.panZoom))==null||v.setClickDistance(m)},unselectNodesAndEdges:c,addSelectedNodes:d,addSelectedEdges:function(m){const v=q(l.multiselectionKeyPressed);l.edges.update(y=>y.map(w=>{const h=m.includes(w.id),C=v&&w.selected||h;return w.selected=C,w})),v||l.nodes.update(y=>y.map(w=>(w.selected=!1,w)))},handleNodeSelection:function(m){var v;const y=(v=q(l.nodes))==null?void 0:v.find(w=>w.id===m);y&&(l.selectionRect.set(null),l.selectionRectMode.set(null),y.selected?y.selected&&q(l.multiselectionKeyPressed)&&c({nodes:[y],edges:[]}):d([m]))},panBy:function(m){const v=q(l.viewport);return async function({delta:y,panZoom:w,transform:h,translateExtent:C,width:b,height:k}){if(!w||!y.x&&!y.y)return Promise.resolve(!1);const _=await w.setViewportConstrained({x:h[0]+y.x,y:h[1]+y.y,zoom:h[2]},[[0,0],[b,k]],C),S=!!_&&(_.x!==h[0]||_.y!==h[1]||_.k!==h[2]);return Promise.resolve(S)}({delta:m,panZoom:q(l.panZoom),transform:[v.x,v.y,v.zoom],translateExtent:q(l.translateExtent),width:q(l.width),height:q(l.height)})},updateConnection:m=>{f.set({...m})},cancelConnection:g,reset:function(){l.fitViewOnInitDone.set(!1),l.selectionRect.set(null),l.selectionRectMode.set(null),l.snapGrid.set(null),l.isValidConnection.set(()=>!0),c(),g()}}}function Ke(){const e=uo(Ai);if(!e)throw new Error("In order to use useStore you need to wrap your component in a <SvelteFlowProvider />");return e.getStore()}function lc(e,t){const{panZoom:n,minZoom:o,maxZoom:r,initialViewport:i,viewport:a,dragging:l,translateExtent:u,paneClickDistance:s}=t,c=Df({domNode:e,minZoom:o,maxZoom:r,translateExtent:u,viewport:i,paneClickDistance:s,onDraggingChange:l.set}),d=c.getViewport();return a.set(d),n.set(c),c.update(t),{update(f){c.update(f)}}}var Ff=ae('<div class="svelte-flow__zoom svelte-4xkw84"><!></div>');const Gf={hash:"svelte-4xkw84",code:".svelte-flow__zoom.svelte-4xkw84 {width:100%;height:100%;position:absolute;top:0;left:0;z-index:4;}"};function sc(e,t){ve(t,!1),qe(e,Gf);const[n,o]=Je(),r=()=>ie(M,"$panActivationKeyPressed",n),i=se(),a=se(),l=se();let u=$(t,"initialViewport",12,void 0),s=$(t,"onMoveStart",12,void 0),c=$(t,"onMove",12,void 0),d=$(t,"onMoveEnd",12,void 0),f=$(t,"panOnScrollMode",12),g=$(t,"preventScrolling",12),m=$(t,"zoomOnScroll",12),v=$(t,"zoomOnDoubleClick",12),y=$(t,"zoomOnPinch",12),w=$(t,"panOnDrag",12),h=$(t,"panOnScroll",12),C=$(t,"paneClickDistance",12);const{viewport:b,panZoom:k,selectionRect:_,minZoom:S,maxZoom:E,dragging:L,translateExtent:P,lib:D,panActivationKeyPressed:M,zoomActivationKeyPressed:H,viewportInitialized:O}=Ke(),V=A=>b.set({x:A[0],y:A[1],zoom:A[2]});tn(()=>{ai(O,!0)}),he(()=>oe(u()),()=>{te(i,u()||{x:0,y:0,zoom:1})}),he(()=>(r(),oe(w())),()=>{te(a,r()||w())}),he(()=>(r(),oe(h())),()=>{te(l,r()||h())}),ut(),Le();var z=Ff();ft(W(z),t,"default",{}),K(z),vt(z,(A,X)=>lc==null?void 0:lc(A,X),()=>({viewport:b,minZoom:ie(S,"$minZoom",n),maxZoom:ie(E,"$maxZoom",n),initialViewport:p(i),dragging:L,panZoom:k,onPanZoomStart:s(),onPanZoom:c(),onPanZoomEnd:d(),zoomOnScroll:m(),zoomOnDoubleClick:v(),zoomOnPinch:y(),panOnScroll:p(l),panOnDrag:p(a),panOnScrollSpeed:.5,panOnScrollMode:f()||Jn.Free,zoomActivationKeyPressed:ie(H,"$zoomActivationKeyPressed",n),preventScrolling:typeof g()!="boolean"||g(),noPanClassName:"nopan",noWheelClassName:"nowheel",userSelectionActive:!!ie(_,"$selectionRect",n),translateExtent:ie(P,"$translateExtent",n),lib:ie(D,"$lib",n),paneClickDistance:C(),onTransformChange:V})),T(e,z);var N=ge({get initialViewport(){return u()},set initialViewport(A){u(A),x()},get onMoveStart(){return s()},set onMoveStart(A){s(A),x()},get onMove(){return c()},set onMove(A){c(A),x()},get onMoveEnd(){return d()},set onMoveEnd(A){d(A),x()},get panOnScrollMode(){return f()},set panOnScrollMode(A){f(A),x()},get preventScrolling(){return g()},set preventScrolling(A){g(A),x()},get zoomOnScroll(){return m()},set zoomOnScroll(A){m(A),x()},get zoomOnDoubleClick(){return v()},set zoomOnDoubleClick(A){v(A),x()},get zoomOnPinch(){return y()},set zoomOnPinch(A){y(A),x()},get panOnDrag(){return w()},set panOnDrag(A){w(A),x()},get panOnScroll(){return h()},set panOnScroll(A){h(A),x()},get paneClickDistance(){return C()},set paneClickDistance(A){C(A),x()}});return o(),N}function uc(e,t){return n=>{n.target===t&&(e==null||e(n))}}function cc(e){return t=>{const n=e.includes(t.id);return t.selected!==n&&(t.selected=n),t}}ce(sc,{initialViewport:{},onMoveStart:{},onMove:{},onMoveEnd:{},panOnScrollMode:{},preventScrolling:{},zoomOnScroll:{},zoomOnDoubleClick:{},zoomOnPinch:{},panOnDrag:{},panOnScroll:{},paneClickDistance:{}},["default"],[],!0);var Uf=ae("<div><!></div>");const Jf={hash:"svelte-1esy7hx",code:".svelte-flow__pane.svelte-1esy7hx {position:absolute;top:0;left:0;width:100%;height:100%;}"};function dc(e,t){ve(t,!1),qe(e,Jf);const[n,o]=Je(),r=()=>ie(D,"$panActivationKeyPressed",n),i=()=>ie(L,"$selectionKeyPressed",n),a=()=>ie(S,"$selectionRect",n),l=()=>ie(_,"$elementsSelectable",n),u=()=>ie(E,"$selectionRectMode",n),s=()=>ie(C,"$edges",n),c=()=>ie(b,"$viewport",n),d=se(),f=se(),g=se();let m=$(t,"panOnDrag",12,void 0),v=$(t,"selectionOnDrag",12,void 0);const y=ri(),{nodes:w,nodeLookup:h,edges:C,viewport:b,dragging:k,elementsSelectable:_,selectionRect:S,selectionRectMode:E,selectionKeyPressed:L,selectionMode:P,panActivationKeyPressed:D,unselectNodesAndEdges:M}=Ke();let H=se(),O=null,V=[],z=!1;function N(F){z?z=!1:(y("paneclick",{event:F}),M(),E.set(null))}function A(F){var I,j;if(O=p(H).getBoundingClientRect(),!_||!p(f)||F.button!==0||F.target!==p(H)||!O)return;(j=(I=F.target)==null?void 0:I.setPointerCapture)==null||j.call(I,F.pointerId);const{x:G,y:le}=On(F,O);M(),S.set({width:0,height:0,startX:G,startY:le,x:G,y:le})}function X(F){if(!p(f)||!O||!a())return;z=!0;const I=On(F,O),j=a().startX??0,G=a().startY??0,le={...a(),x:I.x<j?I.x:j,y:I.y<G?I.y:G,width:Math.abs(I.x-j),height:Math.abs(I.y-G)},we=V.map(Y=>Y.id),Pe=Ra(V,s()).map(Y=>Y.id);V=gu(ie(h,"$nodeLookup",n),le,[c().x,c().y,c().zoom],ie(P,"$selectionMode",n)===Ei.Partial,!0);const Ne=Ra(V,s()).map(Y=>Y.id),ue=V.map(Y=>Y.id);(we.length!==ue.length||ue.some(Y=>!we.includes(Y)))&&w.update(Y=>Y.map(cc(ue))),(Pe.length!==Ne.length||Ne.some(Y=>!Pe.includes(Y)))&&C.update(Y=>Y.map(cc(Ne))),E.set("user"),S.set(le)}function B(F){var I,j;F.button===0&&((j=(I=F.target)==null?void 0:I.releasePointerCapture)==null||j.call(I,F.pointerId),!p(f)&&u()==="user"&&F.target===p(H)&&(N==null||N(F)),S.set(null),V.length>0&&ai(E,"nodes"),i()&&(z=!1))}const Q=F=>{var I;Array.isArray(p(d))&&(I=p(d))!=null&&I.includes(2)?F.preventDefault():y("panecontextmenu",{event:F})};he(()=>(r(),oe(m())),()=>{te(d,r()||m())}),he(()=>(i(),a(),oe(v()),p(d)),()=>{te(f,i()||a()||v()&&p(d)!==!0)}),he(()=>(l(),p(f),u()),()=>{te(g,l()&&(p(f)||u()==="user"))}),ut(),Le();var ne=Uf(),ee=ze(()=>p(g)?void 0:uc(N,p(H))),me=ze(()=>uc(Q,p(H)));let de;ft(W(ne),t,"default",{}),K(ne),Vn(ne,F=>te(H,F),()=>p(H)),Me(F=>de=bt(ne,1,"svelte-flow__pane svelte-1esy7hx",null,de,{draggable:F,dragging:ie(k,"$dragging",n),selection:p(f)}),[()=>m()===!0||Array.isArray(m())&&m().includes(0)],ye),Ze("click",ne,function(...F){var I;(I=p(ee))==null||I.apply(this,F)}),Ze("pointerdown",ne,function(...F){var I;(I=p(g)?A:void 0)==null||I.apply(this,F)}),Ze("pointermove",ne,function(...F){var I;(I=p(g)?X:void 0)==null||I.apply(this,F)}),Ze("pointerup",ne,function(...F){var I;(I=p(g)?B:void 0)==null||I.apply(this,F)}),Ze("contextmenu",ne,function(...F){var I;(I=p(me))==null||I.apply(this,F)}),T(e,ne);var J=ge({get panOnDrag(){return m()},set panOnDrag(F){m(F),x()},get selectionOnDrag(){return v()},set selectionOnDrag(F){v(F),x()}});return o(),J}ce(dc,{panOnDrag:{},selectionOnDrag:{}},["default"],[],!0);var Qf=ae('<div class="svelte-flow__viewport xyflow__viewport svelte-1floaup"><!></div>');const ev={hash:"svelte-1floaup",code:".svelte-flow__viewport.svelte-1floaup {width:100%;height:100%;position:absolute;top:0;left:0;}"};function fc(e,t){ve(t,!1),qe(e,ev);const[n,o]=Je(),r=()=>ie(i,"$viewport",n),{viewport:i}=Ke();Le();var a=Qf();ft(W(a),t,"default",{}),K(a),Me(()=>pe(a,"style",`transform: translate(${r().x??""}px, ${r().y??""}px) scale(${r().zoom??""})`)),T(e,a),ge(),o()}function Ii(e,t){const{store:n,onDrag:o,onDragStart:r,onDragStop:i,onNodeMouseDown:a}=t,l=Nf({onDrag:o,onDragStart:r,onDragStop:i,onNodeMouseDown:a,getStoreItems:()=>{const s=q(n.snapGrid),c=q(n.viewport);return{nodes:q(n.nodes),nodeLookup:q(n.nodeLookup),edges:q(n.edges),nodeExtent:q(n.nodeExtent),snapGrid:s||[0,0],snapToGrid:!!s,nodeOrigin:q(n.nodeOrigin),multiSelectionActive:q(n.multiselectionKeyPressed),domNode:q(n.domNode),transform:[c.x,c.y,c.zoom],autoPanOnNodeDrag:q(n.autoPanOnNodeDrag),nodesDraggable:q(n.nodesDraggable),selectNodesOnDrag:q(n.selectNodesOnDrag),nodeDragThreshold:q(n.nodeDragThreshold),unselectNodesAndEdges:n.unselectNodesAndEdges,updateNodePositions:n.updateNodePositions,panBy:n.panBy}}});function u(s,c){c.disabled?l.destroy():l.update({domNode:s,noDragClassName:c.noDragClass,handleSelector:c.handleSelector,nodeId:c.nodeId,isSelectable:c.isSelectable,nodeClickDistance:c.nodeClickDistance})}return u(e,t),{update(s){u(e,s)},destroy(){l.destroy()}}}ce(fc,{},["default"],[],!0);var tv=ae("<div><!></div>");function vc(e,t){ve(t,!1);const[n,o]=Je(),r=()=>ie(me,"$nodeTypes",n),i=se(void 0,!0),a=se(void 0,!0),l=se(void 0,!0),u=se(void 0,!0);let s=$(t,"node",13),c=$(t,"id",13),d=$(t,"data",29,()=>({})),f=$(t,"selected",13,!1),g=$(t,"draggable",13,void 0),m=$(t,"selectable",13,void 0),v=$(t,"connectable",13,!0),y=$(t,"deletable",13,!0),w=$(t,"hidden",13,!1),h=$(t,"dragging",13,!1),C=$(t,"resizeObserver",13,null),b=$(t,"style",13,void 0),k=$(t,"type",13,"default"),_=$(t,"isParent",13,!1),S=$(t,"positionX",13),E=$(t,"positionY",13),L=$(t,"sourcePosition",13,void 0),P=$(t,"targetPosition",13,void 0),D=$(t,"zIndex",13),M=$(t,"measuredWidth",13,void 0),H=$(t,"measuredHeight",13,void 0),O=$(t,"initialWidth",13,void 0),V=$(t,"initialHeight",13,void 0),z=$(t,"width",13,void 0),N=$(t,"height",13,void 0),A=$(t,"dragHandle",13,void 0),X=$(t,"initialized",13,!1),B=$(t,"parentId",13,void 0),Q=$(t,"nodeClickDistance",13,void 0),ne=$(t,"class",13,"");const ee=Ke(),{nodeTypes:me,nodeDragThreshold:de,selectNodesOnDrag:J,handleNodeSelection:F,updateNodeInternals:I,elementsSelectable:j,nodesDraggable:G}=ee;let le=se(void 0,!0),we=se(null,!0);const Pe=ri(),Ne=be(v());let ue=se(void 0,!0),Y=se(void 0,!0),$e=se(void 0,!0);function Ce(U){m()&&(!q(J)||!g()||q(de)>0)&&F(c()),Pe("nodeclick",{node:s().internals.userNode,event:U})}Ho("svelteflow__node_id",c()),Ho("svelteflow__node_connectable",Ne),$a(()=>{var U;p(we)&&((U=C())==null||U.unobserve(p(we)))}),he(()=>oe(k()),()=>{te(i,k()||"default")}),he(()=>(r(),p(i)),()=>{te(a,!!r()[p(i)])}),he(()=>(r(),p(i),Oi),()=>{te(l,r()[p(i)]||Oi)}),he(()=>(p(a),oe(k())),()=>{p(a)}),he(()=>(oe(z()),oe(N()),oe(O()),oe(V()),oe(M()),oe(H())),()=>{te(u,function({width:U,height:re,initialWidth:Qe,initialHeight:Fe,measuredWidth:tt,measuredHeight:dn}){if(tt===void 0&&dn===void 0){const zt=U??Qe,ot=re??Fe;return{width:zt?`width:${zt}px;`:"",height:ot?`height:${ot}px;`:""}}return{width:U?`width:${U}px;`:"",height:re?`height:${re}px;`:""}}({width:z(),height:N(),initialWidth:O(),initialHeight:V(),measuredWidth:M(),measuredHeight:H()}))}),he(()=>oe(v()),()=>{Ne.set(!!v())}),he(()=>(p(ue),p(i),p(Y),oe(L()),p($e),oe(P()),oe(c()),p(le)),()=>{(p(ue)&&p(i)!==p(ue)||p(Y)&&L()!==p(Y)||p($e)&&P()!==p($e))&&requestAnimationFrame(()=>I(new Map([[c(),{id:c(),nodeElement:p(le),force:!0}]]))),te(ue,p(i)),te(Y,L()),te($e,P())}),he(()=>(oe(C()),p(le),p(we),oe(X())),()=>{C()&&(p(le)!==p(we)||!X())&&(p(we)&&C().unobserve(p(we)),p(le)&&C().observe(p(le)),te(we,p(le)))}),ut(),Le(!0);var Se=Ue(),Ae=ke(Se),Ge=U=>{var re=tv();let Qe;var Fe=W(re);const tt=ye(()=>f()??!1),dn=ye(()=>m()??ie(j,"$elementsSelectable",n)??!0),zt=ye(()=>y()??!0),ot=ye(()=>g()??ie(G,"$nodesDraggable",n)??!0);ds(Fe,()=>p(l),(Re,dt)=>{dt(Re,{get data(){return d()},get id(){return c()},get selected(){return p(tt)},get selectable(){return p(dn)},get deletable(){return p(zt)},get sourcePosition(){return L()},get targetPosition(){return P()},get zIndex(){return D()},get dragging(){return h()},get draggable(){return p(ot)},get dragHandle(){return A()},get parentId(){return B()},get type(){return p(i)},get isConnectable(){return ie(Ne,"$connectableStore",n)},get positionAbsoluteX(){return S()},get positionAbsoluteY(){return E()},get width(){return z()},get height(){return N()}})}),K(re),vt(re,(Re,dt)=>Ii==null?void 0:Ii(Re,dt),()=>({nodeId:c(),isSelectable:m(),disabled:!1,handleSelector:A(),noDragClass:"nodrag",nodeClickDistance:Q(),onNodeMouseDown:F,onDrag:(Re,dt,Rt,Xt)=>{Pe("nodedrag",{event:Re,targetNode:Rt,nodes:Xt})},onDragStart:(Re,dt,Rt,Xt)=>{Pe("nodedragstart",{event:Re,targetNode:Rt,nodes:Xt})},onDragStop:(Re,dt,Rt,Xt)=>{Pe("nodedragstop",{event:Re,targetNode:Rt,nodes:Xt})},store:ee})),Vn(re,Re=>te(le,Re),()=>p(le)),Dt(()=>Ze("click",re,Ce)),Dt(()=>Ze("mouseenter",re,Re=>Pe("nodemouseenter",{node:s(),event:Re}))),Dt(()=>Ze("mouseleave",re,Re=>Pe("nodemouseleave",{node:s(),event:Re}))),Dt(()=>Ze("mousemove",re,Re=>Pe("nodemousemove",{node:s(),event:Re}))),Dt(()=>Ze("contextmenu",re,Re=>Pe("nodecontextmenu",{node:s(),event:Re}))),Me(Re=>{pe(re,"data-id",c()),Qe=bt(re,1,mn(Re),null,Qe,{dragging:h(),selected:f(),draggable:g(),connectable:v(),selectable:m(),nopan:g(),parent:_()}),pe(re,"style",`${b()??""};${p(u).width??""}${p(u).height??""}`),nt(re,"z-index",D()),nt(re,"transform",`translate(${S()??""}px, ${E()??""}px)`),nt(re,"visibility",X()?"visible":"hidden")},[()=>xt(["svelte-flow__node",`svelte-flow__node-${p(i)}`,ne()])],ye),T(U,re)};_e(Ae,U=>{w()||U(Ge)}),T(e,Se);var fe=ge({get node(){return s()},set node(U){s(U),x()},get id(){return c()},set id(U){c(U),x()},get data(){return d()},set data(U){d(U),x()},get selected(){return f()},set selected(U){f(U),x()},get draggable(){return g()},set draggable(U){g(U),x()},get selectable(){return m()},set selectable(U){m(U),x()},get connectable(){return v()},set connectable(U){v(U),x()},get deletable(){return y()},set deletable(U){y(U),x()},get hidden(){return w()},set hidden(U){w(U),x()},get dragging(){return h()},set dragging(U){h(U),x()},get resizeObserver(){return C()},set resizeObserver(U){C(U),x()},get style(){return b()},set style(U){b(U),x()},get type(){return k()},set type(U){k(U),x()},get isParent(){return _()},set isParent(U){_(U),x()},get positionX(){return S()},set positionX(U){S(U),x()},get positionY(){return E()},set positionY(U){E(U),x()},get sourcePosition(){return L()},set sourcePosition(U){L(U),x()},get targetPosition(){return P()},set targetPosition(U){P(U),x()},get zIndex(){return D()},set zIndex(U){D(U),x()},get measuredWidth(){return M()},set measuredWidth(U){M(U),x()},get measuredHeight(){return H()},set measuredHeight(U){H(U),x()},get initialWidth(){return O()},set initialWidth(U){O(U),x()},get initialHeight(){return V()},set initialHeight(U){V(U),x()},get width(){return z()},set width(U){z(U),x()},get height(){return N()},set height(U){N(U),x()},get dragHandle(){return A()},set dragHandle(U){A(U),x()},get initialized(){return X()},set initialized(U){X(U),x()},get parentId(){return B()},set parentId(U){B(U),x()},get nodeClickDistance(){return Q()},set nodeClickDistance(U){Q(U),x()},get class(){return ne()},set class(U){ne(U),x()}});return o(),fe}ce(vc,{node:{},id:{},data:{},selected:{},draggable:{},selectable:{},connectable:{},deletable:{},hidden:{},dragging:{},resizeObserver:{},style:{},type:{},isParent:{},positionX:{},positionY:{},sourcePosition:{},targetPosition:{},zIndex:{},measuredWidth:{},measuredHeight:{},initialWidth:{},initialHeight:{},width:{},height:{},dragHandle:{},initialized:{},parentId:{},nodeClickDistance:{},class:{}},[],[],!0);var nv=ae('<div class="svelte-flow__nodes svelte-tf4uy4"></div>');const ov={hash:"svelte-tf4uy4",code:".svelte-flow__nodes.svelte-tf4uy4 {width:100%;height:100%;position:absolute;left:0;top:0;}"};function gc(e,t){ve(t,!1),qe(e,ov);const[n,o]=Je();let r=$(t,"nodeClickDistance",12,0);const{visibleNodes:i,nodesDraggable:a,nodesConnectable:l,elementsSelectable:u,updateNodeInternals:s,parentLookup:c}=Ke(),d=typeof ResizeObserver>"u"?null:new ResizeObserver(m=>{const v=new Map;m.forEach(y=>{const w=y.target.getAttribute("data-id");v.set(w,{id:w,nodeElement:y.target,force:!0})}),s(v)});$a(()=>{d==null||d.disconnect()}),Le();var f=nv();At(f,5,()=>ie(i,"$visibleNodes",n),m=>m.id,(m,v)=>{const y=ye(()=>!!p(v).selected),w=ye(()=>!!p(v).hidden),h=ye(()=>!!(p(v).draggable||ie(a,"$nodesDraggable",n)&&typeof p(v).draggable>"u")),C=ye(()=>!!(p(v).selectable||ie(u,"$elementsSelectable",n)&&typeof p(v).selectable>"u")),b=ye(()=>!!(p(v).connectable||ie(l,"$nodesConnectable",n)&&typeof p(v).connectable>"u")),k=ye(()=>p(v).deletable??!0),_=ye(()=>ie(c,"$parentLookup",n).has(p(v).id)),S=ye(()=>p(v).type??"default"),E=ye(()=>p(v).internals.z??0),L=ye(()=>ku(p(v)));vc(m,{get node(){return p(v)},get id(){return p(v).id},get data(){return p(v).data},get selected(){return p(y)},get hidden(){return p(w)},get draggable(){return p(h)},get selectable(){return p(C)},get connectable(){return p(b)},get deletable(){return p(k)},get positionX(){return p(v).internals.positionAbsolute.x},get positionY(){return p(v).internals.positionAbsolute.y},get isParent(){return p(_)},get style(){return p(v).style},get class(){return p(v).class},get type(){return p(S)},get sourcePosition(){return p(v).sourcePosition},get targetPosition(){return p(v).targetPosition},get dragging(){return p(v).dragging},get zIndex(){return p(E)},get dragHandle(){return p(v).dragHandle},get initialized(){return p(L)},get width(){return p(v).width},get height(){return p(v).height},get initialWidth(){return p(v).initialWidth},get initialHeight(){return p(v).initialHeight},get measuredWidth(){return p(v).measured.width},get measuredHeight(){return p(v).measured.height},get parentId(){return p(v).parentId},resizeObserver:d,get nodeClickDistance(){return r()},$$events:{nodeclick(P){De.call(this,t,P)},nodemouseenter(P){De.call(this,t,P)},nodemousemove(P){De.call(this,t,P)},nodemouseleave(P){De.call(this,t,P)},nodedrag(P){De.call(this,t,P)},nodedragstart(P){De.call(this,t,P)},nodedragstop(P){De.call(this,t,P)},nodecontextmenu(P){De.call(this,t,P)}}})}),K(f),T(e,f);var g=ge({get nodeClickDistance(){return r()},set nodeClickDistance(m){r(m),x()}});return o(),g}ce(gc,{nodeClickDistance:{}},[],[],!0);var rv=xe('<svg><g role="img"><!></g></svg>');function pc(e,t){ve(t,!1);const[n,o]=Je(),r=()=>ie(de,"$edgeTypes",n),i=()=>ie(J,"$flowId",n),a=()=>ie(F,"$elementsSelectable",n),l=()=>ie(me,"$edgeLookup",n),u=se(void 0,!0),s=se(void 0,!0),c=se(void 0,!0),d=se(void 0,!0),f=se(void 0,!0);let g=$(t,"id",13),m=$(t,"type",13,"default"),v=$(t,"source",13,""),y=$(t,"target",13,""),w=$(t,"data",29,()=>({})),h=$(t,"style",13,void 0),C=$(t,"zIndex",13,void 0),b=$(t,"animated",13,!1),k=$(t,"selected",13,!1),_=$(t,"selectable",13,void 0),S=$(t,"deletable",13,void 0),E=$(t,"hidden",13,!1),L=$(t,"label",13,void 0),P=$(t,"labelStyle",13,void 0),D=$(t,"markerStart",13,void 0),M=$(t,"markerEnd",13,void 0),H=$(t,"sourceHandle",13,void 0),O=$(t,"targetHandle",13,void 0),V=$(t,"sourceX",13),z=$(t,"sourceY",13),N=$(t,"targetX",13),A=$(t,"targetY",13),X=$(t,"sourcePosition",13),B=$(t,"targetPosition",13),Q=$(t,"ariaLabel",13,void 0),ne=$(t,"interactionWidth",13,void 0),ee=$(t,"class",13,"");Ho("svelteflow__edge_id",g());const{edgeLookup:me,edgeTypes:de,flowId:J,elementsSelectable:F}=Ke(),I=ri(),j=Qu();function G(Y){const $e=l().get(g());$e&&(j(g()),I("edgeclick",{event:Y,edge:$e}))}function le(Y,$e){const Ce=l().get(g());Ce&&I($e,{event:Y,edge:Ce})}he(()=>oe(m()),()=>{te(u,m()||"default")}),he(()=>(r(),p(u),Ti),()=>{te(s,r()[p(u)]||Ti)}),he(()=>(oe(D()),i()),()=>{te(c,D()?`url('#${Fa(D(),i())}')`:void 0)}),he(()=>(oe(M()),i()),()=>{te(d,M()?`url('#${Fa(M(),i())}')`:void 0)}),he(()=>(oe(_()),a()),()=>{te(f,_()??a())}),ut(),Le(!0);var we=Ue(),Pe=ke(we),Ne=Y=>{var $e=rv(),Ce=W($e);let Se;var Ae=W(Ce);const Ge=ye(()=>S()??!0);ds(Ae,()=>p(s),(fe,U)=>{U(fe,{get id(){return g()},get source(){return v()},get target(){return y()},get sourceX(){return V()},get sourceY(){return z()},get targetX(){return N()},get targetY(){return A()},get sourcePosition(){return X()},get targetPosition(){return B()},get animated(){return b()},get selected(){return k()},get label(){return L()},get labelStyle(){return P()},get data(){return w()},get style(){return h()},get interactionWidth(){return ne()},get selectable(){return p(f)},get deletable(){return p(Ge)},get type(){return p(u)},get sourceHandleId(){return H()},get targetHandleId(){return O()},get markerStart(){return p(c)},get markerEnd(){return p(d)}})}),K(Ce),K($e),Me(fe=>{nt($e,"z-index",C()),Se=bt(Ce,0,mn(fe),null,Se,{animated:b(),selected:k(),selectable:p(f)}),pe(Ce,"data-id",g()),pe(Ce,"aria-label",Q()===null?void 0:Q()?Q():`Edge from ${v()} to ${y()}`)},[()=>xt(["svelte-flow__edge",ee()])],ye),Ze("click",Ce,G),Ze("contextmenu",Ce,fe=>{le(fe,"edgecontextmenu")}),Ze("mouseenter",Ce,fe=>{le(fe,"edgemouseenter")}),Ze("mouseleave",Ce,fe=>{le(fe,"edgemouseleave")}),T(Y,$e)};_e(Pe,Y=>{E()||Y(Ne)}),T(e,we);var ue=ge({get id(){return g()},set id(Y){g(Y),x()},get type(){return m()},set type(Y){m(Y),x()},get source(){return v()},set source(Y){v(Y),x()},get target(){return y()},set target(Y){y(Y),x()},get data(){return w()},set data(Y){w(Y),x()},get style(){return h()},set style(Y){h(Y),x()},get zIndex(){return C()},set zIndex(Y){C(Y),x()},get animated(){return b()},set animated(Y){b(Y),x()},get selected(){return k()},set selected(Y){k(Y),x()},get selectable(){return _()},set selectable(Y){_(Y),x()},get deletable(){return S()},set deletable(Y){S(Y),x()},get hidden(){return E()},set hidden(Y){E(Y),x()},get label(){return L()},set label(Y){L(Y),x()},get labelStyle(){return P()},set labelStyle(Y){P(Y),x()},get markerStart(){return D()},set markerStart(Y){D(Y),x()},get markerEnd(){return M()},set markerEnd(Y){M(Y),x()},get sourceHandle(){return H()},set sourceHandle(Y){H(Y),x()},get targetHandle(){return O()},set targetHandle(Y){O(Y),x()},get sourceX(){return V()},set sourceX(Y){V(Y),x()},get sourceY(){return z()},set sourceY(Y){z(Y),x()},get targetX(){return N()},set targetX(Y){N(Y),x()},get targetY(){return A()},set targetY(Y){A(Y),x()},get sourcePosition(){return X()},set sourcePosition(Y){X(Y),x()},get targetPosition(){return B()},set targetPosition(Y){B(Y),x()},get ariaLabel(){return Q()},set ariaLabel(Y){Q(Y),x()},get interactionWidth(){return ne()},set interactionWidth(Y){ne(Y),x()},get class(){return ee()},set class(Y){ee(Y),x()}});return o(),ue}function hc(e,t){ve(t,!1);let n=$(t,"onMount",12,void 0),o=$(t,"onDestroy",12,void 0);return tn(()=>{var r;return(r=n())==null||r(),o()}),Le(),ge({get onMount(){return n()},set onMount(r){n(r),x()},get onDestroy(){return o()},set onDestroy(r){o(r),x()}})}ce(pc,{id:{},type:{},source:{},target:{},data:{},style:{},zIndex:{},animated:{},selected:{},selectable:{},deletable:{},hidden:{},label:{},labelStyle:{},markerStart:{},markerEnd:{},sourceHandle:{},targetHandle:{},sourceX:{},sourceY:{},targetX:{},targetY:{},sourcePosition:{},targetPosition:{},ariaLabel:{},interactionWidth:{},class:{}},[],[],!0),ce(hc,{onMount:{},onDestroy:{}},[],[],!0);var iv=xe("<defs></defs>");function mc(e,t){ve(t,!1);const[n,o]=Je(),{markers:r}=Ke();Le();var i=iv();At(i,5,()=>ie(r,"$markers",n),a=>a.id,(a,l)=>{yc(a,lt(()=>p(l)))}),K(i),T(e,i),ge(),o()}ce(mc,{},[],[],!0);var av=xe('<polyline stroke-linecap="round" stroke-linejoin="round" fill="none" points="-5,-4 0,0 -5,4"></polyline>'),lv=xe('<polyline stroke-linecap="round" stroke-linejoin="round" points="-5,-4 0,0 -5,4 -5,-4"></polyline>'),sv=xe('<marker class="svelte-flow__arrowhead" viewBox="-10 -10 20 20" refX="0" refY="0"><!></marker>');function yc(e,t){ve(t,!1);let n=$(t,"id",12),o=$(t,"type",12),r=$(t,"width",12,12.5),i=$(t,"height",12,12.5),a=$(t,"markerUnits",12,"strokeWidth"),l=$(t,"orient",12,"auto-start-reverse"),u=$(t,"color",12,void 0),s=$(t,"strokeWidth",12,void 0);Le();var c=sv(),d=W(c),f=m=>{var v=av();Me(()=>{pe(v,"stroke",u()),pe(v,"stroke-width",s())}),T(m,v)},g=(m,v)=>{var y=w=>{var h=lv();Me(()=>{pe(h,"stroke",u()),pe(h,"stroke-width",s()),pe(h,"fill",u())}),T(w,h)};_e(m,w=>{o()===$r.ArrowClosed&&w(y)},v)};return _e(d,m=>{o()===$r.Arrow?m(f):m(g,!1)}),K(c),Me(()=>{pe(c,"id",n()),pe(c,"markerWidth",`${r()}`),pe(c,"markerHeight",`${i()}`),pe(c,"markerUnits",a()),pe(c,"orient",l())}),T(e,c),ge({get id(){return n()},set id(m){n(m),x()},get type(){return o()},set type(m){o(m),x()},get width(){return r()},set width(m){r(m),x()},get height(){return i()},set height(m){i(m),x()},get markerUnits(){return a()},set markerUnits(m){a(m),x()},get orient(){return l()},set orient(m){l(m),x()},get color(){return u()},set color(m){u(m),x()},get strokeWidth(){return s()},set strokeWidth(m){s(m),x()}})}ce(yc,{id:{},type:{},width:{},height:{},markerUnits:{},orient:{},color:{},strokeWidth:{}},[],[],!0);var uv=ae('<div class="svelte-flow__edges"><svg class="svelte-flow__marker"><!></svg> <!> <!></div>');function wc(e,t){ve(t,!1);const[n,o]=Je(),r=()=>ie(a,"$visibleEdges",n);let i=$(t,"defaultEdgeOptions",12);const{visibleEdges:a,edgesInitialized:l,edges:{setDefaultOptions:u},elementsSelectable:s}=Ke();tn(()=>{i()&&u(i())}),Le();var c=uv(),d=W(c);mc(W(d),{}),K(d);var f=Z(d,2);At(f,1,r,y=>y.id,(y,w)=>{const h=ye(()=>p(w).selectable??ie(s,"$elementsSelectable",n)),C=ye(()=>p(w).type||"default");pc(y,{get id(){return p(w).id},get source(){return p(w).source},get target(){return p(w).target},get data(){return p(w).data},get style(){return p(w).style},get animated(){return p(w).animated},get selected(){return p(w).selected},get selectable(){return p(h)},get deletable(){return p(w).deletable},get hidden(){return p(w).hidden},get label(){return p(w).label},get labelStyle(){return p(w).labelStyle},get markerStart(){return p(w).markerStart},get markerEnd(){return p(w).markerEnd},get sourceHandle(){return p(w).sourceHandle},get targetHandle(){return p(w).targetHandle},get sourceX(){return p(w).sourceX},get sourceY(){return p(w).sourceY},get targetX(){return p(w).targetX},get targetY(){return p(w).targetY},get sourcePosition(){return p(w).sourcePosition},get targetPosition(){return p(w).targetPosition},get ariaLabel(){return p(w).ariaLabel},get interactionWidth(){return p(w).interactionWidth},get class(){return p(w).class},get type(){return p(C)},get zIndex(){return p(w).zIndex},$$events:{edgeclick(b){De.call(this,t,b)},edgecontextmenu(b){De.call(this,t,b)},edgemouseenter(b){De.call(this,t,b)},edgemouseleave(b){De.call(this,t,b)}}})});var g=Z(f,2),m=y=>{hc(0,{onMount:()=>{ai(l,!0)},onDestroy:()=>{ai(l,!1)}})};_e(g,y=>{r().length>0&&y(m)}),K(c),T(e,c);var v=ge({get defaultEdgeOptions(){return i()},set defaultEdgeOptions(y){i(y),x()}});return o(),v}ce(wc,{defaultEdgeOptions:{}},[],[],!0);var cv=ae('<div class="svelte-flow__selection svelte-1iugwpu"></div>');const dv={hash:"svelte-1iugwpu",code:".svelte-flow__selection.svelte-1iugwpu {position:absolute;top:0;left:0;}"};function nl(e,t){ve(t,!1),qe(e,dv);let n=$(t,"x",12,0),o=$(t,"y",12,0),r=$(t,"width",12,0),i=$(t,"height",12,0),a=$(t,"isVisible",12,!0);var l=Ue(),u=ke(l),s=c=>{var d=cv();Me(()=>{nt(d,"width",typeof r()=="string"?r():`${r()}px`),nt(d,"height",typeof i()=="string"?i():`${i()}px`),nt(d,"transform",`translate(${n()}px, ${o()}px)`)}),T(c,d)};return _e(u,c=>{a()&&c(s)}),T(e,l),ge({get x(){return n()},set x(c){n(c),x()},get y(){return o()},set y(c){o(c),x()},get width(){return r()},set width(c){r(c),x()},get height(){return i()},set height(c){i(c),x()},get isVisible(){return a()},set isVisible(c){a(c),x()}})}function bc(e,t){ve(t,!1);const[n,o]=Je(),r=()=>ie(i,"$selectionRect",n),{selectionRect:i,selectionRectMode:a}=Ke();Le();const l=ye(()=>!(!r()||ie(a,"$selectionRectMode",n)!=="user")),u=ye(()=>{var f;return(f=r())==null?void 0:f.width}),s=ye(()=>{var f;return(f=r())==null?void 0:f.height}),c=ye(()=>{var f;return(f=r())==null?void 0:f.x}),d=ye(()=>{var f;return(f=r())==null?void 0:f.y});nl(e,{get isVisible(){return p(l)},get width(){return p(u)},get height(){return p(s)},get x(){return p(c)},get y(){return p(d)}}),ge(),o()}ce(nl,{x:{},y:{},width:{},height:{},isVisible:{}},[],[],!0),ce(bc,{},[],[],!0);var fv=ae('<div class="selection-wrapper nopan svelte-5pxri" role="button" tabindex="-1"><!></div>');const vv={hash:"svelte-5pxri",code:".selection-wrapper.svelte-5pxri {position:absolute;top:0;left:0;z-index:7;pointer-events:all;}"};function xc(e,t){ve(t,!1),qe(e,vv);const[n,o]=Je(),r=()=>ie(u,"$selectionRectMode",n),i=()=>ie(c,"$nodeLookup",n),a=()=>ie(s,"$nodes",n),l=Ke(),{selectionRectMode:u,nodes:s,nodeLookup:c}=l,d=ri();let f=se(null);function g(h){const C=a().filter(b=>b.selected);d("selectioncontextmenu",{nodes:C,event:h})}function m(h){const C=a().filter(b=>b.selected);d("selectionclick",{nodes:C,event:h})}he(()=>(r(),i(),a()),()=>{r()==="nodes"&&(te(f,kr(i(),{filter:h=>!!h.selected})),a())}),ut(),Le();var v=Ue(),y=ke(v),w=h=>{var C=fv();nl(W(C),{width:"100%",height:"100%",x:0,y:0}),K(C),vt(C,(b,k)=>Ii==null?void 0:Ii(b,k),()=>({disabled:!1,store:l,onDrag:(b,k,_,S)=>{d("nodedrag",{event:b,targetNode:null,nodes:S})},onDragStart:(b,k,_,S)=>{d("nodedragstart",{event:b,targetNode:null,nodes:S})},onDragStop:(b,k,_,S)=>{d("nodedragstop",{event:b,targetNode:null,nodes:S})}})),Dt(()=>Ze("contextmenu",C,g)),Dt(()=>Ze("click",C,m)),Dt(()=>Ze("keyup",C,()=>{})),Me(()=>pe(C,"style",`width: ${p(f).width??""}px; height: ${p(f).height??""}px; transform: translate(${p(f).x??""}px, ${p(f).y??""}px)`)),T(h,C)};_e(y,h=>{r()==="nodes"&&p(f)&&Ln(p(f).x)&&Ln(p(f).y)&&h(w)}),T(e,v),ge(),o()}function it(e,t){let{enabled:n=!0,trigger:o,type:r="keydown"}=t;function i(a){const l=Array.isArray(o)?o:[o],u={alt:a.altKey,ctrl:a.ctrlKey,shift:a.shiftKey,meta:a.metaKey};for(const s of l){const c={modifier:[],preventDefault:!1,enabled:!0,...s},{modifier:d,key:f,callback:g,preventDefault:m,enabled:v}=c;if(v){if(d.length&&!(Array.isArray(d)?d:[d]).map(y=>typeof y=="string"?[y]:y).some(y=>y.every(w=>u[w])))continue;if(a.key===f){m&&a.preventDefault();const y={node:e,trigger:c,originalEvent:a};e.dispatchEvent(new CustomEvent("shortcut",{detail:y})),g==null||g(y)}}}}return n&&e.addEventListener(r,i),{update:a=>{const{enabled:l=!0,type:u="keydown"}=a;!n||l&&r===u?!n&&l&&e.addEventListener(u,i):e.removeEventListener(r,i),n=l,r=u,o=a.trigger},destroy:()=>{e.removeEventListener(r,i)}}}function $c(e,t){ve(t,!1);let n=$(t,"selectionKey",12,"Shift"),o=$(t,"multiSelectionKey",28,()=>Hi()?"Meta":"Control"),r=$(t,"deleteKey",12,"Backspace"),i=$(t,"panActivationKey",12," "),a=$(t,"zoomActivationKey",28,()=>Hi()?"Meta":"Control");const{selectionKeyPressed:l,multiselectionKeyPressed:u,deleteKeyPressed:s,panActivationKeyPressed:c,zoomActivationKeyPressed:d,selectionRect:f}=Ke();function g(w){return w!==null&&typeof w=="object"}function m(w){return g(w)&&w.modifier||[]}function v(w,h){return(Array.isArray(w)?w:[w]).map(C=>{const b=function(k){return k==null?"":g(k)?k.key:k}(C);return{key:b,modifier:m(C),enabled:b!==null,callback:h}})}function y(){f.set(null),l.set(!1),u.set(!1),s.set(!1),c.set(!1),d.set(!1)}return Le(),Ze("blur",Pt,y),Ze("contextmenu",Pt,y),vt(Pt,(w,h)=>it==null?void 0:it(w,h),()=>({trigger:v(n(),()=>l.set(!0)),type:"keydown"})),vt(Pt,(w,h)=>it==null?void 0:it(w,h),()=>({trigger:v(n(),()=>l.set(!1)),type:"keyup"})),vt(Pt,(w,h)=>it==null?void 0:it(w,h),()=>({trigger:v(o(),()=>u.set(!0)),type:"keydown"})),vt(Pt,(w,h)=>it==null?void 0:it(w,h),()=>({trigger:v(o(),()=>u.set(!1)),type:"keyup"})),vt(Pt,(w,h)=>it==null?void 0:it(w,h),()=>({trigger:v(r(),w=>{!(w.originalEvent.ctrlKey||w.originalEvent.metaKey||w.originalEvent.shiftKey)&&!function(h){var C,b;const k=((b=(C=h.composedPath)==null?void 0:C.call(h))==null?void 0:b[0])||h.target;return(k==null?void 0:k.nodeType)===1&&(Ef.includes(k.nodeName)||k.hasAttribute("contenteditable")||!!k.closest(".nokey"))}(w.originalEvent)&&s.set(!0)}),type:"keydown"})),vt(Pt,(w,h)=>it==null?void 0:it(w,h),()=>({trigger:v(r(),()=>s.set(!1)),type:"keyup"})),vt(Pt,(w,h)=>it==null?void 0:it(w,h),()=>({trigger:v(i(),()=>c.set(!0)),type:"keydown"})),vt(Pt,(w,h)=>it==null?void 0:it(w,h),()=>({trigger:v(i(),()=>c.set(!1)),type:"keyup"})),vt(Pt,(w,h)=>it==null?void 0:it(w,h),()=>({trigger:v(a(),()=>d.set(!0)),type:"keydown"})),vt(Pt,(w,h)=>it==null?void 0:it(w,h),()=>({trigger:v(a(),()=>d.set(!1)),type:"keyup"})),ge({get selectionKey(){return n()},set selectionKey(w){n(w),x()},get multiSelectionKey(){return o()},set multiSelectionKey(w){o(w),x()},get deleteKey(){return r()},set deleteKey(w){r(w),x()},get panActivationKey(){return i()},set panActivationKey(w){i(w),x()},get zoomActivationKey(){return a()},set zoomActivationKey(w){a(w),x()}})}ce(xc,{},[],[],!0),ce($c,{selectionKey:{},multiSelectionKey:{},deleteKey:{},panActivationKey:{},zoomActivationKey:{}},[],[],!0);var gv=xe('<path fill="none" class="svelte-flow__connection-path"></path>'),pv=xe('<svg class="svelte-flow__connectionline"><g><!><!></g></svg>');function Cc(e,t){ve(t,!1);const[n,o]=Je(),r=()=>ie(d,"$connection",n),i=()=>ie(f,"$connectionLineType",n);let a=$(t,"containerStyle",12,""),l=$(t,"style",12,""),u=$(t,"isCustomComponent",12,!1);const{width:s,height:c,connection:d,connectionLineType:f}=Ke();let g=se(null);he(()=>(r(),oe(u()),i(),p(g),qa),()=>{if(r().inProgress&&!u()){const{from:C,to:b,fromPosition:k,toPosition:_}=r(),S={sourceX:C.x,sourceY:C.y,sourcePosition:k,targetX:b.x,targetY:b.y,targetPosition:_};switch(i()){case Ao.Bezier:h=Pu(S),te(g,h[0]);break;case Ao.Step:(E=>{te(g,E[0])})(Ni({...S,borderRadius:0}));break;case Ao.SmoothStep:(E=>{te(g,E[0])})(Ni(S));break;default:(E=>{te(g,E[0])})(qa(S))}}var h}),ut(),Le();var m=Ue(),v=ke(m),y=h=>{var C=pv(),b=W(C),k=W(b);ft(k,t,"connectionLine",{});var _=Z(k),S=E=>{var L=gv();Me(()=>{pe(L,"d",p(g)),pe(L,"style",l())}),T(E,L)};_e(_,E=>{u()||E(S)}),K(b),K(C),Me(E=>{pe(C,"width",ie(s,"$width",n)),pe(C,"height",ie(c,"$height",n)),pe(C,"style",a()),bt(b,0,mn(E))},[()=>xt(["svelte-flow__connection",kf(r().isValid)])],ye),T(h,C)};_e(v,h=>{r().inProgress&&h(y)}),T(e,m);var w=ge({get containerStyle(){return a()},set containerStyle(h){a(h),x()},get style(){return l()},set style(h){l(h),x()},get isCustomComponent(){return u()},set isCustomComponent(h){u(h),x()}});return o(),w}ce(Cc,{containerStyle:{},style:{},isCustomComponent:{}},["connectionLine"],[],!0);var hv=ae("<div><!></div>");function Mr(e,t){const n=et(t,["children","$$slots","$$events","$$legacy","$$host"]),o=et(n,["position","style","class"]);ve(t,!1);const[r,i]=Je(),a=se();let l=$(t,"position",12,"top-right"),u=$(t,"style",12,void 0),s=$(t,"class",12,void 0);const{selectionRectMode:c}=Ke();he(()=>oe(l()),()=>{te(a,`${l()}`.split("-"))}),ut(),Le();var d=hv();let f;ft(W(d),t,"default",{}),K(d),Me(m=>{f=en(d,f,{class:m,style:u(),...o}),nt(d,"pointer-events",ie(c,"$selectionRectMode",r)?"none":"")},[()=>xt(["svelte-flow__panel",s(),...p(a)])],ye),T(e,d);var g=ge({get position(){return l()},set position(m){l(m),x()},get style(){return u()},set style(m){u(m),x()},get class(){return s()},set class(m){s(m),x()}});return i(),g}ce(Mr,{position:{},style:{},class:{}},["default"],[],!0);var mv=ae('<a href="https://svelteflow.dev" target="_blank" rel="noopener noreferrer" aria-label="Svelte Flow attribution">Svelte Flow</a>');function kc(e,t){ve(t,!1);let n=$(t,"proOptions",12,void 0),o=$(t,"position",12,"bottom-right");Le();var r=Ue(),i=ke(r),a=l=>{Mr(l,{get position(){return o()},class:"svelte-flow__attribution","data-message":"Feel free to remove the attribution or check out how you could support us: https://svelteflow.dev/support-us",children:(u,s)=>{T(u,mv())},$$slots:{default:!0}})};return _e(i,l=>{var u;(u=n())!=null&&u.hideAttribution||l(a)}),T(e,r),ge({get proOptions(){return n()},set proOptions(l){n(l),x()},get position(){return o()},set position(l){o(l),x()}})}function _c(e,{nodeTypes:t,edgeTypes:n,minZoom:o,maxZoom:r,translateExtent:i,paneClickDistance:a}){t!==void 0&&e.setNodeTypes(t),n!==void 0&&e.setEdgeTypes(n),o!==void 0&&e.setMinZoom(o),r!==void 0&&e.setMaxZoom(r),i!==void 0&&e.setTranslateExtent(i),a!==void 0&&e.setPaneClickDistance(a)}ce(kc,{proOptions:{},position:{}},[],[],!0);function Sc(e,t){(n=>Object.keys(n))(t).forEach(n=>{const o=t[n];o!==void 0&&e[n].set(o)})}function yv(e="light"){return qt("light",t=>{if(e!=="system")return void t(e);const n=typeof window>"u"||!window.matchMedia?null:window.matchMedia("(prefers-color-scheme: dark)"),o=()=>t(n!=null&&n.matches?"dark":"light");return t(n!=null&&n.matches?"dark":"light"),n==null||n.addEventListener("change",o),()=>{n==null||n.removeEventListener("change",o)}})}var wv=ae('<!> <!> <div class="svelte-flow__edgelabel-renderer"></div> <div class="svelte-flow__viewport-portal"></div> <!> <!>',1),bv=ae("<!> <!>",1),xv=ae("<div><!> <!> <!> <!></div>");const $v={hash:"svelte-12wlba6",code:".svelte-flow.svelte-12wlba6 {width:100%;height:100%;overflow:hidden;position:relative;z-index:0;background-color:var(--background-color, var(--background-color-default));}:root {--background-color-default: #fff;--background-pattern-color-default: #ddd;--minimap-mask-color-default: rgb(240, 240, 240, 0.6);--minimap-mask-stroke-color-default: none;--minimap-mask-stroke-width-default: 1;--controls-button-background-color-default: #fefefe;--controls-button-background-color-hover-default: #f4f4f4;--controls-button-color-default: inherit;--controls-button-color-hover-default: inherit;--controls-button-border-color-default: #eee;}"};function Ec(e,t){const n=function(R){const In={};R.children&&(In.default=!0);for(const io in R.$$slots)In[io]=!0;return In}(t),o=et(t,["children","$$slots","$$events","$$legacy","$$host"]),r=et(o,["id","nodes","edges","fitView","fitViewOptions","minZoom","maxZoom","initialViewport","viewport","nodeTypes","edgeTypes","selectionKey","selectionMode","panActivationKey","multiSelectionKey","zoomActivationKey","nodesDraggable","nodesConnectable","nodeDragThreshold","elementsSelectable","snapGrid","deleteKey","connectionRadius","connectionLineType","connectionMode","connectionLineStyle","connectionLineContainerStyle","onMoveStart","onMove","onMoveEnd","isValidConnection","translateExtent","nodeExtent","onlyRenderVisibleElements","panOnScrollMode","preventScrolling","zoomOnScroll","zoomOnDoubleClick","zoomOnPinch","panOnScroll","panOnDrag","selectionOnDrag","autoPanOnConnect","autoPanOnNodeDrag","onerror","ondelete","onedgecreate","attributionPosition","proOptions","defaultEdgeOptions","width","height","colorMode","onconnect","onconnectstart","onconnectend","onbeforedelete","oninit","nodeOrigin","paneClickDistance","nodeClickDistance","defaultMarkerColor","style","class"]);ve(t,!1),qe(e,$v);const[i,a]=Je(),l=()=>ie(Fi,"$initialized",i),u=se();let s=$(t,"id",12,"1"),c=$(t,"nodes",12),d=$(t,"edges",12),f=$(t,"fitView",12,void 0),g=$(t,"fitViewOptions",12,void 0),m=$(t,"minZoom",12,void 0),v=$(t,"maxZoom",12,void 0),y=$(t,"initialViewport",12,void 0),w=$(t,"viewport",12,void 0),h=$(t,"nodeTypes",12,void 0),C=$(t,"edgeTypes",12,void 0),b=$(t,"selectionKey",12,void 0),k=$(t,"selectionMode",12,void 0),_=$(t,"panActivationKey",12,void 0),S=$(t,"multiSelectionKey",12,void 0),E=$(t,"zoomActivationKey",12,void 0),L=$(t,"nodesDraggable",12,void 0),P=$(t,"nodesConnectable",12,void 0),D=$(t,"nodeDragThreshold",12,void 0),M=$(t,"elementsSelectable",12,void 0),H=$(t,"snapGrid",12,void 0),O=$(t,"deleteKey",12,void 0),V=$(t,"connectionRadius",12,void 0),z=$(t,"connectionLineType",12,void 0),N=$(t,"connectionMode",28,()=>ho.Strict),A=$(t,"connectionLineStyle",12,""),X=$(t,"connectionLineContainerStyle",12,""),B=$(t,"onMoveStart",12,void 0),Q=$(t,"onMove",12,void 0),ne=$(t,"onMoveEnd",12,void 0),ee=$(t,"isValidConnection",12,void 0),me=$(t,"translateExtent",12,void 0),de=$(t,"nodeExtent",12,void 0),J=$(t,"onlyRenderVisibleElements",12,void 0),F=$(t,"panOnScrollMode",28,()=>Jn.Free),I=$(t,"preventScrolling",12,!0),j=$(t,"zoomOnScroll",12,!0),G=$(t,"zoomOnDoubleClick",12,!0),le=$(t,"zoomOnPinch",12,!0),we=$(t,"panOnScroll",12,!1),Pe=$(t,"panOnDrag",12,!0),Ne=$(t,"selectionOnDrag",12,void 0),ue=$(t,"autoPanOnConnect",12,!0),Y=$(t,"autoPanOnNodeDrag",12,!0),$e=$(t,"onerror",12,void 0),Ce=$(t,"ondelete",12,void 0),Se=$(t,"onedgecreate",12,void 0),Ae=$(t,"attributionPosition",12,void 0),Ge=$(t,"proOptions",12,void 0),fe=$(t,"defaultEdgeOptions",12,void 0),U=$(t,"width",12,void 0),re=$(t,"height",12,void 0),Qe=$(t,"colorMode",12,"light"),Fe=$(t,"onconnect",12,void 0),tt=$(t,"onconnectstart",12,void 0),dn=$(t,"onconnectend",12,void 0),zt=$(t,"onbeforedelete",12,void 0),ot=$(t,"oninit",12,void 0),Re=$(t,"nodeOrigin",12,void 0),dt=$(t,"paneClickDistance",12,0),Rt=$(t,"nodeClickDistance",12,0),Xt=$(t,"defaultMarkerColor",12,"#b1b1b7"),Nt=$(t,"style",12,void 0),Yt=$(t,"class",12,void 0),An=se(),pt=se(),ro=se();const qi=ie(w(),"$viewport",i)||y(),at=function(R){return ga().has(R)}(Ai)?Ke():function({nodes:R,edges:In,width:io,height:Kt,fitView:pl,nodeOrigin:Ir,nodeExtent:Zr}){const Br=ac({nodes:R,edges:In,width:io,height:Kt,fitView:pl,nodeOrigin:Ir,nodeExtent:Zr});return Ho(Ai,{getStore:()=>Br}),Br}({nodes:q(c()),edges:q(d()),width:U(),height:re(),fitView:f(),nodeOrigin:Re(),nodeExtent:de()});tn(()=>(at.width.set(p(pt)),at.height.set(p(ro)),at.domNode.set(p(An)),at.syncNodeStores(c()),at.syncEdgeStores(d()),at.syncViewport(w()),f()!==void 0&&at.fitViewOnInit.set(f()),g()&&at.fitViewOptions.set(g()),_c(at,{nodeTypes:h(),edgeTypes:C(),minZoom:m(),maxZoom:v(),translateExtent:me(),paneClickDistance:dt()}),()=>{at.reset()}));const{initialized:Fi}=at;let Fo=se(!1);he(()=>(p(pt),p(ro)),()=>{p(pt)!==void 0&&p(ro)!==void 0&&(at.width.set(p(pt)),at.height.set(p(ro)))}),he(()=>(p(Fo),l(),oe(ot())),()=>{var R;!p(Fo)&&l()&&((R=ot())==null||R(),te(Fo,!0))}),he(()=>(oe(s()),oe(z()),oe(V()),oe(k()),oe(H()),oe(Xt()),oe(L()),oe(P()),oe(M()),oe(J()),oe(ee()),oe(ue()),oe(Y()),oe($e()),oe(Ce()),oe(Se()),oe(N()),oe(D()),oe(Fe()),oe(tt()),oe(dn()),oe(zt()),oe(Re()),Sc),()=>{const R={flowId:s(),connectionLineType:z(),connectionRadius:V(),selectionMode:k(),snapGrid:H(),defaultMarkerColor:Xt(),nodesDraggable:L(),nodesConnectable:P(),elementsSelectable:M(),onlyRenderVisibleElements:J(),isValidConnection:ee(),autoPanOnConnect:ue(),autoPanOnNodeDrag:Y(),onerror:$e(),ondelete:Ce(),onedgecreate:Se(),connectionMode:N(),nodeDragThreshold:D(),onconnect:Fe(),onconnectstart:tt(),onconnectend:dn(),onbeforedelete:zt(),nodeOrigin:Re()};Sc(at,R)}),he(()=>(oe(h()),oe(C()),oe(m()),oe(v()),oe(me()),oe(dt())),()=>{_c(at,{nodeTypes:h(),edgeTypes:C(),minZoom:m(),maxZoom:v(),translateExtent:me(),paneClickDistance:dt()})}),he(()=>oe(Qe()),()=>{(function(R,In,io){let Kt=io[In];Kt&&Kt.store!==R&&(Kt.unsubscribe(),Kt.unsubscribe=st)})(te(u,yv(Qe())),"$colorModeClass",i)}),ut(),Le();var Gt=xv();let Ar;var Hd=W(Gt);$c(0,{get selectionKey(){return b()},get deleteKey(){return O()},get panActivationKey(){return _()},get multiSelectionKey(){return S()},get zoomActivationKey(){return E()}});var vl=Z(Hd,2);const zd=ye(()=>F()===void 0?Jn.Free:F()),Nd=ye(()=>I()===void 0||I()),Ld=ye(()=>j()===void 0||j()),Od=ye(()=>G()===void 0||G()),Dd=ye(()=>le()===void 0||le()),Td=ye(()=>we()!==void 0&&we()),Ad=ye(()=>Pe()===void 0||Pe()),Id=ye(()=>dt()===void 0?0:dt());sc(vl,{initialViewport:qi,get onMoveStart(){return B()},get onMove(){return Q()},get onMoveEnd(){return ne()},get panOnScrollMode(){return p(zd)},get preventScrolling(){return p(Nd)},get zoomOnScroll(){return p(Ld)},get zoomOnDoubleClick(){return p(Od)},get zoomOnPinch(){return p(Dd)},get panOnScroll(){return p(Td)},get panOnDrag(){return p(Ad)},get paneClickDistance(){return p(Id)},children:(R,In)=>{const io=ye(()=>Pe()===void 0||Pe());dc(R,{get panOnDrag(){return p(io)},get selectionOnDrag(){return Ne()},$$events:{paneclick(Kt){De.call(this,t,Kt)},panecontextmenu(Kt){De.call(this,t,Kt)}},children:(Kt,pl)=>{var Ir=bv(),Zr=ke(Ir);fc(Zr,{children:(Br,$h)=>{var hl=wv(),ml=ke(hl);wc(ml,{get defaultEdgeOptions(){return fe()},$$events:{edgeclick(Ie){De.call(this,t,Ie)},edgecontextmenu(Ie){De.call(this,t,Ie)},edgemouseenter(Ie){De.call(this,t,Ie)},edgemouseleave(Ie){De.call(this,t,Ie)}}});var yl=Z(ml,2);Cc(yl,{get containerStyle(){return X()},get style(){return A()},isCustomComponent:n.connectionLine,$$slots:{connectionLine:(Ie,Ch)=>{var bl=Ue();ft(ke(bl),t,"connectionLine",{}),T(Ie,bl)}}});var wl=Z(yl,6);gc(wl,{get nodeClickDistance(){return Rt()},$$events:{nodeclick(Ie){De.call(this,t,Ie)},nodemouseenter(Ie){De.call(this,t,Ie)},nodemousemove(Ie){De.call(this,t,Ie)},nodemouseleave(Ie){De.call(this,t,Ie)},nodedragstart(Ie){De.call(this,t,Ie)},nodedrag(Ie){De.call(this,t,Ie)},nodedragstop(Ie){De.call(this,t,Ie)},nodecontextmenu(Ie){De.call(this,t,Ie)}}}),xc(Z(wl,2),{$$events:{selectionclick(Ie){De.call(this,t,Ie)},selectioncontextmenu(Ie){De.call(this,t,Ie)},nodedragstart(Ie){De.call(this,t,Ie)},nodedrag(Ie){De.call(this,t,Ie)},nodedragstop(Ie){De.call(this,t,Ie)}}}),T(Br,hl)},$$slots:{default:!0}}),bc(Z(Zr,2),{}),T(Kt,Ir)},$$slots:{default:!0}})},$$slots:{default:!0}});var gl=Z(vl,2);kc(gl,{get proOptions(){return Ge()},get position(){return Ae()}}),ft(Z(gl,2),t,"default",{}),K(Gt),Vn(Gt,R=>te(An,R),()=>p(An)),Me(R=>Ar=en(Gt,Ar,{style:Nt(),class:R,"data-testid":"svelte-flow__wrapper",...r,role:"application"},"svelte-12wlba6"),[()=>xt(["svelte-flow",Yt(),ie(p(u),"$colorModeClass",i)])],ye),ms(Gt,"clientWidth",R=>te(pt,R)),ms(Gt,"clientHeight",R=>te(ro,R)),Ze("dragover",Gt,function(R){De.call(this,t,R)}),Ze("drop",Gt,function(R){De.call(this,t,R)}),T(e,Gt);var Zd=ge({get id(){return s()},set id(R){s(R),x()},get nodes(){return c()},set nodes(R){c(R),x()},get edges(){return d()},set edges(R){d(R),x()},get fitView(){return f()},set fitView(R){f(R),x()},get fitViewOptions(){return g()},set fitViewOptions(R){g(R),x()},get minZoom(){return m()},set minZoom(R){m(R),x()},get maxZoom(){return v()},set maxZoom(R){v(R),x()},get initialViewport(){return y()},set initialViewport(R){y(R),x()},get viewport(){return w()},set viewport(R){w(R),x()},get nodeTypes(){return h()},set nodeTypes(R){h(R),x()},get edgeTypes(){return C()},set edgeTypes(R){C(R),x()},get selectionKey(){return b()},set selectionKey(R){b(R),x()},get selectionMode(){return k()},set selectionMode(R){k(R),x()},get panActivationKey(){return _()},set panActivationKey(R){_(R),x()},get multiSelectionKey(){return S()},set multiSelectionKey(R){S(R),x()},get zoomActivationKey(){return E()},set zoomActivationKey(R){E(R),x()},get nodesDraggable(){return L()},set nodesDraggable(R){L(R),x()},get nodesConnectable(){return P()},set nodesConnectable(R){P(R),x()},get nodeDragThreshold(){return D()},set nodeDragThreshold(R){D(R),x()},get elementsSelectable(){return M()},set elementsSelectable(R){M(R),x()},get snapGrid(){return H()},set snapGrid(R){H(R),x()},get deleteKey(){return O()},set deleteKey(R){O(R),x()},get connectionRadius(){return V()},set connectionRadius(R){V(R),x()},get connectionLineType(){return z()},set connectionLineType(R){z(R),x()},get connectionMode(){return N()},set connectionMode(R){N(R),x()},get connectionLineStyle(){return A()},set connectionLineStyle(R){A(R),x()},get connectionLineContainerStyle(){return X()},set connectionLineContainerStyle(R){X(R),x()},get onMoveStart(){return B()},set onMoveStart(R){B(R),x()},get onMove(){return Q()},set onMove(R){Q(R),x()},get onMoveEnd(){return ne()},set onMoveEnd(R){ne(R),x()},get isValidConnection(){return ee()},set isValidConnection(R){ee(R),x()},get translateExtent(){return me()},set translateExtent(R){me(R),x()},get nodeExtent(){return de()},set nodeExtent(R){de(R),x()},get onlyRenderVisibleElements(){return J()},set onlyRenderVisibleElements(R){J(R),x()},get panOnScrollMode(){return F()},set panOnScrollMode(R){F(R),x()},get preventScrolling(){return I()},set preventScrolling(R){I(R),x()},get zoomOnScroll(){return j()},set zoomOnScroll(R){j(R),x()},get zoomOnDoubleClick(){return G()},set zoomOnDoubleClick(R){G(R),x()},get zoomOnPinch(){return le()},set zoomOnPinch(R){le(R),x()},get panOnScroll(){return we()},set panOnScroll(R){we(R),x()},get panOnDrag(){return Pe()},set panOnDrag(R){Pe(R),x()},get selectionOnDrag(){return Ne()},set selectionOnDrag(R){Ne(R),x()},get autoPanOnConnect(){return ue()},set autoPanOnConnect(R){ue(R),x()},get autoPanOnNodeDrag(){return Y()},set autoPanOnNodeDrag(R){Y(R),x()},get onerror(){return $e()},set onerror(R){$e(R),x()},get ondelete(){return Ce()},set ondelete(R){Ce(R),x()},get onedgecreate(){return Se()},set onedgecreate(R){Se(R),x()},get attributionPosition(){return Ae()},set attributionPosition(R){Ae(R),x()},get proOptions(){return Ge()},set proOptions(R){Ge(R),x()},get defaultEdgeOptions(){return fe()},set defaultEdgeOptions(R){fe(R),x()},get width(){return U()},set width(R){U(R),x()},get height(){return re()},set height(R){re(R),x()},get colorMode(){return Qe()},set colorMode(R){Qe(R),x()},get onconnect(){return Fe()},set onconnect(R){Fe(R),x()},get onconnectstart(){return tt()},set onconnectstart(R){tt(R),x()},get onconnectend(){return dn()},set onconnectend(R){dn(R),x()},get onbeforedelete(){return zt()},set onbeforedelete(R){zt(R),x()},get oninit(){return ot()},set oninit(R){ot(R),x()},get nodeOrigin(){return Re()},set nodeOrigin(R){Re(R),x()},get paneClickDistance(){return dt()},set paneClickDistance(R){dt(R),x()},get nodeClickDistance(){return Rt()},set nodeClickDistance(R){Rt(R),x()},get defaultMarkerColor(){return Xt()},set defaultMarkerColor(R){Xt(R),x()},get style(){return Nt()},set style(R){Nt(R),x()},get class(){return Yt()},set class(R){Yt(R),x()}});return a(),Zd}function Pc(e,t){ve(t,!1);let n=$(t,"initialNodes",12,void 0),o=$(t,"initialEdges",12,void 0),r=$(t,"initialWidth",12,void 0),i=$(t,"initialHeight",12,void 0),a=$(t,"fitView",12,void 0),l=$(t,"nodeOrigin",12,void 0);const u=ac({nodes:n(),edges:o(),width:r(),height:i(),nodeOrigin:l(),fitView:a()});Ho(Ai,{getStore:()=>u}),$a(()=>{u.reset()}),Le();var s=Ue();return ft(ke(s),t,"default",{}),T(e,s),ge({get initialNodes(){return n()},set initialNodes(c){n(c),x()},get initialEdges(){return o()},set initialEdges(c){o(c),x()},get initialWidth(){return r()},set initialWidth(c){r(c),x()},get initialHeight(){return i()},set initialHeight(c){i(c),x()},get fitView(){return a()},set fitView(c){a(c),x()},get nodeOrigin(){return l()},set nodeOrigin(c){l(c),x()}})}ce(Ec,{id:{},nodes:{},edges:{},fitView:{},fitViewOptions:{},minZoom:{},maxZoom:{},initialViewport:{},viewport:{},nodeTypes:{},edgeTypes:{},selectionKey:{},selectionMode:{},panActivationKey:{},multiSelectionKey:{},zoomActivationKey:{},nodesDraggable:{},nodesConnectable:{},nodeDragThreshold:{},elementsSelectable:{},snapGrid:{},deleteKey:{},connectionRadius:{},connectionLineType:{},connectionMode:{},connectionLineStyle:{},connectionLineContainerStyle:{},onMoveStart:{},onMove:{},onMoveEnd:{},isValidConnection:{},translateExtent:{},nodeExtent:{},onlyRenderVisibleElements:{},panOnScrollMode:{},preventScrolling:{},zoomOnScroll:{},zoomOnDoubleClick:{},zoomOnPinch:{},panOnScroll:{},panOnDrag:{},selectionOnDrag:{},autoPanOnConnect:{},autoPanOnNodeDrag:{},onerror:{},ondelete:{},onedgecreate:{},attributionPosition:{},proOptions:{},defaultEdgeOptions:{},width:{},height:{},colorMode:{},onconnect:{},onconnectstart:{},onconnectend:{},onbeforedelete:{},oninit:{},nodeOrigin:{},paneClickDistance:{},nodeClickDistance:{},defaultMarkerColor:{},style:{},class:{}},["connectionLine","default"],[],!0),ce(Pc,{initialNodes:{},initialEdges:{},initialWidth:{},initialHeight:{},fitView:{},nodeOrigin:{}},["default"],[],!0);var Cv=ae("<button><!></button>");function Vr(e,t){const n=et(t,["children","$$slots","$$events","$$legacy","$$host"]),o=et(n,["class","bgColor","bgColorHover","color","colorHover","borderColor"]);ve(t,!1);let r=$(t,"class",12,void 0),i=$(t,"bgColor",12,void 0),a=$(t,"bgColorHover",12,void 0),l=$(t,"color",12,void 0),u=$(t,"colorHover",12,void 0),s=$(t,"borderColor",12,void 0);Le();var c=Cv();let d;return ft(W(c),t,"default",{class:"button-svg"}),K(c),Me(f=>{d=en(c,d,{type:"button",class:f,...o}),nt(c,"--xy-controls-button-background-color-props",i()),nt(c,"--xy-controls-button-background-color-hover-props",a()),nt(c,"--xy-controls-button-color-props",l()),nt(c,"--xy-controls-button-color-hover-props",u()),nt(c,"--xy-controls-button-border-color-props",s())},[()=>xt(["svelte-flow__controls-button",r()])],ye),Ze("click",c,function(f){De.call(this,t,f)}),T(e,c),ge({get class(){return r()},set class(f){r(f),x()},get bgColor(){return i()},set bgColor(f){i(f),x()},get bgColorHover(){return a()},set bgColorHover(f){a(f),x()},get color(){return l()},set color(f){l(f),x()},get colorHover(){return u()},set colorHover(f){u(f),x()},get borderColor(){return s()},set borderColor(f){s(f),x()}})}ce(Vr,{class:{},bgColor:{},bgColorHover:{},color:{},colorHover:{},borderColor:{}},["default"],[],!0);var kv=xe('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32"><path d="M32 18.133H18.133V32h-4.266V18.133H0v-4.266h13.867V0h4.266v13.867H32z"></path></svg>');function Mc(e){T(e,kv())}ce(Mc,{},[],[],!0);var _v=xe('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 5"><path d="M0 0h32v4.2H0z"></path></svg>');function Vc(e){T(e,_v())}ce(Vc,{},[],[],!0);var Sv=xe('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 30"><path d="M3.692 4.63c0-.53.4-.938.939-.938h5.215V0H4.708C2.13 0 0 2.054 0 4.63v5.216h3.692V4.631zM27.354 0h-5.2v3.692h5.17c.53 0 .984.4.984.939v5.215H32V4.631A4.624 4.624 0 0027.354 0zm.954 24.83c0 .532-.4.94-.939.94h-5.215v3.768h5.215c2.577 0 4.631-2.13 4.631-4.707v-5.139h-3.692v5.139zm-23.677.94c-.531 0-.939-.4-.939-.94v-5.138H0v5.139c0 2.577 2.13 4.707 4.708 4.707h5.138V25.77H4.631z"></path></svg>');function Hc(e){T(e,Sv())}ce(Hc,{},[],[],!0);var Ev=xe('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 25 32"><path d="M21.333 10.667H19.81V7.619C19.81 3.429 16.38 0 12.19 0 8 0 4.571 3.429 4.571 7.619v3.048H3.048A3.056 3.056 0 000 13.714v15.238A3.056 3.056 0 003.048 32h18.285a3.056 3.056 0 003.048-3.048V13.714a3.056 3.056 0 00-3.048-3.047zM12.19 24.533a3.056 3.056 0 01-3.047-3.047 3.056 3.056 0 013.047-3.048 3.056 3.056 0 013.048 3.048 3.056 3.056 0 01-3.048 3.047zm4.724-13.866H7.467V7.619c0-2.59 2.133-4.724 4.723-4.724 2.591 0 4.724 2.133 4.724 4.724v3.048z"></path></svg>');function zc(e){T(e,Ev())}ce(zc,{},[],[],!0);var Pv=xe('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 25 32"><path d="M21.333 10.667H19.81V7.619C19.81 3.429 16.38 0 12.19 0c-4.114 1.828-1.37 2.133.305 2.438 1.676.305 4.42 2.59 4.42 5.181v3.048H3.047A3.056 3.056 0 000 13.714v15.238A3.056 3.056 0 003.048 32h18.285a3.056 3.056 0 003.048-3.048V13.714a3.056 3.056 0 00-3.048-3.047zM12.19 24.533a3.056 3.056 0 01-3.047-3.047 3.056 3.056 0 013.047-3.048 3.056 3.056 0 013.048 3.048 3.056 3.056 0 01-3.048 3.047z"></path></svg>');function Nc(e){T(e,Pv())}ce(Nc,{},[],[],!0);var to,Mv=ae("<!> <!>",1),Vv=ae("<!> <!> <!> <!> <!> <!>",1);function Lc(e,t){ve(t,!1);const[n,o]=Je(),r=()=>ie(A,"$nodesDraggable",n),i=()=>ie(X,"$nodesConnectable",n),a=()=>ie(B,"$elementsSelectable",n),l=()=>ie(V,"$viewport",n),u=()=>ie(z,"$minZoom",n),s=()=>ie(N,"$maxZoom",n),c=se(),d=se(),f=se(),g=se();let m=$(t,"position",12,"bottom-left"),v=$(t,"showZoom",12,!0),y=$(t,"showFitView",12,!0),w=$(t,"showLock",12,!0),h=$(t,"buttonBgColor",12,void 0),C=$(t,"buttonBgColorHover",12,void 0),b=$(t,"buttonColor",12,void 0),k=$(t,"buttonColorHover",12,void 0),_=$(t,"buttonBorderColor",12,void 0),S=$(t,"ariaLabel",12,void 0),E=$(t,"style",12,void 0),L=$(t,"orientation",12,"vertical"),P=$(t,"fitViewOptions",12,void 0),D=$(t,"class",12,"");const{zoomIn:M,zoomOut:H,fitView:O,viewport:V,minZoom:z,maxZoom:N,nodesDraggable:A,nodesConnectable:X,elementsSelectable:B}=Ke(),Q={bgColor:h(),bgColorHover:C(),color:b(),colorHover:k(),borderColor:_()},ne=()=>{M()},ee=()=>{H()},me=()=>{O(P())},de=()=>{te(c,!p(c)),A.set(p(c)),X.set(p(c)),B.set(p(c))};he(()=>(r(),i(),a()),()=>{te(c,r()||i()||a())}),he(()=>(l(),u()),()=>{te(d,l().zoom<=u())}),he(()=>(l(),s()),()=>{te(f,l().zoom>=s())}),he(()=>oe(L()),()=>{te(g,L()==="horizontal"?"horizontal":"vertical")}),ut(),Le();const J=ye(()=>xt(["svelte-flow__controls",p(g),D()])),F=ye(()=>S()??"Svelte Flow controls");Mr(e,{get class(){return p(J)},get position(){return m()},"data-testid":"svelte-flow__controls",get"aria-label"(){return p(F)},get style(){return E()},children:(j,G)=>{var le=Vv(),we=ke(le);ft(we,t,"before",{});var Pe=Z(we,2),Ne=Ae=>{var Ge=Mv(),fe=ke(Ge);Vr(fe,lt({class:"svelte-flow__controls-zoomin",title:"zoom in","aria-label":"zoom in",get disabled(){return p(f)}},Q,{$$events:{click:ne},children:(U,re)=>{Mc(U)},$$slots:{default:!0}})),Vr(Z(fe,2),lt({class:"svelte-flow__controls-zoomout",title:"zoom out","aria-label":"zoom out",get disabled(){return p(d)}},Q,{$$events:{click:ee},children:(U,re)=>{Vc(U)},$$slots:{default:!0}})),T(Ae,Ge)};_e(Pe,Ae=>{v()&&Ae(Ne)});var ue=Z(Pe,2),Y=Ae=>{Vr(Ae,lt({class:"svelte-flow__controls-fitview",title:"fit view","aria-label":"fit view"},Q,{$$events:{click:me},children:(Ge,fe)=>{Hc(Ge)},$$slots:{default:!0}}))};_e(ue,Ae=>{y()&&Ae(Y)});var $e=Z(ue,2),Ce=Ae=>{Vr(Ae,lt({class:"svelte-flow__controls-interactive",title:"toggle interactivity","aria-label":"toggle interactivity"},Q,{$$events:{click:de},children:(Ge,fe)=>{var U=Ue(),re=ke(U),Qe=tt=>{Nc(tt)},Fe=tt=>{zc(tt)};_e(re,tt=>{p(c)?tt(Qe):tt(Fe,!1)}),T(Ge,U)},$$slots:{default:!0}}))};_e($e,Ae=>{w()&&Ae(Ce)});var Se=Z($e,2);ft(Se,t,"default",{}),ft(Z(Se,2),t,"after",{}),T(j,le)},$$slots:{default:!0}});var I=ge({get position(){return m()},set position(j){m(j),x()},get showZoom(){return v()},set showZoom(j){v(j),x()},get showFitView(){return y()},set showFitView(j){y(j),x()},get showLock(){return w()},set showLock(j){w(j),x()},get buttonBgColor(){return h()},set buttonBgColor(j){h(j),x()},get buttonBgColorHover(){return C()},set buttonBgColorHover(j){C(j),x()},get buttonColor(){return b()},set buttonColor(j){b(j),x()},get buttonColorHover(){return k()},set buttonColorHover(j){k(j),x()},get buttonBorderColor(){return _()},set buttonBorderColor(j){_(j),x()},get ariaLabel(){return S()},set ariaLabel(j){S(j),x()},get style(){return E()},set style(j){E(j),x()},get orientation(){return L()},set orientation(j){L(j),x()},get fitViewOptions(){return P()},set fitViewOptions(j){P(j),x()},get class(){return D()},set class(j){D(j),x()}});return o(),I}ce(Lc,{position:{},showZoom:{},showFitView:{},showLock:{},buttonBgColor:{},buttonBgColorHover:{},buttonColor:{},buttonColorHover:{},buttonBorderColor:{},ariaLabel:{},style:{},orientation:{},fitViewOptions:{},class:{}},["before","default","after"],[],!0),function(e){e.Lines="lines",e.Dots="dots",e.Cross="cross"}(to||(to={}));var Hv=xe("<circle></circle>");function Oc(e,t){ve(t,!1);let n=$(t,"radius",12,5),o=$(t,"class",12,"");Le();var r=Hv();return Me(i=>{pe(r,"cx",n()),pe(r,"cy",n()),pe(r,"r",n()),bt(r,0,mn(i))},[()=>xt(["svelte-flow__background-pattern","dots",o()])],ye),T(e,r),ge({get radius(){return n()},set radius(i){n(i),x()},get class(){return o()},set class(i){o(i),x()}})}ce(Oc,{radius:{},class:{}},[],[],!0);var zv=xe("<path></path>");function Dc(e,t){ve(t,!1);let n=$(t,"lineWidth",12,1),o=$(t,"dimensions",12),r=$(t,"variant",12,void 0),i=$(t,"class",12,"");Le();var a=zv();return Me(l=>{pe(a,"stroke-width",n()),pe(a,"d",`M${o()[0]/2} 0 V${o()[1]} M0 ${o()[1]/2} H${o()[0]}`),bt(a,0,mn(l))},[()=>xt(["svelte-flow__background-pattern",r(),i()])],ye),T(e,a),ge({get lineWidth(){return n()},set lineWidth(l){n(l),x()},get dimensions(){return o()},set dimensions(l){o(l),x()},get variant(){return r()},set variant(l){r(l),x()},get class(){return i()},set class(l){i(l),x()}})}ce(Dc,{lineWidth:{},dimensions:{},variant:{},class:{}},[],[],!0);const Nv={[to.Dots]:1,[to.Lines]:1,[to.Cross]:6};var Lv=xe('<svg data-testid="svelte-flow__background"><pattern patternUnits="userSpaceOnUse"><!></pattern><rect x="0" y="0" width="100%" height="100%"></rect></svg>');const Ov={hash:"svelte-1r7pe8d",code:".svelte-flow__background.svelte-1r7pe8d {position:absolute;width:100%;height:100%;top:0;left:0;}"};function Tc(e,t){ve(t,!1),qe(e,Ov);const[n,o]=Je(),r=()=>ie(k,"$flowId",n),i=()=>ie(b,"$viewport",n),a=se(),l=se(),u=se(),s=se(),c=se();let d=$(t,"id",12,void 0),f=$(t,"variant",28,()=>to.Dots),g=$(t,"gap",12,20),m=$(t,"size",12,1),v=$(t,"lineWidth",12,1),y=$(t,"bgColor",12,void 0),w=$(t,"patternColor",12,void 0),h=$(t,"patternClass",12,void 0),C=$(t,"class",12,"");const{viewport:b,flowId:k}=Ke(),_=m()||Nv[f()],S=f()===to.Dots,E=f()===to.Cross,L=Array.isArray(g())?g():[g(),g()];he(()=>(r(),oe(d())),()=>{te(a,`background-pattern-${r()}-${d()?d():""}`)}),he(()=>i(),()=>{te(l,[L[0]*i().zoom||1,L[1]*i().zoom||1])}),he(()=>i(),()=>{te(u,_*i().zoom)}),he(()=>(p(u),p(l)),()=>{te(s,E?[p(u),p(u)]:p(l))}),he(()=>(p(u),p(s)),()=>{te(c,S?[p(u)/2,p(u)/2]:[p(s)[0]/2,p(s)[1]/2])}),ut(),Le();var P=Lv(),D=W(P),M=W(D),H=N=>{const A=ye(()=>p(u)/2);Oc(N,{get radius(){return p(A)},get class(){return h()}})},O=N=>{Dc(N,{get dimensions(){return p(s)},get variant(){return f()},get lineWidth(){return v()},get class(){return h()}})};_e(M,N=>{S?N(H):N(O,!1)}),K(D);var V=Z(D);K(P),Me(N=>{bt(P,0,mn(N),"svelte-1r7pe8d"),nt(P,"--xy-background-color-props",y()),nt(P,"--xy-background-pattern-color-props",w()),pe(D,"id",p(a)),pe(D,"x",i().x%p(l)[0]),pe(D,"y",i().y%p(l)[1]),pe(D,"width",p(l)[0]),pe(D,"height",p(l)[1]),pe(D,"patternTransform",`translate(-${p(c)[0]},-${p(c)[1]})`),pe(V,"fill",`url(#${p(a)})`)},[()=>xt(["svelte-flow__background",C()])],ye),T(e,P);var z=ge({get id(){return d()},set id(N){d(N),x()},get variant(){return f()},set variant(N){f(N),x()},get gap(){return g()},set gap(N){g(N),x()},get size(){return m()},set size(N){m(N),x()},get lineWidth(){return v()},set lineWidth(N){v(N),x()},get bgColor(){return y()},set bgColor(N){y(N),x()},get patternColor(){return w()},set patternColor(N){w(N),x()},get patternClass(){return h()},set patternClass(N){h(N),x()},get class(){return C()},set class(N){C(N),x()}});return o(),z}ce(Tc,{id:{},variant:{},gap:{},size:{},lineWidth:{},bgColor:{},patternColor:{},patternClass:{},class:{}},[],[],!0);var Dv=xe("<rect></rect>");function Ac(e,t){ve(t,!1);let n=$(t,"x",12),o=$(t,"y",12),r=$(t,"width",12,0),i=$(t,"height",12,0),a=$(t,"borderRadius",12,5),l=$(t,"color",12,void 0),u=$(t,"shapeRendering",12),s=$(t,"strokeColor",12,void 0),c=$(t,"strokeWidth",12,2),d=$(t,"selected",12,!1),f=$(t,"class",12,"");Le();var g=Dv();let m;return Me(v=>{m=bt(g,0,mn(v),null,m,{selected:d()}),pe(g,"x",n()),pe(g,"y",o()),pe(g,"rx",a()),pe(g,"ry",a()),pe(g,"width",r()),pe(g,"height",i()),pe(g,"style",`${l()?`fill: ${l()};`:""}${s()?`stroke: ${s()};`:""}${c()?`stroke-width: ${c()};`:""}`),pe(g,"shape-rendering",u())},[()=>xt(["svelte-flow__minimap-node",f()])],ye),T(e,g),ge({get x(){return n()},set x(v){n(v),x()},get y(){return o()},set y(v){o(v),x()},get width(){return r()},set width(v){r(v),x()},get height(){return i()},set height(v){i(v),x()},get borderRadius(){return a()},set borderRadius(v){a(v),x()},get color(){return l()},set color(v){l(v),x()},get shapeRendering(){return u()},set shapeRendering(v){u(v),x()},get strokeColor(){return s()},set strokeColor(v){s(v),x()},get strokeWidth(){return c()},set strokeWidth(v){c(v),x()},get selected(){return d()},set selected(v){d(v),x()},get class(){return f()},set class(v){f(v),x()}})}function Ic(e,t){const n=function({domNode:o,panZoom:r,getTransform:i,getViewScale:a}){const l=Ft(o);return{update:function({translateExtent:u,width:s,height:c,zoomStep:d=10,pannable:f=!0,zoomable:g=!0,inversePan:m=!1}){let v=[0,0];const y=cu().on("start",w=>{(w.sourceEvent.type==="mousedown"||w.sourceEvent.type==="touchstart")&&(v=[w.sourceEvent.clientX??w.sourceEvent.touches[0].clientX,w.sourceEvent.clientY??w.sourceEvent.touches[0].clientY])}).on("zoom",f?w=>{const h=i();if(w.sourceEvent.type!=="mousemove"&&w.sourceEvent.type!=="touchmove"||!r)return;const C=[w.sourceEvent.clientX??w.sourceEvent.touches[0].clientX,w.sourceEvent.clientY??w.sourceEvent.touches[0].clientY],b=[C[0]-v[0],C[1]-v[1]];v=C;const k=a()*Math.max(h[2],Math.log(h[2]))*(m?-1:1),_={x:h[0]-b[0]*k,y:h[1]-b[1]*k},S=[[0,0],[s,c]];r.setViewportConstrained({x:_.x,y:_.y,zoom:h[2]},S,u)}:null).on("zoom.wheel",g?w=>{const h=i();if(w.sourceEvent.type!=="wheel"||!r)return;const C=-w.sourceEvent.deltaY*(w.sourceEvent.deltaMode===1?.05:w.sourceEvent.deltaMode?1:.002)*d,b=h[2]*Math.pow(2,C);r.scaleTo(b)}:null);l.call(y,{})},destroy:function(){l.on("zoom",null)},pointer:on}}({domNode:e,panZoom:t.panZoom,getTransform:()=>{const o=q(t.viewport);return[o.x,o.y,o.zoom]},getViewScale:t.getViewScale});return{update:function(o){n.update({translateExtent:o.translateExtent,width:o.width,height:o.height,inversePan:o.inversePan,zoomStep:o.zoomStep,pannable:o.pannable,zoomable:o.zoomable})},destroy(){n.destroy()}}}ce(Ac,{x:{},y:{},width:{},height:{},borderRadius:{},color:{},shapeRendering:{},strokeColor:{},strokeWidth:{},selected:{},class:{}},[],[],!0);const ol=e=>e instanceof Function?e:()=>e;var Tv=xe("<title> </title>"),Av=xe('<svg class="svelte-flow__minimap-svg" role="img"><!><!><path class="svelte-flow__minimap-mask" fill-rule="evenodd" pointer-events="none"></path></svg>');function Zc(e,t){ve(t,!1);const[n,o]=Je(),r=()=>ie(I,"$viewport",n),i=()=>ie(j,"$containerWidth",n),a=()=>ie(G,"$containerHeight",n),l=()=>ie(F,"$nodeLookup",n),u=()=>ie(J,"$nodes",n),s=()=>ie(we,"$panZoom",n),c=se(),d=se(),f=se(),g=se(),m=se(),v=se(),y=se(),w=se(),h=se(),C=se(),b=se(),k=se(),_=se();let S=$(t,"position",12,"bottom-right"),E=$(t,"ariaLabel",12,"Mini map"),L=$(t,"nodeStrokeColor",12,"transparent"),P=$(t,"nodeColor",12,void 0),D=$(t,"nodeClass",12,""),M=$(t,"nodeBorderRadius",12,5),H=$(t,"nodeStrokeWidth",12,2),O=$(t,"bgColor",12,void 0),V=$(t,"maskColor",12,void 0),z=$(t,"maskStrokeColor",12,void 0),N=$(t,"maskStrokeWidth",12,void 0),A=$(t,"width",12,void 0),X=$(t,"height",12,void 0),B=$(t,"pannable",12,!0),Q=$(t,"zoomable",12,!0),ne=$(t,"inversePan",12,void 0),ee=$(t,"zoomStep",12,void 0),me=$(t,"style",12,""),de=$(t,"class",12,"");const{nodes:J,nodeLookup:F,viewport:I,width:j,height:G,flowId:le,panZoom:we,translateExtent:Pe}=Ke(),Ne=P()===void 0?void 0:ol(P()),ue=ol(L()),Y=ol(D()),$e=typeof window>"u"||window.chrome?"crispEdges":"geometricPrecision",Ce=`svelte-flow__minimap-desc-${ie(le,"$flowId",n)}`;let Se=se(p(c));const Ae=()=>p(v);he(()=>(r(),i(),a()),()=>{te(c,{x:-r().x/r().zoom,y:-r().y/r().zoom,width:i()/r().zoom,height:a()/r().zoom})}),he(()=>(l(),p(c),u()),()=>{te(Se,l().size>0?xu(kr(l()),p(c)):p(c)),u()}),he(()=>oe(A()),()=>{te(d,A()??200)}),he(()=>oe(X()),()=>{te(f,X()??150)}),he(()=>(p(Se),p(d)),()=>{te(g,p(Se).width/p(d))}),he(()=>(p(Se),p(f)),()=>{te(m,p(Se).height/p(f))}),he(()=>(p(g),p(m)),()=>{te(v,Math.max(p(g),p(m)))}),he(()=>(p(v),p(d)),()=>{te(y,p(v)*p(d))}),he(()=>(p(v),p(f)),()=>{te(w,p(v)*p(f))}),he(()=>p(v),()=>{te(h,5*p(v))}),he(()=>(p(Se),p(y),p(h)),()=>{te(C,p(Se).x-(p(y)-p(Se).width)/2-p(h))}),he(()=>(p(Se),p(w),p(h)),()=>{te(b,p(Se).y-(p(w)-p(Se).height)/2-p(h))}),he(()=>(p(y),p(h)),()=>{te(k,p(y)+2*p(h))}),he(()=>(p(w),p(h)),()=>{te(_,p(w)+2*p(h))}),ut(),Le();const Ge=ye(()=>me()+(O()?`;--xy-minimap-background-color-props:${O()}`:"")),fe=ye(()=>xt(["svelte-flow__minimap",de()]));Mr(e,{get position(){return S()},get style(){return p(Ge)},get class(){return p(fe)},"data-testid":"svelte-flow__minimap",children:(re,Qe)=>{var Fe=Ue(),tt=ke(Fe),dn=zt=>{var ot=Av();pe(ot,"aria-labelledby",Ce);var Re=W(ot),dt=Nt=>{var Yt=Tv();pe(Yt,"id",Ce);var An=W(Yt,!0);K(Yt),Me(()=>Tt(An,E())),T(Nt,Yt)};_e(Re,Nt=>{E()&&Nt(dt)});var Rt=Z(Re);At(Rt,1,u,Nt=>Nt.id,(Nt,Yt)=>{var An=Ue();const pt=ye(()=>l().get(p(Yt).id));var ro=ke(An),qi=at=>{const Fi=ye(()=>Qn(p(pt))),Fo=ye(()=>Ne==null?void 0:Ne(p(pt))),Gt=ye(()=>ue(p(pt))),Ar=ye(()=>Y(p(pt)));Ac(at,lt({get x(){return p(pt).internals.positionAbsolute.x},get y(){return p(pt).internals.positionAbsolute.y}},()=>p(Fi),{get selected(){return p(pt).selected},get color(){return p(Fo)},get borderRadius(){return M()},get strokeColor(){return p(Gt)},get strokeWidth(){return H()},shapeRendering:$e,get class(){return p(Ar)}}))};_e(ro,at=>{p(pt)&&ku(p(pt))&&at(qi)}),T(Nt,An)});var Xt=Z(Rt);K(ot),vt(ot,(Nt,Yt)=>Ic==null?void 0:Ic(Nt,Yt),()=>({panZoom:s(),viewport:I,getViewScale:Ae,translateExtent:ie(Pe,"$translateExtent",n),width:i(),height:a(),inversePan:ne(),zoomStep:ee(),pannable:B(),zoomable:Q()})),Me(()=>{pe(ot,"width",p(d)),pe(ot,"height",p(f)),pe(ot,"viewBox",`${p(C)??""} ${p(b)??""} ${p(k)??""} ${p(_)??""}`),nt(ot,"--xy-minimap-mask-background-color-props",V()),nt(ot,"--xy-minimap-mask-stroke-color-props",z()),nt(ot,"--xy-minimap-mask-stroke-width-props",N()?N()*p(v):void 0),pe(Xt,"d",`M${p(C)-p(h)},${p(b)-p(h)}h${p(k)+2*p(h)}v${p(_)+2*p(h)}h${-p(k)-2*p(h)}z
      M${p(c).x??""},${p(c).y??""}h${p(c).width??""}v${p(c).height??""}h${-p(c).width}z`)}),T(zt,ot)};_e(tt,zt=>{s()&&zt(dn)}),T(re,Fe)},$$slots:{default:!0}});var U=ge({get position(){return S()},set position(re){S(re),x()},get ariaLabel(){return E()},set ariaLabel(re){E(re),x()},get nodeStrokeColor(){return L()},set nodeStrokeColor(re){L(re),x()},get nodeColor(){return P()},set nodeColor(re){P(re),x()},get nodeClass(){return D()},set nodeClass(re){D(re),x()},get nodeBorderRadius(){return M()},set nodeBorderRadius(re){M(re),x()},get nodeStrokeWidth(){return H()},set nodeStrokeWidth(re){H(re),x()},get bgColor(){return O()},set bgColor(re){O(re),x()},get maskColor(){return V()},set maskColor(re){V(re),x()},get maskStrokeColor(){return z()},set maskStrokeColor(re){z(re),x()},get maskStrokeWidth(){return N()},set maskStrokeWidth(re){N(re),x()},get width(){return A()},set width(re){A(re),x()},get height(){return X()},set height(re){X(re),x()},get pannable(){return B()},set pannable(re){B(re),x()},get zoomable(){return Q()},set zoomable(re){Q(re),x()},get inversePan(){return ne()},set inversePan(re){ne(re),x()},get zoomStep(){return ee()},set zoomStep(re){ee(re),x()},get style(){return me()},set style(re){me(re),x()},get class(){return de()},set class(re){de(re),x()}});return o(),U}ce(Zc,{position:{},ariaLabel:{},nodeStrokeColor:{},nodeColor:{},nodeClass:{},nodeBorderRadius:{},nodeStrokeWidth:{},bgColor:{},maskColor:{},maskStrokeColor:{},maskStrokeWidth:{},width:{},height:{},pannable:{},zoomable:{},inversePan:{},zoomStep:{},style:{},class:{}},[],[],!0);const Bc=e=>(t=>"id"in t&&"position"in t&&!("source"in t)&&!("target"in t))(e);function Ht(){const{zoomIn:e,zoomOut:t,fitView:n,onbeforedelete:o,snapGrid:r,viewport:i,width:a,height:l,minZoom:u,maxZoom:s,panZoom:c,nodes:d,edges:f,domNode:g,nodeLookup:m,nodeOrigin:v,edgeLookup:y,connectionLookup:w}=Ke(),h=b=>{var k,_;const S=q(m),E=Bc(b)?b:S.get(b.id),L=E.parentId?function(D,M={width:0,height:0},H,O,V){const z={...D},N=O.get(H);if(N){const A=N.origin||V;z.x+=N.internals.positionAbsolute.x-(M.width??0)*A[0],z.y+=N.internals.positionAbsolute.y-(M.height??0)*A[1]}return z}(E.position,E.measured,E.parentId,S,q(v)):E.position,P={...E,position:L,width:((k=E.measured)==null?void 0:k.width)??E.width,height:((_=E.measured)==null?void 0:_.height)??E.height};return Zo(P)},C=b=>q(m).get(b);return{zoomIn:e,zoomOut:t,getInternalNode:C,getNode:b=>{var k;return(k=C(b))==null?void 0:k.internals.userNode},getNodes:b=>b===void 0?q(d):Rc(q(m),b),getEdge:b=>q(y).get(b),getEdges:b=>b===void 0?q(f):Rc(q(y),b),setZoom:(b,k)=>{const _=q(c);return _?_.scaleTo(b,{duration:k==null?void 0:k.duration}):Promise.resolve(!1)},getZoom:()=>q(i).zoom,setViewport:async(b,k)=>{const _=q(i),S=q(c);return S?(await S.setViewport({x:b.x??_.x,y:b.y??_.y,zoom:b.zoom??_.zoom},{duration:k==null?void 0:k.duration}),Promise.resolve(!0)):Promise.resolve(!1)},getViewport:()=>q(i),setCenter:async(b,k,_)=>{const S=typeof(_==null?void 0:_.zoom)<"u"?_.zoom:q(s),E=q(c);return E?(await E.setViewport({x:q(a)/2-b*S,y:q(l)/2-k*S,zoom:S},{duration:_==null?void 0:_.duration}),Promise.resolve(!0)):Promise.resolve(!1)},fitView:n,fitBounds:async(b,k)=>{const _=q(c);if(!_)return Promise.resolve(!1);const S=Ka(b,q(a),q(l),q(u),q(s),(k==null?void 0:k.padding)??.1);return await _.setViewport(S,{duration:k==null?void 0:k.duration}),Promise.resolve(!0)},getIntersectingNodes:(b,k=!0,_)=>{const S=$u(b),E=S?b:h(b);return E?(_||q(d)).filter(L=>{const P=q(m).get(L.id);if(!P||!S&&L.id===b.id)return!1;const D=Zo(P),M=_r(D,E);return k&&M>0||M>=E.width*E.height}):[]},isNodeIntersecting:(b,k,_=!0)=>{const S=$u(b)?b:h(b);if(!S)return!1;const E=_r(S,k);return _&&E>0||E>=S.width*S.height},deleteElements:async({nodes:b=[],edges:k=[]})=>{const{nodes:_,edges:S}=await mu({nodesToRemove:b,edgesToRemove:k,nodes:q(d),edges:q(f),onBeforeDelete:q(o)});return _&&d.update(E=>E.filter(L=>!_.some(({id:P})=>P===L.id))),S&&f.update(E=>E.filter(L=>!S.some(({id:P})=>P===L.id))),{deletedNodes:_,deletedEdges:S}},screenToFlowPosition:(b,k={snapToGrid:!0})=>{const _=q(g);if(!_)return b;const S=!!k.snapToGrid&&q(r),{x:E,y:L,zoom:P}=q(i),{x:D,y:M}=_.getBoundingClientRect(),H={x:b.x-D,y:b.y-M};return Sr(H,[E,L,P],S!==null,S||[1,1])},flowToScreenPosition:b=>{const k=q(g);if(!k)return b;const{x:_,y:S,zoom:E}=q(i),{x:L,y:P}=k.getBoundingClientRect(),D=Cu(b,[_,S,E]);return{x:D.x+L,y:D.y+P}},toObject:()=>({nodes:q(d).map(b=>({...b,position:{...b.position},data:{...b.data}})),edges:q(f).map(b=>({...b})),viewport:{...q(i)}}),updateNode:(b,k,_={replace:!1})=>{var S;const E=(S=q(m).get(b))==null?void 0:S.internals.userNode;if(!E)return;const L=typeof k=="function"?k(E):k;_.replace?d.update(P=>P.map(D=>D.id===b?Bc(L)?L:{...D,...L}:D)):(Object.assign(E,L),d.update(P=>P))},updateNodeData:(b,k,_)=>{var S;const E=(S=q(m).get(b))==null?void 0:S.internals.userNode;if(!E)return;const L=typeof k=="function"?k(E):k;E.data=_!=null&&_.replace?L:{...E.data,...L},d.update(P=>P)},getNodesBounds:b=>((k,_={nodeOrigin:[0,0],nodeLookup:void 0})=>{if(k.length===0)return{x:0,y:0,width:0,height:0};const S=k.reduce((E,L)=>{const P=typeof L=="string";let D=_.nodeLookup||P?void 0:L;_.nodeLookup&&(D=P?_.nodeLookup.get(L):Ba(L)?L:_.nodeLookup.get(L.id));const M=D?Vi(D,_.nodeOrigin):{x:0,y:0,x2:0,y2:0};return Pi(E,M)},{x:1/0,y:1/0,x2:-1/0,y2:-1/0});return Mi(S)})(b,{nodeLookup:q(m),nodeOrigin:q(v)}),getHandleConnections:({type:b,id:k,nodeId:_})=>{var S;return Array.from(((S=q(w).get(`${_}-${b}-${k??null}`))==null?void 0:S.values())??[])},viewport:i}}function Rc(e,t){var n;const o=[];for(const r of t){const i=e.get(r);if(i){const a="internals"in i?(n=i.internals)==null?void 0:n.userNode:i;o.push(a)}}return o}var Iv=ae('<div class="svelte-flow__node-toolbar"><!></div>');function Xc(e,t){ve(t,!1);const[n,o]=Je(),r=()=>ie(C,"$nodes",n),i=()=>ie(h,"$nodeLookup",n),a=()=>ie(w,"$viewport",n),l=()=>ie(y,"$domNode",n),u=se(),s=se(),c=se();let d=$(t,"nodeId",12,void 0),f=$(t,"position",12,void 0),g=$(t,"align",12,void 0),m=$(t,"offset",12,void 0),v=$(t,"isVisible",12,void 0);const{domNode:y,viewport:w,nodeLookup:h,nodes:C}=Ke(),{getNodesBounds:b}=Ht(),k=uo("svelteflow__node_id");let _=se(),S=se([]),E=m()!==void 0?m():10,L=f()!==void 0?f():Ee.Top,P=g()!==void 0?g():"center";he(()=>(r(),oe(d()),i()),()=>{r();const V=Array.isArray(d())?d():[d()||k];te(S,V.reduce((z,N)=>{const A=i().get(N);return A&&z.push(A),z},[]))}),he(()=>(p(S),a()),()=>{const V=b(p(S));V&&te(_,function(z,N,A,X,B){let Q=.5;B==="start"?Q=0:B==="end"&&(Q=1);let ne=[(z.x+z.width*Q)*N.zoom+N.x,z.y*N.zoom+N.y-X],ee=[-100*Q,-100];switch(A){case Ee.Right:ne=[(z.x+z.width)*N.zoom+N.x+X,(z.y+z.height*Q)*N.zoom+N.y],ee=[0,-100*Q];break;case Ee.Bottom:ne[1]=(z.y+z.height)*N.zoom+N.y+X,ee[1]=0;break;case Ee.Left:ne=[z.x*N.zoom+N.x-X,(z.y+z.height*Q)*N.zoom+N.y],ee=[-100,-100*Q]}return`translate(${ne[0]}px, ${ne[1]}px) translate(${ee[0]}%, ${ee[1]}%)`}(V,a(),L,E,P))}),he(()=>p(S),()=>{te(u,p(S).length===0?1:Math.max(...p(S).map(V=>(V.internals.z||5)+1)))}),he(()=>r(),()=>{te(s,r().filter(V=>V.selected).length)}),he(()=>(oe(v()),p(S),p(s)),()=>{te(c,typeof v()=="boolean"?v():p(S).length===1&&p(S)[0].selected&&p(s)===1)}),ut(),Le();var D=Ue(),M=ke(D),H=V=>{var z=Iv();ft(W(z),t,"default",{}),K(z),vt(z,(N,A)=>Di==null?void 0:Di(N,A),()=>({domNode:l()})),Me(N=>{pe(z,"data-id",N),nt(z,"position","absolute"),nt(z,"transform",p(_)),nt(z,"z-index",p(u))},[()=>p(S).reduce((N,A)=>`${N}${A.id} `,"").trim()],ye),T(V,z)};_e(M,V=>{l()&&p(c)&&p(S)&&V(H)}),T(e,D);var O=ge({get nodeId(){return d()},set nodeId(V){d(V),x()},get position(){return f()},set position(V){f(V),x()},get align(){return g()},set align(V){g(V),x()},get offset(){return m()},set offset(V){m(V),x()},get isVisible(){return v()},set isVisible(V){v(V),x()}});return o(),O}function yo(e){const{nodes:t,nodeLookup:n}=Ke();let o=[],r=!0;return Fn([t,n],([,i],a)=>{var l;const u=[],s=Array.isArray(e),c=s?e:[e];for(const d of c){const f=(l=i.get(d))==null?void 0:l.internals.userNode;f&&u.push({id:f.id,type:f.type,data:f.data})}(!function(d,f){if(d===null||f===null)return!1;const g=Array.isArray(d)?d:[d],m=Array.isArray(f)?f:[f];if(g.length!==m.length)return!1;for(let v=0;v<g.length;v++)if(g[v].id!==m[v].id||g[v].type!==m[v].type||!Object.is(g[v].data,m[v].data))return!1;return!0}(u,o)||r)&&(o=u,a(s?u:u[0]??null),r=!1)})}ce(Xc,{nodeId:{},position:{},align:{},offset:{},isVisible:{}},["default"],[],!0);const Yc="tinyflow-component";class Zv{constructor(t){if(kt(this,"options"),kt(this,"rootEl"),kt(this,"svelteFlowInstance"),typeof t.element!="string"&&!(t.element instanceof Element))throw new Error("element must be a string or Element");this._setOptions(t),this._init()}_init(){if(typeof this.options.element=="string"){if(this.rootEl=document.querySelector(this.options.element),!this.rootEl)throw new Error(`element not found by document.querySelector('${this.options.element}')`)}else{if(!(this.options.element instanceof Element))throw new Error("element must be a string or Element");this.rootEl=this.options.element}const t=document.createElement(Yc);t.style.display="block",t.style.width="100%",t.style.height="100%",t.classList.add("tf-theme-light"),t.options=this.options,t.onInit=n=>{this.svelteFlowInstance=n},this.rootEl.appendChild(t)}_setOptions(t){this.options={...t}}getOptions(){return this.options}getData(){return this.svelteFlowInstance.toObject()}setData(t){this.options.data=t;const n=document.createElement(Yc);n.style.display="block",n.style.width="100%",n.style.height="100%",n.classList.add("tf-theme-light"),n.options=this.options,n.onInit=o=>{this.svelteFlowInstance=o},this.destroy(),this.rootEl.appendChild(n)}destroy(){for(;this.rootEl.firstChild;)this.rootEl.removeChild(this.rootEl.firstChild)}}const Zi=(()=>{const e=be([]),t=be([]),n=be({x:250,y:100,zoom:1});return{nodes:e,edges:t,viewport:n,init:(o,r)=>{e.set(o),t.set(r)},addNode:o=>{e.update(r=>[...r,o])},removeNode:o=>{e.update(r=>r.filter(i=>i.id!==o))},updateNode:(o,r)=>{e.update(i=>i.map(a=>a.id===o?r:a))},updateNodeData:(o,r)=>{e.update(i=>i.map(a=>a.id===o?{...a,data:{...a.data,...r}}:a))},selectNodeOnly:o=>{e.update(r=>r.map(i=>i.id===o?{...i,selected:!0}:{...i,selected:!1}))},addEdge:o=>{t.update(r=>[...r,o])},removeEdge:o=>{t.update(r=>r.filter(i=>i.id!==o))},updateEdge:(o,r)=>{t.update(i=>i.map(a=>a.id===o?r:a))},updateEdgeData:(o,r)=>{t.update(i=>i.map(a=>a.id===o?{...a,data:r}:a))}}})();var Bv=ae("<button><!></button>");function Ye(e,t){ve(t,!0);const n=$(t,"children",7),o=gt(t,["$$slots","$$events","$$legacy","$$host","children"]);var r=Bv();let i;return co(W(r),()=>n()??st),K(r),Me(()=>i=en(r,i,{type:"button",...o,class:`tf-btn nopan nodrag ${t.class??""}`})),T(e,r),ge({get children(){return n()},set children(a){n(a),x()}})}ce(Ye,{children:{}},[],[],!0);var Rv=ae("<input>");function Kc(e,t){ve(t,!0);const n=gt(t,["$$slots","$$events","$$legacy","$$host"]);var o=Rv();let r;ur(o),Me(()=>r=en(o,r,{type:"checkbox",...n,class:`tf-checkbox nopan nodrag ${t.class??""}`})),T(e,o),ge()}ce(Kc,{},[],[],!0);var Xv=ae("<input>");function $t(e,t){ve(t,!0);const n=gt(t,["$$slots","$$events","$$legacy","$$host"]);var o=Xv();let r;ur(o),Me(()=>r=en(o,r,{type:"text",...n,class:`tf-input  nopan nodrag ${t.class??""}`})),T(e,o),ge()}ce($t,{},[],[],!0);var Yv=ae("<textarea></textarea>");function Ct(e,t){ve(t,!0);const n=gt(t,["$$slots","$$events","$$legacy","$$host"]);var o=Yv();let r;(function(i){Ve&&yt(i)!==null&&ca(i)})(o),Me(()=>r=en(o,r,{...n,class:`tf-textarea nodrag ${t.class??""}`})),T(e,o),ge()}ce(Ct,{},[],[],!0);var Kv=ae('<div role="button"><!></div>'),Wv=ae("<div></div>");function Wc(e,t){const n=et(t,["children","$$slots","$$events","$$legacy","$$host"]),o=et(n,["items","onChange","activeIndex"]);ve(t,!1);let r=$(t,"items",28,()=>[]),i=$(t,"onChange",12,()=>{}),a=$(t,"activeIndex",12,0);function l(c,d){var f;a(d),(f=i())==null||f(c,d)}Le();var u=Wv();let s;return At(u,5,r,ni,(c,d,f)=>{var g=Kv();pe(g,"tabindex",f),g.__click=()=>l(p(d),f),g.__keydown=w=>{(w.key==="Enter"||w.key===" ")&&(w.preventDefault(),l(p(d),f))};var m=W(g),v=w=>{var h=Te();Me(()=>Tt(h,p(d).label)),T(w,h)},y=w=>{var h=Ue();co(ke(h),()=>p(d).label??st),T(w,h)};_e(m,w=>{typeof p(d).label=="string"?w(v):w(y,!1)}),K(g),Me(()=>bt(g,1,`tf-tabs-item ${(f===a()?"active":"")??""}`)),T(c,g)}),K(u),Me(()=>s=en(u,s,{...o,class:`tf-tabs ${o.class??""}`})),T(e,u),ge({get items(){return r()},set items(c){r(c),x()},get onChange(){return i()},set onChange(c){i(c),x()},get activeIndex(){return a()},set activeIndex(c){a(c),x()}})}ti(["click","keydown"]),ce(Wc,{items:{},onChange:{},activeIndex:{}},[],[],!0);var jv=(e,t,n)=>t(p(n)),qv=(e,t,n)=>{(e.key==="Enter"||e.key===" ")&&(e.preventDefault(),t(p(n)))},Fv=ae('<span class="tf-collapse-item-title-icon"><!></span>'),Gv=ae('<div class="tf-collapse-item-description"><!></div>'),Uv=ae('<div class="tf-collapse-item-content"><!></div>'),Jv=ae('<div class="tf-collapse-item"><div class="tf-collapse-item-title" role="button"><!> <!> <span><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M13.1717 12.0007L8.22192 7.05093L9.63614 5.63672L16.0001 12.0007L9.63614 18.3646L8.22192 16.9504L13.1717 12.0007Z"></path></svg></span></div> <!> <!></div>'),Qv=ae("<div></div>");const eg={hash:"svelte-1jfktzw",code:`\r
    /* \u5B9A\u4E49\u65CB\u8F6C\u7684 CSS \u7C7B */.rotate-90.svelte-1jfktzw {transform:rotate(90deg);transition:transform 0.3s ease;}`};function jc(e,t){ve(t,!0),qe(e,eg);let n=$(t,"items",7),o=$(t,"onChange",7),r=$(t,"activeKeys",31,()=>Et([]));function i(l){var u;r().includes(l.key)?r(r().filter(s=>s!==l.key)):(r().push(l.key),r(r())),(u=o())==null||u(l,r())}var a=Qv();return At(a,21,n,ni,(l,u,s)=>{var c=Jv(),d=W(c);pe(d,"tabindex",s),d.__click=[jv,i,u],d.__keydown=[qv,i,u];var f=W(d),g=b=>{var k=Fv();no(W(k),{get target(){return p(u).icon}}),K(k),T(b,k)};_e(f,b=>{p(u).icon&&b(g)});var m=Z(f,2);no(m,{get target(){return p(u).title}});var v=Z(m,2);K(d);var y=Z(d,2),w=b=>{var k=Gv();no(W(k),{get target(){return p(u).description}}),K(k),T(b,k)};_e(y,b=>{p(u).description&&b(w)});var h=Z(y,2),C=b=>{var k=Uv();no(W(k),{get target(){return p(u).content}}),K(k),T(b,k)};_e(h,b=>{r().includes(p(u).key)&&b(C)}),K(c),Me(b=>bt(v,1,`tf-collapse-item-title-arrow ${b??""}`,"svelte-1jfktzw"),[()=>r().includes(p(u).key)?"rotate-90":""]),T(l,c)}),K(a),Me(()=>{pe(a,"style",t.style),bt(a,1,`tf-collapse ${t.class??""}`,"svelte-1jfktzw")}),T(e,a),ge({get items(){return n()},set items(l){n(l),x()},get onChange(){return o()},set onChange(l){o(l),x()},get activeKeys(){return r()},set activeKeys(l=[]){r(l),x()}})}function no(e,t){ve(t,!0);let n=$(t,"target",7);typeof n()>"u"&&n("undefined");var o=Ue(),r=ke(o),i=l=>{var u=Ue();co(ke(u),()=>n()??st),T(l,u)},a=l=>{var u=Ue();cs(ke(u),n),T(l,u)};return _e(r,l=>{typeof n()=="function"?l(i):l(a,!1)}),T(e,o),ge({get target(){return n()},set target(l){n(l),x()}})}ti(["click","keydown"]),ce(jc,{items:{},onChange:{},activeKeys:{}},[],[],!0),ce(no,{target:{}},[],[],!0);var tg=(e,t,n)=>t(p(n)),ng=xe('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M12 14L8 10H16L12 14Z"></path></svg>'),og=ae('<div class="tf-select-content-children"><!></div>'),rg=ae('<button class="tf-select-content-item"><span><!></span> <!></button> <!>',1),ig=ae('<div class="tf-select-content nopan nodrag"><!></div>'),ag=ae("<!> <!>",1),lg=ae('<div class="tf-select-input-placeholder"> </div>'),sg=ae('<button><div class="tf-select-input-value"></div> <div class="tf-select-input-arrow"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M11.9999 13.1714L16.9497 8.22168L18.3639 9.63589L11.9999 15.9999L5.63599 9.63589L7.0502 8.22168L11.9999 13.1714Z"></path></svg></div></button>'),ug=ae("<div><!></div>");function ln(e,t){ve(t,!0);const n=(h,C=st)=>{var b=Ue();At(ke(b),19,C,(k,_)=>`${_}_${k.value}`,(k,_)=>{var S=rg(),E=ke(S);E.__click=[tg,v,_];var L=W(E),P=W(L),D=O=>{T(O,ng())};_e(P,O=>{p(_).children&&p(_).children.length>0&&O(D)}),K(L),no(Z(L,2),{get target(){return p(_).label}}),K(E);var M=Z(E,2),H=O=>{var V=og(),z=W(V);n(z,()=>p(_).children),K(V),T(O,V)};_e(M,O=>{p(_).children&&p(_).children.length>0&&(s()||d().includes(p(_).value))&&O(H)}),T(k,S)}),T(h,b)};let o,r=$(t,"items",7),i=$(t,"onExpand",7),a=$(t,"onSelect",7),l=$(t,"value",23,()=>[]),u=$(t,"defaultValue",23,()=>[]),s=$(t,"expandAll",7,!0),c=$(t,"multiple",7,!1),d=$(t,"expandValue",23,()=>[]),f=$(t,"placeholder",7),g=gt(t,["$$slots","$$events","$$legacy","$$host","items","onExpand","onSelect","value","defaultValue","expandAll","multiple","expandValue","placeholder"]),m=ze(()=>{const h=[],C=b=>{for(let k of b)l().length>0?l().includes(k.value)&&h.push(k):u().includes(k.value)&&h.push(k),k.children&&k.children.length>0&&C(k.children)};return C(r()),h});function v(h){var C,b;h.children&&h.children.length>0?(C=i())==null||C(h):(o==null||o.hide(),(b=a())==null||b(h))}var y=ug();let w;return Vn(Dr(W(y),{floating:h=>{var C=ig(),b=W(C);n(b,r),K(C),T(h,C)},children:(h,C)=>{var b=sg();let k;var _=W(b);At(_,23,()=>p(m),(S,E)=>`${E}_${S.value}`,(S,E,L)=>{var P=Ue(),D=ke(P),M=O=>{var V=Ue(),z=ke(V),N=A=>{no(A,{get target(){return p(E).label}})};_e(z,A=>{p(L)===0&&A(N)}),T(O,V)},H=O=>{var V=ag(),z=ke(V);no(z,{get target(){return p(E).label}});var N=Z(z,2),A=X=>{T(X,Te(","))};_e(N,X=>{p(L)<p(m).length-1&&X(A)}),T(O,V)};_e(D,O=>{c()?O(H,!1):O(M)}),T(S,P)},S=>{var E=lg(),L=W(E,!0);K(E),Me(()=>Tt(L,f())),T(S,E)}),K(_),He(2),K(b),Me(()=>k=en(b,k,{class:"tf-select-input nopan nodrag",...g})),T(h,b)},$$slots:{floating:!0,default:!0}}),h=>o=h,()=>o),K(y),Me(()=>w=en(y,w,{...g,class:`tf-select ${g.class??""}`})),T(e,y),ge({get items(){return r()},set items(h){r(h),x()},get onExpand(){return i()},set onExpand(h){i(h),x()},get onSelect(){return a()},set onSelect(h){a(h),x()},get value(){return l()},set value(h=[]){l(h),x()},get defaultValue(){return u()},set defaultValue(h=[]){u(h),x()},get expandAll(){return s()},set expandAll(h=!0){s(h),x()},get multiple(){return c()},set multiple(h=!1){c(h),x()},get expandValue(){return d()},set expandValue(h=[]){d(h),x()},get placeholder(){return f()},set placeholder(h){f(h),x()}})}ti(["click"]),ce(ln,{items:{},onExpand:{},onSelect:{},value:{},defaultValue:{},expandAll:{},multiple:{},expandValue:{},placeholder:{}},[],[],!0);const Hr=Math.min,Xo=Math.max,Bi=Math.round,bn=e=>({x:e,y:e}),cg={left:"right",right:"left",bottom:"top",top:"bottom"},dg={start:"end",end:"start"};function rl(e,t,n){return Xo(e,Hr(t,n))}function zr(e,t){return typeof e=="function"?e(t):e}function wo(e){return e.split("-")[0]}function Nr(e){return e.split("-")[1]}function qc(e){return e==="x"?"y":"x"}function il(e){return e==="y"?"height":"width"}function Yo(e){return["top","bottom"].includes(wo(e))?"y":"x"}function al(e){return qc(Yo(e))}function ll(e){return e.replace(/start|end/g,t=>dg[t])}function Ri(e){return e.replace(/left|right|bottom|top/g,t=>cg[t])}function Fc(e){return typeof e!="number"?function(t){return{top:0,right:0,bottom:0,left:0,...t}}(e):{top:e,right:e,bottom:e,left:e}}function Xi(e){const{x:t,y:n,width:o,height:r}=e;return{width:o,height:r,top:n,left:t,right:t+o,bottom:n+r,x:t,y:n}}function Gc(e,t,n){let{reference:o,floating:r}=e;const i=Yo(t),a=al(t),l=il(a),u=wo(t),s=i==="y",c=o.x+o.width/2-r.width/2,d=o.y+o.height/2-r.height/2,f=o[l]/2-r[l]/2;let g;switch(u){case"top":g={x:c,y:o.y-r.height};break;case"bottom":g={x:c,y:o.y+o.height};break;case"right":g={x:o.x+o.width,y:d};break;case"left":g={x:o.x-r.width,y:d};break;default:g={x:o.x,y:o.y}}switch(Nr(t)){case"start":g[a]-=f*(n&&s?-1:1);break;case"end":g[a]+=f*(n&&s?-1:1)}return g}async function Uc(e,t){var n;t===void 0&&(t={});const{x:o,y:r,platform:i,rects:a,elements:l,strategy:u}=e,{boundary:s="clippingAncestors",rootBoundary:c="viewport",elementContext:d="floating",altBoundary:f=!1,padding:g=0}=zr(t,e),m=Fc(g),v=l[f?d==="floating"?"reference":"floating":d],y=Xi(await i.getClippingRect({element:(n=await(i.isElement==null?void 0:i.isElement(v)))==null||n?v:v.contextElement||await(i.getDocumentElement==null?void 0:i.getDocumentElement(l.floating)),boundary:s,rootBoundary:c,strategy:u})),w=d==="floating"?{x:o,y:r,width:a.floating.width,height:a.floating.height}:a.reference,h=await(i.getOffsetParent==null?void 0:i.getOffsetParent(l.floating)),C=await(i.isElement==null?void 0:i.isElement(h))&&await(i.getScale==null?void 0:i.getScale(h))||{x:1,y:1},b=Xi(i.convertOffsetParentRelativeRectToViewportRelativeRect?await i.convertOffsetParentRelativeRectToViewportRelativeRect({elements:l,rect:w,offsetParent:h,strategy:u}):w);return{top:(y.top-b.top+m.top)/C.y,bottom:(b.bottom-y.bottom+m.bottom)/C.y,left:(y.left-b.left+m.left)/C.x,right:(b.right-y.right+m.right)/C.x}}function Yi(){return typeof window<"u"}function Ko(e){return Jc(e)?(e.nodeName||"").toLowerCase():"#document"}function Zt(e){var t;return(e==null||(t=e.ownerDocument)==null?void 0:t.defaultView)||window}function Dn(e){var t;return(t=(Jc(e)?e.ownerDocument:e.document)||window.document)==null?void 0:t.documentElement}function Jc(e){return!!Yi()&&(e instanceof Node||e instanceof Zt(e).Node)}function sn(e){return!!Yi()&&(e instanceof Element||e instanceof Zt(e).Element)}function xn(e){return!!Yi()&&(e instanceof HTMLElement||e instanceof Zt(e).HTMLElement)}function Qc(e){return!(!Yi()||typeof ShadowRoot>"u")&&(e instanceof ShadowRoot||e instanceof Zt(e).ShadowRoot)}function Lr(e){const{overflow:t,overflowX:n,overflowY:o,display:r}=un(e);return/auto|scroll|overlay|hidden|clip/.test(t+o+n)&&!["inline","contents"].includes(r)}function fg(e){return["table","td","th"].includes(Ko(e))}function Ki(e){return[":popover-open",":modal"].some(t=>{try{return e.matches(t)}catch{return!1}})}function sl(e){const t=ul(),n=sn(e)?un(e):e;return["transform","translate","scale","rotate","perspective"].some(o=>!!n[o]&&n[o]!=="none")||!!n.containerType&&n.containerType!=="normal"||!t&&!!n.backdropFilter&&n.backdropFilter!=="none"||!t&&!!n.filter&&n.filter!=="none"||["transform","translate","scale","rotate","perspective","filter"].some(o=>(n.willChange||"").includes(o))||["paint","layout","strict","content"].some(o=>(n.contain||"").includes(o))}function ul(){return!(typeof CSS>"u"||!CSS.supports)&&CSS.supports("-webkit-backdrop-filter","none")}function Wo(e){return["html","body","#document"].includes(Ko(e))}function un(e){return Zt(e).getComputedStyle(e)}function Wi(e){return sn(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function oo(e){if(Ko(e)==="html")return e;const t=e.assignedSlot||e.parentNode||Qc(e)&&e.host||Dn(e);return Qc(t)?t.host:t}function ed(e){const t=oo(e);return Wo(t)?e.ownerDocument?e.ownerDocument.body:e.body:xn(t)&&Lr(t)?t:ed(t)}function td(e,t,n){var o;t===void 0&&(t=[]);const r=ed(e),i=r===((o=e.ownerDocument)==null?void 0:o.body),a=Zt(r);return i?(cl(a),t.concat(a,a.visualViewport||[],Lr(r)?r:[],[])):t.concat(r,td(r,[]))}function cl(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function nd(e){const t=un(e);let n=parseFloat(t.width)||0,o=parseFloat(t.height)||0;const r=xn(e),i=r?e.offsetWidth:n,a=r?e.offsetHeight:o,l=Bi(n)!==i||Bi(o)!==a;return l&&(n=i,o=a),{width:n,height:o,$:l}}function od(e){return sn(e)?e:e.contextElement}function jo(e){const t=od(e);if(!xn(t))return bn(1);const n=t.getBoundingClientRect(),{width:o,height:r,$:i}=nd(t);let a=(i?Bi(n.width):n.width)/o,l=(i?Bi(n.height):n.height)/r;return(!a||!Number.isFinite(a))&&(a=1),(!l||!Number.isFinite(l))&&(l=1),{x:a,y:l}}const vg=bn(0);function rd(e){const t=Zt(e);return ul()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:vg}function Or(e,t,n,o){t===void 0&&(t=!1),n===void 0&&(n=!1);const r=e.getBoundingClientRect(),i=od(e);let a=bn(1);t&&(o?sn(o)&&(a=jo(o)):a=jo(e));const l=function(f,g,m){return g===void 0&&(g=!1),!(!m||g&&m!==Zt(f))&&g}(i,n,o)?rd(i):bn(0);let u=(r.left+l.x)/a.x,s=(r.top+l.y)/a.y,c=r.width/a.x,d=r.height/a.y;if(i){const f=Zt(i),g=o&&sn(o)?Zt(o):o;let m=f,v=cl(m);for(;v&&o&&g!==m;){const y=jo(v),w=v.getBoundingClientRect(),h=un(v),C=w.left+(v.clientLeft+parseFloat(h.paddingLeft))*y.x,b=w.top+(v.clientTop+parseFloat(h.paddingTop))*y.y;u*=y.x,s*=y.y,c*=y.x,d*=y.y,u+=C,s+=b,m=Zt(v),v=cl(m)}}return Xi({width:c,height:d,x:u,y:s})}function dl(e,t){const n=Wi(e).scrollLeft;return t?t.left+n:Or(Dn(e)).left+n}function id(e,t,n){n===void 0&&(n=!1);const o=e.getBoundingClientRect();return{x:o.left+t.scrollLeft-(n?0:dl(e,o)),y:o.top+t.scrollTop}}function ad(e,t,n){let o;if(t==="viewport")o=function(r,i){const a=Zt(r),l=Dn(r),u=a.visualViewport;let s=l.clientWidth,c=l.clientHeight,d=0,f=0;if(u){s=u.width,c=u.height;const g=ul();(!g||g&&i==="fixed")&&(d=u.offsetLeft,f=u.offsetTop)}return{width:s,height:c,x:d,y:f}}(e,n);else if(t==="document")o=function(r){const i=Dn(r),a=Wi(r),l=r.ownerDocument.body,u=Xo(i.scrollWidth,i.clientWidth,l.scrollWidth,l.clientWidth),s=Xo(i.scrollHeight,i.clientHeight,l.scrollHeight,l.clientHeight);let c=-a.scrollLeft+dl(r);const d=-a.scrollTop;return un(l).direction==="rtl"&&(c+=Xo(i.clientWidth,l.clientWidth)-u),{width:u,height:s,x:c,y:d}}(Dn(e));else if(sn(t))o=function(r,i){const a=Or(r,!0,i==="fixed"),l=a.top+r.clientTop,u=a.left+r.clientLeft,s=xn(r)?jo(r):bn(1);return{width:r.clientWidth*s.x,height:r.clientHeight*s.y,x:u*s.x,y:l*s.y}}(t,n);else{const r=rd(e);o={x:t.x-r.x,y:t.y-r.y,width:t.width,height:t.height}}return Xi(o)}function ld(e,t){const n=oo(e);return!(n===t||!sn(n)||Wo(n))&&(un(n).position==="fixed"||ld(n,t))}function gg(e,t){const n=t.get(e);if(n)return n;let o=td(e,[]).filter(l=>sn(l)&&Ko(l)!=="body"),r=null;const i=un(e).position==="fixed";let a=i?oo(e):e;for(;sn(a)&&!Wo(a);){const l=un(a),u=sl(a);!u&&l.position==="fixed"&&(r=null),(i?!u&&!r:!u&&l.position==="static"&&r&&["absolute","fixed"].includes(r.position)||Lr(a)&&!u&&ld(e,a))?o=o.filter(s=>s!==a):r=l,a=oo(a)}return t.set(e,o),o}function pg(e,t,n){const o=xn(t),r=Dn(t),i=n==="fixed",a=Or(e,!0,i,t);let l={scrollLeft:0,scrollTop:0};const u=bn(0);if(o||!o&&!i)if((Ko(t)!=="body"||Lr(r))&&(l=Wi(t)),o){const c=Or(t,!0,i,t);u.x=c.x+t.clientLeft,u.y=c.y+t.clientTop}else r&&(u.x=dl(r));const s=!r||o||i?bn(0):id(r,l);return{x:a.left+l.scrollLeft-u.x-s.x,y:a.top+l.scrollTop-u.y-s.y,width:a.width,height:a.height}}function fl(e){return un(e).position==="static"}function sd(e,t){if(!xn(e)||un(e).position==="fixed")return null;if(t)return t(e);let n=e.offsetParent;return Dn(e)===n&&(n=n.ownerDocument.body),n}function ud(e,t){const n=Zt(e);if(Ki(e))return n;if(!xn(e)){let r=oo(e);for(;r&&!Wo(r);){if(sn(r)&&!fl(r))return r;r=oo(r)}return n}let o=sd(e,t);for(;o&&fg(o)&&fl(o);)o=sd(o,t);return o&&Wo(o)&&fl(o)&&!sl(o)?n:o||function(r){let i=oo(r);for(;xn(i)&&!Wo(i);){if(sl(i))return i;if(Ki(i))return null;i=oo(i)}return null}(e)||n}const hg={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:n,offsetParent:o,strategy:r}=e;const i=r==="fixed",a=Dn(o),l=!!t&&Ki(t.floating);if(o===a||l&&i)return n;let u={scrollLeft:0,scrollTop:0},s=bn(1);const c=bn(0),d=xn(o);if((d||!d&&!i)&&((Ko(o)!=="body"||Lr(a))&&(u=Wi(o)),xn(o))){const g=Or(o);s=jo(o),c.x=g.x+o.clientLeft,c.y=g.y+o.clientTop}const f=!a||d||i?bn(0):id(a,u,!0);return{width:n.width*s.x,height:n.height*s.y,x:n.x*s.x-u.scrollLeft*s.x+c.x+f.x,y:n.y*s.y-u.scrollTop*s.y+c.y+f.y}},getDocumentElement:Dn,getClippingRect:function(e){let{element:t,boundary:n,rootBoundary:o,strategy:r}=e;const i=[...n==="clippingAncestors"?Ki(t)?[]:gg(t,this._c):[].concat(n),o],a=i[0],l=i.reduce((u,s)=>{const c=ad(t,s,r);return u.top=Xo(c.top,u.top),u.right=Hr(c.right,u.right),u.bottom=Hr(c.bottom,u.bottom),u.left=Xo(c.left,u.left),u},ad(t,a,r));return{width:l.right-l.left,height:l.bottom-l.top,x:l.left,y:l.top}},getOffsetParent:ud,getElementRects:async function(e){const t=this.getOffsetParent||ud,n=this.getDimensions,o=await n(e.floating);return{reference:pg(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:o.width,height:o.height}}},getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){const{width:t,height:n}=nd(e);return{width:t,height:n}},getScale:jo,isElement:sn,isRTL:function(e){return un(e).direction==="rtl"}},mg=function(e){return e===void 0&&(e=0),{name:"offset",options:e,async fn(t){var n,o;const{x:r,y:i,placement:a,middlewareData:l}=t,u=await async function(s,c){const{placement:d,platform:f,elements:g}=s,m=await(f.isRTL==null?void 0:f.isRTL(g.floating)),v=wo(d),y=Nr(d),w=Yo(d)==="y",h=["left","top"].includes(v)?-1:1,C=m&&w?-1:1,b=zr(c,s);let{mainAxis:k,crossAxis:_,alignmentAxis:S}=typeof b=="number"?{mainAxis:b,crossAxis:0,alignmentAxis:null}:{mainAxis:b.mainAxis||0,crossAxis:b.crossAxis||0,alignmentAxis:b.alignmentAxis};return y&&typeof S=="number"&&(_=y==="end"?-1*S:S),w?{x:_*C,y:k*h}:{x:k*h,y:_*C}}(t,e);return a===((n=l.offset)==null?void 0:n.placement)&&(o=l.arrow)!=null&&o.alignmentOffset?{}:{x:r+u.x,y:i+u.y,data:{...u,placement:a}}}}},yg=function(e){return e===void 0&&(e={}),{name:"shift",options:e,async fn(t){const{x:n,y:o,placement:r}=t,{mainAxis:i=!0,crossAxis:a=!1,limiter:l={fn:y=>{let{x:w,y:h}=y;return{x:w,y:h}}},...u}=zr(e,t),s={x:n,y:o},c=await Uc(t,u),d=Yo(wo(r)),f=qc(d);let g=s[f],m=s[d];if(i){const y=f==="y"?"bottom":"right";g=rl(g+c[f==="y"?"top":"left"],g,g-c[y])}if(a){const y=d==="y"?"bottom":"right";m=rl(m+c[d==="y"?"top":"left"],m,m-c[y])}const v=l.fn({...t,[f]:g,[d]:m});return{...v,data:{x:v.x-n,y:v.y-o,enabled:{[f]:i,[d]:a}}}}}},wg=function(e){return e===void 0&&(e={}),{name:"flip",options:e,async fn(t){var n,o;const{placement:r,middlewareData:i,rects:a,initialPlacement:l,platform:u,elements:s}=t,{mainAxis:c=!0,crossAxis:d=!0,fallbackPlacements:f,fallbackStrategy:g="bestFit",fallbackAxisSideDirection:m="none",flipAlignment:v=!0,...y}=zr(e,t);if((n=i.arrow)!=null&&n.alignmentOffset)return{};const w=wo(r),h=Yo(l),C=wo(l)===l,b=await(u.isRTL==null?void 0:u.isRTL(s.floating)),k=f||(C||!v?[Ri(l)]:function(O){const V=Ri(O);return[ll(O),V,ll(V)]}(l)),_=m!=="none";!f&&_&&k.push(...function(O,V,z,N){const A=Nr(O);let X=function(B,Q,ne){const ee=["left","right"],me=["right","left"],de=["top","bottom"],J=["bottom","top"];switch(B){case"top":case"bottom":return ne?Q?me:ee:Q?ee:me;case"left":case"right":return Q?de:J;default:return[]}}(wo(O),z==="start",N);return A&&(X=X.map(B=>B+"-"+A),V&&(X=X.concat(X.map(ll)))),X}(l,v,m,b));const S=[l,...k],E=await Uc(t,y),L=[];let P=((o=i.flip)==null?void 0:o.overflows)||[];if(c&&L.push(E[w]),d){const O=function(V,z,N){N===void 0&&(N=!1);const A=Nr(V),X=al(V),B=il(X);let Q=X==="x"?A===(N?"end":"start")?"right":"left":A==="start"?"bottom":"top";return z.reference[B]>z.floating[B]&&(Q=Ri(Q)),[Q,Ri(Q)]}(r,a,b);L.push(E[O[0]],E[O[1]])}if(P=[...P,{placement:r,overflows:L}],!L.every(O=>O<=0)){var D,M;const O=(((D=i.flip)==null?void 0:D.index)||0)+1,V=S[O];if(V)return{data:{index:O,overflows:P},reset:{placement:V}};let z=(M=P.filter(N=>N.overflows[0]<=0).sort((N,A)=>N.overflows[1]-A.overflows[1])[0])==null?void 0:M.placement;if(!z)switch(g){case"bestFit":{var H;const N=(H=P.filter(A=>{if(_){const X=Yo(A.placement);return X===h||X==="y"}return!0}).map(A=>[A.placement,A.overflows.filter(X=>X>0).reduce((X,B)=>X+B,0)]).sort((A,X)=>A[1]-X[1])[0])==null?void 0:H[0];N&&(z=N);break}case"initialPlacement":z=l}if(r!==z)return{reset:{placement:z}}}return{}}}},bg=e=>({name:"arrow",options:e,async fn(t){const{x:n,y:o,placement:r,rects:i,platform:a,elements:l,middlewareData:u}=t,{element:s,padding:c=0}=zr(e,t)||{};if(s==null)return{};const d=Fc(c),f={x:n,y:o},g=al(r),m=il(g),v=await a.getDimensions(s),y=g==="y",w=y?"top":"left",h=y?"bottom":"right",C=y?"clientHeight":"clientWidth",b=i.reference[m]+i.reference[g]-f[g]-i.floating[m],k=f[g]-i.reference[g],_=await(a.getOffsetParent==null?void 0:a.getOffsetParent(s));let S=_?_[C]:0;(!S||!await(a.isElement==null?void 0:a.isElement(_)))&&(S=l.floating[C]||i.floating[m]);const E=b/2-k/2,L=S/2-v[m]/2-1,P=Hr(d[w],L),D=Hr(d[h],L),M=P,H=S-v[m]-D,O=S/2-v[m]/2+E,V=rl(M,O,H),z=!u.arrow&&Nr(r)!=null&&O!==V&&i.reference[m]/2-(O<M?P:D)-v[m]/2<0,N=z?O<M?O-M:O-H:0;return{[g]:f[g]+N,data:{[g]:V,centerOffset:O-V-N,...z&&{alignmentOffset:N}},reset:z}}}),xg=(e,t,n)=>{const o=new Map,r={platform:hg,...n},i={...r.platform,_c:o};return(async(a,l,u)=>{const{placement:s="bottom",strategy:c="absolute",middleware:d=[],platform:f}=u,g=d.filter(Boolean),m=await(f.isRTL==null?void 0:f.isRTL(l));let v=await f.getElementRects({reference:a,floating:l,strategy:c}),{x:y,y:w}=Gc(v,s,m),h=s,C={},b=0;for(let k=0;k<g.length;k++){const{name:_,fn:S}=g[k],{x:E,y:L,data:P,reset:D}=await S({x:y,y:w,initialPlacement:s,placement:h,strategy:c,middlewareData:C,rects:v,platform:f,elements:{reference:a,floating:l}});y=E??y,w=L??w,C={...C,[_]:{...C[_],...P}},D&&b<=50&&(b++,typeof D=="object"&&(D.placement&&(h=D.placement),D.rects&&(v=D.rects===!0?await f.getElementRects({reference:a,floating:l,strategy:c}):D.rects),{x:y,y:w}=Gc(v,h,m)),k=-1)}return{x:y,y:w,placement:h,strategy:c,middlewareData:C}})(e,t,{...r,platform:i})},$g=({trigger:e,triggerEvent:t,floatContent:n,placement:o="bottom",offsetOptions:r,flipOptions:i,shiftOptions:a,interactive:l,showArrow:u})=>{if(typeof e=="string"){const y=document.querySelector(e);if(!y)throw new Error("element not found by document.querySelector('"+e+"')");e=y}let s,c;if(typeof n=="string"){const y=document.querySelector(n);if(!y)throw new Error("element not found by document.querySelector('"+n+"')");s=y}else s=n;u&&(c=document.createElement("div"),c.style.position="absolute",c.style.backgroundColor="#222",c.style.width="8px",c.style.height="8px",c.style.transform="rotate(45deg)",c.style.display="none",s.firstElementChild.before(c));let d=!1;function f(){s.style.display="block",s.style.visibility="block",s.style.position="absolute",u&&(c.style.display="block"),d=!0,xg(e,s,{placement:o,middleware:[mg(r),wg(i),yg(a),...u?[bg({element:c})]:[]]}).then(({x:y,y:w,placement:h,middlewareData:C})=>{if(Object.assign(s.style,{left:`${y}px`,top:`${w}px`}),u){const{x:b,y:k}=C.arrow,_={top:"bottom",right:"left",bottom:"top",left:"right"}[h.split("-")[0]];Object.assign(c.style,{zIndex:-1,left:b!=null?`${b}px`:"",top:k!=null?`${k}px`:"",right:"",bottom:"",[_]:"2px"})}})}function g(){s.style.display="none",u&&(c.style.display="none"),d=!1}function m(y){y.stopPropagation(),d?g():f()}function v(y){s.contains(y.target)||g()}return(!t||t.length==0)&&(t=["click"]),t.forEach(y=>{e.addEventListener(y,m)}),document.addEventListener("click",v),{destroy(){t.forEach(y=>{e.removeEventListener(y,m)}),document.removeEventListener("click",v)},hide(){g()},isVisible:()=>d}};var Cg=ae('<div style="position: relative"><div><!></div> <div style="display: none; width: 100%;z-index: 9999"><!></div></div>');function Dr(e,t){ve(t,!0);const n=$(t,"children",7),o=$(t,"floating",7),r=$(t,"placement",7,"bottom");let i,a,l;tn(()=>(l=$g({trigger:i,floatContent:a,interactive:!0,placement:r()}),()=>{l.destroy()}));var u=Cg(),s=W(u);co(W(s),n),K(s),Vn(s,d=>i=d,()=>i);var c=Z(s,2);return co(W(c),o),K(c),Vn(c,d=>a=d,()=>a),K(u),T(e,u),ge({hide:function(){l.hide()},get children(){return n()},set children(d){n(d),x()},get floating(){return o()},set floating(d){o(d),x()},get placement(){return r()},set placement(d="bottom"){r(d),x()}})}function We(e,t){ve(t,!0);const n=$(t,"children",7),o=$(t,"level",7,1),r=$(t,"mt",7),i=$(t,"mb",7);var a=Ue();return M1(ke(a),()=>`h${o()}`,0,(l,u)=>{let s;Me(()=>s=en(l,s,{class:"tf-heading",style:`margin-top:${r()||"0"};margin-bottom:${i()||"0"}`},void 0,l.namespaceURI===El,l.nodeName.includes("-")));var c=Ue();co(ke(c),()=>n()??st),T(u,c)}),T(e,a),ge({get children(){return n()},set children(l){n(l),x()},get level(){return o()},set level(l=1){o(l),x()},get mt(){return r()},set mt(l){r(l),x()},get mb(){return i()},set mb(l){i(l),x()}})}ce(Dr,{children:{},floating:{},placement:{}},[],["hide"],!0),ce(We,{children:{},level:{},mt:{},mb:{}},[],[],!0);var kg=xe('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="svelte-1rvn4a8"><path d="M4.5 10.5C3.675 10.5 3 11.175 3 12C3 12.825 3.675 13.5 4.5 13.5C5.325 13.5 6 12.825 6 12C6 11.175 5.325 10.5 4.5 10.5ZM19.5 10.5C18.675 10.5 18 11.175 18 12C18 12.825 18.675 13.5 19.5 13.5C20.325 13.5 21 12.825 21 12C21 11.175 20.325 10.5 19.5 10.5ZM12 10.5C11.175 10.5 10.5 11.175 10.5 12C10.5 12.825 11.175 13.5 12 13.5C12.825 13.5 13.5 12.825 13.5 12C13.5 11.175 12.825 10.5 12 10.5Z" class="svelte-1rvn4a8"></path></svg>');const _g={hash:"svelte-1rvn4a8",code:".input-btn-more {border:1px solid transparent;padding:3px;&:hover {background:#eee;border:1px solid transparent;}}"};function ji(e,t){ve(t,!0),qe(e,_g);const n=gt(t,["$$slots","$$events","$$legacy","$$host"]);Ye(e,lt(()=>n,{get class(){return`input-btn-more ${t.class??""}`},children:(o,r)=>{T(o,kg())},$$slots:{default:!0}})),ge()}ce(ji,{},[],[],!0);const qo=(e=16)=>{const t="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789",n=new Uint8Array(e);return crypto.getRandomValues(n),Array.from(n,o=>t[o%62]).join("")};var Sg=xe('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M8 18.3915V5.60846L18.2264 12L8 18.3915ZM6 3.80421V20.1957C6 20.9812 6.86395 21.46 7.53 21.0437L20.6432 12.848C21.2699 12.4563 21.2699 11.5436 20.6432 11.152L7.53 2.95621C6.86395 2.53993 6 3.01878 6 3.80421Z"></path></svg>'),Eg=xe('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M6.9998 6V3C6.9998 2.44772 7.44752 2 7.9998 2H19.9998C20.5521 2 20.9998 2.44772 20.9998 3V17C20.9998 17.5523 20.5521 18 19.9998 18H16.9998V20.9991C16.9998 21.5519 16.5499 22 15.993 22H4.00666C3.45059 22 3 21.5554 3 20.9991L3.0026 7.00087C3.0027 6.44811 3.45264 6 4.00942 6H6.9998ZM5.00242 8L5.00019 20H14.9998V8H5.00242ZM8.9998 6H16.9998V16H18.9998V4H8.9998V6Z"></path></svg>'),Pg=xe('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M17 6H22V8H20V21C20 21.5523 19.5523 22 19 22H5C4.44772 22 4 21.5523 4 21V8H2V6H7V3C7 2.44772 7.44772 2 8 2H16C16.5523 2 17 2.44772 17 3V6ZM18 8H6V20H18V8ZM9 11H11V17H9V11ZM13 11H15V17H13V11ZM9 4V6H15V4H9Z"></path></svg>'),Mg=ae('<div class="tf-node-toolbar svelte-44dmwv"><!> <!> <!></div>'),Vg=ae('<!> <div class="tf-node-wrapper"><div class="tf-node-wrapper-title">TinyFlow.ai</div> <div class="tf-node-wrapper-body"><!></div></div> <!> <!> <!>',1);const Hg={hash:"svelte-44dmwv",code:".tf-node-toolbar.svelte-44dmwv {display:flex;gap:5px;padding:5px;border-radius:5px;background:#fff;border:1px solid #eee;box-shadow:0 0 5px rgba(0, 0, 0, 0.1);}.tf-node-toolbar-item {border:1px solid transparent;}"};function cn(e,t){ve(t,!0),qe(e,Hg);const n=$(t,"data",7),o=$(t,"id",7,""),r=$(t,"icon",7),i=$(t,"handle",7),a=$(t,"children",7),l=$(t,"allowExecute",7,!0),u=$(t,"allowCopy",7,!0),s=$(t,"allowDelete",7,!0),c=$(t,"showSourceHandle",7,!0),d=$(t,"showTargetHandle",7,!0);let f=n().expand?["key"]:[];const{updateNodeData:g}=Ht(),m=[{key:"key",icon:r(),title:n().title,description:n().description,content:a()}],{deleteNode:v}=(()=>{const P=Ke();return{deleteNode:D=>{P.nodes.update(M=>M.filter(H=>H.id!==D)),P.edges.update(M=>M.filter(H=>H.source!==D&&H.target!==D))}}})(),{copyNode:y}=(()=>{const{nodes:P,nodeLookup:D}=Ke();return{copyNode:M=>{var H;const O=(H=q(D).get(M))==null?void 0:H.internals.userNode;if(O){const V=qo(),z={...O,id:V,position:{x:O.position.x+50,y:O.position.y+50}};P.update(N=>[...N,z]),P.update(N=>N.map(A=>A.id===V?{...A,selected:!0}:{...A,selected:!1}))}}}})();var w=Vg(),h=ke(w),C=P=>{Xc(P,{get position(){return Ee.Top},align:"end",children:(D,M)=>{var H=Mg(),O=W(H),V=B=>{Ye(B,{class:"tf-node-toolbar-item",children:(Q,ne)=>{T(Q,Sg())},$$slots:{default:!0}})};_e(O,B=>{l()&&B(V)});var z=Z(O,2),N=B=>{Ye(B,{class:"tf-node-toolbar-item",onclick:()=>{y(o())},children:(Q,ne)=>{T(Q,Eg())},$$slots:{default:!0}})};_e(z,B=>{u()&&B(N)});var A=Z(z,2),X=B=>{Ye(B,{class:"tf-node-toolbar-item",onclick:()=>{v(o())},children:(Q,ne)=>{T(Q,Pg())},$$slots:{default:!0}})};_e(A,B=>{s()&&B(X)}),K(H),T(D,H)},$$slots:{default:!0}})};_e(h,P=>{(l()||u()||s())&&P(C)});var b=Z(h,2),k=Z(W(b),2);jc(W(k),{items:m,activeKeys:f,onChange:(P,D)=>{g(o(),{expand:D==null?void 0:D.includes("key")})}}),K(k),K(b);var _=Z(b,2),S=P=>{eo(P,{type:"target",get position(){return Ee.Left},style:" left: -12px;top: 20px"})};_e(_,P=>{d()&&P(S)});var E=Z(_,2),L=P=>{eo(P,{type:"source",get position(){return Ee.Right},style:"right: -12px;top: 20px"})};return _e(E,P=>{c()&&P(L)}),co(Z(E,2),()=>i()??st),T(e,w),ge({get data(){return n()},set data(P){n(P),x()},get id(){return o()},set id(P=""){o(P),x()},get icon(){return r()},set icon(P){r(P),x()},get handle(){return i()},set handle(P){i(P),x()},get children(){return a()},set children(P){a(P),x()},get allowExecute(){return l()},set allowExecute(P=!0){l(P),x()},get allowCopy(){return u()},set allowCopy(P=!0){u(P),x()},get allowDelete(){return s()},set allowDelete(P=!0){s(P),x()},get showSourceHandle(){return c()},set showSourceHandle(P=!0){c(P),x()},get showTargetHandle(){return d()},set showTargetHandle(P=!0){d(P),x()}})}function ct(){return uo("svelteflow__node_id")}ce(cn,{data:{},id:{},icon:{},handle:{},children:{},allowExecute:{},allowCopy:{},allowDelete:{},showSourceHandle:{},showTargetHandle:{}},[],[],!0);const cd=[{value:"String",label:"String"},{value:"Number",label:"Number"},{value:"Boolean",label:"Boolean"},{value:"File",label:"File"},{value:"Object",label:"Object"},{value:"Array",label:"Array"}],zg=[{value:"ref",label:"\u5F15\u7528"},{value:"input",label:"\u56FA\u5B9A\u503C"}];var Ng=ae('<div class="input-more-setting svelte-laou7w"><div class="input-more-item svelte-laou7w">\u53C2\u6570\u7C7B\u578B\uFF1A <!></div> <div class="input-more-item svelte-laou7w">\u9ED8\u8BA4\u503C\uFF1A <!></div> <div class="input-more-item svelte-laou7w">\u53C2\u6570\u63CF\u8FF0\uFF1A <!></div> <div class="input-more-item svelte-laou7w"><!></div></div>'),Lg=xe('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M4.5 10.5C3.675 10.5 3 11.175 3 12C3 12.825 3.675 13.5 4.5 13.5C5.325 13.5 6 12.825 6 12C6 11.175 5.325 10.5 4.5 10.5ZM19.5 10.5C18.675 10.5 18 11.175 18 12C18 12.825 18.675 13.5 19.5 13.5C20.325 13.5 21 12.825 21 12C21 11.175 20.325 10.5 19.5 10.5ZM12 10.5C11.175 10.5 10.5 11.175 10.5 12C10.5 12.825 11.175 13.5 12 13.5C12.825 13.5 13.5 12.825 13.5 12C13.5 11.175 12.825 10.5 12 10.5Z"></path></svg>'),Og=ae('<div class="input-item svelte-laou7w"><!></div> <div class="input-item svelte-laou7w"><!></div> <div class="input-item svelte-laou7w"><!></div>',1);const Dg={hash:"svelte-laou7w",code:".input-item.svelte-laou7w {display:flex;align-items:center;}.input-more-setting.svelte-laou7w {display:flex;flex-direction:column;gap:10px;padding:10px;background:#fff;border:1px solid #ddd;border-radius:5px;width:200px;box-shadow:0 0 10px 2px rgba(0, 0, 0, 0.1);}.input-more-setting.svelte-laou7w .input-more-item:where(.svelte-laou7w) {display:flex;flex-direction:column;gap:3px;font-size:12px;color:#666;}"};function dd(e,t){ve(t,!0),qe(e,Dg);const[n,o]=Je(),r=$(t,"parameter",7),i=$(t,"index",7);let a=ct(),l=ze(()=>yo(a)),u=ze(()=>{var h,C;return{...r(),...(C=(h=ie(p(l),"$node",n))==null?void 0:h.data)==null?void 0:C.parameters[i()]}});const{updateNodeData:s}=Ht(),c=h=>{const C=h.value;C&&s(a,b=>{let k=b.data.parameters;return k[i()].dataType=C,{parameters:k}})};let d;const f=()=>{s(a,h=>{let C=h.data.parameters;return C.splice(i(),1),{parameters:[...C]}}),d==null||d.hide()};var g=Og(),m=ke(g);$t(W(m),{style:"width: 100%;",get value(){return p(u).name},placeholder:"\u8BF7\u8F93\u5165\u53C2\u6570\u540D\u79F0",oninput:h=>{const C=h.target.value;s(a,b=>{let k=b.data.parameters;return k[i()].name=C,{parameters:k}})}}),K(m);var v=Z(m,2);Kc(W(v),{get checked(){return p(u).required},onchange:h=>{const C=h.target.checked;s(a,b=>{let k=b.data.parameters;return k[i()].required=C,{parameters:k}})}}),K(v);var y=Z(v,2);Vn(Dr(W(y),{placement:"bottom",floating:h=>{var C=Ng(),b=W(C),k=Z(W(b));const _=ze(()=>p(u).dataType?[p(u).dataType]:["String"]);ln(k,{items:cd,style:"width: 100%",onSelect:c,get value(){return p(_)}}),K(b);var S=Z(b,2);Ct(Z(W(S)),{rows:1,style:"width: 100%;"}),K(S);var E=Z(S,2);Ct(Z(W(E)),{rows:3,style:"width: 100%;"}),K(E);var L=Z(E,2);Ye(W(L),{onclick:f,children:(P,D)=>{He(),T(P,Te("\u5220\u9664"))},$$slots:{default:!0}}),K(L),K(C),T(h,C)},children:(h,C)=>{Ye(h,{class:"input-btn-more",children:(b,k)=>{T(b,Lg())},$$slots:{default:!0}})},$$slots:{floating:!0,default:!0}}),h=>d=h,()=>d),K(y),T(e,g);var w=ge({get parameter(){return r()},set parameter(h){r(h),x()},get index(){return i()},set index(h){i(h),x()}});return o(),w}ce(dd,{parameter:{},index:{}},[],[],!0);var Tg=ae('<div class="input-header svelte-3n0wca">\u53C2\u6570\u540D\u79F0</div> <div class="input-header svelte-3n0wca">\u5FC5\u586B</div> <div class="input-header svelte-3n0wca"></div>',1),Ag=ae('<div class="none-params svelte-3n0wca">\u65E0\u8F93\u5165\u53C2\u6570</div>'),Ig=ae('<div class="input-container svelte-3n0wca"><!> <!></div>');const Zg={hash:"svelte-3n0wca",code:`.input-container.svelte-3n0wca {display:grid;grid-template-columns:80% 10% 10%;row-gap:5px;column-gap:3px;}.input-container.svelte-3n0wca .none-params:where(.svelte-3n0wca) {font-size:12px;background:#f8f8f8;height:40px;display:flex;justify-content:center;align-items:center;border-radius:5px;width:calc(100% - 5px);grid-column:1 / -1;
  /* \u4ECE\u7B2C\u4E00\u5217\u5F00\u59CB\u5230\u6700\u540E\u4E00\u5217\u7ED3\u675F */}.input-container.svelte-3n0wca .input-header:where(.svelte-3n0wca) {font-size:12px;color:#666;}`};function fd(e,t){ve(t,!0),qe(e,Zg);const[n,o]=Je();let r=ct(),i=ze(()=>yo(r)),a=ze(()=>{var c,d;return[...((d=(c=ie(p(i),"$node",n))==null?void 0:c.data)==null?void 0:d.parameters)||[]]});var l=Ig(),u=W(l),s=c=>{var d=Tg();He(4),T(c,d)};_e(u,c=>{p(a).length!==0&&c(s)}),At(Z(u,2),19,()=>p(a),c=>c.id,(c,d,f)=>{dd(c,{get parameter(){return p(d)},get index(){return p(f)}})},c=>{T(c,Ag())}),K(l),T(e,l),ge(),o()}ce(fd,{},[],[],!0);const vd=e=>{!e||e.length==0||e.forEach(t=>{t.id||(t.id=qo()),vd(t.children)})},$n=()=>{const{updateNodeData:e}=Ht();return{addParameter:(t,n="parameters",o)=>{vd(o==null?void 0:o.children);const r={...o,id:qo()};e(t,i=>{let a=i.data[n];return a?a.push(r):a=[r],{[n]:[...a]}})}}};var Bg=xe('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M12 22C6.47715 22 2 17.5228 2 12C2 6.47715 6.47715 2 12 2C17.5228 2 22 6.47715 22 12C22 17.5228 17.5228 22 12 22ZM12 20C16.4183 20 20 16.4183 20 12C20 7.58172 16.4183 4 12 4C7.58172 4 4 7.58172 4 12C4 16.4183 7.58172 20 12 20ZM12 15C10.3431 15 9 13.6569 9 12C9 10.3431 10.3431 9 12 9C13.6569 9 15 10.3431 15 12C15 13.6569 13.6569 15 12 15Z"></path></svg>'),Rg=xe('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M11 11V5H13V11H19V13H13V19H11V13H5V11H11Z"></path></svg>'),Xg=ae('<div class="heading svelte-r5g35l"><!> <!></div> <!>',1);const Yg={hash:"svelte-r5g35l",code:".heading.svelte-r5g35l {display:flex;margin-bottom:10px;}.input-btn-more {border:1px solid transparent;padding:3px;}.input-btn-more:hover {background:#eee;border:1px solid transparent;}"};function gd(e,t){ve(t,!0),qe(e,Yg);const n=$(t,"data",7),o=gt(t,["$$slots","$$events","$$legacy","$$host","data"]),r=ct(),{addParameter:i}=$n();return cn(e,lt(()=>o,{get data(){return n()},allowExecute:!1,showTargetHandle:!1,icon:a=>{T(a,Bg())},children:(a,l)=>{var u=Xg(),s=ke(u),c=W(s);We(c,{level:3,children:(d,f)=>{He(),T(d,Te("\u8F93\u5165\u53C2\u6570"))},$$slots:{default:!0}}),Ye(Z(c,2),{class:"input-btn-more",style:"margin-left: auto",onclick:()=>{i(r)},children:(d,f)=>{T(d,Rg())},$$slots:{default:!0}}),K(s),fd(Z(s,2),{}),T(a,u)},$$slots:{icon:!0,default:!0}})),ge({get data(){return n()},set data(a){n(a),x()}})}ce(gd,{data:{}},[],[],!0);const pd=(e,t,n)=>{for(let o of n)o.target===t&&o.source&&(e.push(o.source),pd(e,o.source,n))},hd=(e,t)=>{if(e.type==="startNode"){const n=e.data.parameters,o=[];if(n)for(const r of n)o.push({label:r.name+(t?` (Array<${r.dataType||"String"}>)`:` (${r.dataType||"String"})`),value:e.id+"."+r.name});return{label:e.data.title,value:e.id,children:o}}if(e.type==="loopNode"&&t)return{label:e.data.title,value:e.id,children:[{label:"loopItem",value:e.id+".loop"},{label:"index (Number)",value:e.id+".index"}]};{const n=e.data.outputDefs;if(n){const o=(r,i)=>r&&r.length!==0?r.map(a=>({label:a.name+(t?` (Array<${a.dataType||"String"}>)`:` (${a.dataType||"String"})`),value:i+"."+a.name,children:o(a.children,i+"."+a.name)})):[];return{label:e.data.title,value:e.id,children:o(n,e.id)}}}};var Kg=ae('<div class="input-more-setting svelte-laou7w"><div class="input-more-item svelte-laou7w">\u6570\u636E\u6765\u6E90\uFF1A <!></div> <div class="input-more-item svelte-laou7w">\u9ED8\u8BA4\u503C\uFF1A <!></div> <div class="input-more-item svelte-laou7w">\u53C2\u6570\u63CF\u8FF0\uFF1A <!></div> <div class="input-more-item svelte-laou7w"><!></div></div>'),Wg=ae('<div class="input-item svelte-laou7w"><!></div> <div class="input-item svelte-laou7w"><!></div> <div class="input-item svelte-laou7w"><!></div>',1);const jg={hash:"svelte-laou7w",code:".input-item.svelte-laou7w {display:flex;align-items:center;}.input-more-setting.svelte-laou7w {display:flex;flex-direction:column;gap:10px;padding:10px;background:#fff;border:1px solid #ddd;border-radius:5px;width:200px;box-shadow:0 0 10px 2px rgba(0, 0, 0, 0.1);}.input-more-setting.svelte-laou7w .input-more-item:where(.svelte-laou7w) {display:flex;flex-direction:column;gap:3px;font-size:12px;color:#666;}"};function md(e,t){ve(t,!0),qe(e,jg);const[n,o]=Je(),r=$(t,"parameter",7),i=$(t,"index",7),a=$(t,"dataKeyName",7);let l=ct(),u=ze(()=>yo(l)),s=ze(()=>{var P;return{...r(),...(P=ie(p(u),"$node",n))==null?void 0:P.data[a()][i()]}});const{updateNodeData:c}=Ht(),d=(P,D)=>{c(l,M=>{let H=M.data[a()];return H[i()]={...H[i()],[P]:D},{[a()]:H}})},f=P=>{const D=P.target.value;d("value",D)},g=P=>{const D=P.value;d("ref",D)},m=P=>{const D=P.value;d("refType",D)};let v;const y=()=>{c(l,P=>{let D=P.data[a()];return D.splice(i(),1),{[a()]:[...D]}}),v==null||v.hide()},w=((P=!1)=>{const D=ct(),M=yo(D),{nodes:H,edges:O}=Ke();return Fn([M,H,O],([V,z,N])=>{const A=[];if(P){for(let X of z)if(X.parentId===V.id){const B=hd(X,X.parentId===V.id);B&&A.push(B)}}else{const X=[];pd(X,D,N);for(let B of z)if(X.includes(B.id)){const Q=hd(B,B.parentId===V.id);Q&&A.push(Q)}}return A})})();var h=Wg(),C=ke(h);$t(W(C),{style:"width: 100%;",get value(){return p(s).name},placeholder:"\u8BF7\u8F93\u5165\u53C2\u6570\u540D\u79F0",oninput:P=>{const D=P.target.value;d("name",D)}}),K(C);var b=Z(C,2),k=W(b),_=P=>{$t(P,{get value(){return p(s).value},placeholder:"\u8BF7\u8F93\u5165\u53C2\u6570\u503C",oninput:f})},S=P=>{const D=ze(()=>[p(s).ref]);ln(P,{get items(){return ie(w,"$selectItems",n)},style:"width: 100%",defaultValue:["ref"],get value(){return p(D)},expandAll:!0,onSelect:g})};_e(k,P=>{p(s).refType==="input"?P(_):P(S,!1)}),K(b);var E=Z(b,2);Vn(Dr(W(E),{placement:"bottom",floating:P=>{var D=Kg(),M=W(D),H=Z(W(M));const O=ze(()=>p(s).refType?[p(s).refType]:[]);ln(H,{items:zg,style:"width: 100%",defaultValue:["ref"],get value(){return p(O)},onSelect:m}),K(M);var V=Z(M,2);Ct(Z(W(V)),{rows:1,style:"width: 100%;",onchange:A=>{const X=A.target.value;d("defaultValue",X)}}),K(V);var z=Z(V,2);Ct(Z(W(z)),{rows:3,style:"width: 100%;",onchange:A=>{const X=A.target.value;d("description",X)}}),K(z);var N=Z(z,2);Ye(W(N),{onclick:y,children:(A,X)=>{He(),T(A,Te("\u5220\u9664"))},$$slots:{default:!0}}),K(N),K(D),T(P,D)},children:(P,D)=>{ji(P,{})},$$slots:{floating:!0,default:!0}}),P=>v=P,()=>v),K(E),T(e,h);var L=ge({get parameter(){return r()},set parameter(P){r(P),x()},get index(){return i()},set index(P){i(P),x()},get dataKeyName(){return a()},set dataKeyName(P){a(P),x()}});return o(),L}ce(md,{parameter:{},index:{},dataKeyName:{}},[],[],!0);var qg=ae('<div class="input-header svelte-1sm1mgi">\u53C2\u6570\u540D\u79F0</div> <div class="input-header svelte-1sm1mgi">\u53C2\u6570\u503C</div> <div class="input-header svelte-1sm1mgi"></div>',1),Fg=ae('<div class="none-params svelte-1sm1mgi"> </div>'),Gg=ae('<div class="input-container svelte-1sm1mgi"><!> <!></div>');const Ug={hash:"svelte-1sm1mgi",code:`.input-container.svelte-1sm1mgi {display:grid;grid-template-columns:40% 50% 10%;row-gap:5px;column-gap:3px;}.input-container.svelte-1sm1mgi .none-params:where(.svelte-1sm1mgi) {font-size:12px;background:#f8f8f8;height:40px;display:flex;justify-content:center;align-items:center;border-radius:5px;width:calc(100% - 5px);grid-column:1 / -1;
  /* \u4ECE\u7B2C\u4E00\u5217\u5F00\u59CB\u5230\u6700\u540E\u4E00\u5217\u7ED3\u675F */}.input-container.svelte-1sm1mgi .input-header:where(.svelte-1sm1mgi) {font-size:12px;color:#666;}`};function Bt(e,t){ve(t,!0),qe(e,Ug);const[n,o]=Je(),r=$(t,"noneParameterText",7,"\u65E0\u8F93\u5165\u53C2\u6570"),i=$(t,"dataKeyName",7,"parameters");let a=ct(),l=ze(()=>yo(a)),u=ze(()=>{var g;return[...((g=ie(p(l),"$node",n))==null?void 0:g.data[i()])||[]]});var s=Gg(),c=W(s),d=g=>{var m=qg();He(4),T(g,m)};_e(c,g=>{p(u).length!==0&&g(d)}),At(Z(c,2),19,()=>p(u),g=>g.id,(g,m,v)=>{md(g,{get parameter(){return p(m)},get index(){return p(v)},get dataKeyName(){return i()}})},g=>{var m=Fg(),v=W(m,!0);K(m),Me(()=>Tt(v,r())),T(g,m)}),K(s),T(e,s);var f=ge({get noneParameterText(){return r()},set noneParameterText(g="\u65E0\u8F93\u5165\u53C2\u6570"){r(g),x()},get dataKeyName(){return i()},set dataKeyName(g="parameters"){i(g),x()}});return o(),f}ce(Bt,{noneParameterText:{},dataKeyName:{}},[],[],!0);var Jg=xe('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M6 5.1438V16.0002H18.3391L6 5.1438ZM4 2.932C4 2.07155 5.01456 1.61285 5.66056 2.18123L21.6501 16.2494C22.3423 16.8584 21.9116 18.0002 20.9896 18.0002H6V22H4V2.932Z"></path></svg>'),Qg=xe('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M11 11V5H13V11H19V13H13V19H11V13H5V11H11Z"></path></svg>'),ep=ae('<div class="heading svelte-11h445j"><!> <!></div> <!>',1);const tp={hash:"svelte-11h445j",code:".heading.svelte-11h445j {display:flex;margin-bottom:10px;}"};function yd(e,t){ve(t,!0),qe(e,tp);const n=$(t,"data",7),o=gt(t,["$$slots","$$events","$$legacy","$$host","data"]),r=ct(),{addParameter:i}=$n();return cn(e,lt({get data(){return n()}},()=>o,{allowExecute:!1,showSourceHandle:!1,icon:a=>{T(a,Jg())},children:(a,l)=>{var u=ep(),s=ke(u),c=W(s);We(c,{level:3,children:(d,f)=>{He(),T(d,Te("\u8F93\u51FA\u53C2\u6570"))},$$slots:{default:!0}}),Ye(Z(c,2),{class:"input-btn-more",style:"margin-left: auto",onclick:()=>{i(r,"outputDefs")},children:(d,f)=>{T(d,Qg())},$$slots:{default:!0}}),K(s),Bt(Z(s,2),{noneParameterText:"\u65E0\u8F93\u51FA\u53C2\u6570",dataKeyName:"outputDefs"}),T(a,u)},$$slots:{icon:!0,default:!0}})),ge({get data(){return n()},set data(a){n(a),x()}})}ce(yd,{data:{}},[],[],!0);const Tr=()=>uo("tinyflow_options");var np=xe('<svg style="transform: scaleY(-1)" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M13 8V16C13 17.6569 11.6569 19 10 19H7.82929C7.41746 20.1652 6.30622 21 5 21C3.34315 21 2 19.6569 2 18C2 16.3431 3.34315 15 5 15C6.30622 15 7.41746 15.8348 7.82929 17H10C10.5523 17 11 16.5523 11 16V8C11 6.34315 12.3431 5 14 5H17V2L22 6L17 10V7H14C13.4477 7 13 7.44772 13 8ZM5 19C5.55228 19 6 18.5523 6 18C6 17.4477 5.55228 17 5 17C4.44772 17 4 17.4477 4 18C4 18.5523 4.44772 19 5 19Z"></path></svg>'),op=ae('<div class="input-more-item svelte-1cfeest"><!></div>'),rp=ae('<div class="input-more-setting svelte-1cfeest"><div class="input-more-item svelte-1cfeest">\u9ED8\u8BA4\u503C\uFF1A <!></div> <div class="input-more-item svelte-1cfeest">\u53C2\u6570\u63CF\u8FF0\uFF1A <!></div> <!></div>'),ip=ae('<div class="input-item svelte-1cfeest"><!> <!></div> <div class="input-item svelte-1cfeest"><!> <!></div> <div class="input-item svelte-1cfeest"><!></div>',1);const ap={hash:"svelte-1cfeest",code:".input-item.svelte-1cfeest {display:flex;align-items:center;gap:2px;}.input-more-setting.svelte-1cfeest {display:flex;flex-direction:column;gap:10px;padding:10px;background:#fff;border:1px solid #ddd;border-radius:5px;width:200px;box-shadow:0 0 10px 2px rgba(0, 0, 0, 0.1);}.input-more-setting.svelte-1cfeest .input-more-item:where(.svelte-1cfeest) {display:flex;flex-direction:column;gap:3px;font-size:12px;color:#666;}"};function wd(e,t){ve(t,!0),qe(e,ap);const[n,o]=Je(),r=$(t,"parameter",7),i=$(t,"position",7),a=$(t,"dataKeyName",7);let l=ct(),u=ze(()=>yo(l)),s=ze(()=>{var H;let O,V=(H=ie(p(u),"$node",n))==null?void 0:H.data[a()];if(V&&i().length>0){let z=V;for(let N=0;N<i().length;N++){const A=i()[N];N==i().length-1?O=z[A]:z=z[A].children}}return{...r(),...O}});const{updateNodeData:c}=Ht(),d=(H,O)=>{c(l,V=>{const z=V.data[a()];if(z&&i().length>0){let N=z;for(let A=0;A<i().length;A++){const X=i()[A];A==i().length-1?N[X]={...N[X],[H]:O}:N=z[X].children}}return{[a()]:z}})};let f;const g=()=>{c(l,H=>{let O=H.data[a()];if(O&&i().length>0){let V=O;for(let z=0;z<i().length;z++){const N=i()[z];z==i().length-1?V.splice(N,1):V=V[N].children}}return{[a()]:[...O]}}),f==null||f.hide()},m=()=>{c(l,H=>{let O=H.data[a()];if(O&&i().length>0){let V=O;for(let z=0;z<i().length;z++){const N=i()[z];z==i().length-1?V[N].children?V[N].children.push({id:qo(),name:"newParam",dataType:"String"}):V[N].children=[{id:qo(),name:"newParam",dataType:"String"}]:V=V[N].children}}return{[a()]:[...O]}})};var v=ip(),y=ke(v),w=W(y),h=H=>{var O=Ue();At(ke(O),17,i,ni,(V,z)=>{He(),T(V,Te("\xA0"))}),T(H,O)};_e(w,H=>{i().length>1&&H(h)});var C=Z(w,2);const b=ze(()=>p(s).nameDisabled===!0);$t(C,{style:"width: 100%;",get value(){return p(s).name},placeholder:"\u8BF7\u8F93\u5165\u53C2\u6570\u540D\u79F0",oninput:H=>{const O=H.target.value;d("name",O)},get disabled(){return p(b)}}),K(y);var k=Z(y,2),_=W(k);const S=ze(()=>p(s).dataType?[p(s).dataType]:[]),E=ze(()=>p(s).dataTypeDisabled===!0);ln(_,{items:cd,style:"width: 100%",defaultValue:["String"],get value(){return p(S)},get disabled(){return p(E)},onSelect:H=>{const O=H.value;d("dataType",O)}});var L=Z(_,2),P=H=>{Ye(H,{class:"input-btn-more",style:"margin-left: auto",onclick:m,children:(O,V)=>{T(O,np())},$$slots:{default:!0}})};_e(L,H=>{(p(s).dataType==="Object"||p(s).dataType==="Array")&&p(s).addChildDisabled!==!0&&H(P)}),K(k);var D=Z(k,2);Vn(Dr(W(D),{placement:"bottom",floating:H=>{var O=rp(),V=W(O);Ct(Z(W(V)),{rows:1,style:"width: 100%;",onchange:X=>{const B=X.target.value;d("defaultValue",B)}}),K(V);var z=Z(V,2);Ct(Z(W(z)),{rows:3,style:"width: 100%;",onchange:X=>{const B=X.target.value;d("description",B)}}),K(z);var N=Z(z,2),A=X=>{var B=op();Ye(W(B),{onclick:g,children:(Q,ne)=>{He(),T(Q,Te("\u5220\u9664"))},$$slots:{default:!0}}),K(B),T(X,B)};_e(N,X=>{p(s).deleteDisabled!==!0&&X(A)}),K(O),T(H,O)},children:(H,O)=>{ji(H,{})},$$slots:{floating:!0,default:!0}}),H=>f=H,()=>f),K(D),T(e,v);var M=ge({get parameter(){return r()},set parameter(H){r(H),x()},get position(){return i()},set position(H){i(H),x()},get dataKeyName(){return a()},set dataKeyName(H){a(H),x()}});return o(),M}ce(wd,{parameter:{},position:{},dataKeyName:{}},[],[],!0);var lp=ae("<!> <!>",1),sp=ae('<div class="none-params svelte-1sm1mgi"> </div>'),up=ae('<div class="input-header svelte-1sm1mgi">\u53C2\u6570\u540D\u79F0</div> <div class="input-header svelte-1sm1mgi">\u53C2\u6570\u7C7B\u578B</div> <div class="input-header svelte-1sm1mgi"></div>',1),cp=ae('<div class="input-container svelte-1sm1mgi"><!> <!></div>');const dp={hash:"svelte-1sm1mgi",code:`.input-container.svelte-1sm1mgi {display:grid;grid-template-columns:40% 50% 10%;row-gap:5px;column-gap:3px;}.input-container.svelte-1sm1mgi .none-params:where(.svelte-1sm1mgi) {font-size:12px;background:#f8f8f8;height:40px;display:flex;justify-content:center;align-items:center;border-radius:5px;width:calc(100% - 5px);grid-column:1 / -1;
  /* \u4ECE\u7B2C\u4E00\u5217\u5F00\u59CB\u5230\u6700\u540E\u4E00\u5217\u7ED3\u675F */}.input-container.svelte-1sm1mgi .input-header:where(.svelte-1sm1mgi) {font-size:12px;color:#666;}`};function Tn(e,t){ve(t,!0),qe(e,dp);const[n,o]=Je(),r=(v,y=st,w=st)=>{var h=Ue();At(ke(h),19,y,C=>`${C.id}_${C.children?C.children.length:0}`,(C,b,k)=>{var _=lp(),S=ke(_);const E=ze(()=>[...w(),p(k)]);wd(S,{get parameter(){return p(b)},get position(){return p(E)},get dataKeyName(){return a()}});var L=Z(S,2),P=D=>{var M=ye(()=>[...w(),p(k)]);r(D,()=>p(b).children,()=>p(M))};_e(L,D=>{p(b).children&&D(P)}),T(C,_)},C=>{var b=Ue(),k=ke(b),_=S=>{var E=sp(),L=W(E,!0);K(E),Me(()=>Tt(L,i())),T(S,E)};_e(k,S=>{w().length===0&&S(_)}),T(C,b)}),T(v,h)},i=$(t,"noneParameterText",7,"\u65E0\u8F93\u51FA\u53C2\u6570"),a=$(t,"dataKeyName",7,"outputDefs");let l=ct(),u=ze(()=>yo(l)),s=ze(()=>{var v;return[...((v=ie(p(u),"$node",n))==null?void 0:v.data[a()])||[]]});var c=cp(),d=W(c),f=v=>{var y=up();He(4),T(v,y)};_e(d,v=>{p(s).length!==0&&v(f)});var g=Z(d,2);r(g,()=>p(s)||[],()=>[]),K(c),T(e,c);var m=ge({get noneParameterText(){return i()},set noneParameterText(v="\u65E0\u8F93\u51FA\u53C2\u6570"){i(v),x()},get dataKeyName(){return a()},set dataKeyName(v="outputDefs"){a(v),x()}});return o(),m}ce(Tn,{noneParameterText:{},dataKeyName:{}},[],[],!0);var fp=xe('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M20.7134 7.12811L20.4668 7.69379C20.2864 8.10792 19.7136 8.10792 19.5331 7.69379L19.2866 7.12811C18.8471 6.11947 18.0555 5.31641 17.0677 4.87708L16.308 4.53922C15.8973 4.35653 15.8973 3.75881 16.308 3.57612L17.0252 3.25714C18.0384 2.80651 18.8442 1.97373 19.2761 0.930828L19.5293 0.319534C19.7058 -0.106511 20.2942 -0.106511 20.4706 0.319534L20.7238 0.930828C21.1558 1.97373 21.9616 2.80651 22.9748 3.25714L23.6919 3.57612C24.1027 3.75881 24.1027 4.35653 23.6919 4.53922L22.9323 4.87708C21.9445 5.31641 21.1529 6.11947 20.7134 7.12811ZM9 2C13.0675 2 16.426 5.03562 16.9337 8.96494L19.1842 12.5037C19.3324 12.7367 19.3025 13.0847 18.9593 13.2317L17 14.071V17C17 18.1046 16.1046 19 15 19H13.001L13 22H4L4.00025 18.3061C4.00033 17.1252 3.56351 16.0087 2.7555 15.0011C1.65707 13.6313 1 11.8924 1 10C1 5.58172 4.58172 2 9 2ZM9 4C5.68629 4 3 6.68629 3 10C3 11.3849 3.46818 12.6929 4.31578 13.7499C5.40965 15.114 6.00036 16.6672 6.00025 18.3063L6.00013 20H11.0007L11.0017 17H15V12.7519L16.5497 12.0881L15.0072 9.66262L14.9501 9.22118C14.5665 6.25141 12.0243 4 9 4ZM19.4893 16.9929L21.1535 18.1024C22.32 16.3562 23 14.2576 23 12.0001C23 11.317 22.9378 10.6486 22.8186 10L20.8756 10.5C20.9574 10.9878 21 11.489 21 12.0001C21 13.8471 20.4436 15.5642 19.4893 16.9929Z"></path></svg>'),vp=xe('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M11 11V5H13V11H19V13H13V19H11V13H5V11H11Z"></path></svg>'),gp=xe('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M11 11V5H13V11H19V13H13V19H11V13H5V11H11Z"></path></svg>'),pp=ae('<div class="heading svelte-wn2kra"><!> <!></div> <!> <!> <div class="setting-title svelte-wn2kra">\u6A21\u578B</div> <div class="setting-item svelte-wn2kra"><!> <!></div> <div class="setting-title svelte-wn2kra">\u91C7\u6837\u53C2\u6570</div> <div class="setting-item svelte-wn2kra"><div class="slider-container svelte-wn2kra"><label class="svelte-wn2kra"> </label> <input type="range" min="0" max="1" step="0.1" class="svelte-wn2kra"></div></div> <div class="setting-item svelte-wn2kra"><div class="slider-container svelte-wn2kra"><label class="svelte-wn2kra"> </label> <input type="range" min="0" max="1" step="0.1" class="svelte-wn2kra"></div></div> <div class="setting-item svelte-wn2kra"><div class="slider-container svelte-wn2kra"><label class="svelte-wn2kra"> </label> <input type="range" min="0" max="100" step="1" class="svelte-wn2kra"></div></div> <div class="setting-title svelte-wn2kra">\u7CFB\u7EDF\u63D0\u793A\u8BCD</div> <div class="setting-item svelte-wn2kra"><!></div> <div class="setting-title svelte-wn2kra">\u7528\u6237\u63D0\u793A\u8BCD</div> <div class="setting-item svelte-wn2kra"><!></div> <div class="heading svelte-wn2kra"><!> <!></div> <!>',1);const hp={hash:"svelte-wn2kra",code:`.heading.svelte-wn2kra {display:flex;margin-bottom:10px;}.setting-title.svelte-wn2kra {font-size:12px;color:#999;margin-bottom:4px;margin-top:10px;}.setting-item.svelte-wn2kra {display:flex;align-items:center;justify-content:space-between;margin-bottom:10px;gap:10px;}\r
    /* \u65B0\u589E\u6837\u5F0F */.slider-container.svelte-wn2kra {width:100%;display:flex;flex-direction:column;gap:4px;}.slider-container.svelte-wn2kra label:where(.svelte-wn2kra) {font-size:12px;color:#666;display:flex;justify-content:space-between;align-items:center;}input[type="range"].svelte-wn2kra {width:100%;height:4px;background:#ddd;border-radius:2px;outline:none;-webkit-appearance:none;}input[type="range"].svelte-wn2kra::-webkit-slider-thumb {-webkit-appearance:none;width:14px;height:14px;background:#007bff;border-radius:50%;cursor:pointer;}`};function bd(e,t){ve(t,!0),qe(e,hp);const n=$(t,"data",7),o=gt(t,["$$slots","$$events","$$legacy","$$host","data"]),r=ct(),{addParameter:i}=$n(),a=Tr();let l=Yn(Et([]));tn(async()=>{var s,c;const d=await((c=(s=a.provider)==null?void 0:s.llm)==null?void 0:c.call(s));p(l).push(...d||[])});const{updateNodeData:u}=Ht();return cn(e,lt({get data(){return n()}},()=>o,{icon:s=>{T(s,fp())},children:(s,c)=>{var d=pp(),f=ke(d),g=W(f);We(g,{level:3,children:(J,F)=>{He(),T(J,Te("\u8F93\u5165\u53C2\u6570"))},$$slots:{default:!0}}),Ye(Z(g,2),{class:"input-btn-more",style:"margin-left: auto",onclick:()=>{i(r)},children:(J,F)=>{T(J,vp())},$$slots:{default:!0}}),K(f);var m=Z(f,2);Bt(m,{});var v=Z(m,2);We(v,{level:3,mt:"10px",children:(J,F)=>{He(),T(J,Te("\u6A21\u578B\u8BBE\u7F6E"))},$$slots:{default:!0}});var y=Z(v,4),w=W(y);const h=ze(()=>n().llmId?[n().llmId]:[]);ln(w,{get items(){return p(l)},style:"width: 100%",placeholder:"\u8BF7\u9009\u62E9\u6A21\u578B",onSelect:J=>{const F=J.value;u(r,()=>({llmId:F}))},get value(){return p(h)}}),ji(Z(w,2),{}),K(y);var C=Z(y,4),b=W(C),k=W(b),_=W(k);K(k);var S=Z(k,2);ur(S),K(b),K(C);var E=Z(C,2),L=W(E),P=W(L),D=W(P);K(P);var M=Z(P,2);ur(M),K(L),K(E);var H=Z(E,2),O=W(H),V=W(O),z=W(V);K(V);var N=Z(V,2);ur(N),K(O),K(H);var A=Z(H,4),X=W(A);const B=ze(()=>n().systemPrompt||"");Ct(X,{rows:5,placeholder:"\u8BF7\u8F93\u5165\u7CFB\u7EDF\u63D0\u793A\u8BCD",style:"width: 100%",get value(){return p(B)},oninput:J=>{u(r,{systemPrompt:J.target.value})}}),K(A);var Q=Z(A,4),ne=W(Q);const ee=ze(()=>n().userPrompt||"");Ct(ne,{rows:5,placeholder:"\u8BF7\u8F93\u5165\u7528\u6237\u63D0\u793A\u8BCD",style:"width: 100%",get value(){return p(ee)},oninput:J=>{u(r,{userPrompt:J.target.value})}}),K(Q);var me=Z(Q,2),de=W(me);We(de,{level:3,mt:"10px",children:(J,F)=>{He(),T(J,Te("\u8F93\u51FA\u53C2\u6570"))},$$slots:{default:!0}}),Ye(Z(de,2),{class:"input-btn-more",style:"margin-left: auto",onclick:()=>{i(r,"outputDefs")},children:(J,F)=>{T(J,gp())},$$slots:{default:!0}}),K(me),Tn(Z(me,2),{}),Me(()=>{Tt(_,`Temperature: ${n().temperature??.5}`),ya(S,n().temperature??.5),Tt(D,`Top P: ${n().topP??.9}`),ya(M,n().topP??.9),Tt(z,`Top K: ${n().topK??50}`),ya(N,n().topK??50)}),Ze("mousedown",S,xa(function(J){De.call(this,t,J)})),Ze("input",S,J=>u(r,{temperature:parseFloat(J.target.value)})),Ze("mousedown",M,xa(function(J){De.call(this,t,J)})),Ze("input",M,J=>u(r,{topP:parseFloat(J.target.value)})),Ze("mousedown",N,xa(function(J){De.call(this,t,J)})),Ze("input",N,J=>u(r,{topK:parseInt(J.target.value)})),T(s,d)},$$slots:{icon:!0,default:!0}})),ge({get data(){return n()},set data(s){n(s),x()}})}ce(bd,{data:{}},[],[],!0);var mp=xe('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M23 12L15.9289 19.0711L14.5147 17.6569L20.1716 12L14.5147 6.34317L15.9289 4.92896L23 12ZM3.82843 12L9.48528 17.6569L8.07107 19.0711L1 12L8.07107 4.92896L9.48528 6.34317L3.82843 12Z"></path></svg>'),yp=xe('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M11 11V5H13V11H19V13H13V19H11V13H5V11H11Z"></path></svg>'),wp=xe('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M11 11V5H13V11H19V13H13V19H11V13H5V11H11Z"></path></svg>'),bp=ae('<div class="heading svelte-15t2v24"><!> <!></div> <!> <!> <div class="setting-title svelte-15t2v24">\u6267\u884C\u5F15\u64CE</div> <div class="setting-item svelte-15t2v24"><!></div> <div class="setting-title svelte-15t2v24">\u6267\u884C\u4EE3\u7801</div> <div class="setting-item svelte-15t2v24"><!></div> <div class="heading svelte-15t2v24"><!> <!></div> <!>',1);const xp={hash:"svelte-15t2v24",code:".heading.svelte-15t2v24 {display:flex;margin-bottom:10px;}.setting-title.svelte-15t2v24 {font-size:12px;color:#999;margin-bottom:4px;margin-top:10px;}.setting-item.svelte-15t2v24 {display:flex;align-items:center;justify-content:space-between;margin-bottom:10px;gap:10px;}"};function xd(e,t){ve(t,!0),qe(e,xp);const n=$(t,"data",7),o=gt(t,["$$slots","$$events","$$legacy","$$host","data"]),r=ct(),{addParameter:i}=$n(),{updateNodeData:a}=Ht(),l=[{label:"QLExpress",value:"qlexpress"},{label:"Groovy",value:"groovy"},{label:"JavaScript",value:"js"}];return cn(e,lt({get data(){return n()}},()=>o,{icon:u=>{T(u,mp())},children:(u,s)=>{var c=bp(),d=ke(c),f=W(d);We(f,{level:3,children:(S,E)=>{He(),T(S,Te("\u8F93\u5165\u53C2\u6570"))},$$slots:{default:!0}}),Ye(Z(f,2),{class:"input-btn-more",style:"margin-left: auto",onclick:()=>{i(r)},children:(S,E)=>{T(S,yp())},$$slots:{default:!0}}),K(d);var g=Z(d,2);Bt(g,{});var m=Z(g,2);We(m,{level:3,mt:"10px",children:(S,E)=>{He(),T(S,Te("\u4EE3\u7801"))},$$slots:{default:!0}});var v=Z(m,4),y=W(v);const w=ze(()=>n().engine?[n().engine]:["qlexpress"]);ln(y,{items:l,style:"width: 100%",placeholder:"\u8BF7\u9009\u62E9\u6267\u884C\u5F15\u64CE",onSelect:S=>{const E=S.value;a(r,()=>({engine:E}))},get value(){return p(w)}}),K(v);var h=Z(v,4),C=W(h);const b=ze(()=>n().code||"");Ct(C,{rows:10,placeholder:"\u8BF7\u8F93\u5165\u6267\u884C\u4EE3\u7801\uFF0C\u6CE8\uFF1A\u8F93\u51FA\u5185\u5BB9\u9700\u6DFB\u52A0\u5230_result\u4E2D\uFF0C\u5982\uFF1A_result.put(key, value)",style:"width: 100%",onchange:S=>{a(r,()=>({code:S.target.value}))},get value(){return p(b)}}),K(h);var k=Z(h,2),_=W(k);We(_,{level:3,mt:"10px",children:(S,E)=>{He(),T(S,Te("\u8F93\u51FA\u53C2\u6570"))},$$slots:{default:!0}}),Ye(Z(_,2),{class:"input-btn-more",style:"margin-left: auto",onclick:()=>{i(r,"outputDefs")},children:(S,E)=>{T(S,wp())},$$slots:{default:!0}}),K(k),Tn(Z(k,2),{}),T(u,c)},$$slots:{icon:!0,default:!0}})),ge({get data(){return n()},set data(u){n(u),x()}})}ce(xd,{data:{}},[],[],!0);var $p=xe('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M2 4C2 3.44772 2.44772 3 3 3H21C21.5523 3 22 3.44772 22 4V20C22 20.5523 21.5523 21 21 21H3C2.44772 21 2 20.5523 2 20V4ZM4 5V19H20V5H4ZM7 8H17V11H15V10H13V14H14.5V16H9.5V14H11V10H9V11H7V8Z"></path></svg>'),Cp=xe('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M11 11V5H13V11H19V13H13V19H11V13H5V11H11Z"></path></svg>'),kp=ae('<div class="heading svelte-15t2v24"><!> <!></div> <!> <!> <div class="setting-title svelte-15t2v24">\u6267\u884C\u4EE3\u7801</div> <div class="setting-item svelte-15t2v24"><!></div> <div class="heading svelte-15t2v24"><!></div> <!>',1);const _p={hash:"svelte-15t2v24",code:".heading.svelte-15t2v24 {display:flex;margin-bottom:10px;}.setting-title.svelte-15t2v24 {font-size:12px;color:#999;margin-bottom:4px;margin-top:10px;}.setting-item.svelte-15t2v24 {display:flex;align-items:center;justify-content:space-between;margin-bottom:10px;gap:10px;}"};function $d(e,t){ve(t,!0),qe(e,_p);const n=$(t,"data",7),o=gt(t,["$$slots","$$events","$$legacy","$$host","data"]),r=ct(),{addParameter:i}=$n(),{updateNodeData:a}=Ht();return Po(()=>{(!n().outputDefs||n().outputDefs.length===0)&&i(r,"outputDefs",{name:"output",dataType:"String",dataTypeDisabled:!0,deleteDisabled:!0})}),cn(e,lt({get data(){return n()}},()=>o,{icon:l=>{T(l,$p())},children:(l,u)=>{var s=kp(),c=ke(s),d=W(c);We(d,{level:3,children:(h,C)=>{He(),T(h,Te("\u8F93\u5165\u53C2\u6570"))},$$slots:{default:!0}}),Ye(Z(d,2),{class:"input-btn-more",style:"margin-left: auto",onclick:()=>{i(r)},children:(h,C)=>{T(h,Cp())},$$slots:{default:!0}}),K(c);var f=Z(c,2);Bt(f,{});var g=Z(f,2);We(g,{level:3,mt:"10px",children:(h,C)=>{He(),T(h,Te("\u4EE3\u7801"))},$$slots:{default:!0}});var m=Z(g,4),v=W(m);const y=ze(()=>n().template||"");Ct(v,{rows:10,placeholder:"\u8BF7\u8F93\u5165\u6267\u884C\u4EE3\u7801",style:"width: 100%",onchange:h=>{a(r,()=>({template:h.target.value}))},get value(){return p(y)}}),K(m);var w=Z(m,2);We(W(w),{level:3,mt:"10px",children:(h,C)=>{He(),T(h,Te("\u8F93\u51FA\u53C2\u6570"))},$$slots:{default:!0}}),K(w),Tn(Z(w,2),{}),T(l,s)},$$slots:{icon:!0,default:!0}})),ge({get data(){return n()},set data(l){n(l),x()}})}ce($d,{data:{}},[],[],!0);var Sp=xe('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M6.23509 6.45329C4.85101 7.89148 4 9.84636 4 12C4 16.4183 7.58172 20 12 20C13.0808 20 14.1116 19.7857 15.0521 19.3972C15.1671 18.6467 14.9148 17.9266 14.8116 17.6746C14.582 17.115 13.8241 16.1582 12.5589 14.8308C12.2212 14.4758 12.2429 14.2035 12.3636 13.3943L12.3775 13.3029C12.4595 12.7486 12.5971 12.4209 14.4622 12.1248C15.4097 11.9746 15.6589 12.3533 16.0043 12.8777C16.0425 12.9358 16.0807 12.9928 16.1198 13.0499C16.4479 13.5297 16.691 13.6394 17.0582 13.8064C17.2227 13.881 17.428 13.9751 17.7031 14.1314C18.3551 14.504 18.3551 14.9247 18.3551 15.8472V15.9518C18.3551 16.3434 18.3168 16.6872 18.2566 16.9859C19.3478 15.6185 20 13.8854 20 12C20 8.70089 18.003 5.8682 15.1519 4.64482C14.5987 5.01813 13.8398 5.54726 13.575 5.91C13.4396 6.09538 13.2482 7.04166 12.6257 7.11976C12.4626 7.14023 12.2438 7.12589 12.012 7.11097C11.3905 7.07058 10.5402 7.01606 10.268 7.75495C10.0952 8.2232 10.0648 9.49445 10.6239 10.1543C10.7134 10.2597 10.7307 10.4547 10.6699 10.6735C10.59 10.9608 10.4286 11.1356 10.3783 11.1717C10.2819 11.1163 10.0896 10.8931 9.95938 10.7412C9.64554 10.3765 9.25405 9.92233 8.74797 9.78176C8.56395 9.73083 8.36166 9.68867 8.16548 9.64736C7.6164 9.53227 6.99443 9.40134 6.84992 9.09302C6.74442 8.8672 6.74488 8.55621 6.74529 8.22764C6.74529 7.8112 6.74529 7.34029 6.54129 6.88256C6.46246 6.70541 6.35689 6.56446 6.23509 6.45329ZM12 22C6.47715 22 2 17.5228 2 12C2 6.47715 6.47715 2 12 2C17.5228 2 22 6.47715 22 12C22 17.5228 17.5228 22 12 22Z"></path></svg>'),Ep=xe('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M11 11V5H13V11H19V13H13V19H11V13H5V11H11Z"></path></svg>'),Pp=xe('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M11 11V5H13V11H19V13H13V19H11V13H5V11H11Z"></path></svg>'),Mp=xe('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M11 11V5H13V11H19V13H13V19H11V13H5V11H11Z"></path></svg>'),Vp=ae('<div class="heading svelte-1vtcqdz" style="padding-top: 10px"><!> <!></div> <!>',1),Hp=xe('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M11 11V5H13V11H19V13H13V19H11V13H5V11H11Z"></path></svg>'),zp=ae('<div class="heading svelte-1vtcqdz" style="padding-top: 10px"><!> <!></div> <!>',1),Np=ae('<div style="width: 100%"><!></div>'),Lp=ae('<div style="width: 100%"><!></div>'),Op=xe('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M11 11V5H13V11H19V13H13V19H11V13H5V11H11Z"></path></svg>'),Dp=ae('<div style="display: flex;gap: 2px;width: 100%;padding: 10px 0"><div><!></div> <div style="width: 100%"><!></div></div> <div class="heading svelte-1vtcqdz"><!> <!></div> <!> <div class="heading svelte-1vtcqdz" style="padding-top: 10px"><!> <!></div> <!> <!> <div class="radio-group svelte-1vtcqdz"><label class="svelte-1vtcqdz"><!>none</label> <label class="svelte-1vtcqdz"><!>form-data</label> <label class="svelte-1vtcqdz"><!>x-www-form-urlencoded</label> <label class="svelte-1vtcqdz"><!>json</label> <label class="svelte-1vtcqdz"><!>raw</label></div> <!> <!> <!> <!> <div class="heading svelte-1vtcqdz"><!> <!></div> <!>',1);const Tp={hash:"svelte-1vtcqdz",code:".heading.svelte-1vtcqdz {display:flex;margin-bottom:10px;}.radio-group.svelte-1vtcqdz {display:flex;margin:10px 0;}.radio-group.svelte-1vtcqdz label:where(.svelte-1vtcqdz) {display:flex;font-size:14px;}"};function Cd(e,t){ve(t,!0),qe(e,Tp);const n=$(t,"data",7),o=gt(t,["$$slots","$$events","$$legacy","$$host","data"]),r=[{value:"get",label:"GET"},{value:"post",label:"POST"},{value:"put",label:"PUT"},{value:"delete",label:"DELETE"},{value:"head",label:"HEAD"},{value:"patch",label:"PATCH"}],i=ct(),{addParameter:a}=$n(),{updateNodeData:l}=Ht();return cn(e,lt({get data(){return n()}},()=>o,{icon:u=>{T(u,Sp())},children:(u,s)=>{var c=Dp(),d=ke(c),f=W(d),g=W(f);const m=ze(()=>n().method?[n().method]:["get"]);ln(g,{items:r,style:"width: 100%",placeholder:"\u8BF7\u9009\u62E9\u8BF7\u6C42\u65B9\u5F0F",onSelect:ue=>{const Y=ue.value;l(i,()=>({method:Y}))},get value(){return p(m)}}),K(f);var v=Z(f,2),y=W(v);const w=ze(()=>n().url||"");$t(y,{placeholder:"\u8BF7\u8F93\u5165url",style:"width: 100%",onchange:ue=>{l(i,()=>({url:ue.target.value}))},get value(){return p(w)}}),K(v),K(d);var h=Z(d,2),C=W(h);We(C,{level:3,children:(ue,Y)=>{He(),T(ue,Te("Http \u5934\u4FE1\u606F"))},$$slots:{default:!0}}),Ye(Z(C,2),{class:"input-btn-more",style:"margin-left: auto",onclick:()=>{a(i,"headers")},children:(ue,Y)=>{T(ue,Ep())},$$slots:{default:!0}}),K(h);var b=Z(h,2);Bt(b,{dataKeyName:"headers"});var k=Z(b,2),_=W(k);We(_,{level:3,children:(ue,Y)=>{He(),T(ue,Te("\u53C2\u6570"))},$$slots:{default:!0}}),Ye(Z(_,2),{class:"input-btn-more",style:"margin-left: auto",onclick:()=>{a(i,"urlParameters")},children:(ue,Y)=>{T(ue,Pp())},$$slots:{default:!0}}),K(k);var S=Z(k,2);Bt(S,{dataKeyName:"urlParameters"});var E=Z(S,2);We(E,{level:3,mt:"10px",children:(ue,Y)=>{He(),T(ue,Te("Body"))},$$slots:{default:!0}});var L=Z(E,2),P=W(L),D=W(P);const M=ze(()=>!n().bodyType);$t(D,{type:"radio",name:"bodyType",value:"",get checked(){return p(M)},onchange:ue=>{var Y;(Y=ue.target)!=null&&Y.checked&&l(i,{bodyType:""})}}),He(),K(P);var H=Z(P,2),O=W(H);const V=ze(()=>n().bodyType==="form-data");$t(O,{type:"radio",name:"bodyType",value:"form-data",get checked(){return p(V)},onchange:ue=>{var Y;(Y=ue.target)!=null&&Y.checked&&l(i,{bodyType:"form-data"})}}),He(),K(H);var z=Z(H,2),N=W(z);const A=ze(()=>n().bodyType==="x-www-form-urlencoded");$t(N,{type:"radio",name:"bodyType",value:"x-www-form-urlencoded",get checked(){return p(A)},onchange:ue=>{var Y;(Y=ue.target)!=null&&Y.checked&&l(i,{bodyType:"x-www-form-urlencoded"})}}),He(),K(z);var X=Z(z,2),B=W(X);const Q=ze(()=>n().bodyType==="json");$t(B,{type:"radio",name:"bodyType",value:"json",get checked(){return p(Q)},onchange:ue=>{var Y;(Y=ue.target)!=null&&Y.checked&&l(i,{bodyType:"json"})}}),He(),K(X);var ne=Z(X,2),ee=W(ne);const me=ze(()=>n().bodyType==="raw");$t(ee,{type:"radio",name:"bodyType",value:"raw",get checked(){return p(me)},onchange:ue=>{var Y;(Y=ue.target)!=null&&Y.checked&&l(i,{bodyType:"raw"})}}),He(),K(ne),K(L);var de=Z(L,2),J=ue=>{var Y=Vp(),$e=ke(Y),Ce=W($e);We(Ce,{level:3,children:(Se,Ae)=>{He(),T(Se,Te("\u53C2\u6570"))},$$slots:{default:!0}}),Ye(Z(Ce,2),{class:"input-btn-more",style:"margin-left: auto",onclick:()=>{a(i,"fromData")},children:(Se,Ae)=>{T(Se,Mp())},$$slots:{default:!0}}),K($e),Bt(Z($e,2),{dataKeyName:"fromData"}),T(ue,Y)};_e(de,ue=>{n().bodyType==="form-data"&&ue(J)});var F=Z(de,2),I=ue=>{var Y=zp(),$e=ke(Y),Ce=W($e);We(Ce,{level:3,children:(Se,Ae)=>{He(),T(Se,Te("\u53C2\u6570"))},$$slots:{default:!0}}),Ye(Z(Ce,2),{class:"input-btn-more",style:"margin-left: auto",onclick:()=>{a(i,"fromUrlencoded")},children:(Se,Ae)=>{T(Se,Hp())},$$slots:{default:!0}}),K($e),Bt(Z($e,2),{dataKeyName:"fromUrlencoded"}),T(ue,Y)};_e(F,ue=>{n().bodyType==="x-www-form-urlencoded"&&ue(I)});var j=Z(F,2),G=ue=>{var Y=Np();Ct(W(Y),{rows:"5",style:"width: 100%",placeholder:"\u8BF7\u8F93\u5165 json \u4FE1\u606F",get value(){return n().bodyJson},oninput:$e=>{l(i,{bodyJson:$e.target.value})}}),K(Y),T(ue,Y)};_e(j,ue=>{n().bodyType==="json"&&ue(G)});var le=Z(j,2),we=ue=>{var Y=Lp();Ct(W(Y),{rows:"5",style:"width: 100%",placeholder:"\u8BF7\u8F93\u5165\u8BF7\u6C42\u4FE1\u606F",get value(){return n().bodyRaw},oninput:$e=>{l(i,{bodyRaw:$e.target.value})}}),K(Y),T(ue,Y)};_e(le,ue=>{n().bodyType==="raw"&&ue(we)});var Pe=Z(le,2),Ne=W(Pe);We(Ne,{level:3,mt:"10px",children:(ue,Y)=>{He(),T(ue,Te("\u8F93\u51FA\u53C2\u6570"))},$$slots:{default:!0}}),Ye(Z(Ne,2),{class:"input-btn-more",style:"margin-left: auto",onclick:()=>{a(i,"outputDefs")},children:(ue,Y)=>{T(ue,Op())},$$slots:{default:!0}}),K(Pe),Tn(Z(Pe,2),{}),T(u,c)},$$slots:{icon:!0,default:!0}})),ge({get data(){return n()},set data(u){n(u),x()}})}ce(Cd,{data:{}},[],[],!0);var Ap=xe('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M15.5 5C13.567 5 12 6.567 12 8.5C12 10.433 13.567 12 15.5 12C17.433 12 19 10.433 19 8.5C19 6.567 17.433 5 15.5 5ZM10 8.5C10 5.46243 12.4624 3 15.5 3C18.5376 3 21 5.46243 21 8.5C21 9.6575 20.6424 10.7315 20.0317 11.6175L22.7071 14.2929L21.2929 15.7071L18.6175 13.0317C17.7315 13.6424 16.6575 14 15.5 14C12.4624 14 10 11.5376 10 8.5ZM3 4H8V6H3V4ZM3 11H8V13H3V11ZM21 18V20H3V18H21Z"></path></svg>'),Ip=xe('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M11 11V5H13V11H19V13H13V19H11V13H5V11H11Z"></path></svg>'),Zp=ae('<div class="heading svelte-15t2v24"><!> <!></div> <!> <!> <div class="setting-title svelte-15t2v24">\u77E5\u8BC6\u5E93</div> <div class="setting-item svelte-15t2v24"><!></div> <div class="setting-title svelte-15t2v24">\u83B7\u53D6\u6570\u636E\u91CF</div> <div class="setting-item svelte-15t2v24"><!></div> <div class="heading svelte-15t2v24"><!></div> <!>',1);const Bp={hash:"svelte-15t2v24",code:".heading.svelte-15t2v24 {display:flex;margin-bottom:10px;}.setting-title.svelte-15t2v24 {font-size:12px;color:#999;margin-bottom:4px;margin-top:10px;}.setting-item.svelte-15t2v24 {display:flex;align-items:center;justify-content:space-between;margin-bottom:10px;gap:10px;}"};function kd(e,t){ve(t,!0),qe(e,Bp);const n=$(t,"data",7),o=gt(t,["$$slots","$$events","$$legacy","$$host","data"]),r=ct(),{addParameter:i}=$n(),a=Tr();let l=Yn(Et([]));tn(async()=>{var s,c;const d=await((c=(s=a.provider)==null?void 0:s.knowledge)==null?void 0:c.call(s));p(l).push(...d||[])});const{updateNodeData:u}=Ht();return Po(()=>{(!n().outputDefs||n().outputDefs.length===0)&&i(r,"outputDefs",{name:"documents",dataType:"Array",nameDisabled:!0,dataTypeDisabled:!0,addChildDisabled:!0,children:[{name:"title",dataType:"String",nameDisabled:!0,dataTypeDisabled:!0},{name:"content",dataType:"String",nameDisabled:!0,dataTypeDisabled:!0},{name:"documentId",dataType:"Number",nameDisabled:!0,dataTypeDisabled:!0},{name:"knowledgeId",dataType:"Number",nameDisabled:!0,dataTypeDisabled:!0}]})}),cn(e,lt({get data(){return n()}},()=>o,{icon:s=>{T(s,Ap())},children:(s,c)=>{var d=Zp(),f=ke(d),g=W(f);We(g,{level:3,children:(k,_)=>{He(),T(k,Te("\u8F93\u5165\u53C2\u6570"))},$$slots:{default:!0}}),Ye(Z(g,2),{class:"input-btn-more",style:"margin-left: auto",onclick:()=>{i(r)},children:(k,_)=>{T(k,Ip())},$$slots:{default:!0}}),K(f);var m=Z(f,2);Bt(m,{});var v=Z(m,2);We(v,{level:3,mt:"10px",children:(k,_)=>{He(),T(k,Te("\u77E5\u8BC6\u5E93\u8BBE\u7F6E"))},$$slots:{default:!0}});var y=Z(v,4),w=W(y);const h=ze(()=>n().knowledgeId?[n().knowledgeId]:[]);ln(w,{get items(){return p(l)},style:"width: 100%",placeholder:"\u8BF7\u9009\u62E9\u77E5\u8BC6\u5E93",onSelect:k=>{const _=k.value;u(r,()=>({knowledgeId:_}))},get value(){return p(h)}}),K(y);var C=Z(y,4);$t(W(C),{placeholder:"\u641C\u7D22\u7684\u6570\u636E\u6761\u6570",style:"width: 100%"}),K(C);var b=Z(C,2);We(W(b),{level:3,mt:"10px",children:(k,_)=>{He(),T(k,Te("\u8F93\u51FA\u53C2\u6570"))},$$slots:{default:!0}}),K(b),Tn(Z(b,2),{}),T(s,d)},$$slots:{icon:!0,default:!0}})),ge({get data(){return n()},set data(s){n(s),x()}})}ce(kd,{data:{}},[],[],!0);var Rp=xe('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M18.031 16.6168L22.3137 20.8995L20.8995 22.3137L16.6168 18.031C15.0769 19.263 13.124 20 11 20C6.032 20 2 15.968 2 11C2 6.032 6.032 2 11 2C15.968 2 20 6.032 20 11C20 13.124 19.263 15.0769 18.031 16.6168ZM16.0247 15.8748C17.2475 14.6146 18 12.8956 18 11C18 7.1325 14.8675 4 11 4C7.1325 4 4 7.1325 4 11C4 14.8675 7.1325 18 11 18C12.8956 18 14.6146 17.2475 15.8748 16.0247L16.0247 15.8748Z"></path></svg>'),Xp=xe('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M11 11V5H13V11H19V13H13V19H11V13H5V11H11Z"></path></svg>'),Yp=ae('<div class="heading svelte-15t2v24"><!> <!></div> <!> <!> <div class="setting-title svelte-15t2v24">API \u670D\u52A1\u5546</div> <div class="setting-item svelte-15t2v24"><!></div> <div class="setting-title svelte-15t2v24">API Key</div> <div class="setting-item svelte-15t2v24"><!></div> <div class="setting-title svelte-15t2v24">\u5173\u952E\u5B57</div> <div class="setting-item svelte-15t2v24"><!></div> <div class="setting-title svelte-15t2v24">\u6570\u636E\u91CF</div> <div class="setting-item svelte-15t2v24"><!></div> <div class="setting-title svelte-15t2v24">\u5176\u4ED6\u53C2\u6570</div> <div class="setting-item svelte-15t2v24"><!></div> <div class="heading svelte-15t2v24"><!></div> <!>',1);const Kp={hash:"svelte-15t2v24",code:".heading.svelte-15t2v24 {display:flex;margin-bottom:10px;}.setting-title.svelte-15t2v24 {font-size:12px;color:#999;margin-bottom:4px;margin-top:10px;}.setting-item.svelte-15t2v24 {display:flex;align-items:center;justify-content:space-between;margin-bottom:10px;gap:10px;}"};function _d(e,t){ve(t,!0),qe(e,Kp);const n=$(t,"data",7),o=gt(t,["$$slots","$$events","$$legacy","$$host","data"]),r=ct(),{addParameter:i}=$n(),a=Tr();let l=Yn(Et([]));tn(async()=>{var s;const c=await((s=a.provider)==null?void 0:s.knowledge());p(l).push(...c||[])});const{updateNodeData:u}=Ht();return Po(()=>{(!n().outputDefs||n().outputDefs.length===0)&&i(r,"outputDefs",{name:"documents",dataType:"Array",nameDisabled:!0,dataTypeDisabled:!0,addChildDisabled:!0,children:[{name:"title",dataType:"String",nameDisabled:!0,dataTypeDisabled:!0},{name:"content",dataType:"String",nameDisabled:!0,dataTypeDisabled:!0},{name:"documentId",dataType:"Number",nameDisabled:!0,dataTypeDisabled:!0},{name:"knowledgeId",dataType:"Number",nameDisabled:!0,dataTypeDisabled:!0}]})}),cn(e,lt({get data(){return n()}},()=>o,{icon:s=>{T(s,Rp())},children:(s,c)=>{var d=Yp(),f=ke(d),g=W(f);We(g,{level:3,children:(E,L)=>{He(),T(E,Te("\u8F93\u5165\u53C2\u6570"))},$$slots:{default:!0}}),Ye(Z(g,2),{class:"input-btn-more",style:"margin-left: auto",onclick:()=>{i(r)},children:(E,L)=>{T(E,Xp())},$$slots:{default:!0}}),K(f);var m=Z(f,2);Bt(m,{});var v=Z(m,2);We(v,{level:3,mt:"10px",children:(E,L)=>{He(),T(E,Te("\u641C\u7D22\u5F15\u64CE\u8BBE\u7F6E"))},$$slots:{default:!0}});var y=Z(v,4),w=W(y);const h=ze(()=>n().knowledgeId?[n().knowledgeId]:[]);ln(w,{get items(){return p(l)},style:"width: 100%",placeholder:"\u8BF7\u9009\u62E9 API \u670D\u52A1\u5546",onSelect:E=>{const L=E.value;u(r,()=>({knowledgeId:L}))},get value(){return p(h)}}),K(y);var C=Z(y,4);$t(W(C),{placeholder:"\u8BF7\u8F93\u5165 API Key",style:"width: 100%"}),K(C);var b=Z(C,4);$t(W(b),{placeholder:"\u8BF7\u8F93\u5165\u5173\u952E\u5B57",style:"width: 100%"}),K(b);var k=Z(b,4);$t(W(k),{placeholder:"\u641C\u7D22\u7684\u6570\u636E\u6761\u6570",style:"width: 100%"}),K(k);var _=Z(k,4);Ct(W(_),{rows:3,placeholder:"\u8BF7\u8F93\u5165\u5176\u4ED6\u53C2\u6570\uFF08Property \u683C\u5F0F\uFF09",style:"width: 100%"}),K(_);var S=Z(_,2);We(W(S),{level:3,mt:"10px",children:(E,L)=>{He(),T(E,Te("\u8F93\u51FA\u53C2\u6570"))},$$slots:{default:!0}}),K(S),Tn(Z(S,2),{}),T(s,d)},$$slots:{icon:!0,default:!0}})),ge({get data(){return n()},set data(s){n(s),x()}})}ce(_d,{data:{}},[],[],!0);var Wp=xe('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M5.46257 4.43262C7.21556 2.91688 9.5007 2 12 2C17.5228 2 22 6.47715 22 12C22 14.1361 21.3302 16.1158 20.1892 17.7406L17 12H20C20 7.58172 16.4183 4 12 4C9.84982 4 7.89777 4.84827 6.46023 6.22842L5.46257 4.43262ZM18.5374 19.5674C16.7844 21.0831 14.4993 22 12 22C6.47715 22 2 17.5228 2 12C2 9.86386 2.66979 7.88416 3.8108 6.25944L7 12H4C4 16.4183 7.58172 20 12 20C14.1502 20 16.1022 19.1517 17.5398 17.7716L18.5374 19.5674Z"></path></svg>'),jp=xe('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M11 11V5H13V11H19V13H13V19H11V13H5V11H11Z"></path></svg>'),qp=ae('<div class="heading svelte-md8tgj"><!> <!></div> <!> <div class="heading svelte-md8tgj"><!></div> <!>',1);const Fp={hash:"svelte-md8tgj",code:".heading.svelte-md8tgj {display:flex;margin-bottom:10px;}.loop_handle_wrapper ::after {content:'\u5FAA\u73AF\u4F53';width:100px;height:20px;background:#000;color:#fff;display:flex;justify-content:center;align-items:center;}"};function Sd(e,t){ve(t,!0),qe(e,Fp);const n=$(t,"data",7),o=gt(t,["$$slots","$$events","$$legacy","$$host","data"]),r=ct(),{addParameter:i}=$n(),a=Tr();let l=Yn(Et([]));return tn(async()=>{var u;const s=await((u=a.provider)==null?void 0:u.knowledge());p(l).push(...s||[])}),cn(e,lt({get data(){return n()}},()=>o,{icon:u=>{T(u,Wp())},handle:u=>{eo(u,{type:"source",get position(){return Ee.Bottom},id:"loop_handle",style:"bottom: -12px;width: 100px",class:"loop_handle_wrapper"})},children:(u,s)=>{var c=qp(),d=ke(c),f=W(d);We(f,{level:3,children:(v,y)=>{He(),T(v,Te("\u5FAA\u73AF\u53D8\u91CF"))},$$slots:{default:!0}}),Ye(Z(f,2),{class:"input-btn-more",style:"margin-left: auto",onclick:()=>{i(r)},children:(v,y)=>{T(v,jp())},$$slots:{default:!0}}),K(d);var g=Z(d,2);Bt(g,{});var m=Z(g,2);We(W(m),{level:3,mt:"10px",children:(v,y)=>{He(),T(v,Te("\u8F93\u51FA\u53C2\u6570"))},$$slots:{default:!0}}),K(m),Tn(Z(m,2),{}),T(u,c)},$$slots:{icon:!0,handle:!0,default:!0}})),ge({get data(){return n()},set data(u){n(u),x()}})}ce(Sd,{data:{}},[],[],!0);var Gp=xe('<svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" fill="currentColor" p-id="2577" width="200" height="200"><path d="M312.096 408.576l67.84 67.84 45.312-45.216a32 32 0 0 1 45.248 45.248l-45.28 45.248 90.496 90.496 45.28-45.216a32 32 0 0 1 45.248 45.248l-45.248 45.248 67.904 67.872-90.528 90.528a224.064 224.064 0 0 1-292.544 21.024L176.32 906.368a32 32 0 0 1-45.248-45.248l69.504-69.472a224.064 224.064 0 0 1 21.024-292.576l90.496-90.496z m0 90.496L266.848 544.32a160 160 0 0 0-4.8 221.28l4.8 4.992a160 160 0 0 0 221.248 4.8l5.024-4.8 45.248-45.248-226.272-226.24z m610.272-384a32 32 0 0 1 0 45.248l-69.44 69.504a224.064 224.064 0 0 1-21.056 292.544l-90.528 90.528-316.8-316.8 90.56-90.496a224.064 224.064 0 0 1 292.544-21.024l69.44-69.504a32 32 0 0 1 45.28 0zM565.344 246.08l-5.024 4.8-45.248 45.248 226.272 226.272 45.248-45.248a160 160 0 0 0 4.8-221.28l-4.8-4.992a160 160 0 0 0-221.248-4.8z" p-id="2578"></path></svg>'),Up=xe('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M11 11V5H13V11H19V13H13V19H11V13H5V11H11Z"></path></svg>'),Jp=xe('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M11 11V5H13V11H19V13H13V19H11V13H5V11H11Z"></path></svg>'),Qp=ae('<div class="heading svelte-15t2v24"><!> <!></div> <!> <!> <div class="setting-title svelte-15t2v24">\u9009\u62E9\u5185\u90E8\u63A5\u53E3</div> <div class="setting-item svelte-15t2v24"><!></div> <div class="heading svelte-15t2v24"><!> <!></div> <!>',1);const eh={hash:"svelte-15t2v24",code:".heading.svelte-15t2v24 {display:flex;margin-bottom:10px;}.setting-title.svelte-15t2v24 {font-size:12px;color:#999;margin-bottom:4px;margin-top:10px;}.setting-item.svelte-15t2v24 {display:flex;align-items:center;justify-content:space-between;margin-bottom:10px;gap:10px;}"};function Ed(e,t){ve(t,!0),qe(e,eh);const n=$(t,"data",7),o=gt(t,["$$slots","$$events","$$legacy","$$host","data"]),r=ct(),{addParameter:i}=$n(),{updateNodeData:a}=Ht(),l=Tr();let u=Yn(Et([]));return tn(async()=>{var s,c;const d=await((c=(s=l.provider)==null?void 0:s.internal)==null?void 0:c.call(s));p(u).push(...d||[])}),cn(e,lt({get data(){return n()}},()=>o,{icon:s=>{T(s,Gp())},children:(s,c)=>{var d=Qp(),f=ke(d),g=W(f);We(g,{level:3,children:(k,_)=>{He(),T(k,Te("\u8F93\u5165\u53C2\u6570"))},$$slots:{default:!0}}),Ye(Z(g,2),{class:"input-btn-more",style:"margin-left: auto",onclick:()=>{i(r)},children:(k,_)=>{T(k,Up())},$$slots:{default:!0}}),K(f);var m=Z(f,2);Bt(m,{});var v=Z(m,2);We(v,{level:3,mt:"10px",children:(k,_)=>{He(),T(k,Te("\u63A5\u53E3"))},$$slots:{default:!0}});var y=Z(v,4),w=W(y);const h=ze(()=>n().method?[n().method]:[""]);ln(w,{get items(){return p(u)},style:"width: 100%",placeholder:"\u8BF7\u9009\u62E9\u5185\u90E8\u63A5\u53E3",onSelect:k=>{const _=k.value;a(r,()=>({method:_}))},get value(){return p(h)}}),K(y);var C=Z(y,2),b=W(C);We(b,{level:3,mt:"10px",children:(k,_)=>{He(),T(k,Te("\u8F93\u51FA\u53C2\u6570"))},$$slots:{default:!0}}),Ye(Z(b,2),{class:"input-btn-more",style:"margin-left: auto",onclick:()=>{i(r,"outputDefs")},children:(k,_)=>{T(k,Jp())},$$slots:{default:!0}}),K(C),Tn(Z(C,2),{}),T(s,d)},$$slots:{icon:!0,default:!0}})),ge({get data(){return n()},set data(s){n(s),x()}})}ce(Ed,{data:{}},[],[],!0);const th={startNode:gd,codeNode:xd,llmNode:bd,templateNode:$d,httpNode:Cd,knowledgeNode:kd,searchEngineNode:_d,loopNode:Sd,internalNode:Ed,endNode:yd};var nh=ae("<!> ",1);function Pd(e,t){ve(t,!0);const n=$(t,"icon",7),o=$(t,"title",7),r=$(t,"type",7),i=$(t,"description",7),a=$(t,"extra",7);return Ye(e,{draggable:!0,ondragstart:l=>{if(!l.dataTransfer)return null;const u={type:r(),data:{title:o(),description:i(),systemPrompt:"",userPrompt:"",...a()}};l.dataTransfer.setData("application/tinyflow",JSON.stringify(u)),l.dataTransfer.effectAllowed="move"},children:(l,u)=>{var s=nh(),c=ke(s);cs(c,n);var d=Z(c);Me(()=>Tt(d,` ${o()??""}`)),T(l,s)},$$slots:{default:!0}}),ge({get icon(){return n()},set icon(l){n(l),x()},get title(){return o()},set title(l){o(l),x()},get type(){return r()},set type(l){r(l),x()},get description(){return i()},set description(l){i(l),x()},get extra(){return a()},set extra(l){a(l),x()}})}ce(Pd,{icon:{},title:{},type:{},description:{},extra:{}},[],[],!0);var oh=xe('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M4.83582 12L11.0429 18.2071L12.4571 16.7929L7.66424 12L12.4571 7.20712L11.0429 5.79291L4.83582 12ZM10.4857 12L16.6928 18.2071L18.107 16.7929L13.3141 12L18.107 7.20712L16.6928 5.79291L10.4857 12Z"></path></svg>'),rh=xe('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M19.1642 12L12.9571 5.79291L11.5429 7.20712L16.3358 12L11.5429 16.7929L12.9571 18.2071L19.1642 12ZM13.5143 12L7.30722 5.79291L5.89301 7.20712L10.6859 12L5.89301 16.7929L7.30722 18.2071L13.5143 12Z"></path></svg>'),ih=ae('<div><div class="tf-toolbar-container "><div class="tf-toolbar-container-header"><!></div> <div class="tf-toolbar-container-body"><div class="tf-toolbar-container-base"></div> <div class="tf-toolbar-container-tools"><!></div></div></div> <!></div>');function Md(e){let t=Yn("base"),n=Yn("show");const o=[{icon:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M12 22C6.47715 22 2 17.5228 2 12C2 6.47715 6.47715 2 12 2C17.5228 2 22 6.47715 22 12C22 17.5228 17.5228 22 12 22ZM12 20C16.4183 20 20 16.4183 20 12C20 7.58172 16.4183 4 12 4C7.58172 4 4 7.58172 4 12C4 16.4183 7.58172 20 12 20ZM12 15C10.3431 15 9 13.6569 9 12C9 10.3431 10.3431 9 12 9C13.6569 9 15 10.3431 15 12C15 13.6569 13.6569 15 12 15Z"></path></svg>',title:"\u5F00\u59CB\u8282\u70B9",type:"startNode",description:"\u5F00\u59CB\u5B9A\u4E49\u8F93\u5165\u53C2\u6570"},{icon:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M5.46257 4.43262C7.21556 2.91688 9.5007 2 12 2C17.5228 2 22 6.47715 22 12C22 14.1361 21.3302 16.1158 20.1892 17.7406L17 12H20C20 7.58172 16.4183 4 12 4C9.84982 4 7.89777 4.84827 6.46023 6.22842L5.46257 4.43262ZM18.5374 19.5674C16.7844 21.0831 14.4993 22 12 22C6.47715 22 2 17.5228 2 12C2 9.86386 2.66979 7.88416 3.8108 6.25944L7 12H4C4 16.4183 7.58172 20 12 20C14.1502 20 16.1022 19.1517 17.5398 17.7716L18.5374 19.5674Z"></path></svg>',title:"\u5FAA\u73AF",type:"loopNode",description:"\u7528\u4E8E\u5FAA\u73AF\u6267\u884C\u4EFB\u52A1"},{icon:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M20.7134 7.12811L20.4668 7.69379C20.2864 8.10792 19.7136 8.10792 19.5331 7.69379L19.2866 7.12811C18.8471 6.11947 18.0555 5.31641 17.0677 4.87708L16.308 4.53922C15.8973 4.35653 15.8973 3.75881 16.308 3.57612L17.0252 3.25714C18.0384 2.80651 18.8442 1.97373 19.2761 0.930828L19.5293 0.319534C19.7058 -0.106511 20.2942 -0.106511 20.4706 0.319534L20.7238 0.930828C21.1558 1.97373 21.9616 2.80651 22.9748 3.25714L23.6919 3.57612C24.1027 3.75881 24.1027 4.35653 23.6919 4.53922L22.9323 4.87708C21.9445 5.31641 21.1529 6.11947 20.7134 7.12811ZM9 2C13.0675 2 16.426 5.03562 16.9337 8.96494L19.1842 12.5037C19.3324 12.7367 19.3025 13.0847 18.9593 13.2317L17 14.071V17C17 18.1046 16.1046 19 15 19H13.001L13 22H4L4.00025 18.3061C4.00033 17.1252 3.56351 16.0087 2.7555 15.0011C1.65707 13.6313 1 11.8924 1 10C1 5.58172 4.58172 2 9 2ZM9 4C5.68629 4 3 6.68629 3 10C3 11.3849 3.46818 12.6929 4.31578 13.7499C5.40965 15.114 6.00036 16.6672 6.00025 18.3063L6.00013 20H11.0007L11.0017 17H15V12.7519L16.5497 12.0881L15.0072 9.66262L14.9501 9.22118C14.5665 6.25141 12.0243 4 9 4ZM19.4893 16.9929L21.1535 18.1024C22.32 16.3562 23 14.2576 23 12.0001C23 11.317 22.9378 10.6486 22.8186 10L20.8756 10.5C20.9574 10.9878 21 11.489 21 12.0001C21 13.8471 20.4436 15.5642 19.4893 16.9929Z"></path></svg>',title:"\u5927\u6A21\u578B",type:"llmNode",description:"\u4F7F\u7528\u5927\u6A21\u578B\u5904\u7406\u95EE\u9898"},{icon:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M15.5 5C13.567 5 12 6.567 12 8.5C12 10.433 13.567 12 15.5 12C17.433 12 19 10.433 19 8.5C19 6.567 17.433 5 15.5 5ZM10 8.5C10 5.46243 12.4624 3 15.5 3C18.5376 3 21 5.46243 21 8.5C21 9.6575 20.6424 10.7315 20.0317 11.6175L22.7071 14.2929L21.2929 15.7071L18.6175 13.0317C17.7315 13.6424 16.6575 14 15.5 14C12.4624 14 10 11.5376 10 8.5ZM3 4H8V6H3V4ZM3 11H8V13H3V11ZM21 18V20H3V18H21Z"></path></svg>',title:"\u77E5\u8BC6\u5E93",type:"knowledgeNode",description:"\u901A\u8FC7\u77E5\u8BC6\u5E93\u83B7\u53D6\u5185\u5BB9"},{icon:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M18.031 16.6168L22.3137 20.8995L20.8995 22.3137L16.6168 18.031C15.0769 19.263 13.124 20 11 20C6.032 20 2 15.968 2 11C2 6.032 6.032 2 11 2C15.968 2 20 6.032 20 11C20 13.124 19.263 15.0769 18.031 16.6168ZM16.0247 15.8748C17.2475 14.6146 18 12.8956 18 11C18 7.1325 14.8675 4 11 4C7.1325 4 4 7.1325 4 11C4 14.8675 7.1325 18 11 18C12.8956 18 14.6146 17.2475 15.8748 16.0247L16.0247 15.8748Z"></path></svg>',title:"\u641C\u7D22\u5F15\u64CE",type:"searchEngineNode",description:"\u901A\u8FC7\u641C\u7D22\u5F15\u64CE\u641C\u7D22\u5185\u5BB9"},{icon:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M6.23509 6.45329C4.85101 7.89148 4 9.84636 4 12C4 16.4183 7.58172 20 12 20C13.0808 20 14.1116 19.7857 15.0521 19.3972C15.1671 18.6467 14.9148 17.9266 14.8116 17.6746C14.582 17.115 13.8241 16.1582 12.5589 14.8308C12.2212 14.4758 12.2429 14.2035 12.3636 13.3943L12.3775 13.3029C12.4595 12.7486 12.5971 12.4209 14.4622 12.1248C15.4097 11.9746 15.6589 12.3533 16.0043 12.8777C16.0425 12.9358 16.0807 12.9928 16.1198 13.0499C16.4479 13.5297 16.691 13.6394 17.0582 13.8064C17.2227 13.881 17.428 13.9751 17.7031 14.1314C18.3551 14.504 18.3551 14.9247 18.3551 15.8472V15.9518C18.3551 16.3434 18.3168 16.6872 18.2566 16.9859C19.3478 15.6185 20 13.8854 20 12C20 8.70089 18.003 5.8682 15.1519 4.64482C14.5987 5.01813 13.8398 5.54726 13.575 5.91C13.4396 6.09538 13.2482 7.04166 12.6257 7.11976C12.4626 7.14023 12.2438 7.12589 12.012 7.11097C11.3905 7.07058 10.5402 7.01606 10.268 7.75495C10.0952 8.2232 10.0648 9.49445 10.6239 10.1543C10.7134 10.2597 10.7307 10.4547 10.6699 10.6735C10.59 10.9608 10.4286 11.1356 10.3783 11.1717C10.2819 11.1163 10.0896 10.8931 9.95938 10.7412C9.64554 10.3765 9.25405 9.92233 8.74797 9.78176C8.56395 9.73083 8.36166 9.68867 8.16548 9.64736C7.6164 9.53227 6.99443 9.40134 6.84992 9.09302C6.74442 8.8672 6.74488 8.55621 6.74529 8.22764C6.74529 7.8112 6.74529 7.34029 6.54129 6.88256C6.46246 6.70541 6.35689 6.56446 6.23509 6.45329ZM12 22C6.47715 22 2 17.5228 2 12C2 6.47715 6.47715 2 12 2C17.5228 2 22 6.47715 22 12C22 17.5228 17.5228 22 12 22Z"></path></svg>',title:"Http \u8BF7\u6C42",type:"httpNode",description:"\u901A\u8FC7 HTTP \u8BF7\u6C42\u83B7\u53D6\u6570\u636E"},{icon:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M23 12L15.9289 19.0711L14.5147 17.6569L20.1716 12L14.5147 6.34317L15.9289 4.92896L23 12ZM3.82843 12L9.48528 17.6569L8.07107 19.0711L1 12L8.07107 4.92896L9.48528 6.34317L3.82843 12Z"></path></svg>',title:"\u52A8\u6001\u4EE3\u7801",type:"codeNode",description:"\u52A8\u6001\u6267\u884C\u4EE3\u7801"},{icon:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M2 4C2 3.44772 2.44772 3 3 3H21C21.5523 3 22 3.44772 22 4V20C22 20.5523 21.5523 21 21 21H3C2.44772 21 2 20.5523 2 20V4ZM4 5V19H20V5H4ZM7 8H17V11H15V10H13V14H14.5V16H9.5V14H11V10H9V11H7V8Z"></path></svg>',title:"\u5185\u5BB9\u6A21\u677F",type:"templateNode",description:"\u901A\u8FC7\u6A21\u677F\u5F15\u64CE\u751F\u6210\u5185\u5BB9"},{icon:'<svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" fill="currentColor" p-id="2577" width="200" height="200"><path d="M312.096 408.576l67.84 67.84 45.312-45.216a32 32 0 0 1 45.248 45.248l-45.28 45.248 90.496 90.496 45.28-45.216a32 32 0 0 1 45.248 45.248l-45.248 45.248 67.904 67.872-90.528 90.528a224.064 224.064 0 0 1-292.544 21.024L176.32 906.368a32 32 0 0 1-45.248-45.248l69.504-69.472a224.064 224.064 0 0 1 21.024-292.576l90.496-90.496z m0 90.496L266.848 544.32a160 160 0 0 0-4.8 221.28l4.8 4.992a160 160 0 0 0 221.248 4.8l5.024-4.8 45.248-45.248-226.272-226.24z m610.272-384a32 32 0 0 1 0 45.248l-69.44 69.504a224.064 224.064 0 0 1-21.056 292.544l-90.528 90.528-316.8-316.8 90.56-90.496a224.064 224.064 0 0 1 292.544-21.024l69.44-69.504a32 32 0 0 1 45.28 0zM565.344 246.08l-5.024 4.8-45.248 45.248 226.272 226.272 45.248-45.248a160 160 0 0 0 4.8-221.28l-4.8-4.992a160 160 0 0 0-221.248-4.8z" p-id="2578"></path></svg>',title:"\u5185\u90E8\u63A5\u53E3",type:"internalNode",description:"\u6267\u884C\u5185\u90E8\u63D0\u4F9B\u63A5\u53E3"},{icon:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M6 5.1438V16.0002H18.3391L6 5.1438ZM4 2.932C4 2.07155 5.01456 1.61285 5.66056 2.18123L21.6501 16.2494C22.3423 16.8584 21.9116 18.0002 20.9896 18.0002H6V22H4V2.932Z"></path></svg>',title:"\u7ED3\u675F\u8282\u70B9",type:"endNode",description:"\u7ED3\u675F\u5B9A\u4E49\u8F93\u51FA\u53C2\u6570"}];var r=ih(),i=W(r),a=W(i);Wc(W(a),{style:"width: 100%",items:[{label:"\u57FA\u7840\u8282\u70B9",value:"base"},{label:"\u4E1A\u52A1\u5DE5\u5177",value:"tools"}],onChange:c=>{te(t,Et(c.value.toString()))}}),K(a);var l=Z(a,2),u=W(l);At(u,21,()=>o,ni,(c,d)=>{Pd(c,lt(()=>p(d)))}),K(u);var s=Z(u,2);Ye(W(s),{children:(c,d)=>{He(),T(c,Te("\u6D4B\u8BD5\u4E1A\u52A1\u6309\u94AE"))},$$slots:{default:!0}}),K(s),K(l),K(i),Ye(Z(i,2),{onclick:()=>{te(n,Et(p(n)?"":"show"))},children:(c,d)=>{var f=Ue(),g=ke(f),m=y=>{T(y,oh())},v=y=>{T(y,rh())};_e(g,y=>{p(n)==="show"?y(m):y(v,!1)}),T(c,f)},$$slots:{default:!0}}),K(r),Me(()=>{bt(r,1,`tf-toolbar ${p(n)??""}`),pe(u,"style",`display: ${(p(t)==="base"?"flex":"none")??""}`),pe(s,"style",`display: ${(p(t)!=="base"?"flex":"none")??""}`)}),T(e,r)}ce(Md,{},[],[],!0);var ah=ae('<div class="panel-content svelte-1oe15vw"><div>\u8FB9\u5C5E\u6027\u8BBE\u7F6E</div> <div class="setting-title svelte-1oe15vw">\u8FB9\u6761\u4EF6\u8BBE\u7F6E</div> <div class="setting-item"><!></div></div>'),lh=ae("<!> <!> <!> <!>",1),sh=ae('<div style="position: relative; height: 100%; width: 100%"><!> <!></div>');const uh={hash:"svelte-1oe15vw",code:".panel-content.svelte-1oe15vw {padding:10px;background-color:#fff;border-radius:5px;box-shadow:0 2px 4px rgba(0, 0, 0, 0.1);width:200px;border:1px solid #efefef;}.setting-title.svelte-1oe15vw {margin:10px 0;font-size:12px;color:#999;}"};function Vd(e,t){ve(t,!0),qe(e,uh);const n=$(t,"onInit",7),o=Ht();n()(o);let r=Yn(!1);const{getNode:i}=(()=>{const{nodeLookup:f}=Ke();return{getNode:g=>{var m;return(m=q(f).get(g))==null?void 0:m.internals.userNode}}})(),{ensureParentInNodesBefore:a}=(()=>{const{nodes:f}=Ke();return{ensureParentInNodesBefore:(g,m)=>{f.update(v=>{let y=-1;for(let C=0;C<v.length;C++)if(v[C].id===g){y=C;break}if(y<=0)return v;let w=-1;for(let C=0;C<y;C++)if(v[C].parentId===g||v[C].id===m){w=C;break}if(w==-1)return v;const h=v[y];for(let C=y;C>w;C--)v[C]=v[C-1];return v[w]=h,v})}}})(),{getEdgesByTarget:l}=(()=>{const{edges:f}=Ke();return{getEdgesByTarget:g=>q(f).filter(m=>m.target===g)}})();var u=sh(),s=W(u);Md(s);var c=Z(s,2);const d=ze(()=>({markerEnd:{type:$r.ArrowClosed,width:20,height:20}}));return Ec(c,lt({nodeTypes:th},Zi,{class:"tinyflow-logo",isValidConnection:f=>{const g=i(f.source),m=i(f.target);if(f.sourceHandle==="loop_handle"||g.parentId){const v=o.getEdges();for(let y of v)if(y.target===f.target){const w=i(y.source);if(f.sourceHandle==="loop_handle"&&w.parentId!==g.id||g.parentId&&w.parentId!==g.parentId)return!1}}return!(!g.parentId&&m.parentId&&m.parentId!==g.id)},onconnectend:(f,g)=>{if(!g.isValid)return;const m=g.toNode;if(m.parentId)return;const v=g.fromNode,y=g.fromHandle,w={position:{...m.position}};if(y.id==="loop_handle"?w.parentId=v.id:v.parentId&&(w.parentId=v.parentId),w.parentId){const h=i(w.parentId);w.position={x:m.position.x-h.position.x,y:m.position.y-h.position.y},a(w.parentId,m.id),o.updateNode(m.id,w)}},onconnectstart:(f,g)=>{},onconnect:f=>{},connectionRadius:50,ondelete:f=>{f.edges.forEach(g=>{const m=i(g.target);if(m.parentId){const v=l(g.target),y=i(m.parentId);if(v.length===0)o.updateNode(m.id,{parentId:void 0,position:{x:m.position.x+y.position.x,y:m.position.y+y.position.y}});else{let w=!1;for(let h=0;h<v.length;h++){const C=v[h],b=i(C.source);if(b.parentId||b.type==="loopNode"){w=!0;break}}w||o.updateNode(m.id,{parentId:void 0,position:{x:m.position.x+y.position.x,y:m.position.y+y.position.y}})}}})},onclick:f=>{const g=f.target;g.classList.contains("svelte-flow__edge-interaction")||g.classList.contains("panel-content")||g.closest(".panel-content")||te(r,!1)},get defaultEdgeOptions(){return p(d)},$$events:{drop:f=>{var g;f.preventDefault();const m=o.screenToFlowPosition({x:f.clientX-250,y:f.clientY-100}),v=(g=f.dataTransfer)==null?void 0:g.getData("application/tinyflow"),y=v?JSON.parse(v):{},w={id:`node_${qo()}`,position:m,data:{},...y};Zi.addNode(w),Zi.selectNodeOnly(w.id)},dragover:f=>{f.preventDefault(),f.dataTransfer&&(f.dataTransfer.dropEffect="move")},edgeclick:()=>{te(r,!0)}},children:(f,g)=>{var m=lh(),v=ke(m);Tc(v,{});var y=Z(v,2);Lc(y,{});var w=Z(y,2);Zc(w,{});var h=Z(w,2),C=b=>{Mr(b,{children:(k,_)=>{var S=ah(),E=Z(W(S),4);Ct(W(E),{rows:3,placeholder:"\u8BF7\u8F93\u5165\u8FB9\u6761\u4EF6",style:"width: 100%",oninput:L=>{}}),K(E),K(S),T(k,S)},$$slots:{default:!0}})};_e(h,b=>{p(r)&&b(C)}),T(f,m)},$$slots:{default:!0}})),K(u),T(e,u),ge({get onInit(){return n()},set onInit(f){n(f),x()}})}ce(Vd,{onInit:{}},[],[],!0),customElements.define("tinyflow-component",ce(function(e,t){ve(t,!0);const n=$(t,"options",7),o=$(t,"onInit",7),{data:r}=n();return Zi.init((r==null?void 0:r.nodes)||[],(r==null?void 0:r.edges)||[]),Ho("tinyflow_options",n()),Pc(e,{fitView:!0,children:(i,a)=>{Vd(i,{get onInit(){return o()}})},$$slots:{default:!0}}),ge({get options(){return n()},set options(i){n(i),x()},get onInit(){return o()},set onInit(i){o(i),x()}})},{options:{},onInit:{}},[],[],!1));const ch=xl({__name:"Tinyflow",props:{className:{},style:{},data:{},provider:{}},setup(e,{expose:t}){const n=e,o=Zn(null);let r=null;const i={llm:()=>[],knowledge:()=>[],internal:()=>[]};return Rd(()=>{if(o.value){const a={...i,...n.provider};r=new Zv({element:o.value,data:n.data||{},provider:a})}}),Xd(()=>{r&&(r.destroy(),r=null)}),t({getData:()=>r?r.getData():null}),(a,l)=>(Wt(),Cn("div",{ref_key:"divRef",ref:o,class:Kd(["tinyflow",a.className]),style:Yd([a.style,{height:"100%"}])},null,6))}}),dh={class:"relative",style:{width:"100%",height:"700px"}},fh={class:"absolute top-30px right-30px"},vh={class:"p-20px"},gh={class:"mt-20px bg-#f8f9fa"},ph={class:"p-20px"},hh={key:0},mh={key:1},yh={key:2,class:"result-content"},wh={key:3},bh=xl({__name:"WorkflowDesign",props:{provider:{}},setup(e,{expose:t}){const n=Zn(),o=Wd("workflowData"),r=Zn(!1),i=Zn([]),a=Zn({}),l=Zn(null),u=Zn(!1),s=Zn(null),c=()=>{r.value=!r.value},d=async()=>{var v,y,w;try{const h=n.value.getData();u.value=!0,s.value=null,l.value=null;const C=f(),b=((v=C.data)==null?void 0:v.parameters)||[],k={};b.forEach(L=>{k[L.name]=L.dataType});const _={};for(const{key:L,value:P}of i.value){const D=L.trim();if(!D)continue;let M=k[D];M||(M="String");try{_[D]=m(P,M)}catch(H){throw new Error(`\u53C2\u6570 ${D} \u8F6C\u6362\u5931\u8D25: ${H.message}`)}}const S={graph:JSON.stringify(h),params:_},E=await Jd(S);l.value=E}catch(h){s.value=((w=(y=h.response)==null?void 0:y.data)==null?void 0:w.message)||"\u8FD0\u884C\u5931\u8D25\uFF0C\u8BF7\u68C0\u67E5\u53C2\u6570\u548C\u7F51\u7EDC\u8FDE\u63A5"}finally{u.value=!1}};jd(r,v=>{var C;if(!v)return;const y=f(),w=((C=y.data)==null?void 0:C.parameters)||[],h={};w.forEach(b=>{h[b.name]=b}),function(b){let k=[];for(let _ in h){let S=h[_];S.required&&(b.find(E=>E.key===_)||k.push({key:S.name,value:S.defaultValue||""}))}b.push(...k)}(i.value),a.value=h});const f=()=>{const v=n.value.getData().nodes.find(y=>y.type==="startNode");if(!v)throw new Error("\u6D41\u7A0B\u7F3A\u5C11\u5F00\u59CB\u8282\u70B9");return v},g=()=>{i.value.push({key:"",value:""})},m=(v,y)=>{if(v==="")return null;switch(y){case"String":return String(v);case"Number":const w=Number(v);if(isNaN(w))throw new Error("\u975E\u6570\u5B57\u683C\u5F0F");return w;case"Boolean":if(v.toLowerCase()==="true")return!0;if(v.toLowerCase()==="false")return!1;throw new Error("\u5FC5\u987B\u4E3A true/false");case"Object":case"Array":try{return JSON.parse(v)}catch(h){throw new Error(`JSON\u683C\u5F0F\u9519\u8BEF: ${h.message}`)}default:throw new Error(`\u4E0D\u652F\u6301\u7684\u7C7B\u578B: ${y}`)}};return t({validate:async()=>{try{if(!o.value)throw new Error("\u8BF7\u8BBE\u8BA1\u6D41\u7A0B");return o.value=n.value.getData(),!0}catch(v){throw v}}}),(v,y)=>{const w=Qd,h=t1,C=e1,b=n1,k=r1,_=i1,S=qd("hasPermi");return Wt(),Cn("div",dh,[Ut(o)?(Wt(),Gi(ch,{key:0,ref_key:"tinyflowRef",ref:n,className:"custom-class",style:{width:"100%",height:"100%"},data:Ut(o),provider:v.provider},null,8,["data","provider"])):Fd("",!0),kn("div",fh,[Gd((Wt(),Gi(w,{onClick:c,type:"primary"},{default:Bn(()=>y[1]||(y[1]=[bo(" \u6D4B\u8BD5 ")])),_:1})),[[S,["ai:workflow:test"]]])]),_n(_,{modelValue:Ut(r),"onUpdate:modelValue":y[0]||(y[0]=E=>Ud(r)?r.value=E:null),title:"\u5DE5\u4F5C\u6D41\u6D4B\u8BD5",modal:!1},{default:Bn(()=>[kn("fieldset",null,[y[3]||(y[3]=kn("legend",{class:"ml-15px"},[kn("h3",null,"\u8FD0\u884C\u53C2\u6570\u914D\u7F6E")],-1)),kn("div",vh,[(Wt(!0),Cn($l,null,Cl(Ut(i),(E,L)=>(Wt(),Cn("div",{class:"flex justify-around mb-10px",key:L},[_n(C,{class:"w-200px!",modelValue:E.key,"onUpdate:modelValue":P=>E.key=P,placeholder:"\u53C2\u6570\u540D"},{default:Bn(()=>[(Wt(!0),Cn($l,null,Cl(Ut(a),(P,D)=>(Wt(),Gi(h,{key:D,label:(P==null?void 0:P.description)||D,value:D,disabled:!!(P!=null&&P.disabled)},null,8,["label","value","disabled"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue"]),_n(b,{class:"w-200px!",modelValue:E.value,"onUpdate:modelValue":P=>E.value=P,placeholder:"\u53C2\u6570\u503C"},null,8,["modelValue","onUpdate:modelValue"]),_n(w,{type:"danger",plain:"",icon:Ut(o1),circle:"",onClick:P=>(D=>{i.value.splice(D,1)})(L)},null,8,["icon","onClick"])]))),128)),_n(w,{type:"primary",plain:"",onClick:g},{default:Bn(()=>y[2]||(y[2]=[bo("\u6DFB\u52A0\u53C2\u6570")])),_:1})])]),kn("fieldset",gh,[y[6]||(y[6]=kn("legend",{class:"ml-15px"},[kn("h3",null,"\u8FD0\u884C\u7ED3\u679C")],-1)),kn("div",ph,[Ut(u)?(Wt(),Cn("div",hh,[_n(k,{type:"primary"},{default:Bn(()=>y[4]||(y[4]=[bo("\u6267\u884C\u4E2D...")])),_:1})])):Ut(s)?(Wt(),Cn("div",mh,[_n(k,{type:"danger"},{default:Bn(()=>[bo(kl(Ut(s)),1)]),_:1})])):Ut(l)?(Wt(),Cn("pre",yh,kl(JSON.stringify(Ut(l),null,2))+`
          `,1)):(Wt(),Cn("div",wh,[_n(k,{type:"info"},{default:Bn(()=>y[5]||(y[5]=[bo("\u70B9\u51FB\u8FD0\u884C\u67E5\u770B\u7ED3\u679C")])),_:1})]))])]),_n(w,{class:"mt-20px w-100%",size:"large",type:"success",onClick:d},{default:Bn(()=>y[7]||(y[7]=[bo(" \u8FD0\u884C\u6D41\u7A0B ")])),_:1})]),_:1},8,["modelValue"])])}}}),xh=Bd(bh,[["__scopeId","data-v-bb44bc9d"]]);export{xh as default};
