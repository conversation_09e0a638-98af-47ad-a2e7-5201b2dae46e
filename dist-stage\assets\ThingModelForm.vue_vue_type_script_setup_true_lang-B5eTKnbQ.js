import{a as L,d as G,h as H,D as j,M as v}from"./index-Byekp3Iv.js";import{_ as z}from"./Dialog.vue_vue_type_style_index_0_lang-DhV_HFpU.js";import{T as A}from"./ThingModelProperty-DupfDGdB.js";import $ from"./ThingModelService-BZZpBeXz.js";import q from"./ThingModelEvent-ObnicVoY.js";import{T as I}from"./index-DT2adENz.js";import{D as r,T as s,a as B}from"./config-NlBZD0Kw.js";import{ak as J,h as Q,aj as W,s as X,t as Z,k as ee,f as ae,K as le}from"./form-designer-C0ARe9Dh.js";import{k as te,i as de,r as m,y as c,m as i,z as o,A as oe,u as l,H as p,C as E,l as pe,G as re,$ as se,E as h,F as ue,h as ie}from"./form-create-B86qX0W_.js";const k={PRODUCT:"IOT_PRODUCT"},ne=te({name:"IoTThingModelForm",__name:"ThingModelForm",emits:["success"],setup(me,{expose:O,emit:b}){const R=de(k.PRODUCT),{t:T}=L(),N=G(),u=m(!1),P=m(""),n=m(!1),S=m(""),a=m({type:s.PROPERTY,dataType:r.INT,property:{dataType:r.INT,dataSpecs:{dataType:r.INT}},service:{},event:{}}),f=m();O({open:async(e,t)=>{if(u.value=!0,P.value=T("action."+e),S.value=e,M(),t){n.value=!0;try{a.value=await I.getThingModel(t),v(a.value.property)&&(a.value.dataType=r.INT,a.value.property={dataType:r.INT,dataSpecs:{dataType:r.INT}}),v(a.value.service)&&(a.value.service={}),v(a.value.event)&&(a.value.event={})}finally{n.value=!1}}},close:()=>u.value=!1});const g=b,C=async()=>{await f.value.validate(),n.value=!0;try{const e=le(a.value);e.productId=R.value.id,e.productKey=R.value.productKey,w(e),S.value==="create"?(await I.createThingModel(e),N.success(T("common.createSuccess"))):(await I.updateThingModel(e),N.success(T("common.updateSuccess")))}finally{u.value=!1,g("success"),n.value=!1}},w=e=>{e.type===s.PROPERTY&&(V(e.property),e.dataType=e.property.dataType,e.property.identifier=e.identifier,e.property.name=e.name,delete e.service,delete e.event),e.type===s.SERVICE&&(V(e.service),e.dataType=e.service.dataType,e.service.identifier=e.identifier,e.service.name=e.name,delete e.property,delete e.event),e.type===s.EVENT&&(V(e.event),e.dataType=e.event.dataType,e.event.identifier=e.identifier,e.event.name=e.name,delete e.property,delete e.service)},V=e=>{v(e.dataSpecs)&&delete e.dataSpecs,v(e.dataSpecsList)&&delete e.dataSpecsList},M=()=>{var e;a.value={type:s.PROPERTY,dataType:r.INT,property:{dataType:r.INT,dataSpecs:{dataType:r.INT}},service:{},event:{}},(e=f.value)==null||e.resetFields()};return(e,t)=>{const D=Z,x=X,y=W,_=ee,Y=Q,U=ae,F=z,K=J;return i(),c(F,{modelValue:l(u),"onUpdate:modelValue":t[8]||(t[8]=d=>ie(u)?u.value=d:null),title:l(P)},{footer:o(()=>[p(U,{disabled:l(n),type:"primary",onClick:C},{default:o(()=>t[9]||(t[9]=[h("\u786E \u5B9A")])),_:1},8,["disabled"]),p(U,{onClick:t[7]||(t[7]=d=>u.value=!1)},{default:o(()=>t[10]||(t[10]=[h("\u53D6 \u6D88")])),_:1})]),default:o(()=>[oe((i(),c(Y,{ref_key:"formRef",ref:f,model:l(a),rules:l(B),"label-width":"100px"},{default:o(()=>[p(y,{label:"\u529F\u80FD\u7C7B\u578B",prop:"type"},{default:o(()=>[p(x,{modelValue:l(a).type,"onUpdate:modelValue":t[0]||(t[0]=d=>l(a).type=d)},{default:o(()=>[(i(!0),pe(re,null,se(l(H)(l(j).IOT_THING_MODEL_TYPE),d=>(i(),c(D,{key:d.value,value:d.value},{default:o(()=>[h(ue(d.label),1)]),_:2},1032,["value"]))),128))]),_:1},8,["modelValue"])]),_:1}),p(y,{label:"\u529F\u80FD\u540D\u79F0",prop:"name"},{default:o(()=>[p(_,{modelValue:l(a).name,"onUpdate:modelValue":t[1]||(t[1]=d=>l(a).name=d),placeholder:"\u8BF7\u8F93\u5165\u529F\u80FD\u540D\u79F0"},null,8,["modelValue"])]),_:1}),p(y,{label:"\u6807\u8BC6\u7B26",prop:"identifier"},{default:o(()=>[p(_,{modelValue:l(a).identifier,"onUpdate:modelValue":t[2]||(t[2]=d=>l(a).identifier=d),placeholder:"\u8BF7\u8F93\u5165\u6807\u8BC6\u7B26"},null,8,["modelValue"])]),_:1}),l(a).type===l(s).PROPERTY?(i(),c(A,{key:0,modelValue:l(a).property,"onUpdate:modelValue":t[3]||(t[3]=d=>l(a).property=d)},null,8,["modelValue"])):E("",!0),l(a).type===l(s).SERVICE?(i(),c($,{key:1,modelValue:l(a).service,"onUpdate:modelValue":t[4]||(t[4]=d=>l(a).service=d)},null,8,["modelValue"])):E("",!0),l(a).type===l(s).EVENT?(i(),c(q,{key:2,modelValue:l(a).event,"onUpdate:modelValue":t[5]||(t[5]=d=>l(a).event=d)},null,8,["modelValue"])):E("",!0),p(y,{label:"\u63CF\u8FF0",prop:"description"},{default:o(()=>[p(_,{modelValue:l(a).description,"onUpdate:modelValue":t[6]||(t[6]=d=>l(a).description=d),maxlength:200,rows:3,placeholder:"\u8BF7\u8F93\u5165\u5C5E\u6027\u63CF\u8FF0",type:"textarea"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])),[[K,l(n)]])]),_:1},8,["modelValue","title"])}}});export{k as I,ne as _};
