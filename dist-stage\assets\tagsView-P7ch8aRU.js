import{a9 as c,aa as r,a0 as h,C as o,v as n,ab as u}from"./index-Byekp3Iv.js";const f=c("tagsView",{state:()=>({visitedViews:[],cachedViews:new Set,selectedTag:void 0}),getters:{getVisitedViews(){return this.visitedViews},getCachedViews(){return Array.from(this.cachedViews)},getSelectedTag(){return this.selectedTag}},actions:{addView(e){this.addVisitedView(e),this.addCachedView()},addVisitedView(e){var t,a;if(this.visitedViews.some(s=>s.fullPath===e.fullPath)||(t=e.meta)!=null&&t.noTagsView)return;const i=Object.assign({},e,{title:((a=e.meta)==null?void 0:a.title)||"no-name"});if(i.meta){const s=[];if(this.visitedViews.forEach(d=>{var l,V,w;d.path===i.path&&((l=d.meta)==null?void 0:l.title)===((V=i.meta)==null?void 0:V.title)&&s.push(((w=d.meta)==null?void 0:w.titleSuffix)||"1")}),s.length){let d=1;for(;s.includes(`${d}`);)d+=1;i.meta.titleSuffix=d===1?void 0:`${d}`}}this.visitedViews.push(i)},addCachedView(){var i;const e=new Set;for(const t of this.visitedViews){const a=u(t);if((i=a.meta)!=null&&i.noCache)continue;const s=a.name;e.add(s)}Array.from(this.cachedViews).sort().toString()!==Array.from(e).sort().toString()&&(this.cachedViews=e)},delView(e){this.delVisitedView(e),this.delCachedView()},delVisitedView(e){for(const[i,t]of this.visitedViews.entries())if(t.fullPath===e.fullPath){this.visitedViews.splice(i,1);break}},delCachedView(){const e=n.currentRoute.value,i=h(this.getCachedViews,t=>t===e.name);i>-1&&this.cachedViews.delete(this.getCachedViews[i])},delAllViews(){this.delAllVisitedViews(),this.delCachedView()},delAllVisitedViews(){const e=o();this.visitedViews=e.getUser?this.visitedViews.filter(i=>{var t;return(t=i==null?void 0:i.meta)==null?void 0:t.affix}):[]},delOthersViews(e){this.delOthersVisitedViews(e),this.addCachedView()},delOthersVisitedViews(e){this.visitedViews=this.visitedViews.filter(i=>{var t;return((t=i==null?void 0:i.meta)==null?void 0:t.affix)||i.fullPath===e.fullPath})},delLeftViews(e){const i=h(this.visitedViews,t=>t.fullPath===e.fullPath);i>-1&&(this.visitedViews=this.visitedViews.filter((t,a)=>{var s;return((s=t==null?void 0:t.meta)==null?void 0:s.affix)||t.fullPath===e.fullPath||a>i}),this.addCachedView())},delRightViews(e){const i=h(this.visitedViews,t=>t.fullPath===e.fullPath);i>-1&&(this.visitedViews=this.visitedViews.filter((t,a)=>{var s;return((s=t==null?void 0:t.meta)==null?void 0:s.affix)||t.fullPath===e.fullPath||a<i}),this.addCachedView())},updateVisitedView(e){for(let i of this.visitedViews)if(i.fullPath===e.fullPath){i=Object.assign(i,e);break}},setSelectedTag(e){this.selectedTag=e},setTitle(e,i){var t;for(const a of this.visitedViews)if(a.path===(i??((t=this.selectedTag)==null?void 0:t.path))){a.meta.title=e;break}}},persist:!1}),v=()=>f(r);export{v as a,f as u};
