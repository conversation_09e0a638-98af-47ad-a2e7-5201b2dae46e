import{_ as n}from"./CountTo.vue_vue_type_script_setup_true_lang-QUNNIqef.js";import{p as t}from"./index-Byekp3Iv.js";import{a6 as d}from"./form-designer-C0ARe9Dh.js";import{k as o,l as m,m as f,B as c,v as s,H as i,F as a,z as x,E as u}from"./form-create-B86qX0W_.js";const p={class:"flex items-center justify-between text-white/80"},b={class:"text-sm"},g={class:"flex flex-col gap-1"},w={class:"text-2xl font-bold text-white"},v={class:"text-sm text-white/70"},h={class:"flex flex-row items-center justify-between text-sm border-t border-white/20 pt-2"},_={class:"text-white/70"},C={class:"text-white/90 text-lg font-bold"},L=o({name:"ScmMetricCard",__name:"ScmMetricCard",props:{title:t.string.def("").isRequired,tag:t.string.def("\u4ECA\u65E5"),amountPrefix:t.string.def("\xA5"),amount:t.number.def(0).isRequired,count:t.number.def(0).isRequired,decimals:t.number.def(0),amountLabel:t.string.def("\u91D1\u989D"),countLabel:t.string.def("\u8BA2\u5355\u6570"),gradientClass:t.string.def("")},setup:e=>(j,q)=>{const r=d,l=n;return f(),m("div",{class:c(["flex flex-col gap-3 p-4 rounded-lg text-white",e.gradientClass])},[s("div",p,[s("span",b,a(e.title),1),i(r,{type:"info",class:"bg-white/20 border-white/30 text-white text-xs"},{default:x(()=>[u(a(e.tag),1)]),_:1})]),s("div",g,[s("div",w,[i(l,{prefix:e.amountPrefix,"end-val":e.amount,decimals:e.decimals},null,8,["prefix","end-val","decimals"])]),s("div",v,a(e.amountLabel),1)]),s("div",h,[s("span",_,a(e.countLabel),1),s("span",C,[i(l,{"end-val":e.count,decimals:0},null,8,["end-val"])])])],2)}});export{L as _};
