<template>
	<view class="material-detail-container">
		<!-- 标题栏 -->
		<view class="page-header">
			<text class="page-title">{{detailTitle}}</text>
			<text class="material-count">共 {{materialData.length}} 项</text>
		</view>
		
		<!-- 空状态提示 -->
		<view class="empty-tip" v-if="materialData.length === 0">
			<uni-icons type="info-filled" size="32" color="#999"></uni-icons>
			<text>暂无{{isPackageMode ? '包装材料' : '原料'}}数据</text>
		</view>
		
		<!-- 原料卡片列表 -->
		<scroll-view scroll-y="true" class="material-list">
			<view class="material-card" v-for="(row, rowIndex) in materialData" :key="rowIndex" @click="handleEdit(row, rowIndex)">
				<view class="card-header">
					<view class="material-index">
						<text class="index-label">序号:</text>
						<text class="index-value">{{rowIndex + 1}}</text>
					</view>
					<view class="material-name">{{row.materialName || (isPackageMode ? '未命名包装材料' : '未命名原料')}}</view>
					<view class="operation-btns">
						<button class="mini-btn" type="warn" size="mini" @click.stop="handleDelete(row)" :disabled="pageDisabled">删除</button>
					</view>
				</view>
				
				<view class="card-content">
					<view class="info-row">
						<view class="info-item">
							<text class="item-label">顺序:</text>
							<text class="item-value">{{row.num || '-'}}</text>
						</view>
						<view class="info-item">
							<text class="item-label">{{amountLabel}}:</text>
							<text class="item-value">{{row.amount || 0}} {{isPackageMode ? '个' : '份'}}</text>
						</view>
					</view>
					
					<view class="info-row">
						<view class="info-item">
							<text class="item-label">单价:</text>
							<text class="item-value">{{row.price || 0}} {{row.priceUnit || ''}}</text>
						</view>
						<view class="info-item">
							<text class="item-label">{{isPackageMode ? '成本' : '投入成本'}}:</text>
							<text class="item-value">{{row.cost || 0}} {{row.priceUnit || ''}}</text>
						</view>
					</view>
					
					<!-- 元素含量展示 -->
					<view class="elements-section" v-if="hasElements(row) && !isPackageMode">
						<text class="section-title">元素含量:</text>
						<view class="elements-grid">
							<view class="element-item" v-for="(ele, index) in displayedElements" :key="index" v-if="row.element && row.element[ele]">
								<text class="element-name">{{ele}}:</text>
								<text class="element-value">{{row.element[ele]}}</text>
							</view>
						</view>
					</view>
				</view>
			</view>
		</scroll-view>
		
		<!-- 页面内容占位，确保底部栏不遮挡内容 -->
		<view class="content-placeholder"></view>
		
		<!-- 底部固定栏 -->
		<view class="fixed-bottom-bar" v-show="!isPopupOpen">
			<!-- 合计汇总面板 -->
			<view class="summary-panel">
				<view class="summary-content">
					<view class="summary-row">
						<view class="summary-item">
							<text class="item-label">{{isPackageMode ? '总数量' : '总份数'}}:</text>
							<text class="item-value">{{totalAmount}}</text>
						</view>
						<view class="summary-item">
							<text class="item-label">总成本:</text>
							<text class="item-value">{{totalCost}}</text>
						</view>
					</view>
					
					<!-- 元素汇总 -->
					<view class="elements-summary" v-if="displayedElements.length > 0 && !isPackageMode">
						<view class="elements-grid">
							<view class="element-item" v-for="(ele, index) in displayedElements" :key="index">
								<text class="element-name">{{ele}}:</text>
								<text class="element-value">{{summarizedElements[ele] || 0}}%</text>
							</view>
						</view>
					</view>
				</view>
			</view>
			
			<!-- 底部操作栏 -->
			<view class="bottom-actions">
				<view class="button-group">
					<button class="action-button add-button" type="primary" @click="showAddMaterialPopup" :disabled="pageDisabled">增加{{isPackageMode ? '包装材料' : '原料'}}</button>
					<button class="action-button save-button" type="success" @click="saveAndReturn" :disabled="pageDisabled">保存并返回</button>
				</view>
			</view>
		</view>
		
		<!-- 添加/编辑弹出层 -->
		<uni-popup ref="addMaterialPopup" type="bottom" @change="handlePopupChange">
			<view class="popup-content">
				<view class="popup-header">
					<text class="popup-title">{{isEditMode ? (isPackageMode ? '编辑包装材料' : '编辑原料') : (isPackageMode ? '添加包装材料' : '添加原料')}}</text>
					<text class="popup-close" @click="closeAddMaterialPopup">×</text>
				</view>
				<view class="popup-body">
					<!-- 名称选择 - 使用SelectPicker组件 -->
					<view class="form-item">
						<text class="form-label">{{isPackageMode ? '包装材料名称' : '原料名称'}}</text>
						<select-picker 
							:options="materialOptions"
							:value="selectedMaterial"
							:title="isPackageMode ? '选择包装材料' : '选择原料'"
							labelField="name"
							valueField="id"
							:placeholder="isPackageMode ? '请选择包装材料' : '请选择原料'"
							:enableLoadMore="true"
							:pageSize="10"
							@input="handleMaterialSelect"
							@loadMore="loadMoreMaterials"
							@search="searchMaterials"
							@resetSearch="resetMaterialSearch"
							:disabled="isEditMode || pageDisabled"
						></select-picker>
					</view>
					
					<!-- 顺序输入 -->
					<view class="form-item">
						<text class="form-label">顺序</text>
						<input class="material-input" type="number" v-model="newMaterial.num" placeholder="请输入顺序" :disabled="pageDisabled"/>
					</view>
					
					<!-- 投入份数输入 -->
					<view class="form-item">
						<text class="form-label">{{amountLabel}}</text>
						<input class="material-input" type="digit" v-model="newMaterial.amount" :placeholder="'请输入' + amountLabel" :disabled="pageDisabled"/>
					</view>
					
					<!-- 添加/保存按钮 -->
					<button class="confirm-button" type="primary" @click="confirmAddOrUpdateMaterial" :disabled="!canAddMaterial || pageDisabled">
						{{isEditMode ? '保存修改' : '确认添加'}}
					</button>
				</view>
			</view>
		</uni-popup>
	</view>
</template>

<script>
import { getDictLabel } from "../../../../../../utils/dict";
import { getSimpleMaterialPageApi } from "../../../../../../api/scm/base/material";
import SelectPicker from "../../../../../../components/SelectPicker/SelectPicker.vue";

	export default {
	components: {
		SelectPicker
	},
		data() {
			return {
			materialData: [],
			displayedElements: [],
			summarizedElements: {},
			totalAmount: 0,
			totalCost: 0,
			density: 1,
			dataReceived: false, // 标记是否已接收数据
			mergeMode: false, // 是否处于合并模式
			selectedForMerge: null, // 当前选中要合并的项
			isPopupOpen: false, // 弹出层是否打开
			
			// 添加原料相关数据
			materialOptions: [], // 可选原料列表
			selectedMaterial: null, // 当前选中的原料对象
			newMaterial: {
				materialName: '',
				amount: '',
				price: 0,
				priceUnit: '',
				id: null,
				materialCode: '',
				element: {},
				cost: 0,
				num: 0,
				materialType: ''
			}, // 新添加的原料数据
			
			// 编辑模式相关
			isEditMode: false, // 是否处于编辑模式
			editingIndex: -1, // 当前编辑的原料索引
			
			// 分页查询参数
			materialQueryParams: {
				pageNo: 1,
				pageSize: 10,
				type: 1, // 类型1表示原料，类型4表示包装材料
				name: ''
			},
			materialLoading: false,
			isPackageMode: false, // 是否处于包装材料模式
			amountLabel: '投入份数', // 根据模式动态设置label
			detailTitle: '原料明细列表', // 根据模式动态设置标题
			pageDisabled: false // 新增pageDisabled属性
		}
	},
	computed: {
		// 判断是否可以添加原料
		canAddMaterial() {
			return (this.isEditMode || this.selectedMaterial) && 
				(this.isEditMode ? this.newMaterial.materialName : this.selectedMaterial.name) && 
				this.newMaterial.amount && 
				!isNaN(Number(this.newMaterial.amount)) && 
				Number(this.newMaterial.amount) > 0;
		}
	},
	onLoad(options) {
		// 获取传递的数据
		const eventChannel = this.getOpenerEventChannel();
		if (eventChannel) {
			eventChannel.on('formulaData', (data) => {
				this.materialData = data.materials || [];
				this.density = data.density || 1;
				this.dataReceived = true;
				this.pageDisabled = data.isDisabled || false; // 获取并设置页面禁用状态
				
				// 检查是否为包装材料模式
				this.isPackageMode = data.detailType === 'package';
				
				// 根据模式设置标题和标签
				this.detailTitle = this.isPackageMode ? '包装材料明细列表' : '原料明细列表';
				this.amountLabel = data.amountLabel || (this.isPackageMode ? '数量' : '投入份数');
				
				// 更新物料查询类型
				this.materialQueryParams.type = data.materialType || (this.isPackageMode ? 4 : 1);
				
				// 设置页面标题包含配方名称
				if (data.name) {
					uni.setNavigationBarTitle({
						title: data.name + (this.isPackageMode ? ' - 包装材料明细' : ' - 原料明细')
					});
				}
				
				this.calculateDisplayedElements();
				this.calculateSummary();
			});
		} else {
			uni.showToast({
				title: '无法获取数据通道',
				icon: 'none',
				duration: 2000
			});
		}
		
		// 如果3秒内未收到数据，显示提示
		setTimeout(() => {
			if (!this.dataReceived) {
				uni.showToast({
					title: '数据加载失败，请返回重试',
					icon: 'none',
					duration: 2000
				});
			}
		}, 3000);
	},
	methods: {
		// 处理弹出层状态变化，控制底部栏的显示和隐藏
		handlePopupChange(e) {
			// e.show为true表示弹出层打开，false表示关闭
			this.isPopupOpen = e.show;
		},
		
		// 获取原料列表数据
		async fetchMaterialList() {
			try {
				this.materialLoading = true;
				const response = await getSimpleMaterialPageApi(this.materialQueryParams);
				
				if (response.code === 0 && response.data) {
					// 根据页码决定是替换还是追加数据
					if (this.materialQueryParams.pageNo === 1) {
						this.materialOptions = this.formatMaterialOptions(response.data.list || []);
					} else {
						const newOptions = this.formatMaterialOptions(response.data.list || []);
						// 确保不添加重复选项
						newOptions.forEach(option => {
							if (!this.materialOptions.some(item => item.id === option.id)) {
								this.materialOptions.push(option);
							}
						});
					}
					
					// 返回加载结果
					return {
						data: response.data.list || [],
						total: response.data.total || 0,
						hasMore: (this.materialOptions.length < response.data.total)
					};
				}
				
				return { data: [], total: 0, hasMore: false };
			} catch (error) {
				uni.showToast({
					title: '获取原料数据失败',
					icon: 'none'
				});
				return { data: [], total: 0, hasMore: false };
			} finally {
				this.materialLoading = false;
			}
		},
		
		// 格式化原料选项
		formatMaterialOptions(materials) {
			return materials.map(material => {
				// 处理元素数据
				let elementObj = {};
				if (material.elements && Array.isArray(material.elements)) {
					material.elements.forEach(ele => {
						if (ele.element && ele.quantity) {
							elementObj[ele.element] = `${ele.quantity}${ele.unit || '%'}`;
						}
					});
				}
				
				// 如果是编辑模式，不要禁用当前编辑的原料
				const isCurrentEditing = this.isEditMode && this.materialData[this.editingIndex] && 
                                       this.materialData[this.editingIndex].id === material.id;
				
				// 标记已添加的原料 (编辑时不禁用当前正在编辑的原料)
				const isAlreadyAdded = !isCurrentEditing && this.materialData.some(item => item.id === material.id);
				
				return {
					id: material.id,
					name: material.name,
					code: material.code || '',
					price: material.purchasePrice || 0,
					priceUnit: material.priceUnit || 'RMB', // 修复：使用正确的价格单位字段
					materialType: material.type || '1', // 确保materialType有值
					element: elementObj,
					disabled: isAlreadyAdded
				};
			});
		},
		
		// 加载更多原料数据
		async loadMoreMaterials(params) {
			if (this.materialLoading) return;
			
			this.materialQueryParams.pageNo = params.page;
			this.materialQueryParams.pageSize = params.pageSize || 10;
			
			const result = await this.fetchMaterialList();
			params.callback(result);
		},
		
		// 搜索原料
		async searchMaterials(params) {
			this.materialQueryParams.pageNo = params.page || 1;
			this.materialQueryParams.pageSize = params.pageSize || 10;
			this.materialQueryParams.name = params.keyword || '';
			
			const result = await this.fetchMaterialList();
			params.callback(result);
		},
		
		// 重置原料搜索
		async resetMaterialSearch() {
			this.materialQueryParams.pageNo = 1;
			this.materialQueryParams.name = '';
			await this.fetchMaterialList();
		},
		
		// 选择原料回调
		handleMaterialSelect(material) {
			this.selectedMaterial = material;
			this.updateNewMaterialFromSelected();
		},
		
		// 检查原料是否有元素含量
		hasElements(material) {
			return material.element && 
				   typeof material.element === 'object' && 
				   Object.keys(material.element).length > 0;
		},
		
		// 计算表格中显示的元素列
		calculateDisplayedElements() {
			const elements = new Set();
			if (this.materialData && Array.isArray(this.materialData)) {
				this.materialData.forEach((material) => {
					if (material.element && typeof material.element === "object") {
						Object.keys(material.element).forEach((ele) => elements.add(ele));
					}
				});
			}
			this.displayedElements = Array.from(elements);
		},
		
		// 计算汇总数据
		calculateSummary() {
			// 计算总份数/总数量
			this.totalAmount = this.materialData.reduce((sum, material) => {
				return sum + Number(material.amount || 0);
			}, 0);
			
			// 根据不同模式计算成本
			if (this.isPackageMode) {
				// 包装材料: 计算每个包装材料的成本 = 数量 * 单价
				this.materialData.forEach(material => {
					const amount = Number(material.amount || 0);
					const price = Number(material.price || 0);
					material.cost = this.unpad((amount * price).toFixed(4));
				});
			} else {
				// 原料: 重新计算每个原料的成本
				if (this.totalAmount > 0) {
					this.materialData.forEach(material => {
						const amount = Number(material.amount || 0);
						const price = Number(material.price || 0);
						// 按PC端逻辑计算成本：(投入份数 * 单价) / 总份数
						material.cost = this.unpad((amount * price / this.totalAmount).toFixed(4));
					});
				}
				
				// 仅原料模式才计算元素汇总
				this.calculateElementSummary();
			}
			
			// 计算总成本
			this.totalCost = this.materialData.reduce((sum, material) => {
				return sum + Number(material.cost || 0);
			}, 0).toFixed(4);
		},
		
		// 计算元素汇总（仅原料模式使用）
		calculateElementSummary() {
			// 重置元素汇总数据
			this.summarizedElements = {};
			
			// 对每个元素进行汇总
			this.displayedElements.forEach(element => {
				let elementSum = 0;
				
				this.materialData.forEach(material => {
					if (material.element && material.element[element]) {
						// 提取数值部分
						const elementValue = this.getElementValue(material.element[element]);
						// 计算该原料对元素总量的贡献
						elementSum += elementValue * Number(material.amount || 0);
					}
				});
				
				// 计算每个元素的百分比，考虑密度因素
				const value = (elementSum * 1000) / ((this.totalAmount === 0 ? 1 : this.totalAmount) / (this.density || 1));
				// 这里将格式化后的值存储，但不添加单位，单位将在模板中添加
				this.summarizedElements[element] = this.unpad(value.toFixed(4));
			});
		},
		
		// 从元素含量字符串中提取数值
		getElementValue(element) {
			const numberMatch = element.match(/\d+(\.\d+)?/);
			let elementValue = numberMatch ? Number(numberMatch[0]) : 0;
			
			// 处理百分比单位
			const unit = element.replace(/\d+(\.\d+)?/, "").trim();
			if (unit === '%') {
				elementValue = elementValue / 100;
			}
			
			return elementValue;
		},
		
		// 去除字符串数字中小数点后不必要的0
		unpad(num) {
			const strNum = num.toString();
			return strNum.replace(/(\.\d*?)0+$/, "$1").replace(/\.$/, "");
		},
		
		// 处理删除原料
		handleDelete(row) {
			uni.showModal({
				title: '确认删除',
				content: `确定要删除原料"${row.materialName || '未命名原料'}"吗？`,
				confirmText: '删除',
				confirmColor: '#dd524d',
				success: (res) => {
					if (res.confirm) {
						// 找到要删除的索引
						const index = this.materialData.findIndex(item => item === row);
						if (index !== -1) {
							// 删除该项
							this.materialData.splice(index, 1);
							// 重新计算汇总
							this.calculateDisplayedElements();
							this.calculateSummary();
							// 刷新原料选项列表
							this.resetMaterialSearch();
							uni.showToast({
								title: '删除成功',
								icon: 'success'
							});
						}
					}
				}
			});
		},
		
		// 处理编辑原料
		handleEdit(row, index) {
			// 设置为编辑模式
			this.isEditMode = true;
			this.editingIndex = index;
			
			// 复制当前原料数据到新原料对象
			this.newMaterial = {
				materialName: row.materialName,
				amount: row.amount,
				price: row.price,
				priceUnit: row.priceUnit,
				id: row.id,
				materialCode: row.materialCode,
				element: { ...row.element },
				cost: row.cost,
				num: row.num || index + 1,
				materialType: row.materialType || '1' // 确保materialType有值
			};
			
			// 设置选中的原料对象，用于在选择器中显示
			this.selectedMaterial = {
				id: row.id,
				name: row.materialName,
				code: row.materialCode || '',
				price: row.price,
				priceUnit: row.priceUnit,
				element: { ...row.element },
				materialType: row.materialType || '1' // 确保materialType有值
			};
			
			// 加载原料选项列表
			this.resetMaterialSearch();
			
			// 打开弹出层
			this.$refs.addMaterialPopup.open();
		},
		
		// 显示添加原料弹出层
		showAddMaterialPopup() {
			// 重置为添加模式
			this.isEditMode = false;
			this.editingIndex = -1;
			
			// 重置新原料数据
			this.resetNewMaterial();
			// 加载原料选项列表
			this.resetMaterialSearch();
			// 打开弹出层
			this.$refs.addMaterialPopup.open();
		},
		
		// 关闭添加原料弹出层
		closeAddMaterialPopup() {
			this.$refs.addMaterialPopup.close();
		},
		
		// 从选中的原料更新新原料数据
		updateNewMaterialFromSelected() {
			if (this.selectedMaterial && this.selectedMaterial.id) {
				this.newMaterial.materialName = this.selectedMaterial.name;
				this.newMaterial.id = this.selectedMaterial.id;
				this.newMaterial.materialCode = this.selectedMaterial.code;
				// 修复：使用正确的字段名，formatMaterialOptions中已经将purchasePrice映射到price
				this.newMaterial.price = this.selectedMaterial.price || 0;
				this.newMaterial.priceUnit = this.selectedMaterial.priceUnit || '';
				this.newMaterial.element = { ...this.selectedMaterial.element };
				this.newMaterial.materialType = this.selectedMaterial.materialType || '1'; // 默认类型为1（原料）

				// 如果是新增模式，设置顺序号为当前列表长度+1
				if (!this.isEditMode) {
					this.newMaterial.num = this.materialData.length + 1;
				}
				

			}
		},
		
		// 确认添加或更新原料
		confirmAddOrUpdateMaterial() {
			if (!this.canAddMaterial) {
				uni.showToast({
					title: '请完善信息',
					icon: 'none'
				});
				return;
			}
			
			// 获取基础数据
			const amount = Number(this.newMaterial.amount);
			const price = Number(this.newMaterial.price);
			let cost = 0;
			
			if (this.isPackageMode) {
				// 包装材料: 成本 = 数量 * 单价
				cost = (amount * price).toFixed(4);
			} else {
				// 原料: 计算投入成本
				// 获取当前总份数，用于计算成本比例
				const totalAmount = this.materialData.reduce((sum, material) => {
					// 跳过当前正在编辑的原料
					if(this.isEditMode && this.materialData.indexOf(material) === this.editingIndex) {
						return sum;
					}
					return sum + Number(material.amount || 0);
				}, 0);
				
				// 计算投入成本：(投入份数 * 单价) / (总份数 + 当前投入份数)
				const finalTotalAmount = totalAmount + amount;
				cost = (amount * price / (finalTotalAmount === 0 ? 1 : finalTotalAmount)).toFixed(4);
			}
			
			// 创建物料对象
			const materialItem = {
				id: this.newMaterial.id, // 修复：添加id字段
				materialId: this.newMaterial.id,
				materialCode: this.newMaterial.materialCode,
				materialName: this.newMaterial.materialName,
				amount: amount,
				price: price,
				priceUnit: this.newMaterial.priceUnit,
				element: { ...this.newMaterial.element },
				cost: this.unpad(cost),
				num: Number(this.newMaterial.num) || (this.isEditMode ? this.editingIndex + 1 : this.materialData.length + 1),
				materialType: this.newMaterial.materialType || (this.isPackageMode ? '4' : '1')
			};
			
			if (this.isEditMode && this.editingIndex >= 0) {
				// 更新已有物料
				this.materialData.splice(this.editingIndex, 1, materialItem);
				uni.showToast({
					title: '修改成功',
					icon: 'success'
				});
			} else {
				// 添加新物料
				this.materialData.push(materialItem);
				uni.showToast({
					title: '添加成功',
					icon: 'success'
				});
			}
			
			// 重新计算汇总
			this.calculateDisplayedElements();
			this.calculateSummary();
			
			// 关闭弹出层
			this.closeAddMaterialPopup();
		},
		
		// 重置新原料数据
		resetNewMaterial() {
			this.newMaterial = {
				materialName: '',
				amount: '',
				price: 0,
				priceUnit: '',
				id: null,
				materialCode: '',
				element: {},
				cost: 0,
				num: this.materialData.length + 1,
				materialType: ''
			};
			
			this.selectedMaterial = null;
		},
		saveAndReturn() {
			// 确保所有原料的成本计算是最新的
			this.calculateSummary();
			
			// 获取当前页面的事件通道
			const eventChannel = this.getOpenerEventChannel();
			if (eventChannel) {
				// 将当前页面的数据传回父组件
				const returnData = {
					materials: this.materialData,
					totalAmount: this.totalAmount,
					totalCost: this.totalCost,
					summarizedElements: this.summarizedElements
				};
				
				// 通过事件通道发送数据回父组件
				eventChannel.emit('updateFormulaMaterials', returnData);
				
				// 显示保存成功提示
				uni.showToast({
					title: '保存成功',
					icon: 'success',
					duration: 1500
				});
				
				// 延迟返回页面，让用户看到保存成功的提示
				setTimeout(() => {
					uni.navigateBack();
				}, 1500);
			} else {
				// 如果没有事件通道，显示错误提示
				uni.showToast({
					title: '无法保存数据，请稍后重试',
					icon: 'none',
					duration: 2000
				});
			}
		}
	}
}
</script>

<style>
.material-detail-container {
	padding: 12px;
	background-color: #f8f8f8;
	min-height: 100vh;
	box-sizing: border-box;
	display: flex;
	flex-direction: column;
	padding-bottom: 160px; /* 为底部栏留出空间 */
}

/* 页面标题栏 */
.page-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 15px;
}

.page-title {
	font-size: 18px;
	font-weight: bold;
	color: #333;
}

.material-count {
	font-size: 14px;
	color: #666;
}

/* 空状态提示 */
.empty-tip {
	flex: 1;
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
	padding: 40px 0;
	color: #999;
}

/* 原料列表区域 */
.material-list {
	flex: 1;
	margin-bottom: 10px;
}

/* 原料卡片样式 */
.material-card {
	background-color: #fff;
	border-radius: 8px;
	margin-bottom: 10px;
	box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
	overflow: hidden;
	position: relative;
}

/* 添加可点击样式 */
.material-card:active {
	background-color: #f5f5f5;
}

.card-header {
	display: flex;
	align-items: center;
	padding: 10px;
	background-color: #f0f9ff;
	border-bottom: 1px solid #e8e8e8;
}

.material-index {
	display: flex;
	align-items: center;
	margin-right: 10px;
}

.index-label {
	font-size: 12px;
	color: #666;
	margin-right: 2px;
}

.index-value {
	font-size: 14px;
	color: #333;
	font-weight: bold;
}

.material-name {
	flex: 1;
	font-size: 14px;
	font-weight: bold;
	color: #333;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
}

.operation-btns {
	display: flex;
	align-items: center;
}

.mini-btn {
	margin-left: 5px;
	font-size: 12px;
	padding: 0 6px;
	line-height: 1.8;
}

.card-content {
	padding: 10px;
}

.info-row {
	display: flex;
	margin-bottom: 10px;
}

.info-item {
	flex: 1;
	display: flex;
	align-items: center;
}

.item-label {
	font-size: 12px;
	color: #666;
	margin-right: 5px;
}

.item-value {
	font-size: 14px;
	color: #333;
}

/* 元素含量区域 */
.elements-section {
	margin-top: 5px;
}

.section-title {
	font-size: 12px;
	color: #666;
	margin-bottom: 5px;
	display: block;
}

.elements-grid {
	display: flex;
	flex-wrap: wrap;
}

.element-item {
	background-color: #f5f5f5;
	border-radius: 4px;
	padding: 2px 6px;
	margin-right: 6px;
	margin-bottom: 6px;
	display: flex;
	align-items: center;
}

.element-name {
	font-size: 12px;
	color: #666;
	margin-right: 2px;
}

.element-value {
	font-size: 12px;
	color: #333;
}

/* 内容占位，防止底部栏遮挡内容 */
.content-placeholder {
	height: 20px;
}

/* 底部固定栏 */
.fixed-bottom-bar {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	background-color: #fff;
	box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
	z-index: 100;
	border-radius: 16px 16px 0 0;
}

/* 汇总面板 */
.summary-panel {
	background-color: #fff;
	overflow: hidden;
	padding: 10px 12px;
	border-bottom: 1px solid #f0f0f0;
}

.summary-content {
	padding: 0;
}

.summary-row {
	display: flex;
	margin-bottom: 10px;
}

.summary-item {
	flex: 1;
	display: flex;
	align-items: center;
}

/* 元素汇总区域 */
.elements-summary {
	margin-top: 5px;
}

/* 底部操作栏 */
.bottom-actions {
	padding: 10px 12px;
	display: flex;
	justify-content: center;
	background-color: #fff;
	border-radius: 0 0 16px 16px;
}

.button-group {
	display: flex;
	width: 100%;
	gap: 10px;
}

.action-button {
	flex: 1;
	font-size: 16px;
	border-radius: 20px;
	height: 40px;
	line-height: 40px;
}

.add-button {
	background-color: #007aff;
	color: #fff;
}

.save-button {
	background-color: #4cd964;
	color: #fff;
}

/* 弹出层样式 */
.popup-content {
	background-color: #fff;
	border-radius: 16px 16px 0 0;
	padding: 15px;
	margin-bottom: env(safe-area-inset-bottom); /* 适配底部安全区域 */
	min-height: 300px; /* 确保足够的内容高度 */
	max-height: 80vh; /* 最大高度限制 */
}

.popup-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding-bottom: 15px;
	border-bottom: 1px solid #eee;
	margin-bottom: 15px;
}

.popup-title {
	font-size: 18px;
	font-weight: bold;
	color: #333;
}

.popup-close {
	font-size: 24px;
	color: #999;
	padding: 0 5px;
}

.popup-body {
	padding-bottom: 20px;
}

.form-item {
	margin-bottom: 15px;
}

.form-label {
	display: block;
	font-size: 14px;
	color: #333;
	margin-bottom: 8px;
}

.material-input {
	width: 100%;
	height: 38px;
	padding: 0 10px;
	border: 1px solid #ddd;
	border-radius: 4px;
	background-color: #fff;
	box-sizing: border-box;
}

.confirm-button {
	width: 100%;
	height: 40px;
	line-height: 40px;
	border-radius: 20px;
	margin-top: 20px;
}

/* 确保弹出层在底部栏上方 */
:deep(.uni-popup) {
	z-index: 1000 !important;
}

:deep(.uni-popup__wrapper) {
	z-index: 1001 !important;
}

:deep(.uni-popup-bottom) {
	height: auto !important;
	max-height: 80vh !important;
	overflow: auto;
}
</style>
