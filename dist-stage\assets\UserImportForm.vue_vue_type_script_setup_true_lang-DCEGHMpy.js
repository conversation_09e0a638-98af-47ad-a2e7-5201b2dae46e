import{_ as j}from"./Dialog.vue_vue_type_style_index_0_lang-DhV_HFpU.js";import{d as A,_ as B,ah as H,aE as L}from"./index-Byekp3Iv.js";import{i as O}from"./index-DP9Sf6UT.js";import{d as P}from"./download-oWiM5xVU.js";import{z as D,w as R,P as q,f as G}from"./form-designer-C0ARe9Dh.js";import{k as J,r as i,y as K,m as M,z as r,H as m,u as o,h as x,v as f,E as v,n as N}from"./form-create-B86qX0W_.js";const Q={class:"el-upload__tip text-center"},T={class:"el-upload__tip"},W=J({name:"SystemUserImportForm",__name:"UserImportForm",emits:["success"],setup(X,{expose:U,emit:b}){const d=A(),u=i(!1),l=i(!1),_=i(),y=i(),p=i([]),c=i(0);U({open:()=>{u.value=!0,c.value=0,p.value=[],z()}});const g=async()=>{p.value.length!=0?(y.value={Authorization:"Bearer "+L(),"tenant-id":H()},l.value=!0,_.value.submit()):d.error("\u8BF7\u4E0A\u4F20\u6587\u4EF6")},k=b,V=a=>{if(a.code!==0)return d.error(a.msg),void(l.value=!1);const e=a.data;let s="\u4E0A\u4F20\u6210\u529F\u6570\u91CF\uFF1A"+e.createUsernames.length+";";for(let t of e.createUsernames)s+="< "+t+" >";s+="\u66F4\u65B0\u6210\u529F\u6570\u91CF\uFF1A"+e.updateUsernames.length+";";for(const t of e.updateUsernames)s+="< "+t+" >";s+="\u66F4\u65B0\u5931\u8D25\u6570\u91CF\uFF1A"+Object.keys(e.failureUsernames).length+";";for(const t in e.failureUsernames)s+="< "+t+": "+e.failureUsernames[t]+" >";d.alert(s),l.value=!1,u.value=!1,k("success")},w=()=>{d.error("\u4E0A\u4F20\u5931\u8D25\uFF0C\u8BF7\u60A8\u91CD\u65B0\u4E0A\u4F20\uFF01"),l.value=!1},z=async()=>{var a;l.value=!1,await N(),(a=_.value)==null||a.clearFiles()},C=()=>{d.error("\u6700\u591A\u53EA\u80FD\u4E0A\u4F20\u4E00\u4E2A\u6587\u4EF6\uFF01")},F=async()=>{const a=await O();P.excel(a,"\u7528\u6237\u5BFC\u5165\u6A21\u7248.xls")};return(a,e)=>{const s=B,t=R,E=q,I=D,h=G,S=j;return M(),K(S,{modelValue:o(u),"onUpdate:modelValue":e[3]||(e[3]=n=>x(u)?u.value=n:null),title:"\u7528\u6237\u5BFC\u5165",width:"400"},{footer:r(()=>[m(h,{disabled:o(l),type:"primary",onClick:g},{default:r(()=>e[8]||(e[8]=[v("\u786E \u5B9A")])),_:1},8,["disabled"]),m(h,{onClick:e[2]||(e[2]=n=>u.value=!1)},{default:r(()=>e[9]||(e[9]=[v("\u53D6 \u6D88")])),_:1})]),default:r(()=>[m(I,{ref_key:"uploadRef",ref:_,"file-list":o(p),"onUpdate:fileList":e[1]||(e[1]=n=>x(p)?p.value=n:null),action:"https://optest.hbfarmx.com/admin-api/system/user/import?updateSupport="+o(c),"auto-upload":!1,disabled:o(l),headers:o(y),limit:1,"on-error":w,"on-exceed":C,"on-success":V,accept:".xlsx, .xls",drag:""},{tip:r(()=>[f("div",Q,[f("div",T,[m(t,{modelValue:o(c),"onUpdate:modelValue":e[0]||(e[0]=n=>x(c)?c.value=n:null)},null,8,["modelValue"]),e[4]||(e[4]=v(" \u662F\u5426\u66F4\u65B0\u5DF2\u7ECF\u5B58\u5728\u7684\u7528\u6237\u6570\u636E "))]),e[6]||(e[6]=f("span",null,"\u4EC5\u5141\u8BB8\u5BFC\u5165 xls\u3001xlsx \u683C\u5F0F\u6587\u4EF6\u3002",-1)),m(E,{underline:!1,style:{"font-size":"12px","vertical-align":"baseline"},type:"primary",onClick:F},{default:r(()=>e[5]||(e[5]=[v(" \u4E0B\u8F7D\u6A21\u677F ")])),_:1})])]),default:r(()=>[m(s,{icon:"ep:upload"}),e[7]||(e[7]=f("div",{class:"el-upload__text"},[v("\u5C06\u6587\u4EF6\u62D6\u5230\u6B64\u5904\uFF0C\u6216"),f("em",null,"\u70B9\u51FB\u4E0A\u4F20")],-1))]),_:1},8,["file-list","action","disabled","headers"])]),_:1},8,["modelValue"])}}});export{W as _};
