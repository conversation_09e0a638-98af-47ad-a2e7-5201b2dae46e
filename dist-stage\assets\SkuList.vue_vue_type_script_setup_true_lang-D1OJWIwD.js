import{p as B,d as W,Z as K,F as S,L as Q,at as X,M as $}from"./index-Byekp3Iv.js";import{Z as A,_ as Y,k as ee,l as le,f as ae,al as te}from"./form-designer-C0ARe9Dh.js";import{k as ie,r as x,b as j,l as v,m as p,G as U,y as w,C as h,u as c,z as o,H as i,$ as T,v as z,F as m,E as f,q as oe}from"./form-create-B86qX0W_.js";const re={style:{"font-weight":"bold",color:"#40aaff"}},ne={style:{"font-weight":"bold",color:"#40aaff"}},se={style:{"font-weight":"bold",color:"#40aaff"}},de=ie({name:"SkuList",__name:"SkuList",props:{propFormData:{type:Object,default:()=>{}},propertyList:{type:Array,default:()=>[]},ruleConfig:{type:Array,default:()=>[]},isBatch:B.bool.def(!1),isDetail:B.bool.def(!1),isComponent:B.bool.def(!1),isActivityComponent:B.bool.def(!1)},emits:["selectionChange"],setup(g,{expose:R,emit:Z}){const E=W(),y=g,u=x(),N=x([{price:0,marketPrice:0,costPrice:0,barCode:"",picUrl:"",stock:0,weight:0,volume:0,firstBrokeragePrice:0,secondBrokeragePrice:0}]),O=t=>{Q({zIndex:9999999,urlList:[t]})},q=()=>{D(),u.value.skus.forEach(t=>{X(t,N.value[0])})},D=()=>{const t="\u5B58\u5728\u5C5E\u6027\u5C5E\u6027\u503C\u4E3A\u7A7A\uFF0C\u8BF7\u5148\u68C0\u67E5\u5B8C\u5584\u5C5E\u6027\u503C\u540E\u91CD\u8BD5\uFF01\uFF01\uFF01";for(const s of y.propertyList)if(!s.values||$(s.values))throw E.warning(t),new Error(t)},C=x([]),G=(t,s)=>{const e=s.split(".");let r=t;for(const n of e){if(!r||typeof r!="object"||!(n in r)){r=void 0;break}r=r[n]}return r},H=Z,M=t=>{H("selectionChange",t)};j(()=>y.propFormData,t=>{t&&(u.value=t)},{deep:!0,immediate:!0});const I=t=>{const s=t.map(r=>r.values.map(n=>({propertyId:r.id,propertyName:r.name,valueId:n.id,valueName:n.name}))),e=J(s);L(t)||(u.value.skus=[]);for(const r of e){const n={properties:Array.isArray(r)?r:[r],price:0,marketPrice:0,costPrice:0,barCode:"",picUrl:"",stock:0,weight:0,volume:0,firstBrokeragePrice:0,secondBrokeragePrice:0};u.value.skus.findIndex(V=>JSON.stringify(V.properties)===JSON.stringify(n.properties))===-1&&u.value.skus.push(n)}},L=t=>{const s=[];u.value.skus.forEach(r=>{var n,V;return(V=(n=r.properties)==null?void 0:n.map(_=>_.propertyId))==null?void 0:V.forEach(_=>{s.indexOf(_)===-1&&s.push(_)})});const e=t.map(r=>r.id);return s.length===e.length},J=t=>{if(t.length===0)return[];if(t.length===1)return t[0];{const s=[],e=J(t.slice(1));for(let r=0;r<t[0].length;r++)for(let n=0;n<e.length;n++)Array.isArray(e[n])?s.push([t[0][r],...e[n]]):s.push([t[0][r],e[n]]);return s}};j(()=>y.propertyList,t=>{u.value.specType&&(y.isBatch&&(N.value=[{price:0,marketPrice:0,costPrice:0,barCode:"",picUrl:"",stock:0,weight:0,volume:0,firstBrokeragePrice:0,secondBrokeragePrice:0}]),JSON.stringify(t)!=="[]"&&(C.value=[],t.forEach((s,e)=>{C.value.push({prop:`name${e}`,label:s.name})}),L(t)||t.some(s=>!s.values||$(s.values))||I(t)))},{deep:!0,immediate:!0});const F=x();return R({generateTableData:I,validateSku:()=>{D();let t="\u8BF7\u68C0\u67E5\u5546\u54C1\u5404\u884C\u76F8\u5173\u5C5E\u6027\u914D\u7F6E\uFF0C",s=!0;for(const e of u.value.skus){for(const r of y==null?void 0:y.ruleConfig){const n=G(e,r.name);if(!r.rule(n)){s=!1,t+=r.message;break}}if(!s)throw E.warning(t),new Error(t)}},getSkuTableRef:()=>F.value}),(t,s)=>{const e=Y,r=ee,n=le,V=ae,_=te;return p(),v(U,null,[g.isDetail||g.isActivityComponent?h("",!0):(p(),w(c(A),{key:0,data:g.isBatch?c(N):c(u).skus,border:"",class:"tabNumWidth","max-height":"500",size:"small"},{default:o(()=>{var l;return[i(e,{align:"center",label:"\u56FE\u7247","min-width":"65"},{default:o(({row:a})=>[i(c(K),{modelValue:a.picUrl,"onUpdate:modelValue":d=>a.picUrl=d,height:"50px",width:"50px"},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),c(u).specType&&!g.isBatch?(p(!0),v(U,{key:0},T(c(C),(a,d)=>(p(),w(e,{key:d,label:a.label,align:"center","min-width":"120"},{default:o(({row:b})=>{var k,P;return[z("span",re,m((P=(k=b.properties)==null?void 0:k[d])==null?void 0:P.valueName),1)]}),_:2},1032,["label"]))),128)):h("",!0),i(e,{align:"center",label:"\u5546\u54C1\u6761\u7801","min-width":"168"},{default:o(({row:a})=>[i(r,{modelValue:a.barCode,"onUpdate:modelValue":d=>a.barCode=d,class:"w-100%"},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),i(e,{align:"center",label:"\u9500\u552E\u4EF7","min-width":"168"},{default:o(({row:a})=>[i(n,{modelValue:a.price,"onUpdate:modelValue":d=>a.price=d,min:0,precision:2,step:.1,class:"w-100%","controls-position":"right"},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),i(e,{align:"center",label:"\u5E02\u573A\u4EF7","min-width":"168"},{default:o(({row:a})=>[i(n,{modelValue:a.marketPrice,"onUpdate:modelValue":d=>a.marketPrice=d,min:0,precision:2,step:.1,class:"w-100%","controls-position":"right"},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),i(e,{align:"center",label:"\u6210\u672C\u4EF7","min-width":"168"},{default:o(({row:a})=>[i(n,{modelValue:a.costPrice,"onUpdate:modelValue":d=>a.costPrice=d,min:0,precision:2,step:.1,class:"w-100%","controls-position":"right"},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),i(e,{align:"center",label:"\u5E93\u5B58","min-width":"168"},{default:o(({row:a})=>[i(n,{modelValue:a.stock,"onUpdate:modelValue":d=>a.stock=d,min:0,class:"w-100%","controls-position":"right"},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),i(e,{align:"center",label:"\u91CD\u91CF(kg)","min-width":"168"},{default:o(({row:a})=>[i(n,{modelValue:a.weight,"onUpdate:modelValue":d=>a.weight=d,min:0,precision:2,step:.1,class:"w-100%","controls-position":"right"},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),i(e,{align:"center",label:"\u4F53\u79EF(m^3)","min-width":"168"},{default:o(({row:a})=>[i(n,{modelValue:a.volume,"onUpdate:modelValue":d=>a.volume=d,min:0,precision:2,step:.1,class:"w-100%","controls-position":"right"},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),c(u).subCommissionType?(p(),v(U,{key:1},[i(e,{align:"center",label:"\u4E00\u7EA7\u8FD4\u4F63(\u5143)","min-width":"168"},{default:o(({row:a})=>[i(n,{modelValue:a.firstBrokeragePrice,"onUpdate:modelValue":d=>a.firstBrokeragePrice=d,min:0,precision:2,step:.1,class:"w-100%","controls-position":"right"},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),i(e,{align:"center",label:"\u4E8C\u7EA7\u8FD4\u4F63(\u5143)","min-width":"168"},{default:o(({row:a})=>[i(n,{modelValue:a.secondBrokeragePrice,"onUpdate:modelValue":d=>a.secondBrokeragePrice=d,min:0,precision:2,step:.1,class:"w-100%","controls-position":"right"},null,8,["modelValue","onUpdate:modelValue"])]),_:1})],64)):h("",!0),(l=c(u))!=null&&l.specType?(p(),w(e,{key:2,align:"center",fixed:"right",label:"\u64CD\u4F5C",width:"80"},{default:o(({row:a})=>[g.isBatch?(p(),w(V,{key:0,link:"",size:"small",type:"primary",onClick:q},{default:o(()=>s[0]||(s[0]=[f(" \u6279\u91CF\u6DFB\u52A0 ")])),_:1})):(p(),w(V,{key:1,link:"",size:"small",type:"primary",onClick:d=>(b=>{const k=u.value.skus.findIndex(P=>JSON.stringify(P.properties)===JSON.stringify(b.properties));u.value.skus.splice(k,1)})(a)},{default:o(()=>s[1]||(s[1]=[f("\u5220\u9664")])),_:2},1032,["onClick"]))]),_:1})):h("",!0)]}),_:1},8,["data"])),g.isDetail?(p(),w(c(A),{key:1,ref_key:"activitySkuListRef",ref:F,data:c(u).skus,border:"","max-height":"500",size:"small",style:{width:"99%"},onSelectionChange:M},{default:o(()=>[g.isComponent?(p(),w(e,{key:0,type:"selection",width:"45"})):h("",!0),i(e,{align:"center",label:"\u56FE\u7247","min-width":"80"},{default:o(({row:l})=>[l.picUrl?(p(),w(_,{key:0,src:l.picUrl,class:"h-50px w-50px",onClick:a=>O(l.picUrl)},null,8,["src","onClick"])):h("",!0)]),_:1}),c(u).specType&&!g.isBatch?(p(!0),v(U,{key:1},T(c(C),(l,a)=>(p(),w(e,{key:a,label:l.label,align:"center","min-width":"80"},{default:o(({row:d})=>{var b,k;return[z("span",ne,m((k=(b=d.properties)==null?void 0:b[a])==null?void 0:k.valueName),1)]}),_:2},1032,["label"]))),128)):h("",!0),i(e,{align:"center",label:"\u5546\u54C1\u6761\u7801","min-width":"100"},{default:o(({row:l})=>[f(m(l.barCode),1)]),_:1}),i(e,{align:"center",label:"\u9500\u552E\u4EF7(\u5143)","min-width":"80"},{default:o(({row:l})=>[f(m(l.price),1)]),_:1}),i(e,{align:"center",label:"\u5E02\u573A\u4EF7(\u5143)","min-width":"80"},{default:o(({row:l})=>[f(m(l.marketPrice),1)]),_:1}),i(e,{align:"center",label:"\u6210\u672C\u4EF7(\u5143)","min-width":"80"},{default:o(({row:l})=>[f(m(l.costPrice),1)]),_:1}),i(e,{align:"center",label:"\u5E93\u5B58","min-width":"80"},{default:o(({row:l})=>[f(m(l.stock),1)]),_:1}),i(e,{align:"center",label:"\u91CD\u91CF(kg)","min-width":"80"},{default:o(({row:l})=>[f(m(l.weight),1)]),_:1}),i(e,{align:"center",label:"\u4F53\u79EF(m^3)","min-width":"80"},{default:o(({row:l})=>[f(m(l.volume),1)]),_:1}),c(u).subCommissionType?(p(),v(U,{key:2},[i(e,{align:"center",label:"\u4E00\u7EA7\u8FD4\u4F63(\u5143)","min-width":"80"},{default:o(({row:l})=>[f(m(l.firstBrokeragePrice),1)]),_:1}),i(e,{align:"center",label:"\u4E8C\u7EA7\u8FD4\u4F63(\u5143)","min-width":"80"},{default:o(({row:l})=>[f(m(l.secondBrokeragePrice),1)]),_:1})],64)):h("",!0)]),_:1},8,["data"])):h("",!0),g.isActivityComponent?(p(),w(c(A),{key:2,data:c(u).skus,border:"","max-height":"500",size:"small",style:{width:"99%"}},{default:o(()=>[g.isComponent?(p(),w(e,{key:0,type:"selection",width:"45"})):h("",!0),i(e,{align:"center",label:"\u56FE\u7247","min-width":"80"},{default:o(({row:l})=>[i(_,{src:l.picUrl,class:"h-60px w-60px",onClick:a=>O(l.picUrl)},null,8,["src","onClick"])]),_:1}),c(u).specType?(p(!0),v(U,{key:1},T(c(C),(l,a)=>(p(),w(e,{key:a,label:l.label,align:"center","min-width":"80"},{default:o(({row:d})=>{var b,k;return[z("span",se,m((k=(b=d.properties)==null?void 0:b[a])==null?void 0:k.valueName),1)]}),_:2},1032,["label"]))),128)):h("",!0),i(e,{align:"center",label:"\u5546\u54C1\u6761\u7801","min-width":"100"},{default:o(({row:l})=>[f(m(l.barCode),1)]),_:1}),i(e,{align:"center",label:"\u9500\u552E\u4EF7(\u5143)","min-width":"80"},{default:o(({row:l})=>[f(m(c(S)(l.price)),1)]),_:1}),i(e,{align:"center",label:"\u5E02\u573A\u4EF7(\u5143)","min-width":"80"},{default:o(({row:l})=>[f(m(c(S)(l.marketPrice)),1)]),_:1}),i(e,{align:"center",label:"\u6210\u672C\u4EF7(\u5143)","min-width":"80"},{default:o(({row:l})=>[f(m(c(S)(l.costPrice)),1)]),_:1}),i(e,{align:"center",label:"\u5E93\u5B58","min-width":"80"},{default:o(({row:l})=>[f(m(l.stock),1)]),_:1}),oe(t.$slots,"extension")]),_:3},8,["data"])):h("",!0)],64)}}});export{de as _};
