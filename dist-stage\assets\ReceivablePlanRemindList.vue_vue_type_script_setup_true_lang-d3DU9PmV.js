import{u as B,aw as G,D as H,ax as w}from"./index-Byekp3Iv.js";import{_ as M}from"./index.vue_vue_type_script_setup_true_lang-BeMNDf6p.js";import{_ as Y}from"./DictTag.vue_vue_type_script_lang-DdZ_pRVv.js";import{_ as Z}from"./ContentWrap.vue_vue_type_script_setup_true_lang-D3mNNG_R.js";import{b as g,d as R}from"./formatTime-HVkyL6Kg.js";import{e as $}from"./index-EFvRhq4A.js";import{R as J}from"./common-BQQO87UM.js";import{_ as K}from"./ReceivableForm.vue_vue_type_script_setup_true_lang-L3T7DcqW.js";import{h as O,aj as W,x as X,Q as ee,ak as ae,_ as re,P as le,a7 as te,f as ie,Z as oe}from"./form-designer-C0ARe9Dh.js";import{k as ne,r as c,P as pe,Q as me,e as de,af as ue,l as N,m as n,G as P,H as a,z as l,v as se,u as r,$ as ce,y as m,A as E,E as d,F as u}from"./form-create-B86qX0W_.js";const fe=ne({name:"ReceivablePlanRemindList",__name:"ReceivablePlanRemindList",setup(be){const v=c(!0),y=c(0),h=c([]),o=pe({pageNo:1,pageSize:10,remindType:1}),U=c(),s=async()=>{v.value=!0;try{const _=await $(o);h.value=_.list,y.value=_.total}finally{v.value=!1}},z=()=>{o.pageNo=1,s()},x=c(),{push:k}=B();return me(async()=>{await s()}),de(async()=>{await s()}),(_,i)=>{const D=ee,S=X,V=W,F=O,T=Z,C=le,t=re,I=Y,f=te,L=ie,Q=oe,A=M,j=ue("hasPermi"),q=ae;return n(),N(P,null,[a(T,null,{default:l(()=>[i[3]||(i[3]=se("div",{class:"pb-5 text-xl"},"\u5F85\u56DE\u6B3E\u63D0\u9192",-1)),a(F,{ref_key:"queryFormRef",ref:U,inline:!0,model:r(o),class:"-mb-15px","label-width":"68px"},{default:l(()=>[a(V,{label:"\u5408\u540C\u72B6\u6001",prop:"remindType"},{default:l(()=>[a(S,{modelValue:r(o).remindType,"onUpdate:modelValue":i[0]||(i[0]=e=>r(o).remindType=e),class:"!w-240px",placeholder:"\u72B6\u6001",onChange:z},{default:l(()=>[(n(!0),N(P,null,ce(r(J),(e,b)=>(n(),m(D,{label:e.label,value:e.value,key:b},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1}),a(T,null,{default:l(()=>[E((n(),m(Q,{data:r(h),stripe:!0,"show-overflow-tooltip":!0},{default:l(()=>[a(t,{align:"center",fixed:"left",label:"\u5BA2\u6237\u540D\u79F0",prop:"customerName",width:"150"},{default:l(e=>[a(C,{underline:!1,type:"primary",onClick:b=>{return p=e.row.customerId,void k({name:"CrmCustomerDetail",params:{id:p}});var p}},{default:l(()=>[d(u(e.row.customerName),1)]),_:2},1032,["onClick"])]),_:1}),a(t,{align:"center",label:"\u5408\u540C\u7F16\u53F7",prop:"contractNo",width:"200px"}),a(t,{align:"center",label:"\u671F\u6570",prop:"period"},{default:l(e=>[a(C,{underline:!1,type:"primary",onClick:b=>{return p=e.row.id,void k({name:"CrmReceivablePlanDetail",params:{id:p}});var p}},{default:l(()=>[d(u(e.row.period),1)]),_:2},1032,["onClick"])]),_:1}),a(t,{align:"center",label:"\u8BA1\u5212\u56DE\u6B3E\u91D1\u989D\uFF08\u5143\uFF09",prop:"price",width:"160",formatter:r(G)},null,8,["formatter"]),a(t,{formatter:r(g),align:"center",label:"\u8BA1\u5212\u56DE\u6B3E\u65E5\u671F",prop:"returnTime",width:"180px"},null,8,["formatter"]),a(t,{align:"center",label:"\u63D0\u524D\u51E0\u5929\u63D0\u9192",prop:"remindDays",width:"150"}),a(t,{align:"center",label:"\u63D0\u9192\u65E5\u671F",prop:"remindTime",width:"180px",formatter:r(g)},null,8,["formatter"]),a(t,{align:"center",label:"\u56DE\u6B3E\u65B9\u5F0F",prop:"returnType",width:"130px"},{default:l(e=>[a(I,{type:r(H).CRM_RECEIVABLE_RETURN_TYPE,value:e.row.returnType},null,8,["type","value"])]),_:1}),a(t,{align:"center",label:"\u5907\u6CE8",prop:"remark"}),a(t,{label:"\u8D1F\u8D23\u4EBA",prop:"ownerUserName",width:"120"}),a(t,{align:"center",label:"\u5B9E\u9645\u56DE\u6B3E\u91D1\u989D\uFF08\u5143\uFF09",prop:"receivable.price",width:"160"},{default:l(e=>[e.row.receivable?(n(),m(f,{key:0},{default:l(()=>[d(u(r(w)(e.row.receivable.price)),1)]),_:2},1024)):(n(),m(f,{key:1},{default:l(()=>[d(u(r(w)(0)),1)]),_:1}))]),_:1}),a(t,{align:"center",label:"\u5B9E\u9645\u56DE\u6B3E\u65E5\u671F",prop:"receivable.returnTime",width:"180px",formatter:r(g)},null,8,["formatter"]),a(t,{align:"center",label:"\u5B9E\u9645\u56DE\u6B3E\u91D1\u989D\uFF08\u5143\uFF09",prop:"receivable.price",width:"160"},{default:l(e=>[e.row.receivable?(n(),m(f,{key:0},{default:l(()=>[d(u(r(w)(e.row.price-e.row.receivable.price)),1)]),_:2},1024)):(n(),m(f,{key:1},{default:l(()=>[d(u(r(w)(e.row.price)),1)]),_:2},1024))]),_:1}),a(t,{formatter:r(R),align:"center",label:"\u66F4\u65B0\u65F6\u95F4",prop:"updateTime",width:"180px"},null,8,["formatter"]),a(t,{formatter:r(R),align:"center",label:"\u521B\u5EFA\u65F6\u95F4",prop:"createTime",width:"180px"},null,8,["formatter"]),a(t,{align:"center",label:"\u521B\u5EFA\u4EBA",prop:"creatorName",width:"100px"}),a(t,{align:"center",fixed:"right",label:"\u64CD\u4F5C",width:"180px"},{default:l(e=>[E((n(),m(L,{link:"",type:"success",onClick:b=>{return p=e.row,void x.value.open("create",void 0,p);var p},disabled:e.row.receivableId},{default:l(()=>i[4]||(i[4]=[d(" \u521B\u5EFA\u56DE\u6B3E ")])),_:2},1032,["onClick","disabled"])),[[j,["crm:receivable:create"]]])]),_:1})]),_:1},8,["data"])),[[q,r(v)]]),a(A,{total:r(y),page:r(o).pageNo,"onUpdate:page":i[1]||(i[1]=e=>r(o).pageNo=e),limit:r(o).pageSize,"onUpdate:limit":i[2]||(i[2]=e=>r(o).pageSize=e),onPagination:s},null,8,["total","page","limit"])]),_:1}),a(K,{ref_key:"receivableFormRef",ref:x,onSuccess:s},null,512)],64)}}});export{fe as _};
