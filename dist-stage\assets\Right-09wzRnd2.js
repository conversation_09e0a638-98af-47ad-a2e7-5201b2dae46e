import{d as V,aF as W,_ as B,c as C}from"./index-Byekp3Iv.js";import{k as H,c as m,r as j,b as R,y as T,m as A,z as a,v as l,A as f,H as o,E as b,I as h,h as E,u as y}from"./form-create-B86qX0W_.js";import{f as F,k as U,ad as q}from"./form-designer-C0ARe9Dh.js";const D={class:"m-0 px-7 shrink-0 flex items-center justify-between"},G={class:"w-full min-h-full relative flex-grow bg-white box-border p-3 sm:p-7"},J=C(H({__name:"Right",props:{content:{type:String,default:""},isWriting:{type:Boolean,default:!1}},emits:["update:content","stopStream"],setup(i,{expose:x,emit:g}){const v=V(),{copied:_,copy:k}=W(),t=i,c=g,n=m({get:()=>t.content,set(s){c("update:content",s)}}),r=j();x({scrollToBottom(){var s,e;(e=r.value)==null||e.scrollTo(0,(s=r.value)==null?void 0:s.scrollHeight)}});const w=m(()=>t.content&&!t.isWriting),z=()=>{k(t.content)};return R(_,s=>{s&&v.success("\u590D\u5236\u6210\u529F")}),(s,e)=>{const u=B,d=F,S=U,I=q;return A(),T(I,{class:"my-card h-full"},{header:a(()=>[l("h3",D,[e[3]||(e[3]=l("span",null,"\u9884\u89C8",-1)),f(o(d,{color:"#846af7",onClick:z,size:"small"},{icon:a(()=>[o(u,{icon:"ph:copy-bold"})]),default:a(()=>[e[2]||(e[2]=b(" \u590D\u5236 "))]),_:1},512),[[h,y(w)]])])]),default:a(()=>[l("div",{ref_key:"contentRef",ref:r,class:"hide-scroll-bar h-full box-border overflow-y-auto"},[l("div",G,[f(o(d,{class:"absolute bottom-2 sm:bottom-5 left-1/2 -translate-x-1/2 z-36",onClick:e[0]||(e[0]=p=>c("stopStream")),size:"small"},{icon:a(()=>[o(u,{icon:"material-symbols:stop"})]),default:a(()=>[e[4]||(e[4]=b(" \u7EC8\u6B62\u751F\u6210 "))]),_:1},512),[[h,i.isWriting]]),o(S,{id:"inputId",type:"textarea",modelValue:y(n),"onUpdate:modelValue":e[1]||(e[1]=p=>E(n)?n.value=p:null),autosize:"","input-style":{boxShadow:"none"},resize:"none",placeholder:"\u751F\u6210\u7684\u5185\u5BB9\u2026\u2026"},null,8,["modelValue"])])],512)]),_:1})}}}),[["__scopeId","data-v-13472b00"]]);export{J as default};
