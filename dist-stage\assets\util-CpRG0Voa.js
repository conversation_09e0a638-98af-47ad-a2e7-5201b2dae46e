import{_ as Uo}from"./Dialog.vue_vue_type_style_index_0_lang-DhV_HFpU.js";import{Q as zo}from"./Qrcode--mo029lO.js";import{c as Y,N as e,p as w,O as Fo,_ as te,S as No,P as $o,Q as _o,M as Wo,__tla as Go}from"./index-Byekp3Iv.js";import{_ as Ho}from"./IFrame.vue_vue_type_script_setup_true_lang-nGuaSWHR.js";import{aq as go,K as B,o as bo,f as fo,T as yo,e as vo,ab as Yo,ac as Zo,W as qo,U as Jo,a6 as Ko,ad as Qo,a7 as Xo,ar as et}from"./form-designer-C0ARe9Dh.js";import{k as Z,y as C,m as s,aa as ot,ab as tt,z as n,q as ho,ao as rt,c as it,l as O,B as To,v as f,D as re,N as nt,u as t,C as E,F as V,H as i,M as q,P as Po,b as D,h as ie,G as A,$ as ne,r as L,i as at,e as lt,E as pt}from"./form-create-B86qX0W_.js";import{V as Co}from"./vuedraggable.umd-CjS8ClRh.js";let J,Eo,ae,Oo,K,Q,j,ct=Promise.all([(()=>{try{return Go}catch{}})()]).then(async()=>{let le,pe,ce,de,se,me,ue,_e,ge,be,fe,ye,ve,he,M,Te,Pe,U,Ce,Ee,Oe,we,xe,Re,Be,Le,Ie,ke,Ve,Se,De,Ae,je,Me,Ue,X,z,F,ee;le=Y(Z({name:"VerticalButtonGroup",__name:"index",setup:l=>(y,h)=>{const d=go;return s(),C(d,ot(tt(y.$attrs)),{default:n(()=>[ho(y.$slots,"default",{},void 0,!0)]),_:3},16)}}),[["__scopeId","data-v-346ec37c"]]),pe=Object.freeze(Object.defineProperty({__proto__:null,component:{id:"Carousel",name:"\u8F6E\u64AD\u56FE",icon:"system-uicons:carousel",property:{type:"default",indicator:"dot",autoplay:!1,interval:3,height:174,items:[{type:"img",imgUrl:"https://static.iocoder.cn/mall/banner-01.jpg",videoUrl:""},{type:"img",imgUrl:"https://static.iocoder.cn/mall/banner-02.jpg",videoUrl:""}],style:{bgType:"color",bgColor:"#fff",marginBottom:8}}}},Symbol.toStringTag,{value:"Module"})),ce=Object.freeze(Object.defineProperty({__proto__:null,component:{id:"CouponCard",name:"\u4F18\u60E0\u5238",icon:"ep:ticket",property:{columns:1,bgImg:"",textColor:"#E9B461",button:{color:"#434343",bgColor:""},space:0,couponIds:[],style:{bgType:"color",bgColor:"",marginBottom:8}}}},Symbol.toStringTag,{value:"Module"})),de=Object.freeze(Object.defineProperty({__proto__:null,component:{id:"Divider",name:"\u5206\u5272\u7EBF",icon:"tdesign:component-divider-vertical",property:{height:30,lineWidth:1,paddingType:"none",lineColor:"#dcdfe6",borderType:"solid"}}},Symbol.toStringTag,{value:"Module"})),se=Object.freeze(Object.defineProperty({__proto__:null,component:{id:"FloatingActionButton",name:"\u60AC\u6D6E\u6309\u94AE",icon:"tabler:float-right",position:"fixed",property:{direction:"vertical",showText:!0,list:[{textColor:"#fff"}]}}},Symbol.toStringTag,{value:"Module"})),me=Object.freeze(Object.defineProperty({__proto__:null,component:{id:"HotZone",name:"\u70ED\u533A",icon:"tabler:hand-click",property:{imgUrl:"",list:[],style:{bgType:"color",bgColor:"#fff",marginBottom:8}}}},Symbol.toStringTag,{value:"Module"})),ue=Object.freeze(Object.defineProperty({__proto__:null,component:{id:"ImageBar",name:"\u56FE\u7247\u5C55\u793A",icon:"ep:picture",property:{imgUrl:"",url:"",style:{bgType:"color",bgColor:"#fff",marginBottom:8}}}},Symbol.toStringTag,{value:"Module"})),_e=Object.freeze(Object.defineProperty({__proto__:null,component:{id:"MagicCube",name:"\u5E7F\u544A\u9B54\u65B9",icon:"bi:columns",property:{borderRadiusTop:0,borderRadiusBottom:0,space:0,list:[],style:{bgType:"color",bgColor:"#fff",marginBottom:8}}}},Symbol.toStringTag,{value:"Module"})),J={title:"\u6807\u9898",titleColor:"#333",subtitle:"\u526F\u6807\u9898",subtitleColor:"#bbb",badge:{show:!1,textColor:"#fff",bgColor:"#FF6000"}},ge={id:"MenuGrid",name:"\u5BAB\u683C\u5BFC\u822A",icon:"bi:grid-3x3-gap",property:{column:3,list:[B(J)],style:{bgType:"color",bgColor:"#fff",marginBottom:8,marginLeft:8,marginRight:8,padding:8,paddingTop:8,paddingRight:8,paddingBottom:8,paddingLeft:8,borderRadius:8,borderTopLeftRadius:8,borderTopRightRadius:8,borderBottomRightRadius:8,borderBottomLeftRadius:8}}},be=Object.freeze(Object.defineProperty({__proto__:null,EMPTY_MENU_GRID_ITEM_PROPERTY:J,component:ge},Symbol.toStringTag,{value:"Module"})),K={title:"\u6807\u9898",titleColor:"#333",subtitle:"\u526F\u6807\u9898",subtitleColor:"#bbb"},fe={id:"MenuList",name:"\u5217\u8868\u5BFC\u822A",icon:"fa-solid:list",property:{list:[B(K)],style:{bgType:"color",bgColor:"#fff",marginBottom:8}}},ye=Object.freeze(Object.defineProperty({__proto__:null,EMPTY_MENU_LIST_ITEM_PROPERTY:K,component:fe},Symbol.toStringTag,{value:"Module"})),Q={title:"\u6807\u9898",titleColor:"#333",badge:{show:!1,textColor:"#fff",bgColor:"#FF6000"}},ve={id:"MenuSwiper",name:"\u83DC\u5355\u5BFC\u822A",icon:"bi:grid-3x2-gap",property:{layout:"iconText",row:1,column:3,list:[B(Q)],style:{bgType:"color",bgColor:"#fff",marginBottom:8}}},he=Object.freeze(Object.defineProperty({__proto__:null,EMPTY_MENU_SWIPER_ITEM_PROPERTY:Q,component:ve},Symbol.toStringTag,{value:"Module"})),M={id:"NavigationBar",name:"\u9876\u90E8\u5BFC\u822A\u680F",icon:"tabler:layout-navbar",property:{bgType:"color",bgColor:"#fff",bgImg:"",styleType:"normal",alwaysShow:!0,mpCells:[{type:"text",textColor:"#111111"}],otherCells:[{type:"text",textColor:"#111111"}],_local:{previewMp:!0,previewOther:!1}}},Te=Object.freeze(Object.defineProperty({__proto__:null,component:M},Symbol.toStringTag,{value:"Module"})),Pe=Object.freeze(Object.defineProperty({__proto__:null,component:{id:"NoticeBar",name:"\u516C\u544A\u680F",icon:"ep:bell",property:{iconUrl:"http://mall.yudao.iocoder.cn/static/images/xinjian.png",contents:[{text:"",url:""}],backgroundColor:"#fff",textColor:"#333",style:{bgType:"color",bgColor:"#fff",marginBottom:8}}}},Symbol.toStringTag,{value:"Module"})),U={id:"PageConfig",name:"\u9875\u9762\u8BBE\u7F6E",icon:"ep:document",property:{description:"",backgroundColor:"#f5f5f5",backgroundImage:""}},Ce=Object.freeze(Object.defineProperty({__proto__:null,component:U},Symbol.toStringTag,{value:"Module"})),Ee=Object.freeze(Object.defineProperty({__proto__:null,component:{id:"Popover",name:"\u5F39\u7A97\u5E7F\u544A",icon:"carbon:popup",position:"fixed",property:{list:[{showType:"once"}]}}},Symbol.toStringTag,{value:"Module"})),Oe=Object.freeze(Object.defineProperty({__proto__:null,component:{id:"ProductCard",name:"\u5546\u54C1\u5361\u7247",icon:"fluent:text-column-two-left-24-filled",property:{layoutType:"oneColBigImg",fields:{name:{show:!0,color:"#000"},introduction:{show:!0,color:"#999"},price:{show:!0,color:"#ff3000"},marketPrice:{show:!0,color:"#c4c4c4"},salesCount:{show:!0,color:"#c4c4c4"},stock:{show:!1,color:"#c4c4c4"}},badge:{show:!1,imgUrl:""},btnBuy:{type:"text",text:"\u7ACB\u5373\u8D2D\u4E70",bgBeginColor:"#FF6000",bgEndColor:"#FE832A",imgUrl:""},borderRadiusTop:6,borderRadiusBottom:6,space:8,spuIds:[],style:{bgType:"color",bgColor:"",marginLeft:8,marginRight:8,marginBottom:8}}}},Symbol.toStringTag,{value:"Module"})),we=Object.freeze(Object.defineProperty({__proto__:null,component:{id:"ProductList",name:"\u5546\u54C1\u680F",icon:"fluent:text-column-two-24-filled",property:{layoutType:"twoCol",fields:{name:{show:!0,color:"#000"},price:{show:!0,color:"#ff3000"}},badge:{show:!1,imgUrl:""},borderRadiusTop:8,borderRadiusBottom:8,space:8,spuIds:[],style:{bgType:"color",bgColor:"",marginLeft:8,marginRight:8,marginBottom:8}}}},Symbol.toStringTag,{value:"Module"})),xe=Object.freeze(Object.defineProperty({__proto__:null,component:{id:"PromotionArticle",name:"\u8425\u9500\u6587\u7AE0",icon:"ph:article-medium",property:{style:{bgType:"color",bgColor:"",marginLeft:8,marginRight:8,marginBottom:8}}}},Symbol.toStringTag,{value:"Module"})),Re=Object.freeze(Object.defineProperty({__proto__:null,component:{id:"PromotionCombination",name:"\u62FC\u56E2",icon:"mdi:account-group",property:{layoutType:"oneColBigImg",fields:{name:{show:!0,color:"#000"},introduction:{show:!0,color:"#999"},price:{show:!0,color:"#ff3000"},marketPrice:{show:!0,color:"#c4c4c4"},salesCount:{show:!0,color:"#c4c4c4"},stock:{show:!1,color:"#c4c4c4"}},badge:{show:!1,imgUrl:""},btnBuy:{type:"text",text:"\u53BB\u62FC\u56E2",bgBeginColor:"#FF6000",bgEndColor:"#FE832A",imgUrl:""},borderRadiusTop:8,borderRadiusBottom:8,space:8,style:{bgType:"color",bgColor:"",marginLeft:8,marginRight:8,marginBottom:8}}}},Symbol.toStringTag,{value:"Module"})),Be=Object.freeze(Object.defineProperty({__proto__:null,component:{id:"PromotionPoint",name:"\u79EF\u5206\u5546\u57CE",icon:"ep:present",property:{layoutType:"oneColBigImg",fields:{name:{show:!0,color:"#000"},introduction:{show:!0,color:"#999"},price:{show:!0,color:"#ff3000"},marketPrice:{show:!0,color:"#c4c4c4"},salesCount:{show:!0,color:"#c4c4c4"},stock:{show:!1,color:"#c4c4c4"}},badge:{show:!1,imgUrl:""},btnBuy:{type:"text",text:"\u7ACB\u5373\u5151\u6362",bgBeginColor:"#FF6000",bgEndColor:"#FE832A",imgUrl:""},borderRadiusTop:8,borderRadiusBottom:8,space:8,style:{bgType:"color",bgColor:"",marginLeft:8,marginRight:8,marginBottom:8}}}},Symbol.toStringTag,{value:"Module"})),Le=Object.freeze(Object.defineProperty({__proto__:null,component:{id:"PromotionSeckill",name:"\u79D2\u6740",icon:"mdi:calendar-time",property:{layoutType:"oneColBigImg",fields:{name:{show:!0,color:"#000"},introduction:{show:!0,color:"#999"},price:{show:!0,color:"#ff3000"},marketPrice:{show:!0,color:"#c4c4c4"},salesCount:{show:!0,color:"#c4c4c4"},stock:{show:!1,color:"#c4c4c4"}},badge:{show:!1,imgUrl:""},btnBuy:{type:"text",text:"\u7ACB\u5373\u79D2\u6740",bgBeginColor:"#FF6000",bgEndColor:"#FE832A",imgUrl:""},borderRadiusTop:8,borderRadiusBottom:8,space:8,style:{bgType:"color",bgColor:"",marginLeft:8,marginRight:8,marginBottom:8}}}},Symbol.toStringTag,{value:"Module"})),Ie=Object.freeze(Object.defineProperty({__proto__:null,component:{id:"SearchBar",name:"\u641C\u7D22\u6846",icon:"ep:search",property:{height:28,showScan:!1,borderRadius:0,placeholder:"\u641C\u7D22\u5546\u54C1",placeholderPosition:"left",backgroundColor:"rgb(238, 238, 238)",textColor:"rgb(150, 151, 153)",hotKeywords:[],style:{bgType:"color",bgColor:"#fff",marginBottom:8,paddingTop:8,paddingRight:8,paddingBottom:8,paddingLeft:8}}}},Symbol.toStringTag,{value:"Module"})),j={id:"TabBar",name:"\u5E95\u90E8\u5BFC\u822A",icon:"fluent:table-bottom-row-16-filled",property:{theme:"red",style:{bgType:"color",bgColor:"#fff",color:"#282828",activeColor:"#fc4141"},items:[{text:"\u9996\u9875",url:"/pages/index/index",iconUrl:"http://mall.yudao.iocoder.cn/static/images/1-001.png",activeIconUrl:"http://mall.yudao.iocoder.cn/static/images/1-002.png"},{text:"\u5206\u7C7B",url:"/pages/index/category?id=3",iconUrl:"http://mall.yudao.iocoder.cn/static/images/2-001.png",activeIconUrl:"http://mall.yudao.iocoder.cn/static/images/2-002.png"},{text:"\u8D2D\u7269\u8F66",url:"/pages/index/cart",iconUrl:"http://mall.yudao.iocoder.cn/static/images/3-001.png",activeIconUrl:"http://mall.yudao.iocoder.cn/static/images/3-002.png"},{text:"\u6211\u7684",url:"/pages/index/user",iconUrl:"http://mall.yudao.iocoder.cn/static/images/4-001.png",activeIconUrl:"http://mall.yudao.iocoder.cn/static/images/4-002.png"}]}},ae=[{id:"red",name:"\u4E2D\u56FD\u7EA2",icon:"icon-park-twotone:theme",color:"#d10019"},{id:"orange",name:"\u6854\u6A59",icon:"icon-park-twotone:theme",color:"#f37b1d"},{id:"gold",name:"\u660E\u9EC4",icon:"icon-park-twotone:theme",color:"#fbbd08"},{id:"green",name:"\u6A44\u6984\u7EFF",icon:"icon-park-twotone:theme",color:"#8dc63f"},{id:"cyan",name:"\u5929\u9752",icon:"icon-park-twotone:theme",color:"#1cbbb4"},{id:"blue",name:"\u6D77\u84DD",icon:"icon-park-twotone:theme",color:"#0081ff"},{id:"purple",name:"\u59F9\u7D2B",icon:"icon-park-twotone:theme",color:"#6739b6"},{id:"brightRed",name:"\u5AE3\u7EA2",icon:"icon-park-twotone:theme",color:"#e54d42"},{id:"forestGreen",name:"\u68EE\u7EFF",icon:"icon-park-twotone:theme",color:"#39b54a"},{id:"mauve",name:"\u6728\u69FF",icon:"icon-park-twotone:theme",color:"#9c26b0"},{id:"pink",name:"\u6843\u7C89",icon:"icon-park-twotone:theme",color:"#e03997"},{id:"brown",name:"\u68D5\u8910",icon:"icon-park-twotone:theme",color:"#a5673f"},{id:"grey",name:"\u7384\u7070",icon:"icon-park-twotone:theme",color:"#8799a3"},{id:"gray",name:"\u8349\u7070",icon:"icon-park-twotone:theme",color:"#aaaaaa"},{id:"black",name:"\u58A8\u9ED1",icon:"icon-park-twotone:theme",color:"#333333"}],ke=Object.freeze(Object.defineProperty({__proto__:null,THEME_LIST:ae,component:j},Symbol.toStringTag,{value:"Module"})),Ve=Object.freeze(Object.defineProperty({__proto__:null,component:{id:"TitleBar",name:"\u6807\u9898\u680F",icon:"material-symbols:line-start",property:{title:"\u4E3B\u6807\u9898",description:"\u526F\u6807\u9898",titleSize:16,descriptionSize:12,titleWeight:400,textAlign:"left",descriptionWeight:200,titleColor:"rgba(50, 50, 51, 10)",descriptionColor:"rgba(150, 151, 153, 10)",marginLeft:0,height:40,more:{show:!1,type:"icon",text:"\u67E5\u770B\u66F4\u591A",url:""},style:{bgType:"color",bgColor:"#fff"}}}},Symbol.toStringTag,{value:"Module"})),Se=Object.freeze(Object.defineProperty({__proto__:null,component:{id:"UserCard",name:"\u7528\u6237\u5361\u7247",icon:"mdi:user-card-details",property:{style:{bgType:"color",bgColor:"",marginBottom:8}}}},Symbol.toStringTag,{value:"Module"})),De=Object.freeze(Object.defineProperty({__proto__:null,component:{id:"UserCoupon",name:"\u7528\u6237\u5361\u5238",icon:"ep:ticket",property:{style:{bgType:"color",bgColor:"",marginLeft:8,marginRight:8,marginBottom:8}}}},Symbol.toStringTag,{value:"Module"})),Ae=Object.freeze(Object.defineProperty({__proto__:null,component:{id:"UserOrder",name:"\u7528\u6237\u8BA2\u5355",icon:"ep:list",property:{style:{bgType:"color",bgColor:"",marginLeft:8,marginRight:8,marginBottom:8}}}},Symbol.toStringTag,{value:"Module"})),je=Object.freeze(Object.defineProperty({__proto__:null,component:{id:"UserWallet",name:"\u7528\u6237\u8D44\u4EA7",icon:"ep:wallet-filled",property:{style:{bgType:"color",bgColor:"",marginLeft:8,marginRight:8,marginBottom:8}}}},Symbol.toStringTag,{value:"Module"})),Me=Object.freeze(Object.defineProperty({__proto__:null,component:{id:"VideoPlayer",name:"\u89C6\u9891\u64AD\u653E",icon:"ep:video-play",property:{videoUrl:"",posterUrl:"",autoplay:!1,style:{bgType:"color",bgColor:"#fff",marginBottom:8,height:300}}}},Symbol.toStringTag,{value:"Module"})),Ue=Object.assign({"./Carousel/index.vue":()=>e(()=>import("./index-Cp5UFGM6.js"),__vite__mapDeps([0,1,2,3,4,5,6])),"./Carousel/property.vue":()=>e(()=>import("./property-DdjSk3Cv.js"),__vite__mapDeps([7,8,1,2,3,4,9,10,11,12,13,14,15,16,17,18,19,20])),"./CouponCard/index.vue":()=>e(()=>import("./index-D71J-VyG.js"),__vite__mapDeps([21,1,2,3,4,22,23,24])),"./CouponCard/property.vue":()=>e(()=>import("./property-Bwp_MJ5w.js"),__vite__mapDeps([25,8,1,2,3,4,9,10,11,22,23,26,16,17,27,28,29,30,24])),"./Divider/index.vue":()=>e(()=>import("./index-41BmyfGT.js"),__vite__mapDeps([31,3])),"./Divider/property.vue":()=>e(()=>import("./property-I7gMy1m4.js"),__vite__mapDeps([32,1,2,3,4,9,10])),"./FloatingActionButton/index.vue":()=>e(()=>import("./index-Cewb19q3.js"),__vite__mapDeps([33,1,2,3,4,34,6])),"./FloatingActionButton/property.vue":()=>e(()=>import("./property-B0msHP90.js"),__vite__mapDeps([35,1,2,3,4,12,13,14,15,16,17,18,19,20,36,37])),"./HotZone/index.vue":()=>e(()=>import("./index-zwGa7tLd.js"),__vite__mapDeps([38,1,2,3,4,39,6])),"./HotZone/property.vue":()=>e(()=>import("./property-f0gKieva.js"),__vite__mapDeps([40,8,1,2,3,4,9,10,11,15,16,17,18,19,20,41,6])),"./ImageBar/index.vue":()=>e(()=>import("./index-LXngVk7U.js"),__vite__mapDeps([42,1,2,3,4,43,6])),"./ImageBar/property.vue":()=>e(()=>import("./property-dl9yV3s0.js"),__vite__mapDeps([44,8,1,2,3,4,9,10,11,14,15,16,17,18,19,20])),"./MagicCube/index.vue":()=>e(()=>import("./index-CCaySbc8.js"),__vite__mapDeps([45,1,2,3,4,6])),"./MagicCube/property.vue":()=>e(()=>import("./property-D0I_EhoN.js"),__vite__mapDeps([46,8,1,2,3,4,9,10,11,14,15,16,17,18,19,20,47,48])),"./MenuGrid/index.vue":()=>e(()=>import("./index-BhskJiLW.js"),__vite__mapDeps([49,1,2,3,4,6])),"./MenuGrid/property.vue":()=>e(()=>import("./property-BdWpZJg8.js"),__vite__mapDeps([50,8,1,2,3,4,9,10,11,12,13,14,15,16,17,18,19,20,36,37,51,52,53])),"./MenuList/index.vue":()=>e(()=>import("./index-DzCUONq1.js"),__vite__mapDeps([54,1,2,3,4,55,6])),"./MenuList/property.vue":()=>e(()=>import("./property-CvTSeWy4.js"),__vite__mapDeps([56,8,1,2,3,4,9,10,11,12,13,14,15,16,17,18,19,20,36,37,51,52,53])),"./MenuSwiper/index.vue":()=>e(()=>import("./index-xWo01PeV.js"),__vite__mapDeps([57,1,2,3,4,58,5,6])),"./MenuSwiper/property.vue":()=>e(()=>import("./property-BjyrF6Xg.js"),__vite__mapDeps([59,8,1,2,3,4,9,10,11,12,13,14,15,16,17,18,19,20,36,37,51,52,53])),"./NavigationBar/index.vue":()=>e(()=>import("./index-CRR7_iji.js"),__vite__mapDeps([60,61,62,1,2,3,4,63,64])),"./NavigationBar/property.vue":()=>e(()=>import("./property-qiKuQ-WO.js"),__vite__mapDeps([65,1,2,3,4,9,10,14,15,16,17,18,19,20,47,48,61])),"./NoticeBar/index.vue":()=>e(()=>import("./index--QyeTuV3.js"),__vite__mapDeps([66,1,2,3,4,5,6])),"./NoticeBar/property.vue":()=>e(()=>import("./property-CUeL-MvT.js"),__vite__mapDeps([67,8,1,2,3,4,9,10,11,12,13,14,15,16,17,18,19,20])),"./PageConfig/property.vue":()=>e(()=>import("./property-BSOC5fPq.js"),__vite__mapDeps([68,1,2,3,4,9,10])),"./Popover/index.vue":()=>e(()=>import("./index-DRCM5Q1_.js"),__vite__mapDeps([69,1,2,3,4,6])),"./Popover/property.vue":()=>e(()=>import("./property-Cd46SpH_.js"),__vite__mapDeps([70,1,2,3,4,12,13,14,15,16,17,18,19,20])),"./ProductCard/index.vue":()=>e(()=>import("./index-eUJ9Om2J.js"),__vite__mapDeps([71,1,2,3,4,72,6])),"./ProductCard/property.vue":()=>e(()=>import("./property-COK6DXHy.js"),__vite__mapDeps([73,8,1,2,3,4,9,10,11,74,72,75,16,17,29,27,19,20,6,76])),"./ProductList/index.vue":()=>e(()=>import("./index-Drf_FGWL.js"),__vite__mapDeps([77,1,2,3,4,72,6])),"./ProductList/property.vue":()=>e(()=>import("./property-CZTs5P0Q.js"),__vite__mapDeps([78,8,1,2,3,4,9,10,11,74,72,75,16,17,29,27,19,20,6,76])),"./PromotionArticle/index.vue":()=>e(()=>import("./index-uaj5uyWe.js"),__vite__mapDeps([79,80,1,2,3,4])),"./PromotionArticle/property.vue":()=>e(()=>import("./property-CgVcwDWt.js"),__vite__mapDeps([81,8,1,2,3,4,9,10,11,80])),"./PromotionCombination/index.vue":()=>e(()=>import("./index-CO5X5wVl.js"),__vite__mapDeps([82,1,2,3,4,72,83,6])),"./PromotionCombination/property.vue":()=>e(()=>import("./property-BkhVn5Jx.js"),__vite__mapDeps([84,8,1,2,3,4,9,10,11,83,23,85,86,16,17,29,27,28,19,20,87,24,6,88])),"./PromotionPoint/index.vue":()=>e(()=>import("./index-B0jFjG7r.js"),__vite__mapDeps([89,1,2,3,4,72,90,6])),"./PromotionPoint/property.vue":()=>e(()=>import("./property-DdaXStmX.js"),__vite__mapDeps([91,8,1,2,3,4,9,10,11,92,93,16,17,29,27,28,90,87,24,6,94])),"./PromotionSeckill/index.vue":()=>e(()=>import("./index-BMTKCd1-.js"),__vite__mapDeps([95,1,2,3,4,72,96,6])),"./PromotionSeckill/property.vue":()=>e(()=>import("./property-Bylqn1Xq.js"),__vite__mapDeps([97,8,1,2,3,4,9,10,11,96,23,98,99,16,17,29,27,28,19,20,87,24,6,100])),"./SearchBar/index.vue":()=>e(()=>import("./index-COrAlx0N.js"),__vite__mapDeps([62,1,2,3,4,63])),"./SearchBar/property.vue":()=>e(()=>import("./property-B9eO-Ie9.js"),__vite__mapDeps([101,8,1,2,3,4,9,10,11,12,13])),"./TabBar/index.vue":()=>e(()=>import("./index-BiS0V9Of.js"),__vite__mapDeps([102,1,2,3,4,103,6])),"./TabBar/property.vue":()=>e(()=>import("./property-DwQTs-cs.js"),__vite__mapDeps([104,1,2,3,4,12,13,14,15,16,17,18,19,20,9,10,51,52,53])),"./TitleBar/index.vue":()=>e(()=>import("./index-CS-F32Ef.js"),__vite__mapDeps([105,1,2,3,4,106,6])),"./TitleBar/property.vue":()=>e(()=>import("./property-DL43imec.js"),__vite__mapDeps([107,8,1,2,3,4,9,10,11,14,15,16,17,18,19,20,36,37])),"./UserCard/index.vue":()=>e(()=>import("./index-GDaCSvJp.js"),__vite__mapDeps([108,1,2,3,4,109])),"./UserCard/property.vue":()=>e(()=>import("./property-DUcVjGRh.js"),__vite__mapDeps([110,8,1,2,3,4,9,10,11])),"./UserCoupon/index.vue":()=>e(()=>import("./index-tr82vK-o.js"),__vite__mapDeps([111,1,2,3,4,6])),"./UserCoupon/property.vue":()=>e(()=>import("./property-CMufURXY.js"),__vite__mapDeps([112,8,1,2,3,4,9,10,11])),"./UserOrder/index.vue":()=>e(()=>import("./index-rmrUJK8Y.js"),__vite__mapDeps([113,1,2,3,4,6])),"./UserOrder/property.vue":()=>e(()=>import("./property-DV2Y8ZCJ.js"),__vite__mapDeps([114,8,1,2,3,4,9,10,11])),"./UserWallet/index.vue":()=>e(()=>import("./index-Crfw7hOW.js"),__vite__mapDeps([115,1,2,3,4,6])),"./UserWallet/property.vue":()=>e(()=>import("./property-Ugz0QHZj.js"),__vite__mapDeps([116,8,1,2,3,4,9,10,11])),"./VideoPlayer/index.vue":()=>e(()=>import("./index-CjwefACK.js"),__vite__mapDeps([117,1,2,3,4,118,6])),"./VideoPlayer/property.vue":()=>e(()=>import("./property-CoImS9RO.js"),__vite__mapDeps([119,8,1,2,3,4,9,10,11]))}),X=Object.assign({"./Carousel/config.ts":pe,"./CouponCard/config.ts":ce,"./Divider/config.ts":de,"./FloatingActionButton/config.ts":se,"./HotZone/config.ts":me,"./ImageBar/config.ts":ue,"./MagicCube/config.ts":_e,"./MenuGrid/config.ts":be,"./MenuList/config.ts":ye,"./MenuSwiper/config.ts":he,"./NavigationBar/config.ts":Te,"./NoticeBar/config.ts":Pe,"./PageConfig/config.ts":Ce,"./Popover/config.ts":Ee,"./ProductCard/config.ts":Oe,"./ProductList/config.ts":we,"./PromotionArticle/config.ts":xe,"./PromotionCombination/config.ts":Re,"./PromotionPoint/config.ts":Be,"./PromotionSeckill/config.ts":Le,"./SearchBar/config.ts":Ie,"./TabBar/config.ts":ke,"./TitleBar/config.ts":Ve,"./UserCard/config.ts":Se,"./UserCoupon/config.ts":De,"./UserOrder/config.ts":Ae,"./UserWallet/config.ts":je,"./VideoPlayer/config.ts":Me}),z={},F={},ee=(l,y,h)=>{const d=y.replace("config.ts",`${h}.vue`),v=Ue[d];v&&(z[l]=rt(v))},Object.keys(X).forEach(l=>{const y=X[l].component,h=y==null?void 0:y.id;h&&(F[h]=y,ee(h,l,"index"),ee(`${h}Property`,l,"property"))});let ze,Fe,Ne,$e,We,Ge,He,Ye,Ze,qe,Je,Ke,Qe,Xe,eo,oo,to;ze={class:"component-wrap"},Fe={key:0,class:"component-name"},Ne={key:1,class:"component-toolbar"},$e=Y(Z({components:{...z},name:"ComponentContainer",__name:"ComponentContainer",props:{component:Fo().isRequired,active:w.bool.def(!1),canMoveUp:w.bool.def(!1),canMoveDown:w.bool.def(!1),showToolbar:w.bool.def(!0)},emits:["move","copy","delete"],setup(l,{emit:y}){const h=l,d=it(()=>{let o=h.component.property.style;return o?{marginTop:`${o.marginTop||0}px`,marginBottom:`${o.marginBottom||0}px`,marginLeft:`${o.marginLeft||0}px`,marginRight:`${o.marginRight||0}px`,paddingTop:`${o.paddingTop||0}px`,paddingRight:`${o.paddingRight||0}px`,paddingBottom:`${o.paddingBottom||0}px`,paddingLeft:`${o.paddingLeft||0}px`,borderTopLeftRadius:`${o.borderTopLeftRadius||0}px`,borderTopRightRadius:`${o.borderTopRightRadius||0}px`,borderBottomRightRadius:`${o.borderBottomRightRadius||0}px`,borderBottomLeftRadius:`${o.borderBottomLeftRadius||0}px`,overflow:"hidden",background:o.bgType==="color"?o.bgColor:`url(${o.bgImg})`}:{}}),v=y,b=o=>{v("move",o)};return(o,u)=>{const p=te,_=fo,x=bo,S=le;return s(),O("div",{class:To(["component",{active:l.active}])},[f("div",{style:nt({...t(d)})},[(s(),C(re(l.component.id),{property:l.component.property},null,8,["property"]))],4),f("div",ze,[l.component.name?(s(),O("div",Fe,V(l.component.name),1)):E("",!0),l.showToolbar&&l.component.name&&l.active?(s(),O("div",Ne,[i(S,{type:"primary"},{default:n(()=>[i(x,{content:"\u4E0A\u79FB",placement:"right"},{default:n(()=>[i(_,{disabled:!l.canMoveUp,onClick:u[0]||(u[0]=q(m=>b(-1),["stop"]))},{default:n(()=>[i(p,{icon:"ep:arrow-up"})]),_:1},8,["disabled"])]),_:1}),i(x,{content:"\u4E0B\u79FB",placement:"right"},{default:n(()=>[i(_,{disabled:!l.canMoveDown,onClick:u[1]||(u[1]=q(m=>b(1),["stop"]))},{default:n(()=>[i(p,{icon:"ep:arrow-down"})]),_:1},8,["disabled"])]),_:1}),i(x,{content:"\u590D\u5236",placement:"right"},{default:n(()=>[i(_,{onClick:u[2]||(u[2]=q(m=>{v("copy")},["stop"]))},{default:n(()=>[i(p,{icon:"ep:copy-document"})]),_:1})]),_:1}),i(x,{content:"\u5220\u9664",placement:"right"},{default:n(()=>[i(_,{onClick:u[3]||(u[3]=q(m=>{v("delete")},["stop"]))},{default:n(()=>[i(p,{icon:"ep:delete"})]),_:1})]),_:1})]),_:1})])):E("",!0)])],2)}}}),[["__scopeId","data-v-501eb6e2"]]),We={class:"component"},Ge={class:"mt-4px text-12px"},He=Z({name:"ComponentLibrary",__name:"ComponentLibrary",props:{list:{}},setup(l){const y=l,h=Po([]),d=Po([]);D(()=>y.list,()=>{d.length=0,h.length=0,y.list.forEach(b=>{b.extended&&d.push(b.name);const o=b.components.map(u=>F[u]).filter(u=>u);o.length>0&&h.push({name:b.name,components:o})})},{immediate:!0});const v=b=>{const o=B(b);return o.uid=new Date().getTime(),o};return(b,o)=>{const u=te,p=Zo,_=Yo,x=vo,S=yo;return s(),C(S,{class:"editor-left",width:"261px"},{default:n(()=>[i(x,null,{default:n(()=>[i(_,{modelValue:t(d),"onUpdate:modelValue":o[0]||(o[0]=m=>ie(d)?d.value=m:null)},{default:n(()=>[(s(!0),O(A,null,ne(t(h),m=>(s(),C(p,{key:m.name,name:m.name,title:m.name},{default:n(()=>[i(t(Co),{class:"component-container","ghost-class":"draggable-ghost","item-key":"index",list:m.components,sort:!1,group:{name:"component",pull:"clone",put:!1},clone:v,animation:200,"force-fallback":!0},{item:n(({element:N})=>[f("div",null,[o[1]||(o[1]=f("div",{class:"drag-placement"},"\u7EC4\u4EF6\u653E\u7F6E\u533A\u57DF",-1)),f("div",We,[i(u,{icon:N.icon,size:32},null,8,["icon"]),f("span",Ge,V(N.name),1)])])]),_:2},1032,["list"])]),_:2},1032,["name","title"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1})}}}),Ye=Y(He,[["__scopeId","data-v-e6040e05"]]),Ze={class:"header-center flex flex-1 items-center justify-center"},qe={class:"editor-design-top"},Je=["onClick"],Ke={key:0,class:To(["editor-design-bottom","component","cursor-pointer!"])},Qe={class:"fixed-component-action-group"},Xe={class:"flex items-center gap-8px"},eo={class:"flex justify-around"},oo={class:"flex flex-col"},to=Z({components:{...z},name:"DiyPageDetail",__name:"index",props:{modelValue:$o([String,Object]).isRequired,title:w.string.def(""),libs:No(),showNavigationBar:w.bool.def(!0),showTabBar:w.bool.def(!1),showPageConfig:w.bool.def(!0),previewUrl:w.string.def("")},emits:["reset","preview","save","update:modelValue"],setup(l,{emit:y}){const h=L(),d=L(B(U)),v=L(B(M)),b=L(B(j)),o=L(),u=L(-1),p=L([]),_=l;D(()=>_.modelValue,()=>{const r=_o(_.modelValue)&&!Wo(_.modelValue)?JSON.parse(_.modelValue):_.modelValue;d.value.property=typeof r!="string"&&(r==null?void 0:r.page)||U.property,v.value.property=typeof r!="string"&&(r==null?void 0:r.navigationBar)||M.property,b.value.property=typeof r!="string"&&(r==null?void 0:r.tabBar)||j.property,p.value=(typeof r!="string"&&(r==null?void 0:r.components)||[]).map(a=>({...F[a.id],property:a.property}))},{immediate:!0}),D(o,r=>{r&&u.value!==-1&&(p.value[u.value]=o.value)},{deep:!0});const x=()=>{$("save")};D(()=>[d.value.property,v.value.property,b.value.property,p.value],()=>{(()=>{const r={page:d.value.property,navigationBar:v.value.property,tabBar:b.value.property,components:p.value.map(T=>({id:T.id,property:T.property}))};_.showTabBar||delete r.tabBar;const a=_o(_.modelValue)?JSON.stringify(r):r;$("update:modelValue",a)})()},{deep:!0});const S=r=>{var a;_.showPageConfig&&et((a=r==null?void 0:r.target)==null?void 0:a.classList,"page-prop-area")&&m(t(d))},m=(r,a=-1)=>{o.value=r,u.value=a},N=()=>{m(t(v))},wo=()=>{m(t(b))},xo=r=>{if(r.added){const{element:a,newIndex:T}=r.added;m(a,T)}else if(r.moved){const{newIndex:a}=r.moved;u.value=a}},Ro=(r,a)=>{const T=r+a;T<0||T>=p.value.length||((k,I)=>{[p.value[k],p.value[I]]=[p.value[I],p.value[k]],u.value=I})(r,T)},ro=r=>{if(p.value.splice(r,1),r<p.value.length){let a=r;m(p.value[a],a)}else if(p.value.length>0){let a=r-1;m(p.value[a],a)}else m(t(d))},$=y,io=at("reload"),Bo=()=>{io&&io(),$("reset")},W=L(!1),Lo=()=>{W.value=!0,$("preview")},no=()=>{_.showPageConfig?o.value=t(d):_.showNavigationBar?o.value=t(v):_.showTabBar&&(o.value=t(b))};return D(()=>[_.showPageConfig,_.showNavigationBar,_.showTabBar],()=>no()),lt(()=>no()),(r,a)=>{const T=te,k=fo,I=bo,Io=go,ko=qo,oe=$e,ao=vo,lo=Ko,Vo=Qo,So=yo,po=Jo,Do=Ho,Ao=Xo,jo=zo,Mo=Uo;return s(),O(A,null,[i(po,{class:"editor"},{default:n(()=>[i(ko,{class:"editor-header"},{default:n(()=>[ho(r.$slots,"toolBarLeft",{},void 0,!0),f("div",Ze,[f("span",null,V(l.title),1)]),i(Io,{class:"header-right"},{default:n(()=>[i(I,{content:"\u91CD\u7F6E"},{default:n(()=>[i(k,{onClick:Bo},{default:n(()=>[i(T,{size:24,icon:"system-uicons:reset-alt"})]),_:1})]),_:1}),l.previewUrl?(s(),C(I,{key:0,content:"\u9884\u89C8"},{default:n(()=>[i(k,{onClick:Lo},{default:n(()=>[i(T,{size:24,icon:"ep:view"})]),_:1})]),_:1})):E("",!0),i(I,{content:"\u4FDD\u5B58"},{default:n(()=>[i(k,{onClick:x},{default:n(()=>[i(T,{size:24,icon:"ep:check"})]),_:1})]),_:1})]),_:1})]),_:3}),i(po,{class:"editor-container"},{default:n(()=>{var G,co,so,mo,uo;return[l.libs&&l.libs.length>0?(s(),C(Ye,{key:0,ref_key:"componentLibrary",ref:h,list:l.libs},null,8,["list"])):E("",!0),f("div",{class:"editor-center page-prop-area",onClick:S},[f("div",qe,[a[4]||(a[4]=f("img",{alt:"",class:"status-bar",src:"/assets/statusBar-BIIj2dTI.png"},null,-1)),l.showNavigationBar?(s(),C(oe,{key:0,active:((G=t(o))==null?void 0:G.id)===t(v).id,component:t(v),"show-toolbar":!1,class:"cursor-pointer!",onClick:N},null,8,["active","component"])):E("",!0)]),(s(!0),O(A,null,ne(t(p),(c,g)=>{var P;return s(),O("div",{key:g,onClick:R=>m(c,g)},[c.position==="fixed"&&((P=t(o))==null?void 0:P.uid)===c.uid?(s(),C(re(c.id),{key:0,property:c.property},null,8,["property"])):E("",!0)],8,Je)}),128)),i(ao,{"view-style":{backgroundColor:t(d).property.backgroundColor,backgroundImage:`url(${t(d).property.backgroundImage})`},height:"100%","view-class":"phone-container","wrap-class":"editor-design-center page-prop-area"},{default:n(()=>[i(t(Co),{modelValue:t(p),"onUpdate:modelValue":a[0]||(a[0]=c=>ie(p)?p.value=c:null),animation:200,"force-fallback":!0,class:"page-prop-area drag-area",filter:".component-toolbar","ghost-class":"draggable-ghost",group:"component","item-key":"index",onChange:xo},{item:n(({element:c,index:g})=>[c.position&&c.position!=="center"?E("",!0):(s(),C(oe,{key:0,active:t(u)===g,"can-move-down":g<t(p).length-1,"can-move-up":g>0,component:c,onClick:P=>m(c,g),onCopy:P=>(R=>{const H=B(p.value[R]);H.uid=new Date().getTime(),p.value.splice(R+1,0,H)})(g),onDelete:P=>ro(g),onMove:P=>Ro(g,P)},null,8,["active","can-move-down","can-move-up","component","onClick","onCopy","onDelete","onMove"]))]),_:1},8,["modelValue"])]),_:1},8,["view-style"]),l.showTabBar?(s(),O("div",Ke,[i(oe,{active:((co=t(o))==null?void 0:co.id)===t(b).id,component:t(b),"show-toolbar":!1,onClick:wo},null,8,["active","component"])])):E("",!0),f("div",Qe,[l.showPageConfig?(s(),C(lo,{key:0,effect:((so=t(o))==null?void 0:so.uid)===t(d).uid?"dark":"plain",type:((mo=t(o))==null?void 0:mo.uid)===t(d).uid?"primary":"info",size:"large",onClick:a[1]||(a[1]=c=>m(t(d)))},{default:n(()=>[i(T,{icon:t(d).icon,size:12},null,8,["icon"]),f("span",null,V(t(d).name),1)]),_:1},8,["effect","type"])):E("",!0),(s(!0),O(A,null,ne(t(p),(c,g)=>{var P,R;return s(),O(A,{key:g},[c.position==="fixed"?(s(),C(lo,{key:0,effect:((P=t(o))==null?void 0:P.uid)===c.uid?"dark":"plain",type:((R=t(o))==null?void 0:R.uid)===c.uid?"primary":"info",closable:"",size:"large",onClick:H=>m(c),onClose:H=>ro(g)},{default:n(()=>[i(T,{icon:c.icon,size:12},null,8,["icon"]),f("span",null,V(c.name),1)]),_:2},1032,["effect","type","onClick","onClose"])):E("",!0)],64)}),128))])]),(uo=t(o))!=null&&uo.property?(s(),C(So,{key:1,class:"editor-right",width:"350px"},{default:n(()=>[i(Vo,{"body-class":"h-[calc(100%-var(--el-card-padding)-var(--el-card-padding))]",class:"h-full",shadow:"never"},{header:n(()=>{var c,g;return[f("div",Xe,[i(T,{icon:(c=t(o))==null?void 0:c.icon,color:"gray"},null,8,["icon"]),f("span",null,V((g=t(o))==null?void 0:g.name),1)])]}),default:n(()=>[i(ao,{class:"m-[calc(0px-var(--el-card-padding))]","view-class":"p-[var(--el-card-padding)] p-b-[calc(var(--el-card-padding)+var(--el-card-padding))] property"},{default:n(()=>{var c,g,P;return[(s(),C(re(((c=t(o))==null?void 0:c.id)+"Property"),{key:((g=t(o))==null?void 0:g.uid)||((P=t(o))==null?void 0:P.id),modelValue:t(o).property,"onUpdate:modelValue":a[2]||(a[2]=R=>t(o).property=R)},null,8,["modelValue"]))]}),_:1})]),_:1})]),_:1})):E("",!0)]}),_:1})]),_:3}),i(Mo,{modelValue:t(W),"onUpdate:modelValue":a[3]||(a[3]=G=>ie(W)?W.value=G:null),title:"\u9884\u89C8",width:"700"},{default:n(()=>[f("div",eo,[i(Do,{src:l.previewUrl,class:"w-375px border-4px border-rounded-8px border-solid p-2px h-667px!"},null,8,["src"]),f("div",oo,[i(Ao,null,{default:n(()=>a[5]||(a[5]=[pt("\u624B\u673A\u626B\u7801\u9884\u89C8")])),_:1}),i(jo,{text:l.previewUrl,logo:"/logo.gif"},null,8,["text"])])])]),_:1},8,["modelValue"])],64)}}}),Oo=Y(to,[["__scopeId","data-v-ba2c5490"]]),Eo=[{name:"\u57FA\u7840\u7EC4\u4EF6",extended:!0,components:["SearchBar","NoticeBar","MenuSwiper","MenuGrid","MenuList","Popover","FloatingActionButton"]},{name:"\u56FE\u6587\u7EC4\u4EF6",extended:!0,components:["ImageBar","Carousel","TitleBar","VideoPlayer","Divider","MagicCube","HotZone"]},{name:"\u5546\u54C1\u7EC4\u4EF6",extended:!0,components:["ProductCard","ProductList"]},{name:"\u7528\u6237\u7EC4\u4EF6",extended:!0,components:["UserCard","UserOrder","UserWallet","UserCoupon"]},{name:"\u8425\u9500\u7EC4\u4EF6",extended:!0,components:["PromotionCombination","PromotionSeckill","PromotionPoint","CouponCard","PromotionArticle"]}]});export{J as E,Eo as P,ae as T,Oo as _,ct as __tla,K as a,Q as b,j as c};
function __vite__mapDeps(indexes) {
  if (!__vite__mapDeps.viteFileDeps) {
    __vite__mapDeps.viteFileDeps = ["assets/index-Cp5UFGM6.js","assets/index-Byekp3Iv.js","assets/form-designer-C0ARe9Dh.js","assets/form-create-B86qX0W_.js","assets/index-nemfNeNP.css","assets/el-carousel-item-CPSUDgNi.css","assets/el-image-BrUZgf8Q.css","assets/property-DdjSk3Cv.js","assets/ComponentContainerProperty-CJ9-BQpj.js","assets/index-CY05yRuR.js","assets/index-BA1wQZfK.css","assets/ComponentContainerProperty-BW6Fx8m3.css","assets/index.vue_vue_type_script_setup_true_lang-DzmS1V3k.js","assets/vuedraggable.umd-CjS8ClRh.js","assets/index.vue_vue_type_script_setup_true_lang-CyNHKsKJ.js","assets/AppLinkSelectDialog.vue_vue_type_script_setup_true_lang-CizXWwkY.js","assets/Dialog.vue_vue_type_style_index_0_lang-DhV_HFpU.js","assets/Dialog-BdewL7YE.css","assets/ProductCategorySelect.vue_vue_type_script_setup_true_lang-vPIdl3r4.js","assets/tree-COGD3qag.js","assets/category-Tlk-MRkP.js","assets/index-D71J-VyG.js","assets/couponTemplate-8Jpivuy-.js","assets/constants-C3gLHYOK.js","assets/formatTime-HVkyL6Kg.js","assets/property-Bwp_MJ5w.js","assets/CouponSelect.vue_vue_type_script_setup_true_lang-DztHtvMV.js","assets/index.vue_vue_type_script_setup_true_lang-BeMNDf6p.js","assets/DictTag.vue_vue_type_script_lang-DdZ_pRVv.js","assets/ContentWrap.vue_vue_type_script_setup_true_lang-D3mNNG_R.js","assets/formatter-BO89yjPi.js","assets/index-41BmyfGT.js","assets/property-I7gMy1m4.js","assets/index-Cewb19q3.js","assets/index-BFzTTsdu.css","assets/property-B0msHP90.js","assets/index-CrQlhca2.js","assets/index-BlyoT-Vr.css","assets/index-zwGa7tLd.js","assets/index-RVzapDZk.css","assets/property-f0gKieva.js","assets/property-BlsSBIhF.css","assets/index-LXngVk7U.js","assets/index-BJE8u9nS.css","assets/property-dl9yV3s0.js","assets/index-CCaySbc8.js","assets/property-D0I_EhoN.js","assets/index-Cnuh0_Qx.js","assets/index-PVMfHW6Z.css","assets/index-BhskJiLW.js","assets/property-BdWpZJg8.js","assets/Qrcode--mo029lO.js","assets/Qrcode-DNYY6tXC.css","assets/IFrame.vue_vue_type_script_setup_true_lang-nGuaSWHR.js","assets/index-DzCUONq1.js","assets/index-D4PCldq8.css","assets/property-CvTSeWy4.js","assets/index-xWo01PeV.js","assets/index-KtOd4ESO.css","assets/property-BjyrF6Xg.js","assets/index-CRR7_iji.js","assets/app-nav-bar-mp-QvSN8lzY.js","assets/index-COrAlx0N.js","assets/index-DKBkAj4V.css","assets/index-H4tanri9.css","assets/property-qiKuQ-WO.js","assets/index--QyeTuV3.js","assets/property-CUeL-MvT.js","assets/property-BSOC5fPq.js","assets/index-DRCM5Q1_.js","assets/property-Cd46SpH_.js","assets/index-eUJ9Om2J.js","assets/spu-BqmQYIjx.js","assets/property-COK6DXHy.js","assets/SpuShowcase-3b1kSYUH.js","assets/SpuTableSelect.vue_vue_type_script_setup_true_lang-ClJWdneH.js","assets/SpuShowcase-CkN2cOGx.css","assets/index-Drf_FGWL.js","assets/property-CZTs5P0Q.js","assets/index-uaj5uyWe.js","assets/index-CMle53IT.js","assets/property-CgVcwDWt.js","assets/index-CO5X5wVl.js","assets/combinationActivity-D9l1sZja.js","assets/property-BkhVn5Jx.js","assets/CombinationShowcase-B2WlmjPV.js","assets/CombinationTableSelect.vue_vue_type_script_setup_true_lang-DbaSsMpg.js","assets/formatter-CF7Ifi5S.js","assets/CombinationShowcase-DCVcBrCi.css","assets/index-B0jFjG7r.js","assets/index-BEUpdzZp.js","assets/property-DdaXStmX.js","assets/PointShowcase-Bd_62QGW.js","assets/PointTableSelect.vue_vue_type_script_setup_true_lang-Danov2Gr.js","assets/PointShowcase-BTM4g9sO.css","assets/index-BMTKCd1-.js","assets/seckillActivity-BUPSa-wC.js","assets/property-Bylqn1Xq.js","assets/SeckillShowcase-eYMmvTBM.js","assets/SeckillTableSelect.vue_vue_type_script_setup_true_lang-Bu7mD4lE.js","assets/SeckillShowcase-C763-9g3.css","assets/property-B9eO-Ie9.js","assets/index-BiS0V9Of.js","assets/index-DLFcHnC2.css","assets/property-DwQTs-cs.js","assets/index-CS-F32Ef.js","assets/index-CrAO7v3N.css","assets/property-DL43imec.js","assets/index-GDaCSvJp.js","assets/el-avatar-Ca9gIz_i.css","assets/property-DUcVjGRh.js","assets/index-tr82vK-o.js","assets/property-CMufURXY.js","assets/index-rmrUJK8Y.js","assets/property-DV2Y8ZCJ.js","assets/index-Crfw7hOW.js","assets/property-Ugz0QHZj.js","assets/index-CjwefACK.js","assets/index-889ZHh8r.css","assets/property-CoImS9RO.js"]
  }
  return indexes.map((i) => __vite__mapDeps.viteFileDeps[i])
}
