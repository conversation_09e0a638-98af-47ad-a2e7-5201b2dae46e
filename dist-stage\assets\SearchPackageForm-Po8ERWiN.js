import{a3 as M,D as R}from"./index-Byekp3Iv.js";import{M as A}from"./index-hssiq8O8.js";import{V as B,aj as F,k as G,x as H,Q as O,f as Q,h as X,ak as Y,_ as Z,Z as $,ay as q}from"./form-designer-C0ARe9Dh.js";import{r as s,e as I,l as h,m as c,H as e,A as J,z as p,G as K,$ as L,y as w,E as _}from"./form-create-B86qX0W_.js";const W={class:"search-package-form"},ee={__name:"SearchPackageForm",setup(ae,{expose:N}){const a=s({pageNo:1,pageSize:10,name:void 0,spec:void 0,subType:void 0,type:4}),v=s([]),i=s([]),n=s(0),d=s(!1),g=s([]),r=async()=>{if(!d.value){d.value=!0;try{const l=await A.getMaterialPage(a.value);l&&l.list?(i.value=l.list,n.value=l.total||0):(i.value=[],n.value=0)}catch{i.value=[],n.value=0}finally{d.value=!1}}};I(async()=>{v.value=M(R.PRODUCT_SUB_TYPE),v.value=v.value.filter(l=>!isNaN(Number(l.value))),await r()});const V=()=>{a.value.pageNo=1,r()},z=()=>{a.value.name=void 0,a.value.spec=void 0,a.value.subType=void 0,a.value.pageNo=1,r()},S=l=>{a.value.pageNo=l,r()},k=l=>{a.value.pageSize=l,a.value.pageNo=1,r()},P=l=>{g.value=l},x=(l,o)=>!0;return N({getSelectedPackages:()=>g.value}),(l,o)=>{const f=B,b=G,m=F,U=O,C=H,y=Q,T=X,t=Z,j=$,D=q,E=Y;return c(),h("div",W,[e(f),e(T,{model:a.value,ref:"formRef",inline:""},{default:p(()=>[e(m,{label:"\u5305\u88C5\u540D\u79F0"},{default:p(()=>[e(b,{modelValue:a.value.name,"onUpdate:modelValue":o[0]||(o[0]=u=>a.value.name=u),placeholder:"\u8BF7\u8F93\u5165\u5305\u88C5\u540D\u79F0",clearable:""},null,8,["modelValue"])]),_:1}),e(m,{label:"\u5305\u88C5\u89C4\u683C"},{default:p(()=>[e(b,{modelValue:a.value.spec,"onUpdate:modelValue":o[1]||(o[1]=u=>a.value.spec=u),placeholder:"\u8BF7\u8F93\u5165\u5305\u88C5\u89C4\u683C",clearable:""},null,8,["modelValue"])]),_:1}),e(m,{label:"\u5B50\u5206\u7C7B"},{default:p(()=>[e(C,{modelValue:a.value.subType,"onUpdate:modelValue":o[2]||(o[2]=u=>a.value.subType=u),placeholder:"\u8BF7\u9009\u62E9\u5B50\u5206\u7C7B",style:{width:"150px"}},{default:p(()=>[(c(!0),h(K,null,L(v.value,u=>(c(),w(U,{key:u.value,label:u.label,value:u.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(y,{onClick:V,type:"primary"},{default:p(()=>o[5]||(o[5]=[_("\u641C\u7D22")])),_:1}),e(y,{onClick:z,type:"danger"},{default:p(()=>o[6]||(o[6]=[_("\u91CD\u7F6E")])),_:1})]),_:1},8,["model"]),e(f),J((c(),w(j,{data:i.value,"row-key":"id",onSelectionChange:P,border:"","max-height":"330"},{default:p(()=>[e(t,{type:"selection",selectable:x}),e(t,{label:"\u5305\u88C5\u540D\u79F0",prop:"name",width:"200","show-overflow-tooltip":""}),e(t,{label:"\u5168\u79F0",prop:"fullName","show-overflow-tooltip":""}),e(t,{label:"\u6240\u5C5E\u516C\u53F8",prop:"companyName","show-overflow-tooltip":""}),e(t,{label:"\u5305\u88C5\u89C4\u683C",prop:"spec","show-overflow-tooltip":""}),e(t,{label:"\u5B50\u5206\u7C7B",prop:"subType"}),e(t,{label:"\u91C7\u8D2D\u4EF7\u683C",prop:"averagePurchasePrice"}),e(t,{label:"\u6700\u65B0\u91C7\u8D2D\u4EF7\u683C",prop:"purchasePrice",width:"120"}),e(t,{label:"\u4EF7\u683C\u5355\u4F4D",prop:"priceUnit"})]),_:1},8,["data"])),[[E,d.value]]),e(D,{"current-page":a.value.pageNo,"onUpdate:currentPage":o[3]||(o[3]=u=>a.value.pageNo=u),"page-size":a.value.pageSize,"onUpdate:pageSize":o[4]||(o[4]=u=>a.value.pageSize=u),"page-sizes":[10,20,50,100],total:n.value,layout:"total, sizes, prev, pager, next, jumper",onCurrentChange:S,onSizeChange:k,style:{"margin-top":"20px","justify-content":"flex-end"}},null,8,["current-page","page-size","total"])])}}};export{ee as default};
