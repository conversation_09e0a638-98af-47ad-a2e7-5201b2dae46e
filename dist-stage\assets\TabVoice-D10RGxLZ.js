import{d as M,aE as A,_ as B,c as F}from"./index-Byekp3Iv.js";import{W as H}from"./main-CgM-XNl9.js";import P from"./main-C_b7Mv-e.js";import{u as S,U as T}from"./useUpload-ClgG934H.js";import{i as W,f as X,j as Z,v as q,z as D}from"./form-designer-C0ARe9Dh.js";import{k as G,c as J,r as _,P as K,l as y,m as c,y as L,u as s,v as V,H as e,F as N,z as t,E as h,h as O}from"./form-create-B86qX0W_.js";import"./index.vue_vue_type_script_setup_true_lang-BeMNDf6p.js";import"./main-Gr8bO50b.js";import"./main.vue_vue_type_script_setup_true_lang-B8TlAAct.js";import"./index-CnYMp6Ej.js";import"./index-DZDuYCM_.js";import"./formatTime-HVkyL6Kg.js";const Q={key:0,class:"select-item2"},R={class:"item-name"},Y=F(G({__name:"TabVoice",props:{modelValue:{}},emits:["update:modelValue"],setup(I,{emit:k}){const w=M(),b={Authorization:"Bearer "+A()},g=I,j=k,l=J({get:()=>g.modelValue,set:a=>j("update:modelValue",a)}),u=_(!1),m=_([]),i=K({accountId:l.value.accountId,type:"voice",title:"",introduction:""}),x=a=>S(T.Voice,10)(a),z=a=>{if(a.code!==0)return w.error("\u4E0A\u4F20\u51FA\u9519\uFF1A"+a.msg),!1;m.value=[],i.title="",i.introduction="",p(a.data)},U=()=>{l.value.mediaId=null,l.value.url=null,l.value.name=null},p=a=>{u.value=!1,l.value.mediaId=a.mediaId,l.value.url=a.url,l.value.name=a.name};return(a,o)=>{const r=W,n=B,d=X,C=q,f=Z,E=D;return c(),y("div",null,[s(l).url?(c(),y("div",Q,[V("p",R,N(s(l).name),1),e(r,{class:"ope-row",justify:"center"},{default:t(()=>[e(s(P),{url:s(l).url},null,8,["url"])]),_:1}),e(r,{class:"ope-row",justify:"center"},{default:t(()=>[e(d,{type:"danger",circle:"",onClick:U},{default:t(()=>[e(n,{icon:"ep:delete"})]),_:1})]),_:1})])):(c(),L(r,{key:1,style:{"text-align":"center"}},{default:t(()=>[e(f,{span:12,class:"col-select"},{default:t(()=>[e(d,{type:"success",onClick:o[0]||(o[0]=v=>u.value=!0)},{default:t(()=>[o[2]||(o[2]=h(" \u7D20\u6750\u5E93\u9009\u62E9")),e(n,{icon:"ep:circle-check"})]),_:1}),e(C,{title:"\u9009\u62E9\u8BED\u97F3",modelValue:s(u),"onUpdate:modelValue":o[1]||(o[1]=v=>O(u)?u.value=v:null),width:"90%","append-to-body":"","destroy-on-close":""},{default:t(()=>[e(s(H),{type:"voice","account-id":s(l).accountId,onSelectMaterial:p},null,8,["account-id"])]),_:1},8,["modelValue"])]),_:1}),e(f,{span:12,class:"col-add"},{default:t(()=>[e(E,{action:"https://optest.hbfarmx.com/admin-api/mp/material/upload-temporary",headers:b,multiple:"",limit:1,"file-list":s(m),data:s(i),"before-upload":x,"on-success":z},{tip:t(()=>o[4]||(o[4]=[V("div",{class:"el-upload__tip"}," \u683C\u5F0F\u652F\u6301 mp3/wma/wav/amr\uFF0C\u6587\u4EF6\u5927\u5C0F\u4E0D\u8D85\u8FC7 2M\uFF0C\u64AD\u653E\u957F\u5EA6\u4E0D\u8D85\u8FC7 60s ",-1)])),default:t(()=>[e(d,{type:"primary"},{default:t(()=>o[3]||(o[3]=[h("\u70B9\u51FB\u4E0A\u4F20")])),_:1})]),_:1},8,["file-list","data"])]),_:1})]),_:1}))])}}}),[["__scopeId","data-v-1728e622"]]);export{Y as default};
