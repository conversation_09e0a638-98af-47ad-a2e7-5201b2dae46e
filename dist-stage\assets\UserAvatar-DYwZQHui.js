import{p as X,n as Wt,T as ui,U as be,d as mi,c as we,b as gi,V as fi}from"./index-Byekp3Iv.js";import{u as vi}from"./profile-g5w1a4sS.js";import{k as Ct,L as bi,r as P,c as Ht,e as wi,K as yi,u as v,l as st,m as U,N as ye,B as F,A as xi,I as Mi,v as et,M as Ci,H as B,h as Di,z as L,y as Dt,C as ht,E as xe,F as Me,w as ki,b as Bi}from"./form-create-B86qX0W_.js";import{_ as Oi}from"./Dialog.vue_vue_type_style_index_0_lang-DhV_HFpU.js";import{_ as Ti}from"./XButton-g2ipiZNp.js";import{z as zi,o as Ei,at as Wi,ah as Ce,f as De}from"./form-designer-C0ARe9Dh.js";import{a as Hi}from"./avatar-BG6NdH5s.js";/*!
 * Cropper.js v1.6.2
 * https://fengyuanchen.github.io/cropperjs
 *
 * Copyright 2015-present Chen Fengyuan
 * Released under the MIT license
 *
 * Date: 2024-04-21T07:43:05.335Z
 */function ke(t,i){var e=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);i&&(n=n.filter(function(a){return Object.getOwnPropertyDescriptor(t,a).enumerable})),e.push.apply(e,n)}return e}function Be(t){for(var i=1;i<arguments.length;i++){var e=arguments[i]!=null?arguments[i]:{};i%2?ke(Object(e),!0).forEach(function(n){Ni(t,n,e[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(e)):ke(Object(e)).forEach(function(n){Object.defineProperty(t,n,Object.getOwnPropertyDescriptor(e,n))})}return t}function Oe(t){var i=function(e,n){if(typeof e!="object"||!e)return e;var a=e[Symbol.toPrimitive];if(a!==void 0){var r=a.call(e,n);if(typeof r!="object")return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(t,"string");return typeof i=="symbol"?i:i+""}function Nt(t){return Nt=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(i){return typeof i}:function(i){return i&&typeof Symbol=="function"&&i.constructor===Symbol&&i!==Symbol.prototype?"symbol":typeof i},Nt(t)}function Te(t,i){for(var e=0;e<i.length;e++){var n=i[e];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,Oe(n.key),n)}}function Ni(t,i,e){return(i=Oe(i))in t?Object.defineProperty(t,i,{value:e,enumerable:!0,configurable:!0,writable:!0}):t[i]=e,t}function ze(t){return function(i){if(Array.isArray(i))return Rt(i)}(t)||function(i){if(typeof Symbol<"u"&&i[Symbol.iterator]!=null||i["@@iterator"]!=null)return Array.from(i)}(t)||function(i,e){if(i){if(typeof i=="string")return Rt(i,e);var n=Object.prototype.toString.call(i).slice(8,-1);if(n==="Object"&&i.constructor&&(n=i.constructor.name),n==="Map"||n==="Set")return Array.from(i);if(n==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return Rt(i,e)}}(t)||function(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}()}function Rt(t,i){(i==null||i>t.length)&&(i=t.length);for(var e=0,n=new Array(i);e<i;e++)n[e]=t[e];return n}var kt=typeof window<"u"&&window.document!==void 0,$=kt?window:{},Lt=!(!kt||!$.document.documentElement)&&"ontouchstart"in $.document.documentElement,At=!!kt&&"PointerEvent"in $,O="cropper",St="all",Ee="crop",We="move",He="zoom",it="e",at="w",ct="s",Q="n",gt="ne",ft="nw",vt="se",bt="sw",Yt="".concat(O,"-crop"),Ne="".concat(O,"-disabled"),A="".concat(O,"-hidden"),Re="".concat(O,"-hide"),Ri="".concat(O,"-invisible"),Bt="".concat(O,"-modal"),Xt="".concat(O,"-move"),wt="".concat(O,"Action"),Ot="".concat(O,"Preview"),_t="crop",Le="move",Ae="none",It="crop",jt="cropend",Pt="cropmove",Ut="cropstart",Se="dblclick",Ye=At?"pointerdown":Lt?"touchstart":"mousedown",Xe=At?"pointermove":Lt?"touchmove":"mousemove",_e=At?"pointerup pointercancel":Lt?"touchend touchcancel":"mouseup",Ie="ready",je="resize",Pe="wheel",$t="zoom",Ue="image/jpeg",Li=/^e|w|s|n|se|sw|ne|nw|all|crop|move|zoom$/,Ai=/^data:/,Si=/^data:image\/jpeg;base64,/,Yi=/^img|canvas$/i,$e={viewMode:0,dragMode:_t,initialAspectRatio:NaN,aspectRatio:NaN,data:null,preview:"",responsive:!0,restore:!0,checkCrossOrigin:!0,checkOrientation:!0,modal:!0,guides:!0,center:!0,highlight:!0,background:!0,autoCrop:!0,autoCropArea:.8,movable:!0,rotatable:!0,scalable:!0,zoomable:!0,zoomOnTouch:!0,zoomOnWheel:!0,wheelZoomRatio:.1,cropBoxMovable:!0,cropBoxResizable:!0,toggleDragModeOnDblclick:!0,minCanvasWidth:0,minCanvasHeight:0,minCropBoxWidth:0,minCropBoxHeight:0,minContainerWidth:200,minContainerHeight:100,ready:null,cropstart:null,cropmove:null,cropend:null,crop:null,zoom:null},Xi=Number.isNaN||$.isNaN;function M(t){return typeof t=="number"&&!Xi(t)}var qe=function(t){return t>0&&t<1/0};function qt(t){return t===void 0}function nt(t){return Nt(t)==="object"&&t!==null}var _i=Object.prototype.hasOwnProperty;function lt(t){if(!nt(t))return!1;try{var i=t.constructor,e=i.prototype;return i&&e&&_i.call(e,"isPrototypeOf")}catch{return!1}}function S(t){return typeof t=="function"}var Ii=Array.prototype.slice;function Ve(t){return Array.from?Array.from(t):Ii.call(t)}function E(t,i){return t&&S(i)&&(Array.isArray(t)||M(t.length)?Ve(t).forEach(function(e,n){i.call(t,e,n,t)}):nt(t)&&Object.keys(t).forEach(function(e){i.call(t,t[e],e,t)})),t}var T=Object.assign||function(t){for(var i=arguments.length,e=new Array(i>1?i-1:0),n=1;n<i;n++)e[n-1]=arguments[n];return nt(t)&&e.length>0&&e.forEach(function(a){nt(a)&&Object.keys(a).forEach(function(r){t[r]=a[r]})}),t},ji=/\.\d*(?:0|9){12}\d*$/;function dt(t){var i=arguments.length>1&&arguments[1]!==void 0?arguments[1]:1e11;return ji.test(t)?Math.round(t*i)/i:t}var Pi=/^width|height|left|top|marginLeft|marginTop$/;function Z(t,i){var e=t.style;E(i,function(n,a){Pi.test(a)&&M(n)&&(n="".concat(n,"px")),e[a]=n})}function W(t,i){if(i)if(M(t.length))E(t,function(n){W(n,i)});else if(t.classList)t.classList.add(i);else{var e=t.className.trim();e?e.indexOf(i)<0&&(t.className="".concat(e," ").concat(i)):t.className=i}}function q(t,i){i&&(M(t.length)?E(t,function(e){q(e,i)}):t.classList?t.classList.remove(i):t.className.indexOf(i)>=0&&(t.className=t.className.replace(i,"")))}function pt(t,i,e){i&&(M(t.length)?E(t,function(n){pt(n,i,e)}):e?W(t,i):q(t,i))}var Ui=/([a-z\d])([A-Z])/g;function Vt(t){return t.replace(Ui,"$1-$2").toLowerCase()}function Ft(t,i){return nt(t[i])?t[i]:t.dataset?t.dataset[i]:t.getAttribute("data-".concat(Vt(i)))}function yt(t,i,e){nt(e)?t[i]=e:t.dataset?t.dataset[i]=e:t.setAttribute("data-".concat(Vt(i)),e)}var Fe=/\s\s*/,Ke=function(){var t=!1;if(kt){var i=!1,e=function(){},n=Object.defineProperty({},"once",{get:function(){return t=!0,i},set:function(a){i=a}});$.addEventListener("test",e,n),$.removeEventListener("test",e,n)}return t}();function j(t,i,e){var n=arguments.length>3&&arguments[3]!==void 0?arguments[3]:{},a=e;i.trim().split(Fe).forEach(function(r){if(!Ke){var h=t.listeners;h&&h[r]&&h[r][e]&&(a=h[r][e],delete h[r][e],Object.keys(h[r]).length===0&&delete h[r],Object.keys(h).length===0&&delete t.listeners)}t.removeEventListener(r,a,n)})}function _(t,i,e){var n=arguments.length>3&&arguments[3]!==void 0?arguments[3]:{},a=e;i.trim().split(Fe).forEach(function(r){if(n.once&&!Ke){var h=t.listeners,s=h===void 0?{}:h;a=function(){delete s[r][e],t.removeEventListener(r,a,n);for(var c=arguments.length,o=new Array(c),u=0;u<c;u++)o[u]=arguments[u];e.apply(t,o)},s[r]||(s[r]={}),s[r][e]&&t.removeEventListener(r,s[r][e],n),s[r][e]=a,t.listeners=s}t.addEventListener(r,a,n)})}function ut(t,i,e){var n;return S(Event)&&S(CustomEvent)?n=new CustomEvent(i,{detail:e,bubbles:!0,cancelable:!0}):(n=document.createEvent("CustomEvent")).initCustomEvent(i,!0,!0,e),t.dispatchEvent(n)}function Qe(t){var i=t.getBoundingClientRect();return{left:i.left+(window.pageXOffset-document.documentElement.clientLeft),top:i.top+(window.pageYOffset-document.documentElement.clientTop)}}var Kt=$.location,$i=/^(\w+:)\/\/([^:/?#]*):?(\d*)/i;function Ze(t){var i=t.match($i);return i!==null&&(i[1]!==Kt.protocol||i[2]!==Kt.hostname||i[3]!==Kt.port)}function Ge(t){var i="timestamp=".concat(new Date().getTime());return t+(t.indexOf("?")===-1?"?":"&")+i}function xt(t){var i=t.rotate,e=t.scaleX,n=t.scaleY,a=t.translateX,r=t.translateY,h=[];M(a)&&a!==0&&h.push("translateX(".concat(a,"px)")),M(r)&&r!==0&&h.push("translateY(".concat(r,"px)")),M(i)&&i!==0&&h.push("rotate(".concat(i,"deg)")),M(e)&&e!==1&&h.push("scaleX(".concat(e,")")),M(n)&&n!==1&&h.push("scaleY(".concat(n,")"));var s=h.length?h.join(" "):"none";return{WebkitTransform:s,msTransform:s,transform:s}}function Tt(t,i){var e=t.pageX,n=t.pageY,a={endX:e,endY:n};return i?a:Be({startX:e,startY:n},a)}function G(t){var i=t.aspectRatio,e=t.height,n=t.width,a=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"contain",r=qe(n),h=qe(e);if(r&&h){var s=e*i;a==="contain"&&s>n||a==="cover"&&s<n?e=n/i:n=e*i}else r?e=n/i:h&&(n=e*i);return{width:n,height:e}}var Je=String.fromCharCode,qi=/^data:.*,/;function Vi(t){var i,e=new DataView(t);try{var n,a,r;if(e.getUint8(0)===255&&e.getUint8(1)===216)for(var h=e.byteLength,s=2;s+1<h;){if(e.getUint8(s)===255&&e.getUint8(s+1)===225){a=s;break}s+=1}if(a){var c=a+10;if(function(f,g,b){var C="";b+=g;for(var y=g;y<b;y+=1)C+=Je(f.getUint8(y));return C}(e,a+4,4)==="Exif"){var o=e.getUint16(c);if(((n=o===18761)||o===19789)&&e.getUint16(c+2,n)===42){var u=e.getUint32(c+4,n);u>=8&&(r=c+u)}}}if(r){var p,l,m=e.getUint16(r,n);for(l=0;l<m;l+=1)if(p=r+12*l+2,e.getUint16(p,n)===274){p+=8,i=e.getUint16(p,n),e.setUint16(p,1,n);break}}}catch{i=1}return i}var Fi={render:function(){this.initContainer(),this.initCanvas(),this.initCropBox(),this.renderCanvas(),this.cropped&&this.renderCropBox()},initContainer:function(){var t=this.element,i=this.options,e=this.container,n=this.cropper,a=Number(i.minContainerWidth),r=Number(i.minContainerHeight);W(n,A),q(t,A);var h={width:Math.max(e.offsetWidth,a>=0?a:200),height:Math.max(e.offsetHeight,r>=0?r:100)};this.containerData=h,Z(n,{width:h.width,height:h.height}),W(t,A),q(n,A)},initCanvas:function(){var t=this.containerData,i=this.imageData,e=this.options.viewMode,n=Math.abs(i.rotate)%180==90,a=n?i.naturalHeight:i.naturalWidth,r=n?i.naturalWidth:i.naturalHeight,h=a/r,s=t.width,c=t.height;t.height*h>t.width?e===3?s=t.height*h:c=t.width/h:e===3?c=t.width/h:s=t.height*h;var o={aspectRatio:h,naturalWidth:a,naturalHeight:r,width:s,height:c};this.canvasData=o,this.limited=e===1||e===2,this.limitCanvas(!0,!0),o.width=Math.min(Math.max(o.width,o.minWidth),o.maxWidth),o.height=Math.min(Math.max(o.height,o.minHeight),o.maxHeight),o.left=(t.width-o.width)/2,o.top=(t.height-o.height)/2,o.oldLeft=o.left,o.oldTop=o.top,this.initialCanvasData=T({},o)},limitCanvas:function(t,i){var e=this.options,n=this.containerData,a=this.canvasData,r=this.cropBoxData,h=e.viewMode,s=a.aspectRatio,c=this.cropped&&r;if(t){var o=Number(e.minCanvasWidth)||0,u=Number(e.minCanvasHeight)||0;h>1?(o=Math.max(o,n.width),u=Math.max(u,n.height),h===3&&(u*s>o?o=u*s:u=o/s)):h>0&&(o?o=Math.max(o,c?r.width:0):u?u=Math.max(u,c?r.height:0):c&&(o=r.width,(u=r.height)*s>o?o=u*s:u=o/s));var p=G({aspectRatio:s,width:o,height:u});o=p.width,u=p.height,a.minWidth=o,a.minHeight=u,a.maxWidth=1/0,a.maxHeight=1/0}if(i)if(h>(c?0:1)){var l=n.width-a.width,m=n.height-a.height;a.minLeft=Math.min(0,l),a.minTop=Math.min(0,m),a.maxLeft=Math.max(0,l),a.maxTop=Math.max(0,m),c&&this.limited&&(a.minLeft=Math.min(r.left,r.left+(r.width-a.width)),a.minTop=Math.min(r.top,r.top+(r.height-a.height)),a.maxLeft=r.left,a.maxTop=r.top,h===2&&(a.width>=n.width&&(a.minLeft=Math.min(0,l),a.maxLeft=Math.max(0,l)),a.height>=n.height&&(a.minTop=Math.min(0,m),a.maxTop=Math.max(0,m))))}else a.minLeft=-a.width,a.minTop=-a.height,a.maxLeft=n.width,a.maxTop=n.height},renderCanvas:function(t,i){var e=this.canvasData,n=this.imageData;if(i){var a=function(o){var u=o.width,p=o.height,l=o.degree;if((l=Math.abs(l)%180)==90)return{width:p,height:u};var m=l%90*Math.PI/180,f=Math.sin(m),g=Math.cos(m),b=u*g+p*f,C=u*f+p*g;return l>90?{width:C,height:b}:{width:b,height:C}}({width:n.naturalWidth*Math.abs(n.scaleX||1),height:n.naturalHeight*Math.abs(n.scaleY||1),degree:n.rotate||0}),r=a.width,h=a.height,s=e.width*(r/e.naturalWidth),c=e.height*(h/e.naturalHeight);e.left-=(s-e.width)/2,e.top-=(c-e.height)/2,e.width=s,e.height=c,e.aspectRatio=r/h,e.naturalWidth=r,e.naturalHeight=h,this.limitCanvas(!0,!1)}(e.width>e.maxWidth||e.width<e.minWidth)&&(e.left=e.oldLeft),(e.height>e.maxHeight||e.height<e.minHeight)&&(e.top=e.oldTop),e.width=Math.min(Math.max(e.width,e.minWidth),e.maxWidth),e.height=Math.min(Math.max(e.height,e.minHeight),e.maxHeight),this.limitCanvas(!1,!0),e.left=Math.min(Math.max(e.left,e.minLeft),e.maxLeft),e.top=Math.min(Math.max(e.top,e.minTop),e.maxTop),e.oldLeft=e.left,e.oldTop=e.top,Z(this.canvas,T({width:e.width,height:e.height},xt({translateX:e.left,translateY:e.top}))),this.renderImage(t),this.cropped&&this.limited&&this.limitCropBox(!0,!0)},renderImage:function(t){var i=this.canvasData,e=this.imageData,n=e.naturalWidth*(i.width/i.naturalWidth),a=e.naturalHeight*(i.height/i.naturalHeight);T(e,{width:n,height:a,left:(i.width-n)/2,top:(i.height-a)/2}),Z(this.image,T({width:e.width,height:e.height},xt(T({translateX:e.left,translateY:e.top},e)))),t&&this.output()},initCropBox:function(){var t=this.options,i=this.canvasData,e=t.aspectRatio||t.initialAspectRatio,n=Number(t.autoCropArea)||.8,a={width:i.width,height:i.height};e&&(i.height*e>i.width?a.height=a.width/e:a.width=a.height*e),this.cropBoxData=a,this.limitCropBox(!0,!0),a.width=Math.min(Math.max(a.width,a.minWidth),a.maxWidth),a.height=Math.min(Math.max(a.height,a.minHeight),a.maxHeight),a.width=Math.max(a.minWidth,a.width*n),a.height=Math.max(a.minHeight,a.height*n),a.left=i.left+(i.width-a.width)/2,a.top=i.top+(i.height-a.height)/2,a.oldLeft=a.left,a.oldTop=a.top,this.initialCropBoxData=T({},a)},limitCropBox:function(t,i){var e=this.options,n=this.containerData,a=this.canvasData,r=this.cropBoxData,h=this.limited,s=e.aspectRatio;if(t){var c=Number(e.minCropBoxWidth)||0,o=Number(e.minCropBoxHeight)||0,u=h?Math.min(n.width,a.width,a.width+a.left,n.width-a.left):n.width,p=h?Math.min(n.height,a.height,a.height+a.top,n.height-a.top):n.height;c=Math.min(c,n.width),o=Math.min(o,n.height),s&&(c&&o?o*s>c?o=c/s:c=o*s:c?o=c/s:o&&(c=o*s),p*s>u?p=u/s:u=p*s),r.minWidth=Math.min(c,u),r.minHeight=Math.min(o,p),r.maxWidth=u,r.maxHeight=p}i&&(h?(r.minLeft=Math.max(0,a.left),r.minTop=Math.max(0,a.top),r.maxLeft=Math.min(n.width,a.left+a.width)-r.width,r.maxTop=Math.min(n.height,a.top+a.height)-r.height):(r.minLeft=0,r.minTop=0,r.maxLeft=n.width-r.width,r.maxTop=n.height-r.height))},renderCropBox:function(){var t=this.options,i=this.containerData,e=this.cropBoxData;(e.width>e.maxWidth||e.width<e.minWidth)&&(e.left=e.oldLeft),(e.height>e.maxHeight||e.height<e.minHeight)&&(e.top=e.oldTop),e.width=Math.min(Math.max(e.width,e.minWidth),e.maxWidth),e.height=Math.min(Math.max(e.height,e.minHeight),e.maxHeight),this.limitCropBox(!1,!0),e.left=Math.min(Math.max(e.left,e.minLeft),e.maxLeft),e.top=Math.min(Math.max(e.top,e.minTop),e.maxTop),e.oldLeft=e.left,e.oldTop=e.top,t.movable&&t.cropBoxMovable&&yt(this.face,wt,e.width>=i.width&&e.height>=i.height?We:St),Z(this.cropBox,T({width:e.width,height:e.height},xt({translateX:e.left,translateY:e.top}))),this.cropped&&this.limited&&this.limitCanvas(!0,!0),this.disabled||this.output()},output:function(){this.preview(),ut(this.element,It,this.getData())}},Ki={initPreview:function(){var t=this.element,i=this.crossOrigin,e=this.options.preview,n=i?this.crossOriginUrl:this.url,a=t.alt||"The image to preview",r=document.createElement("img");if(i&&(r.crossOrigin=i),r.src=n,r.alt=a,this.viewBox.appendChild(r),this.viewBoxImage=r,e){var h=e;typeof e=="string"?h=t.ownerDocument.querySelectorAll(e):e.querySelector&&(h=[e]),this.previews=h,E(h,function(s){var c=document.createElement("img");yt(s,Ot,{width:s.offsetWidth,height:s.offsetHeight,html:s.innerHTML}),i&&(c.crossOrigin=i),c.src=n,c.alt=a,c.style.cssText='display:block;width:100%;height:auto;min-width:0!important;min-height:0!important;max-width:none!important;max-height:none!important;image-orientation:0deg!important;"',s.innerHTML="",s.appendChild(c)})}},resetPreview:function(){E(this.previews,function(t){var i=Ft(t,Ot);Z(t,{width:i.width,height:i.height}),t.innerHTML=i.html,function(e,n){if(nt(e[n]))try{delete e[n]}catch{e[n]=void 0}else if(e.dataset)try{delete e.dataset[n]}catch{e.dataset[n]=void 0}else e.removeAttribute("data-".concat(Vt(n)))}(t,Ot)})},preview:function(){var t=this.imageData,i=this.canvasData,e=this.cropBoxData,n=e.width,a=e.height,r=t.width,h=t.height,s=e.left-i.left-t.left,c=e.top-i.top-t.top;this.cropped&&!this.disabled&&(Z(this.viewBoxImage,T({width:r,height:h},xt(T({translateX:-s,translateY:-c},t)))),E(this.previews,function(o){var u=Ft(o,Ot),p=u.width,l=u.height,m=p,f=l,g=1;n&&(f=a*(g=p/n)),a&&f>l&&(m=n*(g=l/a),f=l),Z(o,{width:m,height:f}),Z(o.getElementsByTagName("img")[0],T({width:r*g,height:h*g},xt(T({translateX:-s*g,translateY:-c*g},t))))}))}},Qi={bind:function(){var t=this.element,i=this.options,e=this.cropper;S(i.cropstart)&&_(t,Ut,i.cropstart),S(i.cropmove)&&_(t,Pt,i.cropmove),S(i.cropend)&&_(t,jt,i.cropend),S(i.crop)&&_(t,It,i.crop),S(i.zoom)&&_(t,$t,i.zoom),_(e,Ye,this.onCropStart=this.cropStart.bind(this)),i.zoomable&&i.zoomOnWheel&&_(e,Pe,this.onWheel=this.wheel.bind(this),{passive:!1,capture:!0}),i.toggleDragModeOnDblclick&&_(e,Se,this.onDblclick=this.dblclick.bind(this)),_(t.ownerDocument,Xe,this.onCropMove=this.cropMove.bind(this)),_(t.ownerDocument,_e,this.onCropEnd=this.cropEnd.bind(this)),i.responsive&&_(window,je,this.onResize=this.resize.bind(this))},unbind:function(){var t=this.element,i=this.options,e=this.cropper;S(i.cropstart)&&j(t,Ut,i.cropstart),S(i.cropmove)&&j(t,Pt,i.cropmove),S(i.cropend)&&j(t,jt,i.cropend),S(i.crop)&&j(t,It,i.crop),S(i.zoom)&&j(t,$t,i.zoom),j(e,Ye,this.onCropStart),i.zoomable&&i.zoomOnWheel&&j(e,Pe,this.onWheel,{passive:!1,capture:!0}),i.toggleDragModeOnDblclick&&j(e,Se,this.onDblclick),j(t.ownerDocument,Xe,this.onCropMove),j(t.ownerDocument,_e,this.onCropEnd),i.responsive&&j(window,je,this.onResize)}},Zi={resize:function(){if(!this.disabled){var t,i,e=this.options,n=this.container,a=this.containerData,r=n.offsetWidth/a.width,h=n.offsetHeight/a.height,s=Math.abs(r-1)>Math.abs(h-1)?r:h;s!==1&&(e.restore&&(t=this.getCanvasData(),i=this.getCropBoxData()),this.render(),e.restore&&(this.setCanvasData(E(t,function(c,o){t[o]=c*s})),this.setCropBoxData(E(i,function(c,o){i[o]=c*s}))))}},dblclick:function(){var t,i;this.disabled||this.options.dragMode===Ae||this.setDragMode((t=this.dragBox,i=Yt,(t.classList?t.classList.contains(i):t.className.indexOf(i)>-1)?Le:_t))},wheel:function(t){var i=this,e=Number(this.options.wheelZoomRatio)||.1,n=1;this.disabled||(t.preventDefault(),this.wheeling||(this.wheeling=!0,setTimeout(function(){i.wheeling=!1},50),t.deltaY?n=t.deltaY>0?1:-1:t.wheelDelta?n=-t.wheelDelta/120:t.detail&&(n=t.detail>0?1:-1),this.zoom(-n*e,t)))},cropStart:function(t){var i=t.buttons,e=t.button;if(!(this.disabled||(t.type==="mousedown"||t.type==="pointerdown"&&t.pointerType==="mouse")&&(M(i)&&i!==1||M(e)&&e!==0||t.ctrlKey))){var n,a=this.options,r=this.pointers;t.changedTouches?E(t.changedTouches,function(h){r[h.identifier]=Tt(h)}):r[t.pointerId||0]=Tt(t),n=Object.keys(r).length>1&&a.zoomable&&a.zoomOnTouch?He:Ft(t.target,wt),Li.test(n)&&ut(this.element,Ut,{originalEvent:t,action:n})!==!1&&(t.preventDefault(),this.action=n,this.cropping=!1,n===Ee&&(this.cropping=!0,W(this.dragBox,Bt)))}},cropMove:function(t){var i=this.action;if(!this.disabled&&i){var e=this.pointers;t.preventDefault(),ut(this.element,Pt,{originalEvent:t,action:i})!==!1&&(t.changedTouches?E(t.changedTouches,function(n){T(e[n.identifier]||{},Tt(n,!0))}):T(e[t.pointerId||0]||{},Tt(t,!0)),this.change(t))}},cropEnd:function(t){if(!this.disabled){var i=this.action,e=this.pointers;t.changedTouches?E(t.changedTouches,function(n){delete e[n.identifier]}):delete e[t.pointerId||0],i&&(t.preventDefault(),Object.keys(e).length||(this.action=""),this.cropping&&(this.cropping=!1,pt(this.dragBox,Bt,this.cropped&&this.options.modal)),ut(this.element,jt,{originalEvent:t,action:i}))}}},Gi={change:function(t){var i,e=this.options,n=this.canvasData,a=this.containerData,r=this.cropBoxData,h=this.pointers,s=this.action,c=e.aspectRatio,o=r.left,u=r.top,p=r.width,l=r.height,m=o+p,f=u+l,g=0,b=0,C=a.width,y=a.height,D=!0;!c&&t.shiftKey&&(c=p&&l?p/l:1),this.limited&&(g=r.minLeft,b=r.minTop,C=g+Math.min(a.width,n.width,n.left+n.width),y=b+Math.min(a.height,n.height,n.top+n.height));var x=h[Object.keys(h)[0]],d={x:x.endX-x.startX,y:x.endY-x.startY},w=function(k){switch(k){case it:m+d.x>C&&(d.x=C-m);break;case at:o+d.x<g&&(d.x=g-o);break;case Q:u+d.y<b&&(d.y=b-u);break;case ct:f+d.y>y&&(d.y=y-f)}};switch(s){case St:o+=d.x,u+=d.y;break;case it:if(d.x>=0&&(m>=C||c&&(u<=b||f>=y))){D=!1;break}w(it),(p+=d.x)<0&&(s=at,o-=p=-p),c&&(l=p/c,u+=(r.height-l)/2);break;case Q:if(d.y<=0&&(u<=b||c&&(o<=g||m>=C))){D=!1;break}w(Q),l-=d.y,u+=d.y,l<0&&(s=ct,u-=l=-l),c&&(p=l*c,o+=(r.width-p)/2);break;case at:if(d.x<=0&&(o<=g||c&&(u<=b||f>=y))){D=!1;break}w(at),p-=d.x,o+=d.x,p<0&&(s=it,o-=p=-p),c&&(l=p/c,u+=(r.height-l)/2);break;case ct:if(d.y>=0&&(f>=y||c&&(o<=g||m>=C))){D=!1;break}w(ct),(l+=d.y)<0&&(s=Q,u-=l=-l),c&&(p=l*c,o+=(r.width-p)/2);break;case gt:if(c){if(d.y<=0&&(u<=b||m>=C)){D=!1;break}w(Q),l-=d.y,u+=d.y,p=l*c}else w(Q),w(it),d.x>=0?m<C?p+=d.x:d.y<=0&&u<=b&&(D=!1):p+=d.x,d.y<=0?u>b&&(l-=d.y,u+=d.y):(l-=d.y,u+=d.y);p<0&&l<0?(s=bt,u-=l=-l,o-=p=-p):p<0?(s=ft,o-=p=-p):l<0&&(s=vt,u-=l=-l);break;case ft:if(c){if(d.y<=0&&(u<=b||o<=g)){D=!1;break}w(Q),l-=d.y,u+=d.y,p=l*c,o+=r.width-p}else w(Q),w(at),d.x<=0?o>g?(p-=d.x,o+=d.x):d.y<=0&&u<=b&&(D=!1):(p-=d.x,o+=d.x),d.y<=0?u>b&&(l-=d.y,u+=d.y):(l-=d.y,u+=d.y);p<0&&l<0?(s=vt,u-=l=-l,o-=p=-p):p<0?(s=gt,o-=p=-p):l<0&&(s=bt,u-=l=-l);break;case bt:if(c){if(d.x<=0&&(o<=g||f>=y)){D=!1;break}w(at),p-=d.x,o+=d.x,l=p/c}else w(ct),w(at),d.x<=0?o>g?(p-=d.x,o+=d.x):d.y>=0&&f>=y&&(D=!1):(p-=d.x,o+=d.x),d.y>=0?f<y&&(l+=d.y):l+=d.y;p<0&&l<0?(s=gt,u-=l=-l,o-=p=-p):p<0?(s=vt,o-=p=-p):l<0&&(s=ft,u-=l=-l);break;case vt:if(c){if(d.x>=0&&(m>=C||f>=y)){D=!1;break}w(it),l=(p+=d.x)/c}else w(ct),w(it),d.x>=0?m<C?p+=d.x:d.y>=0&&f>=y&&(D=!1):p+=d.x,d.y>=0?f<y&&(l+=d.y):l+=d.y;p<0&&l<0?(s=ft,u-=l=-l,o-=p=-p):p<0?(s=bt,o-=p=-p):l<0&&(s=gt,u-=l=-l);break;case We:this.move(d.x,d.y),D=!1;break;case He:this.zoom(function(k){var Y=Be({},k),R=0;return E(k,function(H,K){delete Y[K],E(Y,function(N){var z=Math.abs(H.startX-N.startX),mt=Math.abs(H.startY-N.startY),J=Math.abs(H.endX-N.endX),rt=Math.abs(H.endY-N.endY),V=Math.sqrt(z*z+mt*mt),ot=(Math.sqrt(J*J+rt*rt)-V)/V;Math.abs(ot)>Math.abs(R)&&(R=ot)})}),R}(h),t),D=!1;break;case Ee:if(!d.x||!d.y){D=!1;break}i=Qe(this.cropper),o=x.startX-i.left,u=x.startY-i.top,p=r.minWidth,l=r.minHeight,d.x>0?s=d.y>0?vt:gt:d.x<0&&(o-=p,s=d.y>0?bt:ft),d.y<0&&(u-=l),this.cropped||(q(this.cropBox,A),this.cropped=!0,this.limited&&this.limitCropBox(!0,!0))}D&&(r.width=p,r.height=l,r.left=o,r.top=u,this.action=s,this.renderCropBox()),E(h,function(k){k.startX=k.endX,k.startY=k.endY})}},Ji={crop:function(){return!this.ready||this.cropped||this.disabled||(this.cropped=!0,this.limitCropBox(!0,!0),this.options.modal&&W(this.dragBox,Bt),q(this.cropBox,A),this.setCropBoxData(this.initialCropBoxData)),this},reset:function(){return this.ready&&!this.disabled&&(this.imageData=T({},this.initialImageData),this.canvasData=T({},this.initialCanvasData),this.cropBoxData=T({},this.initialCropBoxData),this.renderCanvas(),this.cropped&&this.renderCropBox()),this},clear:function(){return this.cropped&&!this.disabled&&(T(this.cropBoxData,{left:0,top:0,width:0,height:0}),this.cropped=!1,this.renderCropBox(),this.limitCanvas(!0,!0),this.renderCanvas(),q(this.dragBox,Bt),W(this.cropBox,A)),this},replace:function(t){var i=arguments.length>1&&arguments[1]!==void 0&&arguments[1];return!this.disabled&&t&&(this.isImg&&(this.element.src=t),i?(this.url=t,this.image.src=t,this.ready&&(this.viewBoxImage.src=t,E(this.previews,function(e){e.getElementsByTagName("img")[0].src=t}))):(this.isImg&&(this.replaced=!0),this.options.data=null,this.uncreate(),this.load(t))),this},enable:function(){return this.ready&&this.disabled&&(this.disabled=!1,q(this.cropper,Ne)),this},disable:function(){return this.ready&&!this.disabled&&(this.disabled=!0,W(this.cropper,Ne)),this},destroy:function(){var t=this.element;return t[O]?(t[O]=void 0,this.isImg&&this.replaced&&(t.src=this.originalUrl),this.uncreate(),this):this},move:function(t){var i=arguments.length>1&&arguments[1]!==void 0?arguments[1]:t,e=this.canvasData,n=e.left,a=e.top;return this.moveTo(qt(t)?t:n+Number(t),qt(i)?i:a+Number(i))},moveTo:function(t){var i=arguments.length>1&&arguments[1]!==void 0?arguments[1]:t,e=this.canvasData,n=!1;return t=Number(t),i=Number(i),this.ready&&!this.disabled&&this.options.movable&&(M(t)&&(e.left=t,n=!0),M(i)&&(e.top=i,n=!0),n&&this.renderCanvas(!0)),this},zoom:function(t,i){var e=this.canvasData;return t=(t=Number(t))<0?1/(1-t):1+t,this.zoomTo(e.width*t/e.naturalWidth,null,i)},zoomTo:function(t,i,e){var n=this.options,a=this.canvasData,r=a.width,h=a.height,s=a.naturalWidth,c=a.naturalHeight;if((t=Number(t))>=0&&this.ready&&!this.disabled&&n.zoomable){var o=s*t,u=c*t;if(ut(this.element,$t,{ratio:t,oldRatio:r/s,originalEvent:e})===!1)return this;if(e){var p=this.pointers,l=Qe(this.cropper),m=p&&Object.keys(p).length?function(f){var g=0,b=0,C=0;return E(f,function(y){var D=y.startX,x=y.startY;g+=D,b+=x,C+=1}),{pageX:g/=C,pageY:b/=C}}(p):{pageX:e.pageX,pageY:e.pageY};a.left-=(o-r)*((m.pageX-l.left-a.left)/r),a.top-=(u-h)*((m.pageY-l.top-a.top)/h)}else lt(i)&&M(i.x)&&M(i.y)?(a.left-=(o-r)*((i.x-a.left)/r),a.top-=(u-h)*((i.y-a.top)/h)):(a.left-=(o-r)/2,a.top-=(u-h)/2);a.width=o,a.height=u,this.renderCanvas(!0)}return this},rotate:function(t){return this.rotateTo((this.imageData.rotate||0)+Number(t))},rotateTo:function(t){return M(t=Number(t))&&this.ready&&!this.disabled&&this.options.rotatable&&(this.imageData.rotate=t%360,this.renderCanvas(!0,!0)),this},scaleX:function(t){var i=this.imageData.scaleY;return this.scale(t,M(i)?i:1)},scaleY:function(t){var i=this.imageData.scaleX;return this.scale(M(i)?i:1,t)},scale:function(t){var i=arguments.length>1&&arguments[1]!==void 0?arguments[1]:t,e=this.imageData,n=!1;return t=Number(t),i=Number(i),this.ready&&!this.disabled&&this.options.scalable&&(M(t)&&(e.scaleX=t,n=!0),M(i)&&(e.scaleY=i,n=!0),n&&this.renderCanvas(!0,!0)),this},getData:function(){var t,i=arguments.length>0&&arguments[0]!==void 0&&arguments[0],e=this.options,n=this.imageData,a=this.canvasData,r=this.cropBoxData;if(this.ready&&this.cropped){t={x:r.left-a.left,y:r.top-a.top,width:r.width,height:r.height};var h=n.width/n.naturalWidth;if(E(t,function(o,u){t[u]=o/h}),i){var s=Math.round(t.y+t.height),c=Math.round(t.x+t.width);t.x=Math.round(t.x),t.y=Math.round(t.y),t.width=c-t.x,t.height=s-t.y}}else t={x:0,y:0,width:0,height:0};return e.rotatable&&(t.rotate=n.rotate||0),e.scalable&&(t.scaleX=n.scaleX||1,t.scaleY=n.scaleY||1),t},setData:function(t){var i=this.options,e=this.imageData,n=this.canvasData,a={};if(this.ready&&!this.disabled&&lt(t)){var r=!1;i.rotatable&&M(t.rotate)&&t.rotate!==e.rotate&&(e.rotate=t.rotate,r=!0),i.scalable&&(M(t.scaleX)&&t.scaleX!==e.scaleX&&(e.scaleX=t.scaleX,r=!0),M(t.scaleY)&&t.scaleY!==e.scaleY&&(e.scaleY=t.scaleY,r=!0)),r&&this.renderCanvas(!0,!0);var h=e.width/e.naturalWidth;M(t.x)&&(a.left=t.x*h+n.left),M(t.y)&&(a.top=t.y*h+n.top),M(t.width)&&(a.width=t.width*h),M(t.height)&&(a.height=t.height*h),this.setCropBoxData(a)}return this},getContainerData:function(){return this.ready?T({},this.containerData):{}},getImageData:function(){return this.sized?T({},this.imageData):{}},getCanvasData:function(){var t=this.canvasData,i={};return this.ready&&E(["left","top","width","height","naturalWidth","naturalHeight"],function(e){i[e]=t[e]}),i},setCanvasData:function(t){var i=this.canvasData,e=i.aspectRatio;return this.ready&&!this.disabled&&lt(t)&&(M(t.left)&&(i.left=t.left),M(t.top)&&(i.top=t.top),M(t.width)?(i.width=t.width,i.height=t.width/e):M(t.height)&&(i.height=t.height,i.width=t.height*e),this.renderCanvas(!0)),this},getCropBoxData:function(){var t,i=this.cropBoxData;return this.ready&&this.cropped&&(t={left:i.left,top:i.top,width:i.width,height:i.height}),t||{}},setCropBoxData:function(t){var i,e,n=this.cropBoxData,a=this.options.aspectRatio;return this.ready&&this.cropped&&!this.disabled&&lt(t)&&(M(t.left)&&(n.left=t.left),M(t.top)&&(n.top=t.top),M(t.width)&&t.width!==n.width&&(i=!0,n.width=t.width),M(t.height)&&t.height!==n.height&&(e=!0,n.height=t.height),a&&(i?n.height=n.width/a:e&&(n.width=n.height*a)),this.renderCropBox()),this},getCroppedCanvas:function(){var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};if(!this.ready||!window.HTMLCanvasElement)return null;var i=this.canvasData,e=function(rt,V,ot,tt){var Qt=V.aspectRatio,ei=V.naturalWidth,ii=V.naturalHeight,Zt=V.rotate,ai=Zt===void 0?0:Zt,Gt=V.scaleX,ni=Gt===void 0?1:Gt,Jt=V.scaleY,ri=Jt===void 0?1:Jt,te=ot.aspectRatio,oi=ot.naturalWidth,si=ot.naturalHeight,ee=tt.fillColor,hi=ee===void 0?"transparent":ee,ie=tt.imageSmoothingEnabled,ci=ie===void 0||ie,ae=tt.imageSmoothingQuality,li=ae===void 0?"low":ae,ne=tt.maxWidth,re=ne===void 0?1/0:ne,oe=tt.maxHeight,se=oe===void 0?1/0:oe,he=tt.minWidth,ce=he===void 0?0:he,le=tt.minHeight,de=le===void 0?0:le,Mt=document.createElement("canvas"),I=Mt.getContext("2d"),pe=G({aspectRatio:te,width:re,height:se}),ue=G({aspectRatio:te,width:ce,height:de},"cover"),zt=Math.min(pe.width,Math.max(ue.width,oi)),Et=Math.min(pe.height,Math.max(ue.height,si)),me=G({aspectRatio:Qt,width:re,height:se}),ge=G({aspectRatio:Qt,width:ce,height:de},"cover"),fe=Math.min(me.width,Math.max(ge.width,ei)),ve=Math.min(me.height,Math.max(ge.height,ii)),di=[-fe/2,-ve/2,fe,ve];return Mt.width=dt(zt),Mt.height=dt(Et),I.fillStyle=hi,I.fillRect(0,0,zt,Et),I.save(),I.translate(zt/2,Et/2),I.rotate(ai*Math.PI/180),I.scale(ni,ri),I.imageSmoothingEnabled=ci,I.imageSmoothingQuality=li,I.drawImage.apply(I,[rt].concat(ze(di.map(function(pi){return Math.floor(dt(pi))})))),I.restore(),Mt}(this.image,this.imageData,i,t);if(!this.cropped)return e;var n=this.getData(t.rounded),a=n.x,r=n.y,h=n.width,s=n.height,c=e.width/Math.floor(i.naturalWidth);c!==1&&(a*=c,r*=c,h*=c,s*=c);var o=h/s,u=G({aspectRatio:o,width:t.maxWidth||1/0,height:t.maxHeight||1/0}),p=G({aspectRatio:o,width:t.minWidth||0,height:t.minHeight||0},"cover"),l=G({aspectRatio:o,width:t.width||(c!==1?e.width:h),height:t.height||(c!==1?e.height:s)}),m=l.width,f=l.height;m=Math.min(u.width,Math.max(p.width,m)),f=Math.min(u.height,Math.max(p.height,f));var g=document.createElement("canvas"),b=g.getContext("2d");g.width=dt(m),g.height=dt(f),b.fillStyle=t.fillColor||"transparent",b.fillRect(0,0,m,f);var C=t.imageSmoothingEnabled,y=C===void 0||C,D=t.imageSmoothingQuality;b.imageSmoothingEnabled=y,D&&(b.imageSmoothingQuality=D);var x,d,w,k,Y,R,H=e.width,K=e.height,N=a,z=r;N<=-h||N>H?(N=0,x=0,w=0,Y=0):N<=0?(w=-N,N=0,Y=x=Math.min(H,h+N)):N<=H&&(w=0,Y=x=Math.min(h,H-N)),x<=0||z<=-s||z>K?(z=0,d=0,k=0,R=0):z<=0?(k=-z,z=0,R=d=Math.min(K,s+z)):z<=K&&(k=0,R=d=Math.min(s,K-z));var mt=[N,z,x,d];if(Y>0&&R>0){var J=m/h;mt.push(w*J,k*J,Y*J,R*J)}return b.drawImage.apply(b,[e].concat(ze(mt.map(function(rt){return Math.floor(dt(rt))})))),g},setAspectRatio:function(t){var i=this.options;return this.disabled||qt(t)||(i.aspectRatio=Math.max(0,t)||NaN,this.ready&&(this.initCropBox(),this.cropped&&this.renderCropBox())),this},setDragMode:function(t){var i=this.options,e=this.dragBox,n=this.face;if(this.ready&&!this.disabled){var a=t===_t,r=i.movable&&t===Le;t=a||r?t:Ae,i.dragMode=t,yt(e,wt,t),pt(e,Yt,a),pt(e,Xt,r),i.cropBoxMovable||(yt(n,wt,t),pt(n,Yt,a),pt(n,Xt,r))}return this}},ta=$.Cropper,ti=function(){function t(a){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};if(function(h,s){if(!(h instanceof s))throw new TypeError("Cannot call a class as a function")}(this,t),!a||!Yi.test(a.tagName))throw new Error("The first argument is required and must be an <img> or <canvas> element.");this.element=a,this.options=T({},$e,lt(r)&&r),this.cropped=!1,this.disabled=!1,this.pointers={},this.ready=!1,this.reloading=!1,this.replaced=!1,this.sized=!1,this.sizing=!1,this.init()}return i=t,n=[{key:"noConflict",value:function(){return window.Cropper=ta,t}},{key:"setDefaults",value:function(a){T($e,lt(a)&&a)}}],(e=[{key:"init",value:function(){var a,r=this.element,h=r.tagName.toLowerCase();if(!r[O]){if(r[O]=this,h==="img"){if(this.isImg=!0,a=r.getAttribute("src")||"",this.originalUrl=a,!a)return;a=r.src}else h==="canvas"&&window.HTMLCanvasElement&&(a=r.toDataURL());this.load(a)}}},{key:"load",value:function(a){var r=this;if(a){this.url=a,this.imageData={};var h=this.element,s=this.options;if(s.rotatable||s.scalable||(s.checkOrientation=!1),s.checkOrientation&&window.ArrayBuffer)if(Ai.test(a))Si.test(a)?this.read((c=a.replace(qi,""),o=atob(c),u=new ArrayBuffer(o.length),E(p=new Uint8Array(u),function(f,g){p[g]=o.charCodeAt(g)}),u)):this.clone();else{var c,o,u,p,l=new XMLHttpRequest,m=this.clone.bind(this);this.reloading=!0,this.xhr=l,l.onabort=m,l.onerror=m,l.ontimeout=m,l.onprogress=function(){l.getResponseHeader("content-type")!==Ue&&l.abort()},l.onload=function(){r.read(l.response)},l.onloadend=function(){r.reloading=!1,r.xhr=null},s.checkCrossOrigin&&Ze(a)&&h.crossOrigin&&(a=Ge(a)),l.open("GET",a,!0),l.responseType="arraybuffer",l.withCredentials=h.crossOrigin==="use-credentials",l.send()}else this.clone()}}},{key:"read",value:function(a){var r=this.options,h=this.imageData,s=Vi(a),c=0,o=1,u=1;if(s>1){this.url=function(l,m){for(var f=[],g=new Uint8Array(l);g.length>0;)f.push(Je.apply(null,Ve(g.subarray(0,8192)))),g=g.subarray(8192);return"data:".concat(m,";base64,").concat(btoa(f.join("")))}(a,Ue);var p=function(l){var m=0,f=1,g=1;switch(l){case 2:f=-1;break;case 3:m=-180;break;case 4:g=-1;break;case 5:m=90,g=-1;break;case 6:m=90;break;case 7:m=90,f=-1;break;case 8:m=-90}return{rotate:m,scaleX:f,scaleY:g}}(s);c=p.rotate,o=p.scaleX,u=p.scaleY}r.rotatable&&(h.rotate=c),r.scalable&&(h.scaleX=o,h.scaleY=u),this.clone()}},{key:"clone",value:function(){var a=this.element,r=this.url,h=a.crossOrigin,s=r;this.options.checkCrossOrigin&&Ze(r)&&(h||(h="anonymous"),s=Ge(r)),this.crossOrigin=h,this.crossOriginUrl=s;var c=document.createElement("img");h&&(c.crossOrigin=h),c.src=s||r,c.alt=a.alt||"The image to crop",this.image=c,c.onload=this.start.bind(this),c.onerror=this.stop.bind(this),W(c,Re),a.parentNode.insertBefore(c,a.nextSibling)}},{key:"start",value:function(){var a=this,r=this.image;r.onload=null,r.onerror=null,this.sizing=!0;var h=$.navigator&&/(?:iPad|iPhone|iPod).*?AppleWebKit/i.test($.navigator.userAgent),s=function(u,p){T(a.imageData,{naturalWidth:u,naturalHeight:p,aspectRatio:u/p}),a.initialImageData=T({},a.imageData),a.sizing=!1,a.sized=!0,a.build()};if(!r.naturalWidth||h){var c=document.createElement("img"),o=document.body||document.documentElement;this.sizingImage=c,c.onload=function(){s(c.width,c.height),h||o.removeChild(c)},c.src=r.src,h||(c.style.cssText="left:0;max-height:none!important;max-width:none!important;min-height:0!important;min-width:0!important;opacity:0;position:absolute;top:0;z-index:-1;",o.appendChild(c))}else s(r.naturalWidth,r.naturalHeight)}},{key:"stop",value:function(){var a=this.image;a.onload=null,a.onerror=null,a.parentNode.removeChild(a),this.image=null}},{key:"build",value:function(){if(this.sized&&!this.ready){var a=this.element,r=this.options,h=this.image,s=a.parentNode,c=document.createElement("div");c.innerHTML='<div class="cropper-container" touch-action="none"><div class="cropper-wrap-box"><div class="cropper-canvas"></div></div><div class="cropper-drag-box"></div><div class="cropper-crop-box"><span class="cropper-view-box"></span><span class="cropper-dashed dashed-h"></span><span class="cropper-dashed dashed-v"></span><span class="cropper-center"></span><span class="cropper-face"></span><span class="cropper-line line-e" data-cropper-action="e"></span><span class="cropper-line line-n" data-cropper-action="n"></span><span class="cropper-line line-w" data-cropper-action="w"></span><span class="cropper-line line-s" data-cropper-action="s"></span><span class="cropper-point point-e" data-cropper-action="e"></span><span class="cropper-point point-n" data-cropper-action="n"></span><span class="cropper-point point-w" data-cropper-action="w"></span><span class="cropper-point point-s" data-cropper-action="s"></span><span class="cropper-point point-ne" data-cropper-action="ne"></span><span class="cropper-point point-nw" data-cropper-action="nw"></span><span class="cropper-point point-sw" data-cropper-action="sw"></span><span class="cropper-point point-se" data-cropper-action="se"></span></div></div>';var o=c.querySelector(".".concat(O,"-container")),u=o.querySelector(".".concat(O,"-canvas")),p=o.querySelector(".".concat(O,"-drag-box")),l=o.querySelector(".".concat(O,"-crop-box")),m=l.querySelector(".".concat(O,"-face"));this.container=s,this.cropper=o,this.canvas=u,this.dragBox=p,this.cropBox=l,this.viewBox=o.querySelector(".".concat(O,"-view-box")),this.face=m,u.appendChild(h),W(a,A),s.insertBefore(o,a.nextSibling),q(h,Re),this.initPreview(),this.bind(),r.initialAspectRatio=Math.max(0,r.initialAspectRatio)||NaN,r.aspectRatio=Math.max(0,r.aspectRatio)||NaN,r.viewMode=Math.max(0,Math.min(3,Math.round(r.viewMode)))||0,W(l,A),r.guides||W(l.getElementsByClassName("".concat(O,"-dashed")),A),r.center||W(l.getElementsByClassName("".concat(O,"-center")),A),r.background&&W(o,"".concat(O,"-bg")),r.highlight||W(m,Ri),r.cropBoxMovable&&(W(m,Xt),yt(m,wt,St)),r.cropBoxResizable||(W(l.getElementsByClassName("".concat(O,"-line")),A),W(l.getElementsByClassName("".concat(O,"-point")),A)),this.render(),this.ready=!0,this.setDragMode(r.dragMode),r.autoCrop&&this.crop(),this.setData(r.data),S(r.ready)&&_(a,Ie,r.ready,{once:!0}),ut(a,Ie)}}},{key:"unbuild",value:function(){if(this.ready){this.ready=!1,this.unbind(),this.resetPreview();var a=this.cropper.parentNode;a&&a.removeChild(this.cropper),q(this.element,A)}}},{key:"uncreate",value:function(){this.ready?(this.unbuild(),this.ready=!1,this.cropped=!1):this.sizing?(this.sizingImage.onload=null,this.sizing=!1,this.sized=!1):this.reloading?(this.xhr.onabort=null,this.xhr.abort()):this.image&&this.stop()}}])&&Te(i.prototype,e),n&&Te(i,n),Object.defineProperty(i,"prototype",{writable:!1}),i;var i,e,n}();T(ti.prototype,Fi,Ki,Qi,Zi,Gi,Ji);const ea=["alt","crossorigin","src"],ia=Ct({name:"Cropper",__name:"Cropper",props:{src:X.string.def(""),alt:X.string.def(""),circled:X.bool.def(!1),realTimePreview:X.bool.def(!0),height:X.string.def("360px"),crossorigin:{type:String,default:void 0},imageStyle:{type:Object,default:()=>({})},options:{type:Object,default:()=>({})}},emits:["cropend","ready","cropendError"],setup(t,{emit:i}){const e={aspectRatio:1,zoomable:!0,zoomOnTouch:!0,zoomOnWheel:!0,cropBoxMovable:!0,cropBoxResizable:!0,toggleDragModeOnDblclick:!0,autoCrop:!0,background:!0,highlight:!0,center:!0,responsive:!0,restore:!0,checkCrossOrigin:!0,checkOrientation:!0,scalable:!0,modal:!0,guides:!0,movable:!0,rotatable:!0},n=t,a=i,r=bi(),h=P(),s=P(),c=P(!1),{getPrefixCls:o}=Wt(),u=o("cropper-image"),p=ui(g,80),l=Ht(()=>({height:n.height,maxWidth:"100%",...n.imageStyle})),m=Ht(()=>[u,r.class,{[`${u}--circled`]:n.circled}]),f=Ht(()=>({height:`${n.height}`.replace(/px/,"")+"px"}));function g(){n.realTimePreview&&function(){if(!s.value)return;let b=s.value.getData();(n.circled?function(){const C=s.value.getCroppedCanvas(),y=document.createElement("canvas"),D=y.getContext("2d"),x=C.width,d=C.height;return y.width=x,y.height=d,D.imageSmoothingEnabled=!0,D.drawImage(C,0,0,x,d),D.globalCompositeOperation="destination-in",D.beginPath(),D.arc(x/2,d/2,Math.min(x,d)/2,0,2*Math.PI,!0),D.fill(),y}():s.value.getCroppedCanvas()).toBlob(C=>{if(!C)return;let y=new FileReader;y.readAsDataURL(C),y.onloadend=D=>{var x;a("cropend",{imgBase64:((x=D.target)==null?void 0:x.result)??"",imgInfo:b})},y.onerror=()=>{a("cropendError")}},"image/png")}()}return wi(async function(){const b=v(h);b&&(s.value=new ti(b,{...e,ready:()=>{c.value=!0,g(),a("ready",s.value)},crop(){p()},zoom(){p()},cropmove(){p()},...n.options}))}),yi(()=>{var b;(b=s.value)==null||b.destroy()}),(b,C)=>(U(),st("div",{class:F(v(m)),style:ye(v(f))},[xi(et("img",{ref_key:"imgElRef",ref:h,alt:t.alt,crossorigin:t.crossorigin,src:t.src,style:ye(v(l))},null,12,ea),[[Mi,v(c)]])],6))}}),aa=["alt","src"],na=Ct({name:"CopperModal",__name:"CopperModal",props:{srcValue:X.string.def(""),circled:X.bool.def(!0)},emits:["uploadSuccess"],setup(t,{expose:i,emit:e}){const n=t,a=e,{t:r}=be.useI18n(),{getPrefixCls:h}=Wt(),s=h("cropper-am"),c=P(n.srcValue),o=P(""),u=P(),p=P(!1);let l="",m=1,f=1;function g(x){const d=new FileReader;return d.readAsDataURL(x),c.value="",o.value="",d.onload=function(w){var k;c.value=((k=w.target)==null?void 0:k.result)??"",l=x.name},!1}function b({imgBase64:x}){o.value=x}function C(x){u.value=x}function y(x,d){var w,k;x==="scaleX"&&(m=d=m===-1?1:-1),x==="scaleY"&&(f=d=f===-1?1:-1),(k=(w=u==null?void 0:u.value)==null?void 0:w[x])==null||k.call(w,d)}async function D(){const x=(d=>{const w=d.split(","),k=w[0].match(/:(.*?);/)[1],Y=window.atob(w[1]);let R=Y.length;const H=new Uint8Array(R);for(;R--;)H[R]=Y.charCodeAt(R);return new Blob([H],{type:k})})(o.value);a("uploadSuccess",{source:o.value,data:x,filename:l})}return i({openModal:function(){p.value=!0},closeModal:function(){p.value=!1}}),(x,d)=>{const w=Ti,k=Ei,Y=zi,R=Wi,H=Ce,K=De,N=Oi;return U(),st("div",{onClick:d[8]||(d[8]=Ci(()=>{},["stop"]))},[B(N,{modelValue:v(p),"onUpdate:modelValue":d[7]||(d[7]=z=>Di(p)?p.value=z:null),canFullscreen:!1,title:v(r)("cropper.modalTitle"),maxHeight:"380px",width:"800px"},{footer:L(()=>[B(K,{type:"primary",onClick:D},{default:L(()=>[xe(Me(v(r)("cropper.okText")),1)]),_:1})]),default:L(()=>[et("div",{class:F(v(s))},[et("div",{class:F(`${v(s)}-left`)},[et("div",{class:F(`${v(s)}-cropper`)},[v(c)?(U(),Dt(v(ia),{key:0,circled:t.circled,src:v(c),height:"300px",onCropend:b,onReady:C},null,8,["circled","src"])):ht("",!0)],2),et("div",{class:F(`${v(s)}-toolbar`)},[B(Y,{beforeUpload:g,fileList:[],accept:"image/*"},{default:L(()=>[B(k,{content:v(r)("cropper.selectImage"),placement:"bottom"},{default:L(()=>[B(w,{preIcon:"ant-design:upload-outlined",type:"primary"})]),_:1},8,["content"])]),_:1}),B(R,null,{default:L(()=>[B(k,{content:v(r)("cropper.btn_reset"),placement:"bottom"},{default:L(()=>[B(w,{disabled:!v(c),preIcon:"ant-design:reload-outlined",size:"small",type:"primary",onClick:d[0]||(d[0]=z=>y("reset"))},null,8,["disabled"])]),_:1},8,["content"]),B(k,{content:v(r)("cropper.btn_rotate_left"),placement:"bottom"},{default:L(()=>[B(w,{disabled:!v(c),preIcon:"ant-design:rotate-left-outlined",size:"small",type:"primary",onClick:d[1]||(d[1]=z=>y("rotate",-45))},null,8,["disabled"])]),_:1},8,["content"]),B(k,{content:v(r)("cropper.btn_rotate_right"),placement:"bottom"},{default:L(()=>[B(w,{disabled:!v(c),preIcon:"ant-design:rotate-right-outlined",size:"small",type:"primary",onClick:d[2]||(d[2]=z=>y("rotate",45))},null,8,["disabled"])]),_:1},8,["content"]),B(k,{content:v(r)("cropper.btn_scale_x"),placement:"bottom"},{default:L(()=>[B(w,{disabled:!v(c),preIcon:"vaadin:arrows-long-h",size:"small",type:"primary",onClick:d[3]||(d[3]=z=>y("scaleX"))},null,8,["disabled"])]),_:1},8,["content"]),B(k,{content:v(r)("cropper.btn_scale_y"),placement:"bottom"},{default:L(()=>[B(w,{disabled:!v(c),preIcon:"vaadin:arrows-long-v",size:"small",type:"primary",onClick:d[4]||(d[4]=z=>y("scaleY"))},null,8,["disabled"])]),_:1},8,["content"]),B(k,{content:v(r)("cropper.btn_zoom_in"),placement:"bottom"},{default:L(()=>[B(w,{disabled:!v(c),preIcon:"ant-design:zoom-in-outlined",size:"small",type:"primary",onClick:d[5]||(d[5]=z=>y("zoom",.1))},null,8,["disabled"])]),_:1},8,["content"]),B(k,{content:v(r)("cropper.btn_zoom_out"),placement:"bottom"},{default:L(()=>[B(w,{disabled:!v(c),preIcon:"ant-design:zoom-out-outlined",size:"small",type:"primary",onClick:d[6]||(d[6]=z=>y("zoom",-.1))},null,8,["disabled"])]),_:1},8,["content"])]),_:1})],2)],2),et("div",{class:F(`${v(s)}-right`)},[et("div",{class:F(`${v(s)}-preview`)},[v(o)?(U(),st("img",{key:0,alt:v(r)("cropper.preview"),src:v(o)},null,8,aa)):ht("",!0)],2),v(o)?(U(),st("div",{key:0,class:F(`${v(s)}-group`)},[B(H,{src:v(o),size:"large"},null,8,["src"]),B(H,{size:48,src:v(o)},null,8,["src"]),B(H,{size:64,src:v(o)},null,8,["src"]),B(H,{size:80,src:v(o)},null,8,["src"])],2)):ht("",!0)],2)],2)]),_:1},8,["modelValue","title"])])}}}),ra=we(Ct({name:"CropperAvatar",__name:"CropperAvatar",props:{width:X.string.def("200px"),value:X.string.def(""),showBtn:X.bool.def(!0),btnText:X.string.def("")},emits:["update:value","change"],setup(t,{expose:i,emit:e}){const n=t,a=e,r=P(n.value),{getPrefixCls:h}=Wt(),s=h("cropper-avatar"),c=mi(),{t:o}=be.useI18n(),u=P();function p({source:m,data:f,filename:g}){r.value=m,a("change",{source:m,data:f,filename:g}),c.success(o("cropper.uploadSuccess"))}function l(){u.value.openModal()}return ki(()=>{r.value=n.value}),Bi(()=>r.value,m=>{a("update:value",m)}),i({open:l,close:function(){u.value.closeModal()}}),(m,f)=>{const g=Ce,b=De;return U(),st("div",{class:"user-info-head",onClick:f[1]||(f[1]=C=>l())},[v(r)?(U(),Dt(g,{key:0,src:v(r),alt:"avatar",class:"img-circle img-lg"},null,8,["src"])):ht("",!0),v(r)?ht("",!0):(U(),Dt(g,{key:1,src:v(Hi),alt:"avatar",class:"img-circle img-lg"},null,8,["src"])),t.showBtn?(U(),Dt(b,{key:2,class:F(`${v(s)}-upload-btn`),onClick:f[0]||(f[0]=C=>l())},{default:L(()=>[xe(Me(t.btnText?t.btnText:v(o)("cropper.selectImage")),1)]),_:1},8,["class"])):ht("",!0),B(na,{ref_key:"cropperModelRef",ref:u,srcValue:v(r),onUploadSuccess:p},null,8,["srcValue"])])}}}),[["__scopeId","data-v-e5ea03b5"]]),oa={class:"change-avatar"},sa=we(Ct({name:"UserAvatar",__name:"UserAvatar",props:{img:X.string.def("")},setup(t){const i=gi(),e=P(),n=async({data:a})=>{const{httpRequest:r}=fi(),h=(await r({file:a,filename:"avatar.png"})).data;await vi({avatar:h}),e.value.close(),await i.setUserAvatarAction(h)};return(a,r)=>(U(),st("div",oa,[B(v(ra),{ref_key:"cropperRef",ref:e,btnProps:{preIcon:"ant-design:cloud-upload-outlined"},showBtn:!1,value:t.img,width:"120px",onChange:n},null,8,["value"])]))}}),[["__scopeId","data-v-92270c8e"]]);export{sa as default};
