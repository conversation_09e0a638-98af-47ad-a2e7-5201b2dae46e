import{p as j,d as C,H}from"./index-Byekp3Iv.js";import{_ as z}from"./Dialog.vue_vue_type_style_index_0_lang-DhV_HFpU.js";import{g as A}from"./spu-BqmQYIjx.js";import{Z as B,_ as E,u as F,al as N,ak as Z}from"./form-designer-C0ARe9Dh.js";import{k as q,r as p,e as D,b as G,y as f,m as h,z as a,A as J,u as s,H as t,h as _,E as c,F as b}from"./form-create-B86qX0W_.js";const K=q({name:"SkuTableSelect",__name:"SkuTableSelect",props:{spuId:j.number.def(null)},emits:["change"],setup(g,{expose:y,emit:V}){const i=g;C();const v=p([]),m=p(!1),o=p(!1),r=p(),k=V;return y({open:()=>{o.value=!0}}),D(async()=>{}),G(()=>i.spuId,()=>{i.spuId&&(async()=>{m.value=!0;try{const w=await A(i.spuId);v.value=w.skus}finally{m.value=!1}})()}),(w,l)=>{const x=F,n=E,I=N,S=z,U=Z;return h(),f(S,{modelValue:s(o),"onUpdate:modelValue":l[1]||(l[1]=e=>_(o)?o.value=e:null),appendToBody:!0,title:"\u9009\u62E9\u89C4\u683C",width:"700"},{default:a(()=>[J((h(),f(s(B),{data:s(v),"show-overflow-tooltip":""},{default:a(()=>[t(n,{label:"#",width:"55"},{default:a(({row:e})=>[t(x,{value:e.id,modelValue:s(r),"onUpdate:modelValue":l[0]||(l[0]=u=>_(r)?r.value=u:null),onChange:u=>(d=>{k("change",d),o.value=!1,r.value=void 0})(e)},{default:a(()=>l[2]||(l[2]=[c("\xA0 ")])),_:2},1032,["value","modelValue","onChange"])]),_:1}),t(n,{label:"\u56FE\u7247","min-width":"80"},{default:a(({row:e})=>[t(I,{src:e.picUrl,class:"h-30px w-30px","preview-src-list":[e.picUrl],"preview-teleported":""},null,8,["src","preview-src-list"])]),_:1}),t(n,{label:"\u89C4\u683C",align:"center","min-width":"80"},{default:a(({row:e})=>{var u,d;return[c(b((d=(u=e.properties)==null?void 0:u.map(T=>T.valueName))==null?void 0:d.join(" ")),1)]}),_:1}),t(n,{align:"center",label:"\u9500\u552E\u4EF7(\u5143)","min-width":"80"},{default:a(({row:e})=>[c(b(s(H)(e.price)),1)]),_:1})]),_:1},8,["data"])),[[U,s(m)]])]),_:1},8,["modelValue"])}}});export{K as _};
