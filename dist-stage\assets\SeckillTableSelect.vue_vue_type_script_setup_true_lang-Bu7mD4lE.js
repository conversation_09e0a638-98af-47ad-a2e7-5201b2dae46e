import{p as ue,h as ie,D as j,_ as re,H as ne}from"./index-Byekp3Iv.js";import{_ as se}from"./Dialog.vue_vue_type_style_index_0_lang-DhV_HFpU.js";import{_ as de}from"./ContentWrap.vue_vue_type_script_setup_true_lang-D3mNNG_R.js";import{_ as me}from"./index.vue_vue_type_script_setup_true_lang-BeMNDf6p.js";import{_ as pe}from"./DictTag.vue_vue_type_script_lang-DdZ_pRVv.js";import{k as ve,r as n,e as ce,y as b,m as w,_ as fe,z as u,H as l,E as p,A as he,u as t,Z as we,l as _e,G as be,$ as ge,h as S,F as x}from"./form-create-B86qX0W_.js";import{h as Ve}from"./tree-COGD3qag.js";import{g as ye}from"./category-Tlk-MRkP.js";import{a as ke}from"./seckillActivity-BUPSa-wC.js";import{b as Ce}from"./formatter-CF7Ifi5S.js";import{f as B,d as Se}from"./formatTime-HVkyL6Kg.js";import{f as xe,h as Ue,aj as Te,k as Me,x as Ne,Q as Ye,Z as ze,_ as De,w as Oe,u as Ee,al as Pe,ak as Ae,bi as F}from"./form-designer-C0ARe9Dh.js";const je=ve({name:"SeckillTableSelect",__name:"SeckillTableSelect",props:{multiple:ue.bool.def(!1)},emits:["change"],setup(U,{expose:H,emit:I}){const T=n(0),v=n([]),C=n(!1),m=n(!1),r=n({pageNo:1,pageSize:10,name:null,status:void 0});H({open:o=>{c.value=[],s.value={},d.value=!1,V.value=!1,o&&o.length>0&&(c.value=[...o],s.value=Object.fromEntries(o.map(e=>[e.id,!0]))),m.value=!0,N()}});const g=async()=>{C.value=!0;try{const o=await ke(r.value);v.value=o.list,T.value=o.total,v.value.forEach(e=>s.value[e.id]=s.value[e.id]||!1),D()}finally{C.value=!1}},M=()=>{r.value.pageNo=1,g()},N=()=>{r.value={pageNo:1,pageSize:10,name:void 0,createTime:[]},g()},Z=o=>{const e=Math.min(...o.map(_=>_.seckillPrice));return`\uFFE5${ne(e)}`},d=n(!1),V=n(!1),c=n([]),s=n({}),y=n(),$=()=>{m.value=!1,Y(F,[...c.value])},Y=I,q=o=>{d.value=o,V.value=!1,v.value.forEach(e=>z(o,e,!1))},z=(o,e,_)=>{if(o)c.value.push(e),s.value[e.id]=!0;else{const f=G(e);f>-1&&(c.value.splice(f,1),s.value[e.id]=!1,d.value=!1)}_&&D()},G=o=>c.value.findIndex(e=>e.id===o.id),D=()=>{d.value=v.value.every(o=>s.value[o.id]),V.value=!d.value&&v.value.some(o=>s.value[o.id])},O=n(),K=n();return ce(async()=>{await g(),O.value=await ye({}),K.value=Ve(O.value,"id","parentId")}),(o,e)=>{const _=Me,f=Te,Q=Ye,R=Ne,E=re,k=xe,J=Ue,P=Oe,i=De,L=Ee,W=Pe,X=pe,ee=ze,ae=me,le=de,te=se,oe=Ae;return w(),b(te,{modelValue:t(m),"onUpdate:modelValue":e[7]||(e[7]=a=>S(m)?m.value=a:null),appendToBody:!0,title:"\u9009\u62E9\u6D3B\u52A8",width:"70%"},fe({default:u(()=>[l(le,null,{default:u(()=>[l(J,{ref:"queryFormRef",inline:!0,model:t(r),class:"-mb-15px","label-width":"68px"},{default:u(()=>[l(f,{label:"\u6D3B\u52A8\u540D\u79F0",prop:"name"},{default:u(()=>[l(_,{modelValue:t(r).name,"onUpdate:modelValue":e[0]||(e[0]=a=>t(r).name=a),placeholder:"\u8BF7\u8F93\u5165\u6D3B\u52A8\u540D\u79F0",clearable:"",onKeyup:we(M,["enter"]),class:"!w-240px"},null,8,["modelValue"])]),_:1}),l(f,{label:"\u6D3B\u52A8\u72B6\u6001",prop:"status"},{default:u(()=>[l(R,{modelValue:t(r).status,"onUpdate:modelValue":e[1]||(e[1]=a=>t(r).status=a),placeholder:"\u8BF7\u9009\u62E9\u6D3B\u52A8\u72B6\u6001",clearable:"",class:"!w-240px"},{default:u(()=>[(w(!0),_e(be,null,ge(t(ie)(t(j).COMMON_STATUS),a=>(w(),b(Q,{key:a.value,label:a.label,value:a.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),l(f,null,{default:u(()=>[l(k,{onClick:M},{default:u(()=>[l(E,{class:"mr-5px",icon:"ep:search"}),e[8]||(e[8]=p(" \u641C\u7D22 "))]),_:1}),l(k,{onClick:N},{default:u(()=>[l(E,{class:"mr-5px",icon:"ep:refresh"}),e[9]||(e[9]=p(" \u91CD\u7F6E "))]),_:1})]),_:1})]),_:1},8,["model"]),he((w(),b(ee,{data:t(v),"show-overflow-tooltip":""},{default:u(()=>[U.multiple?(w(),b(i,{key:0,width:"55"},{header:u(()=>[l(P,{modelValue:t(d),"onUpdate:modelValue":e[2]||(e[2]=a=>S(d)?d.value=a:null),indeterminate:t(V),onChange:q},null,8,["modelValue","indeterminate"])]),default:u(({row:a})=>[l(P,{modelValue:t(s)[a.id],"onUpdate:modelValue":h=>t(s)[a.id]=h,onChange:h=>z(h,a,!0)},null,8,["modelValue","onUpdate:modelValue","onChange"])]),_:1})):(w(),b(i,{key:1,label:"#",width:"55"},{default:u(({row:a})=>[l(L,{value:a.id,modelValue:t(y),"onUpdate:modelValue":e[3]||(e[3]=h=>S(y)?y.value=h:null),onChange:h=>{return Y(F,A=a),m.value=!1,void(y.value=A.id);var A}},{default:u(()=>e[10]||(e[10]=[p(" \xA0 ")])),_:2},1032,["value","modelValue","onChange"])]),_:1})),l(i,{label:"\u6D3B\u52A8\u7F16\u53F7",prop:"id","min-width":"80"}),l(i,{label:"\u6D3B\u52A8\u540D\u79F0",prop:"name","min-width":"140"}),l(i,{label:"\u6D3B\u52A8\u65F6\u95F4","min-width":"210"},{default:u(a=>[p(x(t(B)(a.row.startTime,"YYYY-MM-DD"))+" ~ "+x(t(B)(a.row.endTime,"YYYY-MM-DD")),1)]),_:1}),l(i,{label:"\u5546\u54C1\u56FE\u7247",prop:"spuName","min-width":"80"},{default:u(a=>[l(W,{src:a.row.picUrl,class:"h-40px w-40px","preview-src-list":[a.row.picUrl],"preview-teleported":""},null,8,["src","preview-src-list"])]),_:1}),l(i,{label:"\u5546\u54C1\u6807\u9898",prop:"spuName","min-width":"300"}),l(i,{label:"\u539F\u4EF7",prop:"marketPrice","min-width":"100",formatter:t(Ce)},null,8,["formatter"]),l(i,{label:"\u62FC\u56E2\u4EF7",prop:"seckillPrice","min-width":"100"},{default:u(a=>[p(x(Z(a.row.products)),1)]),_:1}),l(i,{label:"\u5F00\u56E2\u7EC4\u6570",prop:"groupCount","min-width":"100"}),l(i,{label:"\u6210\u56E2\u7EC4\u6570",prop:"groupSuccessCount","min-width":"100"}),l(i,{label:"\u8D2D\u4E70\u6B21\u6570",prop:"recordCount","min-width":"100"}),l(i,{label:"\u6D3B\u52A8\u72B6\u6001",align:"center",prop:"status","min-width":"100"},{default:u(a=>[l(X,{type:t(j).COMMON_STATUS,value:a.row.status},null,8,["type","value"])]),_:1}),l(i,{label:"\u521B\u5EFA\u65F6\u95F4",align:"center",prop:"createTime",formatter:t(Se),width:"180px"},null,8,["formatter"])]),_:1},8,["data"])),[[oe,t(C)]]),l(ae,{limit:t(r).pageSize,"onUpdate:limit":e[4]||(e[4]=a=>t(r).pageSize=a),page:t(r).pageNo,"onUpdate:page":e[5]||(e[5]=a=>t(r).pageNo=a),total:t(T),onPagination:g},null,8,["limit","page","total"])]),_:1})]),_:2},[U.multiple?{name:"footer",fn:u(()=>[l(k,{type:"primary",onClick:$},{default:u(()=>e[11]||(e[11]=[p("\u786E \u5B9A")])),_:1}),l(k,{onClick:e[6]||(e[6]=a=>m.value=!1)},{default:u(()=>e[12]||(e[12]=[p("\u53D6 \u6D88")])),_:1})]),key:"0"}:void 0]),1032,["modelValue"])}}});export{je as _};
