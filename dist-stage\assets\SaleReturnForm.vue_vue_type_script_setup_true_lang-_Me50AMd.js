import{a as le,d as te,_ as oe,X as re,ax as g,ay as ue}from"./index-Byekp3Iv.js";import{_ as de}from"./Dialog.vue_vue_type_style_index_0_lang-DhV_HFpU.js";import{_ as se}from"./ContentWrap.vue_vue_type_script_setup_true_lang-D3mNNG_R.js";import{S}from"./index-C4tM2b7F.js";import{_ as ie}from"./SaleReturnItemForm.vue_vue_type_script_setup_true_lang-C9lGdP__.js";import{C as ne}from"./index-C7EeEAX_.js";import{A as me}from"./index-BlTZBlHS.js";import{_ as ce}from"./SaleOrderReturnEnableList.vue_vue_type_script_setup_true_lang-XmS99p0_.js";import{g as pe}from"./index-DP9Sf6UT.js";import{h as fe,i as ve,j as _e,aj as be,k as Ve,F as Pe,f as Ue,x as Ie,Q as he,a0 as ke,$ as ye,l as we,ak as ge}from"./form-designer-C0ARe9Dh.js";import{k as Se,r as i,P as xe,c as Re,b as Ce,l as v,m,G as _,H as a,u as t,h as q,z as o,A as Te,y as f,E as x,$ as R,C as Fe}from"./form-create-B86qX0W_.js";const Ne=Se({name:"SaleReturnForm",__name:"SaleReturnForm",emits:["success"],setup(je,{expose:G,emit:Q}){const{t:b}=le(),C=te(),c=i(!1),T=i(""),p=i(!1),V=i(""),l=i({id:void 0,customerId:void 0,accountId:void 0,saleUserId:void 0,returnTime:void 0,remark:void 0,fileUrl:"",discountPercent:0,discountPrice:0,totalPrice:0,otherPrice:0,orderNo:void 0,items:[],no:void 0}),$=xe({customerId:[{required:!0,message:"\u5BA2\u6237\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],returnTime:[{required:!0,message:"\u9000\u8D27\u65F6\u95F4\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}]}),P=Re(()=>V.value==="detail"),U=i(),F=i([]),I=i([]),N=i([]),h=i("item"),j=i();Ce(()=>l.value,u=>{if(!u)return;const e=u.items.reduce((d,s)=>d+s.totalPrice,0),n=u.discountPercent!=null?ue(e,u.discountPercent/100):0;l.value.totalPrice=e-n+u.otherPrice},{deep:!0}),G({open:async(u,e)=>{if(c.value=!0,T.value=b("action."+u),V.value=u,W(),e){p.value=!0;try{l.value=await S.getSaleReturn(e)}finally{p.value=!1}}F.value=await ne.getCustomerSimpleList(),N.value=await pe(),I.value=await me.getAccountSimpleList();const n=I.value.find(d=>d.defaultStatus);n&&(l.value.accountId=n.id)}});const A=i(),z=()=>{A.value.open()},H=u=>{l.value.orderId=u.id,l.value.orderNo=u.no,l.value.customerId=u.customerId,l.value.accountId=u.accountId,l.value.saleUserId=u.saleUserId,l.value.discountPercent=u.discountPercent,l.value.remark=u.remark,l.value.fileUrl=u.fileUrl,u.items.forEach(e=>{e.count=e.outCount-e.returnCount,e.orderItemId=e.id,e.id=void 0}),l.value.items=u.items.filter(e=>e.count>0)},K=Q,O=async()=>{await U.value.validate(),await j.value.validate(),p.value=!0;try{const u=l.value;V.value==="create"?(await S.createSaleReturn(u),C.success(b("common.createSuccess"))):(await S.updateSaleReturn(u),C.success(b("common.updateSuccess"))),c.value=!1,K("success")}finally{p.value=!1}},W=()=>{var u;l.value={id:void 0,customerId:void 0,accountId:void 0,saleUserId:void 0,returnTime:void 0,remark:void 0,fileUrl:void 0,discountPercent:0,discountPrice:0,totalPrice:0,otherPrice:0,items:[]},(u=U.value)==null||u.resetFields()};return(u,e)=>{const n=Ve,d=be,s=_e,X=Pe,B=oe,k=Ue,y=he,w=Ie,D=re,E=ve,J=ye,M=ke,Y=se,L=we,Z=fe,ee=de,ae=ge;return m(),v(_,null,[a(ee,{title:t(T),modelValue:t(c),"onUpdate:modelValue":e[14]||(e[14]=r=>q(c)?c.value=r:null),width:"1440"},{footer:o(()=>[t(P)?Fe("",!0):(m(),f(k,{key:0,onClick:O,type:"primary",disabled:t(p)},{default:o(()=>e[16]||(e[16]=[x(" \u786E \u5B9A ")])),_:1},8,["disabled"])),a(k,{onClick:e[13]||(e[13]=r=>c.value=!1)},{default:o(()=>e[17]||(e[17]=[x("\u53D6 \u6D88")])),_:1})]),default:o(()=>[Te((m(),f(Z,{ref_key:"formRef",ref:U,model:t(l),rules:t($),"label-width":"100px",disabled:t(P)},{default:o(()=>[a(E,{gutter:20},{default:o(()=>[a(s,{span:8},{default:o(()=>[a(d,{label:"\u9000\u8D27\u5355\u53F7",prop:"no"},{default:o(()=>[a(n,{disabled:"",modelValue:t(l).no,"onUpdate:modelValue":e[0]||(e[0]=r=>t(l).no=r),placeholder:"\u4FDD\u5B58\u65F6\u81EA\u52A8\u751F\u6210"},null,8,["modelValue"])]),_:1})]),_:1}),a(s,{span:8},{default:o(()=>[a(d,{label:"\u9000\u8D27\u65F6\u95F4",prop:"returnTime"},{default:o(()=>[a(X,{modelValue:t(l).returnTime,"onUpdate:modelValue":e[1]||(e[1]=r=>t(l).returnTime=r),type:"date","value-format":"x",placeholder:"\u9009\u62E9\u9000\u8D27\u65F6\u95F4",class:"!w-1/1"},null,8,["modelValue"])]),_:1})]),_:1}),a(s,{span:8},{default:o(()=>[a(d,{label:"\u5173\u8054\u8BA2\u5355",prop:"orderNo"},{default:o(()=>[a(n,{modelValue:t(l).orderNo,"onUpdate:modelValue":e[2]||(e[2]=r=>t(l).orderNo=r),readonly:""},{append:o(()=>[a(k,{onClick:z},{default:o(()=>[a(B,{icon:"ep:search"}),e[15]||(e[15]=x(" \u9009\u62E9 "))]),_:1})]),_:1},8,["modelValue"])]),_:1})]),_:1}),a(s,{span:8},{default:o(()=>[a(d,{label:"\u5BA2\u6237",prop:"customerId"},{default:o(()=>[a(w,{modelValue:t(l).customerId,"onUpdate:modelValue":e[3]||(e[3]=r=>t(l).customerId=r),clearable:"",filterable:"",disabled:"",placeholder:"\u8BF7\u9009\u62E9\u5BA2\u6237",class:"!w-1/1"},{default:o(()=>[(m(!0),v(_,null,R(t(F),r=>(m(),f(y,{key:r.id,label:r.name,value:r.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),a(s,{span:8},{default:o(()=>[a(d,{label:"\u9500\u552E\u4EBA\u5458",prop:"saleUserId"},{default:o(()=>[a(w,{modelValue:t(l).saleUserId,"onUpdate:modelValue":e[4]||(e[4]=r=>t(l).saleUserId=r),clearable:"",filterable:"",placeholder:"\u8BF7\u9009\u62E9\u9500\u552E\u4EBA\u5458",class:"!w-1/1"},{default:o(()=>[(m(!0),v(_,null,R(t(N),r=>(m(),f(y,{key:r.id,label:r.nickname,value:r.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),a(s,{span:16},{default:o(()=>[a(d,{label:"\u5907\u6CE8",prop:"remark"},{default:o(()=>[a(n,{type:"textarea",modelValue:t(l).remark,"onUpdate:modelValue":e[5]||(e[5]=r=>t(l).remark=r),rows:1,placeholder:"\u8BF7\u8F93\u5165\u5907\u6CE8"},null,8,["modelValue"])]),_:1})]),_:1}),a(s,{span:8},{default:o(()=>[a(d,{label:"\u9644\u4EF6",prop:"fileUrl"},{default:o(()=>[a(D,{"is-show-tip":!1,modelValue:t(l).fileUrl,"onUpdate:modelValue":e[6]||(e[6]=r=>t(l).fileUrl=r),limit:1},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),a(Y,null,{default:o(()=>[a(M,{modelValue:t(h),"onUpdate:modelValue":e[7]||(e[7]=r=>q(h)?h.value=r:null),class:"-mt-15px -mb-10px"},{default:o(()=>[a(J,{label:"\u9000\u8D27\u4EA7\u54C1\u6E05\u5355",name:"item"},{default:o(()=>[a(ie,{ref_key:"itemFormRef",ref:j,items:t(l).items,disabled:t(P)},null,8,["items","disabled"])]),_:1})]),_:1},8,["modelValue"])]),_:1}),a(E,{gutter:20},{default:o(()=>[a(s,{span:8},{default:o(()=>[a(d,{label:"\u4F18\u60E0\u7387\uFF08%\uFF09",prop:"discountPercent"},{default:o(()=>[a(L,{modelValue:t(l).discountPercent,"onUpdate:modelValue":e[8]||(e[8]=r=>t(l).discountPercent=r),"controls-position":"right",min:0,precision:2,placeholder:"\u8BF7\u8F93\u5165\u4F18\u60E0\u7387",class:"!w-1/1"},null,8,["modelValue"])]),_:1})]),_:1}),a(s,{span:8},{default:o(()=>[a(d,{label:"\u9000\u6B3E\u4F18\u60E0",prop:"discountPrice"},{default:o(()=>[a(n,{disabled:"",modelValue:t(l).discountPrice,"onUpdate:modelValue":e[9]||(e[9]=r=>t(l).discountPrice=r),formatter:t(g)},null,8,["modelValue","formatter"])]),_:1})]),_:1}),a(s,{span:8},{default:o(()=>[a(d,{label:"\u4F18\u60E0\u540E\u91D1\u989D"},{default:o(()=>[a(n,{disabled:"","model-value":t(l).totalPrice-t(l).otherPrice,formatter:t(g)},null,8,["model-value","formatter"])]),_:1})]),_:1}),a(s,{span:8},{default:o(()=>[a(d,{label:"\u5176\u5B83\u8D39\u7528",prop:"otherPrice"},{default:o(()=>[a(L,{modelValue:t(l).otherPrice,"onUpdate:modelValue":e[10]||(e[10]=r=>t(l).otherPrice=r),"controls-position":"right",min:0,precision:2,placeholder:"\u8BF7\u8F93\u5165\u5176\u5B83\u8D39\u7528",class:"!w-1/1"},null,8,["modelValue"])]),_:1})]),_:1}),a(s,{span:8},{default:o(()=>[a(d,{label:"\u7ED3\u7B97\u8D26\u6237",prop:"accountId"},{default:o(()=>[a(w,{modelValue:t(l).accountId,"onUpdate:modelValue":e[11]||(e[11]=r=>t(l).accountId=r),clearable:"",filterable:"",placeholder:"\u8BF7\u9009\u62E9\u7ED3\u7B97\u8D26\u6237",class:"!w-1/1"},{default:o(()=>[(m(!0),v(_,null,R(t(I),r=>(m(),f(y,{key:r.id,label:r.name,value:r.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),a(s,{span:8},{default:o(()=>[a(d,{label:"\u5E94\u9000\u91D1\u989D",prop:"totalPrice"},{default:o(()=>[a(n,{disabled:"",modelValue:t(l).totalPrice,"onUpdate:modelValue":e[12]||(e[12]=r=>t(l).totalPrice=r),formatter:t(g)},null,8,["modelValue","formatter"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model","rules","disabled"])),[[ae,t(p)]])]),_:1},8,["title","modelValue"]),a(ce,{ref_key:"saleOrderReturnEnableListRef",ref:A,onSuccess:H},null,512)],64)}}});export{Ne as _};
