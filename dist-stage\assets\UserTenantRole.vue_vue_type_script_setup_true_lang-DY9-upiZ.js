import{_ as i}from"./Dialog.vue_vue_type_style_index_0_lang-DhV_HFpU.js";import{_ as d}from"./index.vue_vue_type_script_setup_true_lang-DkS4DtCe.js";import{k as c,r as n,y as p,m as _,z as f,H as v,u,h as k}from"./form-create-B86qX0W_.js";const I=c({__name:"UserTenantRole",emits:["success"],setup(R,{expose:m,emit:V}){const a=n(!1),l=n(!1),e=n({id:-1,nickname:"",username:"",roleIds:[]});return m({open:async s=>{a.value=!0,e.value.id=s.id,e.value.username=s.username,e.value.nickname=s.nickname,l.value=!0}}),(s,r)=>{const o=i;return _(),p(o,{modelValue:u(a),"onUpdate:modelValue":r[0]||(r[0]=t=>k(a)?a.value=t:null),title:"\u5206\u914D\u79DF\u6237",width:"60%"},{default:f(()=>[v(d,{ref:"userTenantRoleRef",userId:u(e).id,userName:u(e).username},null,8,["userId","userName"])]),_:1},8,["modelValue"])}}});export{I as _};
