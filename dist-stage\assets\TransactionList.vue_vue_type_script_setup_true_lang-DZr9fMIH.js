import{a as D,d as L,D as g}from"./index-Byekp3Iv.js";import{_ as S}from"./ContentWrap.vue_vue_type_script_setup_true_lang-D3mNNG_R.js";import{_ as q}from"./DictTag.vue_vue_type_script_lang-DdZ_pRVv.js";import{d as I}from"./formatTime-HVkyL6Kg.js";import{S as x}from"./index-BMktDmkp.js";import{g as z}from"./commonBiz-BtTo-n4b.js";import{f}from"./formatter-CF7Ifi5S.js";import{ak as B,Z as R,_ as k}from"./form-designer-C0ARe9Dh.js";import{k as M,r as v,b as O,e as Y,y as T,m as b,z as l,A as P,l as W,C as Z,u as i,H as a,E as y,F as w}from"./form-create-B86qX0W_.js";const j={key:0,class:"p-4 text-center text-gray-500"},F=M({__name:"TransactionList",props:{inventoryId:{}},setup(E){const{t:H}=D(),Q=L(),u=E,d=v(!1),p=v([]),c=v(new Map),_=async()=>{try{const e=await z();if(!e||e.length===0)return;e.forEach(n=>{n&&n.id&&n.name&&c.value.set(n.id,n.name)})}catch{}},A=e=>{if(!e&&e!==0)return"";if(typeof e=="number"){const n=c.value.get(e);if(n)return n}if(typeof e=="string"){for(const[t,o]of c.value)if(t.toString()===e)return o;const n=parseInt(e);if(!isNaN(n)){const t=c.value.get(n);if(t)return t}}return e.toString()},N=async()=>{if(u.inventoryId){d.value=!0;try{c.value.size===0&&await _();const e=await x.getTransactionListByInventoryId(u.inventoryId);p.value=e||[]}catch{p.value=[],Q.error("\u83B7\u53D6\u5386\u53F2\u4EA4\u6613\u8BB0\u5F55\u5931\u8D25")}finally{d.value=!1}}else p.value=[]},C=({columns:e,data:n})=>{const t=[];return e.forEach((o,s)=>{if(s===0)return void(t[s]="\u5408\u8BA1");if(["quantity","auxiliaryQuantity","beforeQuantity","afterQuantity"].includes(o.property)){const h=n.map(m=>Number(m[o.property])||0).reduce((m,r)=>m+r,0);t[s]=f(h)}else t[s]=""}),t};return O(()=>u.inventoryId,()=>{u.inventoryId?N():p.value=[]},{immediate:!0}),Y(async()=>{await _(),u.inventoryId&&N()}),(e,n)=>{const t=k,o=q,s=R,h=S,m=B;return b(),T(h,null,{default:l(()=>[P((b(),T(s,{data:i(p),stripe:!0,"show-overflow-tooltip":!0,border:"",size:"small","show-summary":"","summary-method":C},{default:l(()=>[a(t,{label:"\u4E1A\u52A1\u5355\u53F7",align:"center",prop:"bizNo",width:"150"}),a(t,{label:"\u4EA4\u6613\u7C7B\u578B",align:"center",prop:"transactionType",width:"100"},{default:l(r=>[a(o,{type:i(g).SCM_BIZ_TYPE,value:r.row.transactionType},null,8,["type","value"])]),_:1}),a(t,{label:"\u4EA4\u6613\u65B9\u5411",align:"center",prop:"transactionDirection",width:"100"},{default:l(r=>[a(o,{type:i(g).INVENTORY_TRANSACTION_DIRECTION,value:r.row.transactionDirection},null,8,["type","value"])]),_:1}),a(t,{label:"\u7269\u6599\u7F16\u7801",align:"center",prop:"materialCode",width:"120"}),a(t,{label:"\u7269\u6599\u540D\u79F0",align:"left",prop:"materialName",width:"200"}),a(t,{label:"\u7269\u6599\u7C7B\u578B",align:"center",prop:"materialType",width:"100"},{default:l(r=>[a(o,{type:i(g).MATERIAL_TYPE,value:r.row.materialType},null,8,["type","value"])]),_:1}),a(t,{label:"\u79FB\u52A8\u65E5\u671F",align:"center",prop:"moveDate",formatter:i(I),width:"140"},null,8,["formatter"]),a(t,{label:"\u5E93\u5B58\u6279\u53F7",align:"center",prop:"inventoryBatchNo",width:"120"}),a(t,{label:"\u6570\u91CF",align:"center",prop:"quantity",width:"100"},{default:l(r=>[y(w(i(f)(r.row.quantity)),1)]),_:1}),a(t,{label:"\u5355\u4F4D",align:"center",width:"80"},{default:l(r=>[y(w(A(r.row.quantityUnit)),1)]),_:1}),a(t,{label:"\u51FA\u5165\u5E93\u524D\u6570\u91CF",align:"center",prop:"beforeQuantity",width:"120"},{default:l(r=>[y(w(i(f)(r.row.beforeQuantity)),1)]),_:1}),a(t,{label:"\u51FA\u5165\u5E93\u540E\u6570\u91CF",align:"center",prop:"afterQuantity",width:"120"},{default:l(r=>[y(w(i(f)(r.row.afterQuantity)),1)]),_:1}),a(t,{label:"\u6458\u8981",align:"center",prop:"summary",width:"150"}),a(t,{label:"\u79FB\u52A8\u7C7B\u578B",align:"center",prop:"moveType",width:"100"}),a(t,{label:"\u79FB\u52A8\u6E90\u4ED3\u5E93",align:"center",prop:"fromWarehouseName",width:"120"}),a(t,{label:"\u79FB\u52A8\u6E90\u4ED3\u4F4D",align:"center",prop:"fromLocationName",width:"120"}),a(t,{label:"\u79FB\u52A8\u5230\u4ED3\u5E93",align:"center",prop:"toWarehouseName",width:"120"}),a(t,{label:"\u79FB\u52A8\u5230\u4ED3\u4F4D",align:"center",prop:"toLocationName",width:"120"}),a(t,{label:"\u6765\u6E90\u5355\u53F7",align:"center",prop:"sourceNo",width:"150"}),a(t,{label:"\u521B\u5EFA\u65F6\u95F4",align:"center",prop:"createTime",formatter:i(I),width:"160"},null,8,["formatter"])]),_:1},8,["data"])),[[m,i(d)]]),i(d)||i(p).length!==0?Z("",!0):(b(),W("div",j," \u6682\u65E0\u5386\u53F2\u4EA4\u6613\u8BB0\u5F55 "))]),_:1})}}});export{F as _};
