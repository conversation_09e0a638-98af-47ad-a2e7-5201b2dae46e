import{d as B,_ as F,as as O}from"./index-Byekp3Iv.js";import{k as C,P as R,b as A,l as N,e as z,m as T,v as E,u as g,M as $,N as j,r as W,y as V,h as I,z as _,H as y,E as M}from"./form-create-B86qX0W_.js";import{d as q}from"./download-oWiM5xVU.js";import{v as H,f as G}from"./form-designer-C0ARe9Dh.js";var X=Object.defineProperty,Y=Object.prototype.hasOwnProperty,L=Object.getOwnPropertySymbols,J=Object.prototype.propertyIsEnumerable,U=(p,e,t)=>e in p?X(p,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):p[e]=t,k=(p,e)=>{for(var t in e||(e={}))Y.call(e,t)&&U(p,t,e[t]);if(L)for(var t of L(e))J.call(e,t)&&U(p,t,e[t]);return p};/*!
 * Signature Pad v3.0.0-beta.4 | https://github.com/szimek/signature_pad
 * (c) 2020 Szymon Nowak | Released under the MIT license
 */class f{constructor(e,t,s){this.x=e,this.y=t,this.time=s||Date.now()}distanceTo(e){return Math.sqrt(Math.pow(this.x-e.x,2)+Math.pow(this.y-e.y,2))}equals(e){return this.x===e.x&&this.y===e.y&&this.time===e.time}velocityFrom(e){return this.time!==e.time?this.distanceTo(e)/(this.time-e.time):0}}class P{constructor(e,t,s,i,o,l){this.startPoint=e,this.control2=t,this.control1=s,this.endPoint=i,this.startWidth=o,this.endWidth=l}static fromPoints(e,t){const s=this.calculateControlPoints(e[0],e[1],e[2]).c2,i=this.calculateControlPoints(e[1],e[2],e[3]).c1;return new P(e[1],s,i,e[2],t.start,t.end)}static calculateControlPoints(e,t,s){const i=e.x-t.x,o=e.y-t.y,l=t.x-s.x,r=t.y-s.y,c=(e.x+t.x)/2,n=(e.y+t.y)/2,h=(t.x+s.x)/2,a=(t.y+s.y)/2,m=Math.sqrt(i*i+o*o),d=Math.sqrt(l*l+r*r),u=d/(m+d),v=h+(c-h)*u,x=a+(n-a)*u,D=t.x-v,S=t.y-x;return{c1:new f(c+D,n+S),c2:new f(h+D,a+S)}}length(){let e,t,s=0;for(let i=0;i<=10;i+=1){const o=i/10,l=this.point(o,this.startPoint.x,this.control1.x,this.control2.x,this.endPoint.x),r=this.point(o,this.startPoint.y,this.control1.y,this.control2.y,this.endPoint.y);if(i>0){const c=l-e,n=r-t;s+=Math.sqrt(c*c+n*n)}e=l,t=r}return s}point(e,t,s,i,o){return t*(1-e)*(1-e)*(1-e)+3*s*(1-e)*(1-e)*e+3*i*(1-e)*e*e+o*e*e*e}}class b{constructor(e,t={}){this.canvas=e,this.options=t,this._handleMouseDown=s=>{s.which===1&&(this._mouseButtonDown=!0,this._strokeBegin(s))},this._handleMouseMove=s=>{this._mouseButtonDown&&this._strokeMoveUpdate(s)},this._handleMouseUp=s=>{s.which===1&&this._mouseButtonDown&&(this._mouseButtonDown=!1,this._strokeEnd(s))},this._handleTouchStart=s=>{if(s.preventDefault(),s.targetTouches.length===1){const i=s.changedTouches[0];this._strokeBegin(i)}},this._handleTouchMove=s=>{s.preventDefault();const i=s.targetTouches[0];this._strokeMoveUpdate(i)},this._handleTouchEnd=s=>{if(s.target===this.canvas){s.preventDefault();const i=s.changedTouches[0];this._strokeEnd(i)}},this.velocityFilterWeight=t.velocityFilterWeight||.7,this.minWidth=t.minWidth||.5,this.maxWidth=t.maxWidth||2.5,this.throttle="throttle"in t?t.throttle:16,this.minDistance="minDistance"in t?t.minDistance:5,this.dotSize=t.dotSize||function(){return(this.minWidth+this.maxWidth)/2},this.penColor=t.penColor||"black",this.backgroundColor=t.backgroundColor||"rgba(0,0,0,0)",this.onBegin=t.onBegin,this.onEnd=t.onEnd,this._strokeMoveUpdate=this.throttle?function(s,i=250){let o,l,r,c=0,n=null;const h=()=>{c=Date.now(),n=null,o=s.apply(l,r),n||(l=null,r=[])};return function(...a){const m=Date.now(),d=i-(m-c);return l=this,r=a,d<=0||d>i?(n&&(clearTimeout(n),n=null),c=m,o=s.apply(l,r),n||(l=null,r=[])):n||(n=window.setTimeout(h,d)),o}}(b.prototype._strokeUpdate,this.throttle):b.prototype._strokeUpdate,this._ctx=e.getContext("2d"),this.clear(),this.on()}clear(){const{_ctx:e,canvas:t}=this;e.fillStyle=this.backgroundColor,e.clearRect(0,0,t.width,t.height),e.fillRect(0,0,t.width,t.height),this._data=[],this._reset(),this._isEmpty=!0}fromDataURL(e,t={},s){const i=new Image,o=t.ratio||window.devicePixelRatio||1,l=t.width||this.canvas.width/o,r=t.height||this.canvas.height/o;this._reset(),i.onload=()=>{this._ctx.drawImage(i,0,0,l,r),s&&s()},i.onerror=c=>{s&&s(c)},i.src=e,this._isEmpty=!1}toDataURL(e="image/png",t){return e==="image/svg+xml"?this._toSVG():this.canvas.toDataURL(e,t)}on(){this.canvas.style.touchAction="none",this.canvas.style.msTouchAction="none",window.PointerEvent?this._handlePointerEvents():(this._handleMouseEvents(),"ontouchstart"in window&&this._handleTouchEvents())}off(){this.canvas.style.touchAction="auto",this.canvas.style.msTouchAction="auto",this.canvas.removeEventListener("pointerdown",this._handleMouseDown),this.canvas.removeEventListener("pointermove",this._handleMouseMove),document.removeEventListener("pointerup",this._handleMouseUp),this.canvas.removeEventListener("mousedown",this._handleMouseDown),this.canvas.removeEventListener("mousemove",this._handleMouseMove),document.removeEventListener("mouseup",this._handleMouseUp),this.canvas.removeEventListener("touchstart",this._handleTouchStart),this.canvas.removeEventListener("touchmove",this._handleTouchMove),this.canvas.removeEventListener("touchend",this._handleTouchEnd)}isEmpty(){return this._isEmpty}fromData(e){this.clear(),this._fromData(e,({color:t,curve:s})=>this._drawCurve({color:t,curve:s}),({color:t,point:s})=>this._drawDot({color:t,point:s})),this._data=e}toData(){return this._data}_strokeBegin(e){const t={color:this.penColor,points:[]};typeof this.onBegin=="function"&&this.onBegin(e),this._data.push(t),this._reset(),this._strokeUpdate(e)}_strokeUpdate(e){if(this._data.length===0)return void this._strokeBegin(e);const t=e.clientX,s=e.clientY,i=this._createPoint(t,s),o=this._data[this._data.length-1],l=o.points,r=l.length>0&&l[l.length-1],c=!!r&&i.distanceTo(r)<=this.minDistance,n=o.color;if(!r||!r||!c){const h=this._addPoint(i);r?h&&this._drawCurve({color:n,curve:h}):this._drawDot({color:n,point:i}),l.push({time:i.time,x:i.x,y:i.y})}}_strokeEnd(e){this._strokeUpdate(e),typeof this.onEnd=="function"&&this.onEnd(e)}_handlePointerEvents(){this._mouseButtonDown=!1,this.canvas.addEventListener("pointerdown",this._handleMouseDown),this.canvas.addEventListener("pointermove",this._handleMouseMove),document.addEventListener("pointerup",this._handleMouseUp)}_handleMouseEvents(){this._mouseButtonDown=!1,this.canvas.addEventListener("mousedown",this._handleMouseDown),this.canvas.addEventListener("mousemove",this._handleMouseMove),document.addEventListener("mouseup",this._handleMouseUp)}_handleTouchEvents(){this.canvas.addEventListener("touchstart",this._handleTouchStart),this.canvas.addEventListener("touchmove",this._handleTouchMove),this.canvas.addEventListener("touchend",this._handleTouchEnd)}_reset(){this._lastPoints=[],this._lastVelocity=0,this._lastWidth=(this.minWidth+this.maxWidth)/2,this._ctx.fillStyle=this.penColor}_createPoint(e,t){const s=this.canvas.getBoundingClientRect();return new f(e-s.left,t-s.top,new Date().getTime())}_addPoint(e){const{_lastPoints:t}=this;if(t.push(e),t.length>2){t.length===3&&t.unshift(t[0]);const s=this._calculateCurveWidths(t[1],t[2]),i=P.fromPoints(t,s);return t.shift(),i}return null}_calculateCurveWidths(e,t){const s=this.velocityFilterWeight*t.velocityFrom(e)+(1-this.velocityFilterWeight)*this._lastVelocity,i=this._strokeWidth(s),o={end:i,start:this._lastWidth};return this._lastVelocity=s,this._lastWidth=i,o}_strokeWidth(e){return Math.max(this.maxWidth/(e+1),this.minWidth)}_drawCurveSegment(e,t,s){const i=this._ctx;i.moveTo(e,t),i.arc(e,t,s,0,2*Math.PI,!1),this._isEmpty=!1}_drawCurve({color:e,curve:t}){const s=this._ctx,i=t.endWidth-t.startWidth,o=2*Math.floor(t.length());s.beginPath(),s.fillStyle=e;for(let l=0;l<o;l+=1){const r=l/o,c=r*r,n=c*r,h=1-r,a=h*h,m=a*h;let d=m*t.startPoint.x;d+=3*a*r*t.control1.x,d+=3*h*c*t.control2.x,d+=n*t.endPoint.x;let u=m*t.startPoint.y;u+=3*a*r*t.control1.y,u+=3*h*c*t.control2.y,u+=n*t.endPoint.y;const v=Math.min(t.startWidth+n*i,this.maxWidth);this._drawCurveSegment(d,u,v)}s.closePath(),s.fill()}_drawDot({color:e,point:t}){const s=this._ctx,i=typeof this.dotSize=="function"?this.dotSize():this.dotSize;s.beginPath(),this._drawCurveSegment(t.x,t.y,i),s.closePath(),s.fillStyle=e,s.fill()}_fromData(e,t,s){for(const i of e){const{color:o,points:l}=i;if(l.length>1)for(let r=0;r<l.length;r+=1){const c=l[r],n=new f(c.x,c.y,c.time);this.penColor=o,r===0&&this._reset();const h=this._addPoint(n);h&&t({color:o,curve:h})}else this._reset(),s({color:o,point:l[0]})}}_toSVG(){const e=this._data,t=Math.max(window.devicePixelRatio||1,1),s=this.canvas.width/t,i=this.canvas.height/t,o=document.createElementNS("http://www.w3.org/2000/svg","svg");o.setAttribute("width",this.canvas.width.toString()),o.setAttribute("height",this.canvas.height.toString()),this._fromData(e,({color:c,curve:n})=>{const h=document.createElement("path");if(!(isNaN(n.control1.x)||isNaN(n.control1.y)||isNaN(n.control2.x)||isNaN(n.control2.y))){const a=`M ${n.startPoint.x.toFixed(3)},${n.startPoint.y.toFixed(3)} C ${n.control1.x.toFixed(3)},${n.control1.y.toFixed(3)} ${n.control2.x.toFixed(3)},${n.control2.y.toFixed(3)} ${n.endPoint.x.toFixed(3)},${n.endPoint.y.toFixed(3)}`;h.setAttribute("d",a),h.setAttribute("stroke-width",(2.25*n.endWidth).toFixed(3)),h.setAttribute("stroke",c),h.setAttribute("fill","none"),h.setAttribute("stroke-linecap","round"),o.appendChild(h)}},({color:c,point:n})=>{const h=document.createElement("circle"),a=typeof this.dotSize=="function"?this.dotSize():this.dotSize;h.setAttribute("r",a.toString()),h.setAttribute("cx",n.x.toString()),h.setAttribute("cy",n.y.toString()),h.setAttribute("fill",c),o.appendChild(h)});const l=`<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 ${s} ${i}" width="${s}" height="${i}">`;let r=o.innerHTML;if(r===void 0){const c=document.createElement("dummy"),n=o.childNodes;c.innerHTML="";for(let h=0;h<n.length;h+=1)c.appendChild(n[h].cloneNode(!0));r=c.innerHTML}return"data:image/svg+xml;base64,"+btoa(l+r+"</svg>")}}const K=["id","data-uid","disabled"];var w,Q=C(k(k({},{name:"Vue3Signature"}),{props:{sigOption:{type:Object,default:()=>({backgroundColor:"rgb(255,255,255)",penColor:"rgb(0, 0, 0)"})},w:{type:String,default:"100%"},h:{type:String,default:"100%"},clearOnResize:{type:Boolean,default:!1},waterMark:{type:Object,default:()=>({})},disabled:{type:Boolean,default:!1},defaultUrl:{type:String,default:""}},emits:["begin","end"],setup(p,{expose:e,emit:t}){const s=p,i={width:"100%",height:"100%"};let o=R({sig:void 0,option:k({backgroundColor:"rgb(255,255,255)",penColor:"rgb(0, 0, 0)"},s.sigOption),uid:"canvas"+Math.random()});A(()=>s.disabled,a=>{a?o.sig.off():o.sig.on()});const l=()=>{o.sig.clear()},r=a=>a?o.sig.toDataURL(a):o.sig.toDataURL(),c=a=>{o.sig.fromDataURL(a)},n=()=>o.sig.isEmpty(),h=a=>{if(Object.prototype.toString.call(a)!="[object Object]")throw new Error("Expected Object, got "+typeof a+".");{let m=document.getElementById(o.uid),d={text:a.text||"",x:a.x||20,y:a.y||20,sx:a.sx||40,sy:a.sy||40},u=m.getContext("2d");u.font=a.font||"20px sans-serif",u.fillStyle=a.fillStyle||"#333",u.strokeStyle=a.strokeStyle||"#333",a.style=="all"?(u.fillText(d.text,d.x,d.y),u.strokeText(d.text,d.sx,d.sy)):a.style=="stroke"?u.strokeText(d.text,d.sx,d.sy):u.fillText(d.text,d.x,d.y),o.sig._isEmpty=!1}};return z(()=>{(()=>{let a=document.getElementById(o.uid);function m(d){let u;n()||(u=r());let v=Math.max(window.devicePixelRatio||1,1);const x=RegExp(/px/);d.width=x.test(s.w)?Number(s.w.replace(/px/g,""))*v:d.offsetWidth*v,d.height=x.test(s.h)?Number(s.h.replace(/px/g,""))*v:d.offsetHeight*v,d.getContext("2d").scale(v,v),l(),!s.clearOnResize&&u!==void 0&&c(u),Object.keys(s.waterMark).length&&h(s.waterMark)}o.sig=new b(a,o.option),o.sig.onBegin=d=>t("begin"),o.sig.onEnd=d=>t("end"),window.addEventListener("resize",()=>m(a)),m(a),s.defaultUrl!==""&&c(s.defaultUrl),s.disabled?o.sig.off():o.sig.on()})()}),e({save:r,clear:l,isEmpty:n,undo:()=>{let a=o.sig.toData();a&&(a.pop(),o.sig.fromData(a))},addWaterMark:h,fromDataURL:c}),(a,m)=>(T(),N("div",{style:j({width:p.w,height:p.h}),onTouchmove:m[0]||(m[0]=$(()=>{},["prevent"]))},[E("canvas",{id:g(o).uid,"data-uid":g(o).uid,disabled:g(o).disabled,style:i},null,8,K)],36))}})),Z=((w=Q).install=p=>{p.component(w.name,w)},w);const tt={class:"position-relative"},et={class:"dialog-footer"},st=C({__name:"SignDialog",emits:["success"],setup(p,{expose:e,emit:t}){const s=B(),i=W(!1),o=W();e({open:async()=>{i.value=!0}});const l=t,r=async()=>{s.success("\u7B7E\u540D\u4E0A\u4F20\u4E2D\u8BF7\u7A0D\u7B49\u3002\u3002\u3002");const c=await O({file:q.base64ToFile(o.value.save("image/png"),"\u7B7E\u540D")});l("success",c.data),i.value=!1};return(c,n)=>{const h=F,a=G,m=H;return T(),V(m,{modelValue:g(i),"onUpdate:modelValue":n[2]||(n[2]=d=>I(i)?i.value=d:null),title:"\u7B7E\u540D",width:"935"},{footer:_(()=>[E("div",et,[y(a,{onClick:n[1]||(n[1]=d=>i.value=!1)},{default:_(()=>n[4]||(n[4]=[M("\u53D6\u6D88")])),_:1}),y(a,{type:"primary",onClick:r},{default:_(()=>n[5]||(n[5]=[M(" \u63D0\u4EA4 ")])),_:1})])]),default:_(()=>[E("div",tt,[y(g(Z),{class:"b b-solid b-gray",ref_key:"signature",ref:o,w:"900px",h:"400px"},null,512),y(a,{class:"pos-absolute bottom-20px right-10px",type:"primary",text:"",size:"small",onClick:n[0]||(n[0]=d=>g(o).clear())},{default:_(()=>[y(h,{icon:"ep:delete",class:"mr-5px"}),n[3]||(n[3]=M(" \u6E05\u9664 "))]),_:1})])]),_:1},8,["modelValue"])}}});export{st as _};
