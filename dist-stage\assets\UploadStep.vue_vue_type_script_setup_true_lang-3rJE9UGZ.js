import{d as F,aA as G,V as I,_ as x}from"./index-Byekp3Iv.js";import{h as K,z as W,f as J,aj as Q}from"./form-designer-C0ARe9Dh.js";import{k as Y,r as c,i as Z,c as v,e as ee,y as le,m,z as i,H as o,v as t,l as g,C as ae,u as n,E as w,F as h,G as te,$ as se,g as oe}from"./form-create-B86qX0W_.js";const ue={class:"w-full"},re={class:"w-full border-2 border-dashed border-[#dcdfe6] rounded-md p-20px text-center hover:border-[#409eff]"},de={class:"flex flex-col items-center justify-center py-20px"},ie={class:"el-upload__tip mt-10px text-[#909399] text-[12px]"},ne={key:0,class:"mt-15px grid grid-cols-1 gap-2"},pe={class:"flex items-center"},ce={class:"text-[13px] text-[#303133] break-all"},me={class:"flex justify-end w-full"},fe=Y({__name:"UploadStep",props:{modelValue:{type:Object,required:!0}},emits:["update:modelValue"],setup(k,{emit:C}){const u=k,p=C,T=c(),X=c(),L=Z("parent",null),{uploadUrl:S,httpRequest:j}=I(),r=F(),D=c([]),a=c(0),f=["TXT","MARKDOWN","MDX","PDF","HTML","XLSX","XLS","DOC","DOCX","CSV","EML","MSG","PPTX","XML","EPUB","PPT","MD","HTM"],P=f.map(e=>e.toLowerCase()),O=v(()=>G(f)),s=v({get:()=>u.modelValue,set:e=>p("update:modelValue",e)}),b=()=>{u.modelValue.list||p("update:modelValue",{...u.modelValue,list:[]})},q=v(()=>s.value.list&&s.value.list.length>0&&a.value===0),R=e=>{const l=e.name.toLowerCase(),d=l.substring(l.lastIndexOf(".")+1);return P.includes(d)?e.size/1024/1024<15?(a.value++,!0):(r.error("\u6587\u4EF6\u5927\u5C0F\u4E0D\u80FD\u8D85\u8FC7 15 MB\uFF01"),!1):(r.error("\u4E0D\u652F\u6301\u7684\u6587\u4EF6\u7C7B\u578B\uFF01"),!1)},$=(e,l)=>{e&&e.data?(b(),p("update:modelValue",{...u.modelValue,list:[...u.modelValue.list,{name:l.name,url:e.data}]})):r.error(`\u6587\u4EF6 ${l.name} \u4E0A\u4F20\u5931\u8D25`),a.value=Math.max(0,a.value-1)},z=(e,l)=>{r.error(`\u6587\u4EF6 ${l.name} \u4E0A\u4F20\u5931\u8D25: ${e}`),a.value=Math.max(0,a.value-1)},B=e=>{e.status!=="success"&&e.status!=="fail"||(a.value=Math.max(0,a.value-1))},E=e=>{e.status==="uploading"&&(a.value=Math.max(0,a.value-1))},H=()=>{var l,d;if(!s.value.list||s.value.list.length===0)return void r.warning("\u8BF7\u4E0A\u4F20\u81F3\u5C11\u4E00\u4E2A\u6587\u4EF6");if(a.value>0)return void r.warning("\u8BF7\u7B49\u5F85\u6240\u6709\u6587\u4EF6\u4E0A\u4F20\u5B8C\u6210");const e=L||((l=oe())==null?void 0:l.parent);e&&typeof((d=e.exposed)==null?void 0:d.goToNextStep)=="function"&&e.exposed.goToNextStep()};return ee(()=>{b()}),(e,l)=>{const d=W,_=J,y=Q,N=K;return m(),le(N,{ref_key:"formRef",ref:T,model:s.value,"label-width":"0",class:"mt-20px"},{default:i(()=>[o(y,{class:"mb-20px"},{default:i(()=>[t("div",ue,[t("div",re,[o(d,{ref_key:"uploadRef",ref:X,class:"upload-demo",drag:"",action:n(S),"auto-upload":!0,"on-success":$,"on-error":z,"on-change":B,"on-remove":E,"before-upload":R,"http-request":n(j),"file-list":D.value,multiple:!0,"show-file-list":!1,accept:O.value},{default:i(()=>[t("div",de,[o(n(x),{icon:"ep:upload-filled",class:"text-[48px] text-[#c0c4cc] mb-10px"}),l[0]||(l[0]=t("div",{class:"el-upload__text text-[16px] text-[#606266]"},[w(" \u62D6\u62FD\u6587\u4EF6\u81F3\u6B64\uFF0C\u6216\u8005 "),t("em",{class:"text-[#409eff] not-italic cursor-pointer"},"\u9009\u62E9\u6587\u4EF6")],-1)),t("div",ie," \u5DF2\u652F\u6301 "+h(f.join("\u3001"))+"\uFF0C\u6BCF\u4E2A\u6587\u4EF6\u4E0D\u8D85\u8FC7 "+h(15)+" MB\u3002 ",1)])]),_:1},8,["action","http-request","file-list","accept"])]),s.value.list&&s.value.list.length>0?(m(),g("div",ne,[(m(!0),g(te,null,se(s.value.list,(U,V)=>(m(),g("div",{key:V,class:"flex justify-between items-center py-4px px-12px border-l-4 border-l-[#409eff] rounded-sm shadow-sm hover:bg-[#ecf5ff] transition-all duration-300"},[t("div",pe,[o(n(x),{icon:"ep:document",class:"mr-8px text-[#409eff]"}),t("span",ce,h(U.name),1)]),o(_,{type:"danger",link:"",onClick:xe=>(A=>{const M=[...u.modelValue.list];M.splice(A,1),p("update:modelValue",{...u.modelValue,list:M})})(V),class:"ml-2"},{default:i(()=>[o(n(x),{icon:"ep:delete"})]),_:2},1032,["onClick"])]))),128))])):ae("",!0)])]),_:1}),o(y,null,{default:i(()=>[t("div",me,[o(_,{type:"primary",onClick:H,disabled:!q.value},{default:i(()=>l[1]||(l[1]=[w(" \u4E0B\u4E00\u6B65 ")])),_:1},8,["disabled"])])]),_:1})]),_:1},8,["model"])}}});export{fe as _};
