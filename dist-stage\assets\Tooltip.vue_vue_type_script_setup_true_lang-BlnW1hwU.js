import{p as o,_ as a}from"./index-Byekp3Iv.js";import{o as l}from"./form-designer-C0ARe9Dh.js";import{k as i,l as p,m as r,G as m,v as c,H as s,F as f,z as u}from"./form-create-B86qX0W_.js";const d=i({name:"Tooltip",__name:"Tooltip",props:{title:o.string.def(""),message:o.string.def(""),icon:o.string.def("ep:question-filled")},setup:t=>(g,_)=>{const e=a,n=l;return r(),p(m,null,[c("span",null,f(t.title),1),s(n,{content:t.message,placement:"top"},{default:u(()=>[s(e,{icon:t.icon,class:"relative top-1px ml-1px"},null,8,["icon"])]),_:1},8,["content"])],64)}});export{d as _};
