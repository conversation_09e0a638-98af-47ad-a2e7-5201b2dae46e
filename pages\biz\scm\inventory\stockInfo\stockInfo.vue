<template>
	<view class="stock-info-container">
		<!-- 顶部导航 -->
		<view class="top-nav">
			<view class="nav-left">
				<uni-breadcrumb separator="/">
					<uni-breadcrumb-item v-for="(item,index) in warehouseNav" :key="index">{{item.name}}</uni-breadcrumb-item>
				</uni-breadcrumb>
			</view>
			<view class="nav-right" @click="openWarehouseDrawer">
				<uni-icons type="list" size="24"></uni-icons>
			</view>
		</view>
		
		<!-- 搜索区域 -->
		<view class="search-section">
			<view class="search-bar">
				<uv-search 
					shape="round" 
					v-model="searchParams.materialName" 
					placeholder="请输入物料名称" 
					clearable 
					bgColor="#f5f5f5"
					@search="handleSearch"
					@custom="handleSearch"
					@clear="handleClear"
				></uv-search>
			</view>
		</view>

		<!-- 库存状态Tab -->
		<view class="status-tabs">
			<scroll-view scroll-x class="tab-scroll">
				<view class="tab-container">
					<view 
						class="tab-item" 
						:class="{ 'active': !searchParams.status }"
						@click="selectStatus('')">
						<text>全部</text>
					</view>
					<view 
						v-for="item in statusOptions" 
						:key="item.value"
						class="tab-item"
						:class="{ 'active': searchParams.status === item.value }"
						@click="selectStatus(item.value)">
						<text>{{ item.label }}</text>
					</view>
				</view>
			</scroll-view>
		</view>

		<!-- 库存列表 -->
		<scroll-view 
			scroll-y="true" 
			class="stock-list" 
			@scrolltolower="loadMoreList"
			refresher-enabled
			:refresher-triggered="isRefreshing"
			@refresherrefresh="onPullDownRefresh"
			enable-flex="true"
		>
					<!-- 有数据时显示列表 -->
		<block v-if="stockList && stockList.length > 0">
			<view class="list-container">
				<view class="list-item-wrapper" v-for="item in stockListWithClasses" :key="item.id">
						<view class="stock-card">
							<!-- 物料基本信息 -->
							<view class="material-info">
							<view class="material-header">
								<text class="material-name">{{ item.materialName }}</text>
								<view class="material-type" :class="item.materialTypeClass">
									{{ getMaterialTypeText(item.materialType) }}
								</view>
							</view>
								<text class="material-code">编码：{{ item.materialCode }}</text>
								<text class="material-source">来源：{{ getMaterialSourceText(item.materialSource) }}</text>
							</view>
							
							<!-- 库存数量信息 -->
							<view class="quantity-section">
								<view class="quantity-row">
									<view class="quantity-item primary">
										<text class="quantity-label">库存数量</text>
										<text class="quantity-value">{{ formatQuantity(item.quantity) }}</text>
										<text class="quantity-unit">{{ getUnitName(item.quantityUnit) }}</text>
									</view>
									<view class="quantity-item">
										<text class="quantity-label">锁定数量</text>
										<text class="quantity-value lock">{{ formatQuantity(item.lockQuantity) }}</text>
									</view>
								</view>
								<view class="quantity-row">
									<view class="quantity-item">
										<text class="quantity-label">可用数量</text>
										<text class="quantity-value available">{{ formatQuantity(item.unlockQuantity) }}</text>
									</view>
									<view class="quantity-item">
										<text class="quantity-label">在途数量</text>
										<text class="quantity-value transit">{{ formatQuantity(item.transitQuantity) }}</text>
									</view>
								</view>
							</view>
							
							<!-- 价格和仓库信息 -->
							<view class="detail-section">
								<view class="detail-row">
									<text class="detail-label">价格：</text>
									<text class="detail-value">{{ formatAmount(item.price) }}</text>
								</view>
								<view class="detail-row">
									<text class="detail-label">总价值：</text>
									<text class="detail-value amount">{{ formatAmount(item.totalCost) }}</text>
								</view>
								<view class="detail-row">
									<text class="detail-label">仓库：</text>
									<text class="detail-value">{{ item.warehouseName }}</text>
								</view>
															<view class="detail-row">
								<text class="detail-label">状态：</text>
								<view class="status-tag" :class="item.statusClass">
									{{ getStatusText(item.status) }}
								</view>
							</view>
							</view>
						</view>
					</view>
				</view>
				
				<!-- 加载更多 -->
				<view class="load-more-container">
					<uni-load-more :status="loadMoreStatus" @clickLoadMore="loadMoreList"></uni-load-more>
				</view>
			</block>
			
			<!-- 无数据时显示暂无数据提示 -->
			<view v-if="!stockList || stockList.length === 0" class="empty-data">
				<text>暂无库存数据</text>
			</view>
		</scroll-view>
		
		<!-- 左侧仓库选择抽屉 -->
		<uni-popup ref="warehousePopup" type="left" background-color="#ffffff">
			<view class="popup-content">
				<view class="popup-title">
					<text>仓库选择</text>
					<uni-icons type="close" size="20" @click="closeWarehouseDrawer"></uni-icons>
				</view>
				
				<scroll-view scroll-y class="warehouse-tree">
					<!-- 全部仓库选项 -->
					<view class="warehouse-item level-0" 
						:class="{ 'active': !searchParams.warehouseName }"
						@click="selectWarehouse('', '全部仓库')">
						<text>全部仓库</text>
					</view>
					
					<!-- 树形仓库列表 -->
					<view v-for="warehouse in warehouseTree" :key="warehouse.id">
						<view class="warehouse-item level-0"
							:class="{ 'active': searchParams.warehouseName === warehouse.name }"
							@click="handleWarehouseClick(warehouse)">
							<text>{{ warehouse.name }}</text>
							<uni-icons v-if="warehouse.children && warehouse.children.length > 0" 
								:type="expandedWarehouse === warehouse.id ? 'bottom' : 'right'" 
								size="16"
								@click.stop="toggleWarehouse(warehouse.id)"></uni-icons>
						</view>
						
						<!-- 子仓库 -->
						<view v-if="expandedWarehouse === warehouse.id && warehouse.children && warehouse.children.length > 0" 
							class="sub-warehouses">
							<view v-for="subWarehouse in warehouse.children" 
								:key="subWarehouse.id" 
								class="warehouse-item level-1"
								:class="{ 'active': searchParams.warehouseName === subWarehouse.name }"
								@click="handleWarehouseClick(subWarehouse, warehouse)">
								<text>{{ subWarehouse.name }}</text>
								<text class="warehouse-desc">{{ subWarehouse.address || '' }}</text>
								<uni-icons v-if="subWarehouse.children && subWarehouse.children.length > 0" 
									:type="expandedWarehouse === subWarehouse.id ? 'bottom' : 'right'" 
									size="14"
									@click.stop="toggleWarehouse(subWarehouse.id)"></uni-icons>
							</view>
							
							<!-- 三级子仓库 -->
							<view v-for="subWarehouse in warehouse.children" :key="subWarehouse.id">
								<view v-if="expandedWarehouse === subWarehouse.id && subWarehouse.children && subWarehouse.children.length > 0" 
									class="sub-sub-warehouses">
									<view v-for="subSubWarehouse in subWarehouse.children" 
										:key="subSubWarehouse.id" 
										class="warehouse-item level-2"
										:class="{ 'active': searchParams.warehouseName === subSubWarehouse.name }"
										@click="handleWarehouseClick(subSubWarehouse, subWarehouse)">
										<text>{{ subSubWarehouse.name }}</text>
										<text class="warehouse-desc">{{ subSubWarehouse.address || '' }}</text>
									</view>
								</view>
							</view>
						</view>
					</view>
				</scroll-view>
			</view>
		</uni-popup>
	</view>
</template>

<script>
import { 
	getStockInfoPageApi, 
	getUnitListApi, 
	getWarehouseListApi 
} from '../../../../../api/scm/inventory/stockInfo/index.js'
import { getBatchDictOptions, getDictLabel, DICT_TYPE } from '../../../../../utils/dict.js'
import { handleTree } from '../../../../../utils/tree.js'

export default {
	data() {
		return {
			stockList: [],
			pageNo: 1,
			pageSize: 10,
			loadMoreStatus: 'more',
			isRefreshing: false,
			
			// 搜索参数
			searchParams: {
				materialName: '',
				materialCode: '',
				materialType: '',
				warehouseName: '',
				status: '',
				detail: true
			},
			
			// 导航面包屑
			warehouseNav: [
				{
					name: '全部仓库'
				}
			],
			
			// 仓库树形展开状态
			expandedWarehouse: '',
			
			// 选项数据
			materialTypeOptions: [],
			statusOptions: [],
			warehouseOptions: [],
			warehouseTree: [],
			unitOptions: [],
			unitMap: new Map(),
			
			// 字典数据
			dictData: {}
		}
	},
	computed: {
		warehouseText() {
			return this.searchParams.warehouseName
		},
		
		// 为每个库存项目计算类名
		stockListWithClasses() {
			return this.stockList.map(item => ({
				...item,
				materialTypeClass: this.getMaterialTypeClass(item.materialType),
				statusClass: this.getStatusClass(item.status)
			}))
		}
	},
	onLoad() {
		this.initData()
	},
	methods: {
		// 初始化数据
		async initData() {
			await this.getDictData()
			await this.getUnitList()
			await this.getWarehouseList()
			this.getStockList()
		},
		
		// 获取字典数据
		async getDictData() {
			try {
				const dictTypes = [DICT_TYPE.MATERIAL_TYPE, DICT_TYPE.STOCK_STATUS, DICT_TYPE.MATERIAL_SOURCE]
				this.dictData = await getBatchDictOptions(dictTypes)
				
				// 设置选项数据
				this.materialTypeOptions = this.dictData[DICT_TYPE.MATERIAL_TYPE] || []
				this.statusOptions = this.dictData[DICT_TYPE.STOCK_STATUS] || []
			} catch (error) {
				console.error('获取字典数据失败:', error)
			}
		},
		
		// 获取库存列表
		async getStockList(isLoadMore = false) {
			if (!isLoadMore) {
				this.pageNo = 1
				this.stockList = []
			}
			
			this.loadMoreStatus = 'loading'
			
			try {
				const params = {
					...this.searchParams,
					pageNo: this.pageNo,
					pageSize: this.pageSize
				}
				
				const response = await getStockInfoPageApi(params)
				
				if (response && response.code === 0) {
					const newList = response.data?.list || []
					
					if (isLoadMore) {
						this.stockList = [...this.stockList, ...newList]
					} else {
						this.stockList = newList
					}
					
					this.loadMoreStatus = newList.length < this.pageSize ? 'noMore' : 'more'
				} else {
					this.loadMoreStatus = 'noMore'
					this.$modal.msgError('获取库存数据失败')
				}
			} catch (error) {
				this.loadMoreStatus = 'noMore'
				this.$modal.msgError('网络错误，请重试')
			} finally {
				if (this.isRefreshing) {
					this.isRefreshing = false
				}
			}
		},
		
		// 获取单位列表
		async getUnitList() {
			try {
				const response = await getUnitListApi({ pageSize: 100, pageNo: 1 })
				if (response && response.code === 0) {
					this.unitOptions = response.data?.list || []
					this.unitOptions.forEach(unit => {
						this.unitMap.set(unit.id, unit.name)
					})
				}
			} catch (error) {
				console.error('获取单位列表失败:', error)
			}
		},
		
		// 获取仓库列表
		async getWarehouseList() {
			try {
				const response = await getWarehouseListApi({ pageNo: 1, pageSize: 100 })
				if (response && response.code === 0) {
					this.warehouseOptions = response.data || []
					// 使用handleTree处理成树形结构
					this.warehouseTree = handleTree(this.warehouseOptions, 'id', 'parentId', 'children')
				}
			} catch (error) {
				console.error('获取仓库列表失败:', error)
			}
		},
		
		// 搜索处理
		handleSearch() {
			this.getStockList()
		},
		
		// 清空搜索
		handleClear() {
			this.searchParams.materialName = ''
			this.getStockList()
		},
		
		// 下拉刷新
		onPullDownRefresh() {
			this.isRefreshing = true
			this.getStockList()
		},
		
		// 加载更多
		loadMoreList() {
			if (this.loadMoreStatus === 'loading' || this.loadMoreStatus === 'noMore') {
				return
			}
			this.pageNo++
			this.getStockList(true)
		},
		
		// 打开仓库选择抽屉
		openWarehouseDrawer() {
			this.$refs.warehousePopup.open()
		},
		
		// 关闭仓库选择抽屉
		closeWarehouseDrawer() {
			this.$refs.warehousePopup.close()
		},
		
		// 切换仓库展开状态
		toggleWarehouse(warehouseId) {
			this.expandedWarehouse = this.expandedWarehouse === warehouseId ? '' : warehouseId
		},
		
		// 处理仓库点击事件
		handleWarehouseClick(warehouse, parentWarehouse) {
			if (warehouse.children && warehouse.children.length > 0) {
				// 如果是父节点，则展开/收缩
				this.toggleWarehouse(warehouse.id)
			} else {
				// 如果是叶子节点，则选择
				this.selectWarehouse(warehouse.name, warehouse.name, parentWarehouse)
			}
		},
		
		// 选择仓库
		selectWarehouse(warehouseName, displayName, parentWarehouse) {
			this.searchParams.warehouseName = warehouseName
			this.updateWarehouseNav(displayName, parentWarehouse)
			this.closeWarehouseDrawer()
			this.getStockList()
		},
		
		// 更新仓库导航
		updateWarehouseNav(displayName, parentWarehouse) {
			if (displayName && displayName !== '全部仓库') {
				this.warehouseNav = [{ name: '全部仓库' }]
				if (parentWarehouse) {
					this.warehouseNav.push({ name: parentWarehouse.name })
				}
				this.warehouseNav.push({ name: displayName })
			} else {
				this.warehouseNav = [{ name: '全部仓库' }]
			}
		},
		
		// 选择状态
		selectStatus(value) {
			this.searchParams.status = value
			this.getStockList()
		},
		
		// 格式化数量
		formatQuantity(value) {
			if (!value && value !== 0) return '0'
			return Number(value).toLocaleString()
		},
		
		// 格式化金额
		formatAmount(value) {
			if (!value && value !== 0) return '¥0.00'
			return '¥' + Number(value).toFixed(2)
		},
		
		// 获取单位名称
		getUnitName(unitId) {
			if (!unitId) return ''
			return this.unitMap.get(unitId) || unitId.toString()
		},
		
		// 获取物料类型文本
		getMaterialTypeText(type) {
			return getDictLabel(this.materialTypeOptions, type) || type || '未知'
		},
		
		// 获取物料类型样式类
		getMaterialTypeClass(type) {
			const typeClassMap = {
				'RAW_MATERIAL': 'type-raw',
				'SEMI_FINISHED': 'type-semi', 
				'FINISHED_PRODUCT': 'type-finished',
				'AUXILIARY_MATERIAL': 'type-auxiliary'
			}
			return typeClassMap[type] || 'type-default'
		},
		
		// 获取物料来源文本
		getMaterialSourceText(source) {
			const materialSourceOptions = this.dictData[DICT_TYPE.MATERIAL_SOURCE] || []
			return getDictLabel(materialSourceOptions, source) || source || '未知'
		},
		
		// 获取状态文本
		getStatusText(status) {
			return getDictLabel(this.statusOptions, status) || status || '未知'
		},
		
		// 获取状态样式类
		getStatusClass(status) {
			const statusClassMap = {
				'NORMAL': 'status-normal',
				'LOCKED': 'status-locked',
				'FROZEN': 'status-frozen',
				'DAMAGED': 'status-damaged'
			}
			return statusClassMap[status] || 'status-default'
		}
	}
}
</script>

<style lang="scss" scoped>
.stock-info-container {
	height: 100vh;
	background-color: #f5f5f5;
	display: flex;
	flex-direction: column;
}

.top-nav {
	border-bottom: 1px solid #eee;
	width: 100%;
	background-color: #fff;
	padding: 10px;
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.nav-left {
	flex: 1;
	overflow: hidden;
}

.nav-right {
	width: 40px;
	text-align: right;
}

.search-section {
	background-color: white;
	padding: 20rpx;
	border-bottom: 1px solid #eee;
}

.search-bar {
	margin-bottom: 0;
}

// 状态Tab样式
.status-tabs {
	background-color: white;
	border-bottom: 1px solid #eee;
}

.tab-scroll {
	white-space: nowrap;
}

.tab-container {
	display: flex;
	padding: 0 20rpx;
}

.tab-item {
	flex-shrink: 0;
	padding: 24rpx 32rpx;
	margin-right: 20rpx;
	text-align: center;
	border-bottom: 4rpx solid transparent;
	
	&.active {
		color: #2979ff;
		border-bottom-color: #2979ff;
		
		text {
			font-weight: bold;
		}
	}
	
	text {
		font-size: 28rpx;
		color: #666;
	}
	
	&.active text {
		color: #2979ff;
	}
}

.stock-list {
	flex: 1;
	padding: 20rpx;
}

.list-container {
	.list-item-wrapper {
		margin-bottom: 20rpx;
	}
}

.stock-card {
	background-color: white;
	border-radius: 12rpx;
	padding: 24rpx;
	box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

.material-info {
	border-bottom: 1px solid #f0f0f0;
	padding-bottom: 20rpx;
	margin-bottom: 20rpx;
	
	.material-header {
		display: flex;
		align-items: center;
		justify-content: space-between;
		margin-bottom: 12rpx;
		
		.material-name {
			font-size: 32rpx;
			font-weight: bold;
			color: #333;
			flex: 1;
		}
		
		.material-type {
			padding: 6rpx 12rpx;
			border-radius: 16rpx;
			font-size: 22rpx;
			color: white;
			
			&.type-raw { background-color: #409EFF; }
			&.type-semi { background-color: #E6A23C; }
			&.type-finished { background-color: #67C23A; }
			&.type-auxiliary { background-color: #909399; }
			&.type-default { background-color: #C0C4CC; }
		}
	}
	
	.material-code,
	.material-source {
		font-size: 26rpx;
		color: #666;
		margin-bottom: 8rpx;
		
		&:last-child {
			margin-bottom: 0;
		}
	}
}

.quantity-section {
	margin-bottom: 20rpx;
	
	.quantity-row {
		display: flex;
		margin-bottom: 16rpx;
		
		&:last-child {
			margin-bottom: 0;
		}
	}
	
	.quantity-item {
		flex: 1;
		margin-right: 20rpx;
		
		&:last-child {
			margin-right: 0;
		}
		
		&.primary {
			background-color: #f0f9ff;
			padding: 16rpx;
			border-radius: 8rpx;
			border: 2rpx solid #409EFF;
		}
		
		.quantity-label {
			font-size: 24rpx;
			color: #666;
			display: block;
			margin-bottom: 8rpx;
		}
		
		.quantity-value {
			font-size: 28rpx;
			font-weight: bold;
			color: #333;
			
			&.lock { color: #E6A23C; }
			&.available { color: #67C23A; }
			&.transit { color: #909399; }
		}
		
		.quantity-unit {
			font-size: 22rpx;
			color: #999;
			margin-left: 8rpx;
		}
	}
}

.detail-section {
	.detail-row {
		display: flex;
		align-items: center;
		margin-bottom: 12rpx;
		
		&:last-child {
			margin-bottom: 0;
		}
		
		.detail-label {
			font-size: 26rpx;
			color: #666;
			min-width: 120rpx;
		}
		
		.detail-value {
			font-size: 26rpx;
			color: #333;
			
			&.amount {
				color: #E6A23C;
				font-weight: bold;
			}
		}
	}
	
	.status-tag {
		padding: 4rpx 12rpx;
		border-radius: 12rpx;
		font-size: 22rpx;
		color: white;
		
		&.status-normal { background-color: #67C23A; }
		&.status-locked { background-color: #E6A23C; }
		&.status-frozen { background-color: #409EFF; }
		&.status-damaged { background-color: #F56C6C; }
		&.status-default { background-color: #C0C4CC; }
	}
}

.load-more-container {
	padding: 20rpx 0;
}

.empty-data {
	text-align: center;
	padding: 100rpx 0;
	color: #999;
	font-size: 28rpx;
}

// 仓库选择抽屉样式
.popup-content {
	width: 80vw;
	height: 100vh;
	display: flex;
	flex-direction: column;
}

.popup-title {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 12px;
	border-bottom: 1px solid #eee;
	font-size: 32rpx;
	font-weight: bold;
}

.warehouse-tree {
	flex: 1;
	padding: 10px;
}

.warehouse-item {
	padding: 15px 10px;
	border-bottom: 1px solid #f5f5f5;
	display: flex;
	align-items: center;
	justify-content: space-between;
	
	&.active {
		color: #2979ff;
		background-color: #f0f9ff;
	}
	
	&.level-1 {
		padding-left: 30px;
		background-color: #fafafa;
	}
	
	&.level-2 {
		padding-left: 50px;
		background-color: #f5f5f5;
	}
	
	.warehouse-desc {
		font-size: 24rpx;
		color: #999;
		margin-top: 8rpx;
		display: block;
	}
}

.sub-warehouses {
	.warehouse-item {
		border-left: 2px solid #e6e6e6;
	}
}

.sub-sub-warehouses {
	.warehouse-item {
		border-left: 4px solid #d0d0d0;
	}
}
</style>
