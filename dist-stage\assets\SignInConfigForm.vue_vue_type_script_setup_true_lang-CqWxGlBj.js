import{W as p,a as T,d as j,h as z,D as B}from"./index-Byekp3Iv.js";import{_ as G}from"./Dialog.vue_vue_type_style_index_0_lang-DhV_HFpU.js";import{C as H}from"./constants-C3gLHYOK.js";import{h as I,aj as L,l as $,a7 as P,s as R,u as W,ak as J,f as K}from"./form-designer-C0ARe9Dh.js";import{k as Q,r as d,P as X,y as b,m as f,z as i,A as Y,u as a,H as u,E as g,l as Z,G as ee,$ as ae,F as le,h as te}from"./form-create-B86qX0W_.js";const se=async()=>await p.get({url:"/member/sign-in/config/list"}),ie=async _=>await p.delete({url:"/member/sign-in/config/delete?id="+_}),ue=Q({__name:"SignInConfigForm",emits:["success"],setup(_,{expose:A,emit:C}){const{t:v}=T(),V=j(),o=d(!1),x=d(""),n=d(!1),w=d(""),l=d({}),k=(t,e,r)=>{if(!l.value.point&&!l.value.experience)return void r(new Error("\u5956\u52B1\u79EF\u5206\u4E0E\u5956\u52B1\u7ECF\u9A8C\u81F3\u5C11\u914D\u7F6E\u4E00\u4E2A"));const y=(t==null?void 0:t.field)==="point"?"experience":"point";m.value.validateField(y,()=>null),r()},F=X({day:[{required:!0,message:"\u7B7E\u5230\u5929\u6570\u4E0D\u80FD\u7A7A",trigger:"blur"}],point:[{required:!0,message:"\u5956\u52B1\u79EF\u5206\u4E0D\u80FD\u7A7A",trigger:"blur"},{validator:k,trigger:"blur"}],experience:[{required:!0,message:"\u5956\u52B1\u7ECF\u9A8C\u4E0D\u80FD\u7A7A",trigger:"blur"},{validator:k,trigger:"blur"}]}),m=d();A({open:async(t,e)=>{if(o.value=!0,x.value=v("action."+t),w.value=t,q(),e){n.value=!0;try{l.value=await(async r=>await p.get({url:"/member/sign-in/config/get?id="+r}))(e)}finally{n.value=!1}}}});const S=C,h=async()=>{if(m&&await m.value.validate()){n.value=!0;try{w.value==="create"?(await(async t=>await p.post({url:"/member/sign-in/config/create",data:t}))(l.value),V.success(v("common.createSuccess"))):(await(async t=>await p.put({url:"/member/sign-in/config/update",data:t}))(l.value),V.success(v("common.updateSuccess"))),o.value=!1,S("success")}finally{n.value=!1}}},q=()=>{var t;l.value={id:void 0,day:void 0,point:0,experience:0,status:H.ENABLE},(t=m.value)==null||t.resetFields()};return(t,e)=>{const r=$,y=P,c=L,E=W,D=R,M=I,U=K,N=G,O=J;return f(),b(N,{title:a(x),modelValue:a(o),"onUpdate:modelValue":e[5]||(e[5]=s=>te(o)?o.value=s:null)},{footer:i(()=>[u(U,{onClick:h,type:"primary",disabled:a(n)},{default:i(()=>e[7]||(e[7]=[g("\u786E \u5B9A")])),_:1},8,["disabled"]),u(U,{onClick:e[4]||(e[4]=s=>o.value=!1)},{default:i(()=>e[8]||(e[8]=[g("\u53D6 \u6D88")])),_:1})]),default:i(()=>[Y((f(),b(M,{ref_key:"formRef",ref:m,model:a(l),rules:a(F),"label-width":"100px"},{default:i(()=>[u(c,{label:"\u7B7E\u5230\u5929\u6570",prop:"day"},{default:i(()=>[u(r,{modelValue:a(l).day,"onUpdate:modelValue":e[0]||(e[0]=s=>a(l).day=s),min:1,max:7,precision:0},null,8,["modelValue"]),u(y,{class:"mx-1",style:{"margin-left":"10px"},type:"danger"},{default:i(()=>e[6]||(e[6]=[g(" \u53EA\u5141\u8BB8\u8BBE\u7F6E 1-7\uFF0C\u9ED8\u8BA4\u7B7E\u5230 7 \u5929\u4E3A\u4E00\u4E2A\u5468\u671F ")])),_:1})]),_:1}),u(c,{label:"\u5956\u52B1\u79EF\u5206",prop:"point"},{default:i(()=>[u(r,{modelValue:a(l).point,"onUpdate:modelValue":e[1]||(e[1]=s=>a(l).point=s),min:0,precision:0},null,8,["modelValue"])]),_:1}),u(c,{label:"\u5956\u52B1\u7ECF\u9A8C",prop:"experience"},{default:i(()=>[u(r,{modelValue:a(l).experience,"onUpdate:modelValue":e[2]||(e[2]=s=>a(l).experience=s),min:0,precision:0},null,8,["modelValue"])]),_:1}),u(c,{label:"\u5F00\u542F\u72B6\u6001",prop:"status"},{default:i(()=>[u(D,{modelValue:a(l).status,"onUpdate:modelValue":e[3]||(e[3]=s=>a(l).status=s)},{default:i(()=>[(f(!0),Z(ee,null,ae(a(z)(a(B).COMMON_STATUS),s=>(f(),b(E,{key:s.value,value:s.value},{default:i(()=>[g(le(s.label),1)]),_:2},1032,["value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])),[[O,a(n)]])]),_:1},8,["title","modelValue"])}}});export{ue as _,ie as d,se as g};
