import{a as F,d as j}from"./index-Byekp3Iv.js";import{_ as q}from"./Dialog.vue_vue_type_style_index_0_lang-DhV_HFpU.js";import{g as L,a as P}from"./index-B75IgnbX.js";import{_ as S}from"./MemberLevelSelect.vue_vue_type_script_setup_true_lang-TAVwAbD-.js";import{h as z,aj as A,k as E,ak as H,f as R}from"./form-designer-C0ARe9Dh.js";import{k as B,r as n,P as D,y as c,m as f,z as s,A as G,u as l,H as d,E as V,h as J}from"./form-create-B86qX0W_.js";const K=B({__name:"UserLevelUpdateForm",emits:["success"],setup(M,{expose:_,emit:k}){const{t:y}=F(),b=j(),u=n(!1),r=n(!1),a=n({id:void 0,nickname:void 0,levelId:void 0,reason:void 0}),w=D({reason:[{required:!0,message:"\u4FEE\u6539\u539F\u56E0\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}]}),i=n();_({open:async t=>{if(u.value=!0,x(),t){r.value=!0;try{a.value=await L(t)}finally{r.value=!1}}}});const U=k,h=async()=>{if(i&&await i.value.validate()){r.value=!0;try{await P(a.value),b.success(y("common.updateSuccess")),u.value=!1,U("success")}finally{r.value=!1}}},x=()=>{var t;a.value={id:void 0,nickname:void 0,levelId:void 0,reason:void 0},(t=i.value)==null||t.resetFields()};return(t,e)=>{const p=E,m=A,g=z,v=R,I=q,C=H;return f(),c(I,{title:"\u4FEE\u6539\u7528\u6237\u7B49\u7EA7",modelValue:l(u),"onUpdate:modelValue":e[5]||(e[5]=o=>J(u)?u.value=o:null),width:"600"},{footer:s(()=>[d(v,{onClick:h,type:"primary",disabled:l(r)},{default:s(()=>e[6]||(e[6]=[V("\u786E \u5B9A")])),_:1},8,["disabled"]),d(v,{onClick:e[4]||(e[4]=o=>u.value=!1)},{default:s(()=>e[7]||(e[7]=[V("\u53D6 \u6D88")])),_:1})]),default:s(()=>[G((f(),c(g,{ref_key:"formRef",ref:i,model:l(a),rules:l(w),"label-width":"100px"},{default:s(()=>[d(m,{label:"\u7528\u6237\u7F16\u53F7",prop:"id"},{default:s(()=>[d(p,{modelValue:l(a).id,"onUpdate:modelValue":e[0]||(e[0]=o=>l(a).id=o),placeholder:"\u8BF7\u8F93\u5165\u7528\u6237\u6635\u79F0",class:"!w-240px",disabled:""},null,8,["modelValue"])]),_:1}),d(m,{label:"\u7528\u6237\u6635\u79F0",prop:"nickname"},{default:s(()=>[d(p,{modelValue:l(a).nickname,"onUpdate:modelValue":e[1]||(e[1]=o=>l(a).nickname=o),placeholder:"\u8BF7\u8F93\u5165\u7528\u6237\u6635\u79F0",class:"!w-240px",disabled:""},null,8,["modelValue"])]),_:1}),d(m,{label:"\u7528\u6237\u7B49\u7EA7",prop:"levelId"},{default:s(()=>[d(S,{modelValue:l(a).levelId,"onUpdate:modelValue":e[2]||(e[2]=o=>l(a).levelId=o)},null,8,["modelValue"])]),_:1}),d(m,{label:"\u4FEE\u6539\u539F\u56E0",prop:"reason"},{default:s(()=>[d(p,{type:"textarea",modelValue:l(a).reason,"onUpdate:modelValue":e[3]||(e[3]=o=>l(a).reason=o),placeholder:"\u8BF7\u8F93\u5165\u4FEE\u6539\u539F\u56E0"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])),[[C,l(r)]])]),_:1},8,["modelValue"])}}});export{K as _};
