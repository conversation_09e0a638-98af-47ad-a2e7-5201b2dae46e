import{a as W,d as L,I as v}from"./index-Byekp3Iv.js";import{_ as R}from"./ContentWrap.vue_vue_type_script_setup_true_lang-D3mNNG_R.js";import{d as k}from"./formatTime-HVkyL6Kg.js";import{W as f}from"./index-BC-xKroV.js";import{P as C}from"./index-BKpB9XUZ.js";import{R as F}from"./index-ByxWixVk.js";import{f as h}from"./formatter-CF7Ifi5S.js";import{g as M,d as j}from"./commonBiz-BtTo-n4b.js";import{ak as U,Z as V,_ as Y}from"./form-designer-C0ARe9Dh.js";import{k as H,r as O,b as Z,e as $,y as I,m as A,z as y,A as D,u as c,H as t,E as w,F as g}from"./form-create-B86qX0W_.js";const q=H({__name:"WorkOrderDetailList",props:{bizOrderId:{},detailType:{}},setup(B){const{t:G}=W();L();const d=B,N=O(!1),o=O([]),z=O(new Map),T=O(new Map),Q=async()=>{try{const e=await M();if(!e||e.length===0)return;e.forEach(a=>{a&&a.id&&a.name&&z.value.set(a.id,a.name)})}catch{}},S=async()=>{try{const e=await j();if(!e)return;Object.keys(e).forEach(a=>{const r=e[a];r&&r.id&&r.name&&T.value.set(r.id,r.name)})}catch{}},P=e=>{if(!e)return"";const a=typeof e=="string"?parseInt(e):e;return z.value.get(a)||e.toString()},E=e=>{if(!e)return"";const a=typeof e=="string"?parseInt(e):e;return T.value.get(a)||e.toString()},_=async()=>{N.value=!0;try{switch(d.detailType){case"workOrderDetail":try{const e=await f.getWorkOrder(d.bizOrderId);if(e&&e.workNo){const a=await C.getPickingReceiptPage({sourceNo:e.workNo,pageNo:1,pageSize:100});a&&Array.isArray(a.list)?o.value=a.list:a&&Array.isArray(a)?o.value=a:o.value=[]}else o.value=[]}catch{try{const a=await f.getWorkOrderDetailListByBizOrderId(d.bizOrderId);o.value=Array.isArray(a)?a:[]}catch{o.value=[]}}break;case"feedOrderDetail":try{const e=await f.getWorkOrderDetailListByBizOrderId(d.bizOrderId);o.value=Array.isArray(e)?e:[]}catch{o.value=[]}break;case"reportOrderDetail":try{const e=await F.getReportOrderPage({workOrderId:d.bizOrderId,pageNo:1,pageSize:100});e&&Array.isArray(e.list)?o.value=e.list:e&&Array.isArray(e)?o.value=e:o.value=[]}catch{try{const a=await f.getWorkOrderDetailListByBizOrderId(d.bizOrderId);o.value=Array.isArray(a)?a:[]}catch{o.value=[]}}break;case"inspectOrderDetail":try{const e=await f.getWorkOrderDetailListByBizOrderId(d.bizOrderId);o.value=Array.isArray(e)?e:[]}catch{o.value=[]}break;case"stockInOrderDetail":try{const e=await f.getWorkOrderDetailListByBizOrderId(d.bizOrderId);o.value=Array.isArray(e)?e:[]}catch{o.value=[]}break;default:try{const e=await f.getWorkOrderDetailListByBizOrderId(d.bizOrderId);o.value=Array.isArray(e)?e:[]}catch{o.value=[]}}}finally{N.value=!1}};Z(()=>({bizOrderId:d.bizOrderId,detailType:d.detailType}),async()=>{d.bizOrderId&&await _()},{immediate:!1});const x=e=>{const{columns:a,data:r}=e,s=[];return a.forEach((b,n)=>{if(n!==0)if(d.detailType!=="workOrderDetail")if(b.property==="plannedQuantity"||n===11){const l=r.map(i=>Number(i.plannedQuantity)||0);if(l.every(i=>Number.isNaN(i)))s[n]="";else{const i=l.reduce((p,m)=>{const u=Number(m);return Number.isNaN(u)?p:p+u},0);s[n]=h(i)}}else if(b.property==="fulfilledQuantity"||n===12){const l=r.map(i=>Number(i.fulfilledQuantity)||0);if(l.every(i=>Number.isNaN(i)))s[n]="";else{const i=l.reduce((p,m)=>{const u=Number(m);return Number.isNaN(u)?p:p+u},0);s[n]=h(i)}}else if(b.property==="standardPlannedQuantity"||n===13){const l=r.map(i=>Number(i.standardPlannedQuantity)||0);if(l.every(i=>Number.isNaN(i)))s[n]="";else{const i=l.reduce((p,m)=>{const u=Number(m);return Number.isNaN(u)?p:p+u},0);s[n]=h(i)}}else if(b.property==="standardFulfilledQuantity"||n===14){const l=r.map(i=>Number(i.standardFulfilledQuantity)||0);if(l.every(i=>Number.isNaN(i)))s[n]="";else{const i=l.reduce((p,m)=>{const u=Number(m);return Number.isNaN(u)?p:p+u},0);s[n]=h(i)}}else s[n]="";else s[n]=n===1?`\u5171 ${r.length} \u6761\u8BB0\u5F55`:"";else s[n]="\u5408\u8BA1"}),s};return $(async()=>{await Promise.all([Q(),S()]),_()}),(e,a)=>{const r=Y,s=V,b=R,n=U;return A(),I(b,null,{default:y(()=>[d.detailType==="workOrderDetail"?D((A(),I(s,{key:0,data:c(o),stripe:!0,"show-overflow-tooltip":!0,"show-summary":"","summary-method":x,border:"",height:"600"},{default:y(()=>[t(r,{label:"\u5355\u53F7",align:"left",prop:"orderNo",width:"120px",fixed:"left"}),t(r,{label:"\u4E1A\u52A1\u7C7B\u578B",align:"left",prop:"bizType",width:"100px"},{default:y(l=>[w(g(c(v)("INVENTORY_TRANSACTION_TYPE",l.row.bizType)),1)]),_:1}),t(r,{label:"\u6765\u6E90\u7C7B\u578B",align:"center",prop:"sourceType",width:"100px"},{default:y(l=>[w(g(c(v)("MATERIAL_SOURCE",l.row.sourceType)),1)]),_:1}),t(r,{label:"\u6765\u6E90\u5355\u7F16\u53F7",align:"left",prop:"sourceNo",width:"120px"}),t(r,{label:"\u4EA4\u6613\u65E5\u671F",align:"center",prop:"date",formatter:c(k),width:"100px"},null,8,["formatter"]),t(r,{label:"\u5BA1\u6279\u72B6\u6001",align:"center",prop:"approveStatus",width:"100px"},{default:y(l=>[w(g(c(v)("APPROVE_STATUS",l.row.approveStatus)),1)]),_:1}),t(r,{label:"\u5BA1\u6279\u5355\u53F7",align:"left",prop:"approveNo",width:"120px"}),t(r,{label:"\u5BA1\u6279\u4EBA",align:"left",prop:"approverName",width:"100px"}),t(r,{label:"\u5BA1\u6279\u65F6\u95F4",align:"center",prop:"approveDate",formatter:c(k),width:"180px"},null,8,["formatter"]),t(r,{label:"\u6458\u8981",align:"left",prop:"note",width:"120px"}),t(r,{label:"\u5907\u6CE8",align:"left",prop:"remark",width:"120px"}),t(r,{label:"\u521B\u5EFA\u65F6\u95F4",align:"center",prop:"createTime",formatter:c(k),width:"180px"},null,8,["formatter"])]),_:1},8,["data"])),[[n,c(N)]]):D((A(),I(s,{key:1,data:c(o),stripe:!0,"show-overflow-tooltip":!0,"show-summary":"","summary-method":x},{default:y(()=>[t(r,{label:"\u5E8F\u53F7",align:"center",prop:"num",width:"70"}),t(r,{label:"\u4ED3\u5E93",align:"center",width:"120px"},{default:y(l=>[w(g(E(l.row.warehouseId)||l.row.warehouseId||""),1)]),_:1}),t(r,{label:"\u7269\u6599\u540D\u79F0",align:"left",prop:"materialName"}),t(r,{label:"\u7269\u6599\u7F16\u53F7",align:"center",prop:"materialCode"}),t(r,{label:"\u89C4\u683C",align:"center",prop:"spec"}),t(r,{label:"\u5355\u4F4D",align:"center",width:"80px"},{default:y(l=>[w(g(P(l.row.unit)||l.row.unit||""),1)]),_:1}),t(r,{label:"\u5907\u6CE8",align:"center",prop:"remark"}),t(r,{label:"\u8BA1\u5212\u6570\u91CF",align:"center",prop:"plannedQuantity"}),t(r,{label:"\u5C65\u7EA6\u6570\u91CF",align:"center",prop:"fulfilledQuantity"}),t(r,{label:"\u89C4\u683C\u6570\u91CF",align:"center",prop:"plannedSpecQuantity"}),t(r,{label:"\u5C65\u7EA6\u89C4\u683C\u6570\u91CF",align:"center",prop:"fulfilledSpecQuantity"}),t(r,{label:"\u8BF4\u660E",align:"center",prop:"note"}),t(r,{label:"\u6279\u53F7",align:"center",prop:"batchNo"})]),_:1},8,["data"])),[[n,c(N)]])]),_:1})}}});export{q as _};
