import{a as H,d as P}from"./index-Byekp3Iv.js";import{_ as R}from"./Dialog.vue_vue_type_style_index_0_lang-DhV_HFpU.js";import{W as _}from"./index-DWM7TlB0.js";import{WarehouseApi as D}from"./index-DmNtGmQq.js";import{h as Y}from"./tree-COGD3qag.js";import{h as q,i as B,j as G,aj as J,k as K,a1 as M,ak as O,f as Q}from"./form-designer-C0ARe9Dh.js";import{k as T,r as s,P as X,y as N,m as U,z as d,A as Z,u as o,H as e,E as k,h as $}from"./form-create-B86qX0W_.js";const ee=T({name:"WarehouseLocationForm",__name:"WarehouseLocationForm",emits:["success"],setup(ae,{expose:W,emit:g}){const{t:f}=H(),b=P(),p=s(!1),w=s(""),c=s(!1),I=s(""),l=s({id:void 0,code:void 0,name:void 0,warehouseId:void 0,warehouseName:void 0,district:void 0,row:void 0,column:void 0,floor:void 0,available:void 0,capacity:void 0,materialId:void 0,materialName:void 0,batchId:void 0,batchNo:void 0,remark:void 0,tenantName:void 0}),L=X({}),h=s(),V=s([]);W({open:async(m,a)=>{if(p.value=!0,w.value=f("action."+m),I.value=m,F(),await j(),a){c.value=!0;try{l.value=await _.getWarehouseLocation(a)}finally{c.value=!1}}}});const x=g,C=async()=>{await h.value.validate(),c.value=!0;try{const m=l.value;I.value==="create"?(await _.createWarehouseLocation(m),b.success(f("common.createSuccess"))):(await _.updateWarehouseLocation(m),b.success(f("common.updateSuccess"))),p.value=!1,x("success")}finally{c.value=!1}},F=()=>{var m;l.value={id:void 0,code:void 0,name:void 0,warehouseId:void 0,warehouseName:void 0,district:void 0,row:void 0,column:void 0,floor:void 0,available:void 0,capacity:void 0,materialId:void 0,materialName:void 0,batchId:void 0,batchNo:void 0,remark:void 0,tenantName:void 0},(m=h.value)==null||m.resetFields()},j=async()=>{const m=await D.getWarehouseList({});V.value=Y(m,"id","parentId")},A=m=>{const a=(i,r)=>{for(const n of i){if(n.id===r)return n;if(n.children&&n.children.length>0){const v=a(n.children,r);if(v)return v}}return null},t=a(V.value,m);t?(l.value.warehouseName=t.name,l.value.warehouseId=t.id):(l.value.warehouseName=void 0,l.value.warehouseId=void 0)};return(m,a)=>{const t=K,i=J,r=G,n=M,v=B,S=q,y=Q,z=R,E=O;return U(),N(z,{title:o(w),modelValue:o(p),"onUpdate:modelValue":a[15]||(a[15]=u=>$(p)?p.value=u:null)},{footer:d(()=>[e(y,{onClick:C,type:"primary",disabled:o(c)},{default:d(()=>a[16]||(a[16]=[k("\u786E \u5B9A")])),_:1},8,["disabled"]),e(y,{onClick:a[14]||(a[14]=u=>p.value=!1)},{default:d(()=>a[17]||(a[17]=[k("\u53D6 \u6D88")])),_:1})]),default:d(()=>[Z((U(),N(S,{ref_key:"formRef",ref:h,model:o(l),rules:o(L),"label-width":"auto",inline:""},{default:d(()=>[e(v,{gutter:20},{default:d(()=>[e(r,{span:12},{default:d(()=>[e(i,{label:"\u5E93\u4F4D\u7F16\u7801",prop:"code"},{default:d(()=>[e(t,{modelValue:o(l).code,"onUpdate:modelValue":a[0]||(a[0]=u=>o(l).code=u),placeholder:"\u8BF7\u8F93\u5165\u5E93\u4F4D\u7F16\u7801"},null,8,["modelValue"])]),_:1})]),_:1}),e(r,{span:12},{default:d(()=>[e(i,{label:"\u5E93\u4F4D\u540D\u79F0",prop:"name"},{default:d(()=>[e(t,{modelValue:o(l).name,"onUpdate:modelValue":a[1]||(a[1]=u=>o(l).name=u),placeholder:"\u8BF7\u8F93\u5165\u5E93\u4F4D\u540D\u79F0"},null,8,["modelValue"])]),_:1})]),_:1}),e(r,{span:12},{default:d(()=>[e(i,{label:"\u4ED3\u5E93\u540D\u79F0",prop:"warehouseId"},{default:d(()=>[e(n,{modelValue:o(l).warehouseId,"onUpdate:modelValue":a[2]||(a[2]=u=>o(l).warehouseId=u),data:o(V),props:{value:"id",label:"name",children:"children"},placeholder:"\u8BF7\u9009\u62E9\u4ED3\u5E93",clearable:"",filterable:"",class:"!w-240px","node-key":"id","render-after-expand":!1,onChange:A},null,8,["modelValue","data"])]),_:1})]),_:1}),e(r,{span:12},{default:d(()=>[e(i,{label:"\u533A\u57DF",prop:"district"},{default:d(()=>[e(t,{modelValue:o(l).district,"onUpdate:modelValue":a[3]||(a[3]=u=>o(l).district=u),placeholder:"\u8BF7\u8F93\u5165\u533A\u57DF"},null,8,["modelValue"])]),_:1})]),_:1}),e(r,{span:12},{default:d(()=>[e(i,{label:"\u7B2C\u51E0\u884C\uFF08\u6392\uFF09",prop:"row"},{default:d(()=>[e(t,{modelValue:o(l).row,"onUpdate:modelValue":a[4]||(a[4]=u=>o(l).row=u),placeholder:"\u8BF7\u8F93\u5165\u7B2C\u51E0\u884C\uFF08\u6392\uFF09"},null,8,["modelValue"])]),_:1})]),_:1}),e(r,{span:12},{default:d(()=>[e(i,{label:"\u7B2C\u51E0\u5217",prop:"column"},{default:d(()=>[e(t,{modelValue:o(l).column,"onUpdate:modelValue":a[5]||(a[5]=u=>o(l).column=u),placeholder:"\u8BF7\u8F93\u5165\u7B2C\u51E0\u5217"},null,8,["modelValue"])]),_:1})]),_:1}),e(r,{span:12},{default:d(()=>[e(i,{label:"\u7B2C\u51E0\u5C42",prop:"floor"},{default:d(()=>[e(t,{modelValue:o(l).floor,"onUpdate:modelValue":a[6]||(a[6]=u=>o(l).floor=u),placeholder:"\u8BF7\u8F93\u5165\u7B2C\u51E0\u5C42"},null,8,["modelValue"])]),_:1})]),_:1}),e(r,{span:12},{default:d(()=>[e(i,{label:"\u5269\u4F59\u5BB9\u91CF",prop:"available"},{default:d(()=>[e(t,{modelValue:o(l).available,"onUpdate:modelValue":a[7]||(a[7]=u=>o(l).available=u),placeholder:"\u8BF7\u8F93\u5165\u5269\u4F59\u5BB9\u91CF"},null,8,["modelValue"])]),_:1})]),_:1}),e(r,{span:12},{default:d(()=>[e(i,{label:"\u5BB9\u91CF",prop:"capacity"},{default:d(()=>[e(t,{modelValue:o(l).capacity,"onUpdate:modelValue":a[8]||(a[8]=u=>o(l).capacity=u),placeholder:"\u8BF7\u8F93\u5165\u5BB9\u91CF"},null,8,["modelValue"])]),_:1})]),_:1}),e(r,{span:12},{default:d(()=>[e(i,{label:"\u7269\u6599\u7F16\u53F7",prop:"materialId"},{default:d(()=>[e(t,{modelValue:o(l).materialId,"onUpdate:modelValue":a[9]||(a[9]=u=>o(l).materialId=u),placeholder:"\u8BF7\u8F93\u5165\u7269\u6599\u7F16\u53F7"},null,8,["modelValue"])]),_:1})]),_:1}),e(r,{span:12},{default:d(()=>[e(i,{label:"\u7269\u6599\u540D\u79F0",prop:"materialName"},{default:d(()=>[e(t,{modelValue:o(l).materialName,"onUpdate:modelValue":a[10]||(a[10]=u=>o(l).materialName=u),placeholder:"\u8BF7\u8F93\u5165\u7269\u6599\u540D\u79F0"},null,8,["modelValue"])]),_:1})]),_:1}),e(r,{span:12},{default:d(()=>[e(i,{label:"\u6279\u53F7\u552F\u4E00\u6807\u8BC6",prop:"batchId"},{default:d(()=>[e(t,{modelValue:o(l).batchId,"onUpdate:modelValue":a[11]||(a[11]=u=>o(l).batchId=u),placeholder:"\u8BF7\u8F93\u5165\u6279\u53F7\u552F\u4E00\u6807\u8BC6"},null,8,["modelValue"])]),_:1})]),_:1}),e(r,{span:12},{default:d(()=>[e(i,{label:"\u6279\u53F7",prop:"batchNo"},{default:d(()=>[e(t,{modelValue:o(l).batchNo,"onUpdate:modelValue":a[12]||(a[12]=u=>o(l).batchNo=u),placeholder:"\u8BF7\u8F93\u5165\u6279\u53F7"},null,8,["modelValue"])]),_:1})]),_:1}),e(r,{span:12},{default:d(()=>[e(i,{label:"\u5907\u6CE8",prop:"remark"},{default:d(()=>[e(t,{modelValue:o(l).remark,"onUpdate:modelValue":a[13]||(a[13]=u=>o(l).remark=u),placeholder:"\u8BF7\u8F93\u5165\u5907\u6CE8"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model","rules"])),[[E,o(c)]])]),_:1},8,["title","modelValue"])}}});export{ee as _};
