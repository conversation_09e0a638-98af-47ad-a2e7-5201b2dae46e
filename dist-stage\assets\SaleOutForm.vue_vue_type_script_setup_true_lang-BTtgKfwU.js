import{a as le,d as te,_ as oe,X as ue,ax as g,ay as de}from"./index-Byekp3Iv.js";import{_ as re}from"./Dialog.vue_vue_type_style_index_0_lang-DhV_HFpU.js";import{_ as se}from"./ContentWrap.vue_vue_type_script_setup_true_lang-D3mNNG_R.js";import{S}from"./index-Dth2jxQN.js";import{_ as ie}from"./SaleOutItemForm.vue_vue_type_script_setup_true_lang-CQ9DmXPy.js";import{C as me}from"./index-C7EeEAX_.js";import{A as ne}from"./index-BlTZBlHS.js";import{_ as ce}from"./SaleOrderOutEnableList.vue_vue_type_script_setup_true_lang-CBrA1CiW.js";import{g as pe}from"./index-DP9Sf6UT.js";import{h as fe,i as ve,j as _e,aj as be,k as Ve,F as Pe,f as Ue,x as Ie,Q as he,a0 as ke,$ as ye,l as we,ak as ge}from"./form-designer-C0ARe9Dh.js";import{k as Se,r as m,P as xe,c as Ce,b as Oe,l as v,m as n,G as _,H as a,u as t,h as R,z as o,A as Te,y as f,E as x,$ as C,C as Fe}from"./form-create-B86qX0W_.js";const Ne=Se({name:"SaleOutForm",__name:"SaleOutForm",emits:["success"],setup(je,{expose:q,emit:H}){const{t:b}=le(),O=te(),c=m(!1),T=m(""),p=m(!1),V=m(""),l=m({id:void 0,customerId:void 0,accountId:void 0,saleUserId:void 0,outTime:void 0,remark:void 0,fileUrl:"",discountPercent:0,discountPrice:0,totalPrice:0,otherPrice:0,orderNo:void 0,items:[],no:void 0}),X=xe({customerId:[{required:!0,message:"\u5BA2\u6237\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],outTime:[{required:!0,message:"\u51FA\u5E93\u65F6\u95F4\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}]}),P=Ce(()=>V.value==="detail"),U=m(),F=m([]),I=m([]),N=m([]),h=m("item"),j=m();Oe(()=>l.value,d=>{if(!d)return;const e=d.items.reduce((r,s)=>r+s.totalPrice,0),i=d.discountPercent!=null?de(e,d.discountPercent/100):0;l.value.discountPrice=i,l.value.totalPrice=e-i+d.otherPrice},{deep:!0}),q({open:async(d,e)=>{if(c.value=!0,T.value=b("action."+d),V.value=d,Q(),e){p.value=!0;try{l.value=await S.getSaleOut(e)}finally{p.value=!1}}F.value=await me.getCustomerSimpleList(),N.value=await pe(),I.value=await ne.getAccountSimpleList();const i=I.value.find(r=>r.defaultStatus);i&&(l.value.accountId=i.id)}});const A=m(),$=()=>{A.value.open()},z=d=>{l.value.orderId=d.id,l.value.orderNo=d.no,l.value.customerId=d.customerId,l.value.accountId=d.accountId,l.value.saleUserId=d.saleUserId,l.value.discountPercent=d.discountPercent,l.value.remark=d.remark,l.value.fileUrl=d.fileUrl,d.items.forEach(e=>{e.totalCount=e.count,e.count=e.totalCount-e.outCount,e.orderItemId=e.id,e.id=void 0}),l.value.items=d.items.filter(e=>e.count>0)},G=H,K=async()=>{await U.value.validate(),await j.value.validate(),p.value=!0;try{const d=l.value;V.value==="create"?(await S.createSaleOut(d),O.success(b("common.createSuccess"))):(await S.updateSaleOut(d),O.success(b("common.updateSuccess"))),c.value=!1,G("success")}finally{p.value=!1}},Q=()=>{var d;l.value={id:void 0,customerId:void 0,accountId:void 0,saleUserId:void 0,outTime:void 0,remark:void 0,fileUrl:void 0,discountPercent:0,discountPrice:0,totalPrice:0,otherPrice:0,items:[]},(d=U.value)==null||d.resetFields()};return(d,e)=>{const i=Ve,r=be,s=_e,W=Pe,B=oe,k=Ue,y=he,w=Ie,D=ue,E=ve,J=ye,M=ke,Y=se,L=we,Z=fe,ee=re,ae=ge;return n(),v(_,null,[a(ee,{title:t(T),modelValue:t(c),"onUpdate:modelValue":e[14]||(e[14]=u=>R(c)?c.value=u:null),width:"1440"},{footer:o(()=>[t(P)?Fe("",!0):(n(),f(k,{key:0,onClick:K,type:"primary",disabled:t(p)},{default:o(()=>e[16]||(e[16]=[x(" \u786E \u5B9A ")])),_:1},8,["disabled"])),a(k,{onClick:e[13]||(e[13]=u=>c.value=!1)},{default:o(()=>e[17]||(e[17]=[x("\u53D6 \u6D88")])),_:1})]),default:o(()=>[Te((n(),f(Z,{ref_key:"formRef",ref:U,model:t(l),rules:t(X),"label-width":"100px",disabled:t(P)},{default:o(()=>[a(E,{gutter:20},{default:o(()=>[a(s,{span:8},{default:o(()=>[a(r,{label:"\u51FA\u5E93\u5355\u53F7",prop:"no"},{default:o(()=>[a(i,{disabled:"",modelValue:t(l).no,"onUpdate:modelValue":e[0]||(e[0]=u=>t(l).no=u),placeholder:"\u4FDD\u5B58\u65F6\u81EA\u52A8\u751F\u6210"},null,8,["modelValue"])]),_:1})]),_:1}),a(s,{span:8},{default:o(()=>[a(r,{label:"\u51FA\u5E93\u65F6\u95F4",prop:"outTime"},{default:o(()=>[a(W,{modelValue:t(l).outTime,"onUpdate:modelValue":e[1]||(e[1]=u=>t(l).outTime=u),type:"date","value-format":"x",placeholder:"\u9009\u62E9\u51FA\u5E93\u65F6\u95F4",class:"!w-1/1"},null,8,["modelValue"])]),_:1})]),_:1}),a(s,{span:8},{default:o(()=>[a(r,{label:"\u5173\u8054\u8BA2\u5355",prop:"orderNo"},{default:o(()=>[a(i,{modelValue:t(l).orderNo,"onUpdate:modelValue":e[2]||(e[2]=u=>t(l).orderNo=u),readonly:""},{append:o(()=>[a(k,{onClick:$},{default:o(()=>[a(B,{icon:"ep:search"}),e[15]||(e[15]=x(" \u9009\u62E9 "))]),_:1})]),_:1},8,["modelValue"])]),_:1})]),_:1}),a(s,{span:8},{default:o(()=>[a(r,{label:"\u5BA2\u6237",prop:"customerId"},{default:o(()=>[a(w,{modelValue:t(l).customerId,"onUpdate:modelValue":e[3]||(e[3]=u=>t(l).customerId=u),clearable:"",filterable:"",disabled:"",placeholder:"\u8BF7\u9009\u62E9\u5BA2\u6237",class:"!w-1/1"},{default:o(()=>[(n(!0),v(_,null,C(t(F),u=>(n(),f(y,{key:u.id,label:u.name,value:u.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),a(s,{span:8},{default:o(()=>[a(r,{label:"\u9500\u552E\u4EBA\u5458",prop:"saleUserId"},{default:o(()=>[a(w,{modelValue:t(l).saleUserId,"onUpdate:modelValue":e[4]||(e[4]=u=>t(l).saleUserId=u),clearable:"",filterable:"",placeholder:"\u8BF7\u9009\u62E9\u9500\u552E\u4EBA\u5458",class:"!w-1/1"},{default:o(()=>[(n(!0),v(_,null,C(t(N),u=>(n(),f(y,{key:u.id,label:u.nickname,value:u.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),a(s,{span:16},{default:o(()=>[a(r,{label:"\u5907\u6CE8",prop:"remark"},{default:o(()=>[a(i,{type:"textarea",modelValue:t(l).remark,"onUpdate:modelValue":e[5]||(e[5]=u=>t(l).remark=u),rows:1,placeholder:"\u8BF7\u8F93\u5165\u5907\u6CE8"},null,8,["modelValue"])]),_:1})]),_:1}),a(s,{span:8},{default:o(()=>[a(r,{label:"\u9644\u4EF6",prop:"fileUrl"},{default:o(()=>[a(D,{"is-show-tip":!1,modelValue:t(l).fileUrl,"onUpdate:modelValue":e[6]||(e[6]=u=>t(l).fileUrl=u),limit:1},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),a(Y,null,{default:o(()=>[a(M,{modelValue:t(h),"onUpdate:modelValue":e[7]||(e[7]=u=>R(h)?h.value=u:null),class:"-mt-15px -mb-10px"},{default:o(()=>[a(J,{label:"\u51FA\u5E93\u4EA7\u54C1\u6E05\u5355",name:"item"},{default:o(()=>[a(ie,{ref_key:"itemFormRef",ref:j,items:t(l).items,disabled:t(P)},null,8,["items","disabled"])]),_:1})]),_:1},8,["modelValue"])]),_:1}),a(E,{gutter:20},{default:o(()=>[a(s,{span:8},{default:o(()=>[a(r,{label:"\u4F18\u60E0\u7387\uFF08%\uFF09",prop:"discountPercent"},{default:o(()=>[a(L,{modelValue:t(l).discountPercent,"onUpdate:modelValue":e[8]||(e[8]=u=>t(l).discountPercent=u),"controls-position":"right",min:0,precision:2,placeholder:"\u8BF7\u8F93\u5165\u4F18\u60E0\u7387",class:"!w-1/1"},null,8,["modelValue"])]),_:1})]),_:1}),a(s,{span:8},{default:o(()=>[a(r,{label:"\u6536\u6B3E\u4F18\u60E0",prop:"discountPrice"},{default:o(()=>[a(i,{disabled:"",modelValue:t(l).discountPrice,"onUpdate:modelValue":e[9]||(e[9]=u=>t(l).discountPrice=u),formatter:t(g)},null,8,["modelValue","formatter"])]),_:1})]),_:1}),a(s,{span:8},{default:o(()=>[a(r,{label:"\u4F18\u60E0\u540E\u91D1\u989D"},{default:o(()=>[a(i,{disabled:"","model-value":t(l).totalPrice-t(l).otherPrice,formatter:t(g)},null,8,["model-value","formatter"])]),_:1})]),_:1}),a(s,{span:8},{default:o(()=>[a(r,{label:"\u5176\u5B83\u8D39\u7528",prop:"otherPrice"},{default:o(()=>[a(L,{modelValue:t(l).otherPrice,"onUpdate:modelValue":e[10]||(e[10]=u=>t(l).otherPrice=u),"controls-position":"right",min:0,precision:2,placeholder:"\u8BF7\u8F93\u5165\u5176\u5B83\u8D39\u7528",class:"!w-1/1"},null,8,["modelValue"])]),_:1})]),_:1}),a(s,{span:8},{default:o(()=>[a(r,{label:"\u7ED3\u7B97\u8D26\u6237",prop:"accountId"},{default:o(()=>[a(w,{modelValue:t(l).accountId,"onUpdate:modelValue":e[11]||(e[11]=u=>t(l).accountId=u),clearable:"",filterable:"",placeholder:"\u8BF7\u9009\u62E9\u7ED3\u7B97\u8D26\u6237",class:"!w-1/1"},{default:o(()=>[(n(!0),v(_,null,C(t(I),u=>(n(),f(y,{key:u.id,label:u.name,value:u.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),a(s,{span:8},{default:o(()=>[a(r,{label:"\u5E94\u6536\u91D1\u989D"},{default:o(()=>[a(i,{disabled:"",modelValue:t(l).totalPrice,"onUpdate:modelValue":e[12]||(e[12]=u=>t(l).totalPrice=u),formatter:t(g)},null,8,["modelValue","formatter"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model","rules","disabled"])),[[ae,t(p)]])]),_:1},8,["title","modelValue"]),a(ce,{ref_key:"saleOrderOutEnableListRef",ref:A,onSuccess:z},null,512)],64)}}});export{Ne as _};
