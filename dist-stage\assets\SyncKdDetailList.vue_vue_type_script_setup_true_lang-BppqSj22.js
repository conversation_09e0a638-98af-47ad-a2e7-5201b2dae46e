import{a as G,d as H,_ as L,D as j}from"./index-Byekp3Iv.js";import{_ as F}from"./ContentWrap.vue_vue_type_script_setup_true_lang-D3mNNG_R.js";import{_ as J}from"./index.vue_vue_type_script_setup_true_lang-BeMNDf6p.js";import{_ as R}from"./DictTag.vue_vue_type_script_lang-DdZ_pRVv.js";import{d as b}from"./formatTime-HVkyL6Kg.js";import{S as I}from"./index-BNFkcgor.js";import{_ as Y}from"./SyncKdDetailForm.vue_vue_type_script_setup_true_lang-f0CLFNel.js";import{f as Z,ak as q,_ as B,Z as M}from"./form-designer-C0ARe9Dh.js";import{k as O,r as c,P as Q,b as V,af as W,l as X,m as i,G as $,H as a,z as n,A as m,y as d,E as k,u as t}from"./form-create-B86qX0W_.js";const aa=O({__name:"SyncKdDetailList",props:{syncId:{}},setup(h){const{t:D}=G(),u=H(),f=h,y=c(!1),w=c([]),S=c(0),s=Q({pageNo:1,pageSize:10,syncId:void 0});V(()=>f.syncId,o=>{o&&(s.syncId=o,T())},{immediate:!0,deep:!0});const p=async()=>{y.value=!0;try{const o=await I.getSyncKdDetailPage(s);w.value=o.list,S.value=o.total}finally{y.value=!1}},T=()=>{s.pageNo=1,p()},v=c(),C=(o,e)=>{f.syncId?v.value.open(o,e,f.syncId):u.error("\u8BF7\u9009\u62E9\u4E00\u4E2A\u6570\u636E\u540C\u6B65\u72B6\u6001")};return(o,e)=>{const x=L,g=Z,l=B,K=R,N=M,P=J,z=F,_=W("hasPermi"),U=q;return i(),X($,null,[a(z,null,{default:n(()=>[m((i(),d(g,{type:"primary",plain:"",onClick:e[0]||(e[0]=r=>C("create"))},{default:n(()=>[a(x,{icon:"ep:plus",class:"mr-5px"}),e[3]||(e[3]=k(" \u65B0\u589E "))]),_:1})),[[_,["base:sync-kd:create"]]]),m((i(),d(N,{data:t(w),stripe:!0,"show-overflow-tooltip":!0},{default:n(()=>[a(l,{label:"ID",align:"center",prop:"id"}),a(l,{label:"\u540C\u6B65\u72B6\u6001",align:"center",prop:"status"},{default:n(r=>[a(K,{type:t(j).KD_SYNC_STATUS,value:r.row.status},null,8,["type","value"])]),_:1}),a(l,{label:"\u5F00\u59CB\u65F6\u95F4",align:"center",prop:"startTime",formatter:t(b),width:"180px"},null,8,["formatter"]),a(l,{label:"\u7ED3\u675F\u65F6\u95F4",align:"center",prop:"endTime",formatter:t(b),width:"180px"},null,8,["formatter"]),a(l,{label:"\u5907\u6CE8",align:"center",prop:"remark"}),a(l,{label:"\u521B\u5EFA\u65F6\u95F4",align:"center",prop:"createTime",formatter:t(b),width:"180px"},null,8,["formatter"]),a(l,{label:"\u91D1\u8776\u6570\u636E\u5E93",align:"center",prop:"db"}),a(l,{label:"\u4E0A\u6B21\u540C\u6B65\u9875\u7801",align:"center",prop:"lastPage"}),a(l,{label:"\u540C\u6B65\u53C2\u6570",align:"center",prop:"params"}),a(l,{label:"\u64CD\u4F5C",align:"center"},{default:n(r=>[m((i(),d(g,{link:"",type:"primary",onClick:A=>C("update",r.row.id)},{default:n(()=>e[4]||(e[4]=[k(" \u7F16\u8F91 ")])),_:2},1032,["onClick"])),[[_,["base:sync-kd:update"]]]),m((i(),d(g,{link:"",type:"danger",onClick:A=>(async E=>{try{await u.delConfirm(),await I.deleteSyncKdDetail(E),u.success(D("common.delSuccess")),await p()}catch{}})(r.row.id)},{default:n(()=>e[5]||(e[5]=[k(" \u5220\u9664 ")])),_:2},1032,["onClick"])),[[_,["base:sync-kd:delete"]]])]),_:1})]),_:1},8,["data"])),[[U,t(y)]]),a(P,{total:t(S),page:t(s).pageNo,"onUpdate:page":e[1]||(e[1]=r=>t(s).pageNo=r),limit:t(s).pageSize,"onUpdate:limit":e[2]||(e[2]=r=>t(s).pageSize=r),onPagination:p},null,8,["total","page","limit"])]),_:1}),a(Y,{ref_key:"formRef",ref:v,onSuccess:p},null,512)],64)}}});export{aa as _};
