import{_ as A,aN as U,aw as T}from"./index-Byekp3Iv.js";import{_ as B}from"./Dialog.vue_vue_type_style_index_0_lang-DhV_HFpU.js";import{_ as K}from"./index.vue_vue_type_script_setup_true_lang-BeMNDf6p.js";import{_ as Q}from"./ContentWrap.vue_vue_type_script_setup_true_lang-D3mNNG_R.js";import{k as R,r as d,P as $,y as _,m as f,z as o,H as e,u as l,Z as J,l as W,G as X,$ as ee,E as p,A as ae,h as z,n as le}from"./form-create-B86qX0W_.js";import{S as te}from"./index-D8pULT6U.js";import{b as oe}from"./formatTime-HVkyL6Kg.js";import{P as re}from"./index-1HLiV7Gt.js";import{h as ne,aj as de,k as ie,x as pe,Q as se,F as me,f as ue,Z as ce,_ as fe,u as ve,ak as ge}from"./form-designer-C0ARe9Dh.js";const be=R({name:"ErpSaleOrderOutEnableList",__name:"SaleOrderOutEnableList",emits:["success"],setup(_e,{expose:E,emit:O}){const w=d([]),h=d(0),v=d(!1),i=d(!1),r=$({pageNo:1,pageSize:10,no:void 0,productId:void 0,orderTime:[],outEnable:!0}),y=d(),V=d([]),s=d(void 0),m=d(void 0);E({open:async()=>{i.value=!0,await le(),await k(),V.value=await re.getProductSimpleList()}});const D=O,I=()=>{try{D("success",m.value)}finally{i.value=!1}},x=async()=>{v.value=!0;try{const b=await te.getSaleOrderPage(r);w.value=b.list,h.value=b.total}finally{v.value=!1}},k=()=>{y.value.resetFields(),g()},g=()=>{r.pageNo=1,s.value=void 0,m.value=void 0,x()};return(b,a)=>{const Y=ie,u=de,F=se,H=pe,L=me,C=A,c=ue,j=ne,N=Q,G=ve,n=fe,M=K,Z=B,q=ge;return f(),_(Z,{title:"\u9009\u62E9\u9500\u552E\u8BA2\u5355\uFF08\u4EC5\u5C55\u793A\u53EF\u51FA\u5E93\uFF09",modelValue:l(i),"onUpdate:modelValue":a[7]||(a[7]=t=>z(i)?i.value=t:null),appendToBody:!0,scroll:!0,width:"1080"},{footer:o(()=>[e(c,{disabled:!l(m),type:"primary",onClick:I},{default:o(()=>a[11]||(a[11]=[p("\u786E \u5B9A")])),_:1},8,["disabled"]),e(c,{onClick:a[6]||(a[6]=t=>i.value=!1)},{default:o(()=>a[12]||(a[12]=[p("\u53D6 \u6D88")])),_:1})]),default:o(()=>[e(N,null,{default:o(()=>[e(j,{class:"-mb-15px",model:l(r),ref_key:"queryFormRef",ref:y,inline:!0,"label-width":"68px"},{default:o(()=>[e(u,{label:"\u8BA2\u5355\u5355\u53F7",prop:"no"},{default:o(()=>[e(Y,{modelValue:l(r).no,"onUpdate:modelValue":a[0]||(a[0]=t=>l(r).no=t),placeholder:"\u8BF7\u8F93\u5165\u8BA2\u5355\u5355\u53F7",clearable:"",onKeyup:J(g,["enter"]),class:"!w-160px"},null,8,["modelValue"])]),_:1}),e(u,{label:"\u4EA7\u54C1",prop:"productId"},{default:o(()=>[e(H,{modelValue:l(r).productId,"onUpdate:modelValue":a[1]||(a[1]=t=>l(r).productId=t),clearable:"",filterable:"",placeholder:"\u8BF7\u9009\u62E9\u4EA7\u54C1",class:"!w-160px"},{default:o(()=>[(f(!0),W(X,null,ee(l(V),t=>(f(),_(F,{key:t.id,label:t.name,value:t.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(u,{label:"\u8BA2\u5355\u65F6\u95F4",prop:"orderTime"},{default:o(()=>[e(L,{modelValue:l(r).orderTime,"onUpdate:modelValue":a[2]||(a[2]=t=>l(r).orderTime=t),"value-format":"YYYY-MM-DD HH:mm:ss",type:"daterange","start-placeholder":"\u5F00\u59CB\u65E5\u671F","end-placeholder":"\u7ED3\u675F\u65E5\u671F","default-time":[new Date("1 00:00:00"),new Date("1 23:59:59")],class:"!w-160px"},null,8,["modelValue","default-time"])]),_:1}),e(u,null,{default:o(()=>[e(c,{onClick:g},{default:o(()=>[e(C,{icon:"ep:search",class:"mr-5px"}),a[8]||(a[8]=p(" \u641C\u7D22"))]),_:1}),e(c,{onClick:k},{default:o(()=>[e(C,{icon:"ep:refresh",class:"mr-5px"}),a[9]||(a[9]=p(" \u91CD\u7F6E"))]),_:1})]),_:1})]),_:1},8,["model"])]),_:1}),e(N,null,{default:o(()=>[ae((f(),_(l(ce),{data:l(w),"show-overflow-tooltip":!0,stripe:!0},{default:o(()=>[e(n,{align:"center",width:"65"},{default:o(t=>[e(G,{value:t.row.id,modelValue:l(s),"onUpdate:modelValue":a[3]||(a[3]=P=>z(s)?s.value=P:null),onChange:P=>{return S=t.row,void(m.value=S);var S}},{default:o(()=>a[10]||(a[10]=[p(" \xA0 ")])),_:2},1032,["value","modelValue","onChange"])]),_:1}),e(n,{"min-width":"180",label:"\u8BA2\u5355\u5355\u53F7",align:"center",prop:"no"}),e(n,{label:"\u5BA2\u6237",align:"center",prop:"customerName"}),e(n,{label:"\u4EA7\u54C1\u4FE1\u606F",align:"center",prop:"productNames","min-width":"200"}),e(n,{label:"\u8BA2\u5355\u65F6\u95F4",align:"center",prop:"orderTime",formatter:l(oe),width:"120px"},null,8,["formatter"]),e(n,{label:"\u521B\u5EFA\u4EBA",align:"center",prop:"creatorName"}),e(n,{label:"\u603B\u6570\u91CF",align:"center",prop:"totalCount",formatter:l(U)},null,8,["formatter"]),e(n,{label:"\u51FA\u5E93\u6570\u91CF",align:"center",prop:"outCount",formatter:l(U)},null,8,["formatter"]),e(n,{label:"\u91D1\u989D\u5408\u8BA1",align:"center",prop:"totalProductPrice",formatter:l(T)},null,8,["formatter"]),e(n,{label:"\u542B\u7A0E\u91D1\u989D",align:"center",prop:"totalPrice",formatter:l(T)},null,8,["formatter"])]),_:1},8,["data"])),[[q,l(v)]]),e(M,{limit:l(r).pageSize,"onUpdate:limit":a[4]||(a[4]=t=>l(r).pageSize=t),page:l(r).pageNo,"onUpdate:page":a[5]||(a[5]=t=>l(r).pageNo=t),total:l(h),onPagination:x},null,8,["limit","page","total"])]),_:1})]),_:1},8,["modelValue"])}}});export{be as _};
