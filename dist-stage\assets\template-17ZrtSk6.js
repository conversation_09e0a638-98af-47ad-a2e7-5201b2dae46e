import{W as t}from"./index-Byekp3Iv.js";const e=async a=>await t.get({url:"/promotion/diy-template/page",params:a}),o=async a=>await t.get({url:"/promotion/diy-template/get?id="+a}),p=async a=>await t.post({url:"/promotion/diy-template/create",data:a}),i=async a=>await t.put({url:"/promotion/diy-template/update",data:a}),r=async a=>await t.delete({url:"/promotion/diy-template/delete?id="+a}),s=async a=>await t.put({url:"/promotion/diy-template/use?id="+a}),d=async a=>await t.get({url:"/promotion/diy-template/get-property?id="+a}),m=async a=>await t.put({url:"/promotion/diy-template/update-property",data:a});export{o as a,i as b,p as c,e as d,r as e,s as f,d as g,m as u};
