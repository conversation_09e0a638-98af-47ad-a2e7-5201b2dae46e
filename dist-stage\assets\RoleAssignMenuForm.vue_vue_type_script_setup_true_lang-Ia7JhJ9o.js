import{a as z,d as D}from"./index-Byekp3Iv.js";import{_ as P}from"./Dialog.vue_vue_type_style_index_0_lang-DhV_HFpU.js";import{d as q,h as B}from"./tree-COGD3qag.js";import{g as G}from"./index-BtJACIYJ.js";import{g as Y,a as J}from"./index-Nk6p0_SU.js";import{h as L,aj as O,a6 as Q,ad as T,y as W,D as X,ak as Z,f as $}from"./form-designer-C0ARe9Dh.js";import{k as ee,r as d,P as ae,y as w,m as C,z as l,A as le,u as o,H as s,E as c,F as V,h as _}from"./form-create-B86qX0W_.js";const se=ee({name:"SystemRoleAssignMenuForm",__name:"RoleAssignMenuForm",emits:["success"],setup(te,{expose:g,emit:b}){const{t:I}=z(),F=D(),n=d(!1),m=d(!1),t=ae({id:void 0,name:"",code:"",menuIds:[]}),p=d(),y=d([]),i=d(!1),u=d(),v=d(!1);g({open:async a=>{n.value=!0,M(),y.value=B(await G()),t.id=a.id,t.name=a.name,t.code=a.code,m.value=!0;try{t.value.menuIds=await Y(a.id),t.value.menuIds.forEach(e=>{u.value.setChecked(e,!0,!1)})}finally{m.value=!1}}});const R=b,A=async()=>{if(p&&await p.value.validate()){m.value=!0;try{const a={roleId:t.id,menuIds:[...u.value.getCheckedKeys(!1),...u.value.getHalfCheckedKeys()]};await J(a),F.success(I("common.updateSuccess")),n.value=!1,R("success")}finally{m.value=!1}}},M=()=>{var a,e;v.value=!1,i.value=!1,t.value={id:void 0,name:"",code:"",menuIds:[]},(a=u.value)==null||a.setCheckedNodes([]),(e=p.value)==null||e.resetFields()},U=()=>{u.value.setCheckedNodes(v.value?y.value:[])},E=()=>{var e;const a=(e=u.value)==null?void 0:e.store.nodesMap;for(let f in a)a[f].expanded!==i.value&&(a[f].expanded=i.value)};return(a,e)=>{const f=Q,h=O,k=X,H=W,K=T,N=L,x=$,S=P,j=Z;return C(),w(S,{modelValue:o(n),"onUpdate:modelValue":e[3]||(e[3]=r=>_(n)?n.value=r:null),title:"\u83DC\u5355\u6743\u9650"},{footer:l(()=>[s(x,{disabled:o(m),type:"primary",onClick:A},{default:l(()=>e[6]||(e[6]=[c("\u786E \u5B9A")])),_:1},8,["disabled"]),s(x,{onClick:e[2]||(e[2]=r=>n.value=!1)},{default:l(()=>e[7]||(e[7]=[c("\u53D6 \u6D88")])),_:1})]),default:l(()=>[le((C(),w(N,{ref_key:"formRef",ref:p,model:o(t),"label-width":"80px"},{default:l(()=>[s(h,{label:"\u89D2\u8272\u540D\u79F0"},{default:l(()=>[s(f,null,{default:l(()=>[c(V(o(t).name),1)]),_:1})]),_:1}),s(h,{label:"\u89D2\u8272\u6807\u8BC6"},{default:l(()=>[s(f,null,{default:l(()=>[c(V(o(t).code),1)]),_:1})]),_:1}),s(h,{label:"\u83DC\u5355\u6743\u9650"},{default:l(()=>[s(K,{class:"w-full h-400px !overflow-y-scroll",shadow:"never"},{header:l(()=>[e[4]||(e[4]=c(" \u5168\u9009/\u5168\u4E0D\u9009: ")),s(k,{modelValue:o(v),"onUpdate:modelValue":e[0]||(e[0]=r=>_(v)?v.value=r:null),"active-text":"\u662F","inactive-text":"\u5426","inline-prompt":"",onChange:U},null,8,["modelValue"]),e[5]||(e[5]=c(" \u5168\u90E8\u5C55\u5F00/\u6298\u53E0: ")),s(k,{modelValue:o(i),"onUpdate:modelValue":e[1]||(e[1]=r=>_(i)?i.value=r:null),"active-text":"\u5C55\u5F00","inactive-text":"\u6298\u53E0","inline-prompt":"",onChange:E},null,8,["modelValue"])]),default:l(()=>[s(H,{ref_key:"treeRef",ref:u,data:o(y),props:o(q),"empty-text":"\u52A0\u8F7D\u4E2D\uFF0C\u8BF7\u7A0D\u5019","node-key":"id","show-checkbox":""},null,8,["data","props"])]),_:1})]),_:1})]),_:1},8,["model"])),[[j,o(m)]])]),_:1},8,["modelValue"])}}});export{se as _};
