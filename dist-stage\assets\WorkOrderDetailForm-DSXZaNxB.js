import{_ as na,c as la}from"./index-Byekp3Iv.js";import{W as ua}from"./index-BC-xKroV.js";import{B as oa}from"./index-ChDRG3BK.js";import{WarehouseApi as ra}from"./index-DmNtGmQq.js";import{U as da}from"./index-C4fKXT07.js";import{h as sa}from"./tree-COGD3qag.js";import{f as w,a as ca}from"./formatter-CF7Ifi5S.js";import{f as pa,p as ma,g as z,c as P,C as ya,B as Qa,a as va}from"./bomCalculation-DYv5_fDl.js";import{h as fa,_ as Na,aj as ba,a1 as ha,x as Ia,Q as Sa,l as wa,k as ka,Z as xa,ak as _a,f as ga,i as Va}from"./form-designer-C0ARe9Dh.js";import{k as Ua,r as x,P as qa,b as G,e as Oa,n as A,l as C,m as _,G as B,A as Ta,H as d,y as F,u as f,z as p,v as S,F as E,$ as H,E as $a}from"./form-create-B86qX0W_.js";const za={class:"material-info"},Pa={class:"material-name"},Aa={class:"value"},Ca={class:"material-code"},Ba={class:"value"},Fa={class:"material-spec"},Ea={class:"value"},ja=la(Ua({__name:"WorkOrderDetailForm",props:{bizOrderId:{},bomId:{},slotQuantity:{},orderQuantity:{},orderSpecQuantity:{},isTransferMode:{type:Boolean}},setup(X,{expose:Z}){const N=X,U=x(!1),y=x([]),k=qa({}),j=x();G(()=>({orderId:N.bizOrderId,bomId:N.bomId}),async({orderId:t,bomId:n})=>{if(y.value=[],t)try{U.value=!0,y.value=await ua.getWorkOrderDetailListByBizOrderId(t),D(),await A(),$()}finally{U.value=!1}else if(n){const a=await Y(n,"");y.value=a.map(i=>({...i,bizOrderId:N.bizOrderId,unit:i.materialUnit,slotQuantity:i.quantity||0,quantity:g({slotQuantity:i.quantity||0})})),A(()=>{$()})}},{immediate:!0});const J=()=>{const t={id:void 0,num:y.value.length>0?Math.max(...y.value.map(n=>Number(n.num)||0))+1:1,bizOrderId:void 0,bizOrderNo:void 0,warehouseId:void 0,locationId:void 0,materialId:void 0,materialName:void 0,materialCode:void 0,spec:void 0,unit:void 0,unitPrice:void 0,amount:void 0,quantity:void 0,plannedQuantity:void 0,fulfilledQuantity:void 0,standardPlannedQuantity:void 0,standardFulfilledQuantity:void 0,standardUnit:void 0,plannedSpecQuantity:void 0,fulfilledSpecQuantity:void 0,slotQuantity:0,slotSpecQuantity:0,lockQuantity:void 0,lockTransitQuantity:void 0,readyQuantity:void 0,stockQuantity:void 0,shortageQuantity:void 0,purchaseQuantity:void 0,transitQuantity:void 0,lockStockQuantity:void 0,readyStatus:void 0,lossRate:void 0,lossQuantity:void 0,taxPrice:void 0,taxAmount:void 0,invoiceQuantity:void 0,invoiceAmount:void 0,standardInvoiceQuantity:void 0,note:void 0,batchNo:void 0,remark:void 0,costObjectId:void 0,costObjectName:void 0,accountingVoucherNumber:void 0,kdId:void 0,kdOrderId:void 0,createTime:void 0,updateTime:void 0,creator:void 0,updater:void 0,deleted:void 0};t.bizOrderId=N.bizOrderId,y.value.push(t)},D=()=>{y.value.forEach((t,n)=>{t.num=n+1})},L=()=>{const t=N.orderQuantity||0,n=N.slotQuantity&&N.slotQuantity>0?N.slotQuantity:Qa.DEFAULT_SLOT_QUANTITY;return va(t,n)},g=t=>{const n=typeof t=="object"?t.slotQuantity||0:t||0,a=L();return pa(n*a,ya.QUANTITY_PRECISION)},q=ma,O=t=>{if(!t)return"\u4E2A";if(!I.value||I.value.length===0)return R(t);const n=I.value.find(i=>i.id===t);return(n?n.name:"")||R(t)},R=t=>{if(I.value&&I.value.length>0){const n=I.value.find(a=>a.id===t);if(n&&n.name)return n.name}return"\u4E2A"},T=t=>{const n=q(t.spec||""),a=t.slotQuantity||0;let i="";if(I.value&&I.value.length>0&&t.unit)i=O(t.unit);else{if(t.unit)return a||1;i="\u4E2A"}const Q=z(i);return!n.value||n.value<=0?a||1:P(a,n,Q,i).displayText},V=t=>{const n=L();if(t.slotSpecQuantity!==void 0&&t.slotSpecQuantity!==null&&t.slotSpecQuantity!==0||(t.slotSpecQuantity=T(t)),typeof t.slotSpecQuantity=="string"){const a=q(t.spec||""),i=O(t.unit||0),Q=z(i);return P((t.slotQuantity||0)*n,a,Q,i).displayText}{const a=q(t.spec||""),i=O(t.unit||0),Q=z(i);return P((t.slotQuantity||0)*n,a,Q,i).displayText}},W=()=>{y.value&&Array.isArray(y.value)&&y.value.forEach(t=>{t&&(t.slotSpecQuantity=T(t),t.quantity=g(t),t.plannedSpecQuantity=V(t))})},$=()=>{W()};G(()=>[N.slotQuantity,N.orderQuantity,N.orderSpecQuantity],()=>{y.value&&Array.isArray(y.value)&&y.value.forEach(t=>{t&&(t.quantity=g(t),t.plannedSpecQuantity=V(t))})},{immediate:!0});const K=t=>{const{columns:n,data:a}=t,i=[];return n.forEach((Q,s)=>{if(s===0)return void(i[s]="\u5408\u8BA1");const b=Q.property;if(b==="slotQuantity"){const l=a.map(e=>Number(e.slotQuantity)||0);if(l.every(e=>Number.isNaN(e)))i[s]="";else{const e=l.reduce((u,m)=>{const o=Number(m);return Number.isNaN(o)?u:u+o},0);i[s]=w(e)}}else if(b==="slotSpecQuantity"){const l=a.map(e=>Number(e.slotSpecQuantity)||0);if(l.every(e=>Number.isNaN(e)))i[s]="";else{const e=l.reduce((u,m)=>{const o=Number(m);return Number.isNaN(o)?u:u+o},0);i[s]=w(e)}}else if(b==="quantity"){const l=a.map(e=>Number(e.quantity)||0);if(l.every(e=>Number.isNaN(e)))i[s]="";else{const e=l.reduce((u,m)=>{const o=Number(m);return Number.isNaN(o)?u:u+o},0);i[s]=w(e)}}else if(b==="plannedQuantity"){const l=a.map(e=>Number(e.plannedQuantity)||0);if(l.every(e=>Number.isNaN(e)))i[s]="";else{const e=l.reduce((u,m)=>{const o=Number(m);return Number.isNaN(o)?u:u+o},0);i[s]=w(e)}}else if(b==="fulfilledQuantity"){const l=a.map(e=>Number(e.fulfilledQuantity)||0);if(l.every(e=>Number.isNaN(e)))i[s]="";else{const e=l.reduce((u,m)=>{const o=Number(m);return Number.isNaN(o)?u:u+o},0);i[s]=w(e)}}else if(b==="standardPlannedQuantity"){const l=a.map(e=>Number(e.standardPlannedQuantity)||0);if(l.every(e=>Number.isNaN(e)))i[s]="";else{const e=l.reduce((u,m)=>{const o=Number(m);return Number.isNaN(o)?u:u+o},0);i[s]=w(e)}}else if(b==="standardFulfilledQuantity"){const l=a.map(e=>Number(e.standardFulfilledQuantity)||0);if(l.every(e=>Number.isNaN(e)))i[s]="";else{const e=l.reduce((u,m)=>{const o=Number(m);return Number.isNaN(o)?u:u+o},0);i[s]=w(e)}}else if(b==="plannedSpecQuantity"){const l=a.map(e=>Number(e.plannedSpecQuantity)||0);if(l.every(e=>Number.isNaN(e)))i[s]="";else{const e=l.reduce((u,m)=>{const o=Number(m);return Number.isNaN(o)?u:u+o},0);i[s]=w(e)}}else if(b==="fulfilledSpecQuantity"){const l=a.map(e=>Number(e.fulfilledSpecQuantity)||0);if(l.every(e=>Number.isNaN(e)))i[s]="";else{const e=l.reduce((u,m)=>{const o=Number(m);return Number.isNaN(o)?u:u+o},0);i[s]=w(e)}}else if(b==="amount"){const l=a.map(e=>Number(e.amount)||0);if(l.every(e=>Number.isNaN(e)))i[s]="";else{const e=l.reduce((u,m)=>{const o=Number(m);return Number.isNaN(o)?u:u+o},0);i[s]=ca(e)}}else i[s]=""}),i},M=x([]),aa=x([]),I=x([]);Oa(async()=>{await(async()=>{const t=await ra.getWarehouseList({});M.value=sa(t,"id","parentId")})(),await(async()=>{const t=await da.getUnitPage({pageNo:1,pageSize:100});I.value=t.list})(),await A(),$()});const Y=async(t,n)=>(await oa.getBomMaterialListByBomId(t,n)).map(a=>({...a,unit:a.materialUnit||a.unit,bizOrderId:N.bizOrderId,num:a.num||1,plannedQuantity:a.quantity||0,fulfilledQuantity:0,unitPrice:0,amount:0,remark:a.remark||""}));return Z({validate:()=>j.value.validate(),getData:()=>y.value,getMaterialsByBomId:Y,setDetails:t=>{if(!t||t.length===0)return;y.value=[];const n=t.map((a,i)=>({num:i+1,materialId:a.materialId,materialCode:a.materialCode,materialName:a.materialName,spec:a.spec||a.materialSpec,unit:a.unit,quantity:a.pendingQuantity||a.quantity||a.plannedQuantity,plannedQuantity:a.plannedQuantity||a.pendingQuantity||a.quantity,fulfilledQuantity:a.fulfilledQuantity||0,standardUnit:a.standardUnit||a.standardUnitId||a.unit,standardPlannedQuantity:a.standardPlannedQuantity||a.plannedQuantity||a.pendingQuantity||a.quantity,standardFulfilledQuantity:a.standardFulfilledQuantity||0,plannedSpecQuantity:a.plannedSpecQuantity||a.slotSpecQuantity||0,fulfilledSpecQuantity:a.fulfilledSpecQuantity||0,slotQuantity:a.slotQuantity||0,slotSpecQuantity:a.slotSpecQuantity||0,readyQuantity:a.readyQuantity||0,stockQuantity:a.stockQuantity||0,shortageQuantity:a.shortageQuantity||0,purchaseQuantity:a.purchaseQuantity||0,transitQuantity:a.transitQuantity||0,lockStockQuantity:a.lockStockQuantity||0,lockTransitQuantity:a.lockTransitQuantity||0,lockQuantity:a.lockQuantity||0,readyStatus:a.readyStatus||0,lossRate:a.lossRate||0,lossQuantity:a.lossQuantity||0,unitPrice:a.unitPrice||0,amount:a.amount||0,remark:a.remark||a.requirement||"",note:a.note||"",batchNo:a.batchNo||"",warehouseId:a.warehouseId,locationId:a.locationId,bizOrderId:N.bizOrderId,version:a.version||1,materialType:a.materialType}));y.value=n},recalculateAll:W}),(t,n)=>{const a=Na,i=ha,Q=ba,s=Sa,b=Ia,l=wa,e=ka,u=na,m=xa,o=fa,ta=ga,ea=Va,ia=_a;return _(),C(B,null,[Ta((_(),F(o,{ref_key:"formRef",ref:j,model:f(y),rules:f(k),"label-width":"0px","inline-message":!0},{default:p(()=>[d(m,{data:f(y),class:"-mt-10px",border:"","max-height":400,style:{width:"100%"},"show-summary":"","summary-method":K},{default:p(()=>[d(a,{label:"\u7269\u6599\u4FE1\u606F","min-width":"200"},{default:p(({row:r})=>[S("div",za,[S("div",Pa,[n[0]||(n[0]=S("span",{class:"label"},"\u540D\u79F0\uFF1A",-1)),S("span",Aa,E(r.materialName||"-"),1)]),S("div",Ca,[n[1]||(n[1]=S("span",{class:"label"},"\u7F16\u53F7\uFF1A",-1)),S("span",Ba,E(r.materialCode||"-"),1)]),S("div",Fa,[n[2]||(n[2]=S("span",{class:"label"},"\u89C4\u683C\uFF1A",-1)),S("span",Ea,E(r.spec||"-"),1)])])]),_:1}),d(a,{label:"\u4ED3\u5E93","min-width":"150"},{default:p(({row:r,$index:v})=>[d(Q,{prop:`${v}.warehouseId`,rules:f(k).warehouseId,class:"mb-0px!"},{default:p(()=>[d(i,{modelValue:r.warehouseId,"onUpdate:modelValue":c=>r.warehouseId=c,data:f(M),props:{value:"id",label:"name",children:"children"},placeholder:"\u8BF7\u9009\u62E9\u4ED3\u5E93",clearable:"",filterable:"",class:"!w-240px","node-key":"id","render-after-expand":!1},null,8,["modelValue","onUpdate:modelValue","data"])]),_:2},1032,["prop","rules"])]),_:1}),d(a,{label:"\u5E93\u4F4D","min-width":"150"},{default:p(({row:r,$index:v})=>[d(Q,{prop:`${v}.locationId`,rules:f(k).locationId,class:"mb-0px!"},{default:p(()=>[d(b,{modelValue:r.locationId,"onUpdate:modelValue":c=>r.locationId=c,placeholder:"\u8BF7\u9009\u62E9\u5E93\u4F4D",class:"!w-240px"},{default:p(()=>[(_(!0),C(B,null,H(f(aa),c=>(_(),F(s,{key:c.id,label:c.name,value:c.id},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue"])]),_:2},1032,["prop","rules"])]),_:1}),d(a,{label:"\u5355\u4F4D","min-width":"150"},{default:p(({row:r,$index:v})=>[d(Q,{prop:`${v}.unit`,rules:f(k).unit,class:"mb-0px!"},{default:p(()=>[d(b,{modelValue:r.unit,"onUpdate:modelValue":c=>r.unit=c,placeholder:"\u8BF7\u9009\u62E9\u5355\u4F4D",class:"!w-240px",disabled:""},{default:p(()=>[(_(!0),C(B,null,H(f(I),c=>(_(),F(s,{key:c.id,label:c.name,value:c.id},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue"])]),_:2},1032,["prop","rules"])]),_:1}),d(a,{label:"\u6BCF\u69FD\u6570\u91CF","min-width":"150",prop:"slotQuantity"},{default:p(({row:r,$index:v})=>[d(Q,{prop:`${v}.slotQuantity`,rules:f(k).slotQuantity,class:"mb-0px!"},{default:p(()=>[d(l,{modelValue:r.slotQuantity,"onUpdate:modelValue":c=>r.slotQuantity=c,placeholder:"\u8BF7\u8F93\u5165\u6BCF\u69FD\u6570\u91CF",precision:4,step:1e-4,min:0,"controls-position":"right",class:"w-full",onChange:c=>(h=>{h.quantity=g(h),(h.slotSpecQuantity===void 0||h.slotSpecQuantity===null||h.slotSpecQuantity===0)&&(h.slotSpecQuantity=T(h)),h.plannedSpecQuantity=V(h)})(r)},null,8,["modelValue","onUpdate:modelValue","onChange"])]),_:2},1032,["prop","rules"])]),_:1}),d(a,{label:"\u6570\u91CF","min-width":"150",prop:"quantity"},{default:p(({row:r,$index:v})=>[d(Q,{prop:`${v}.quantity`,rules:f(k).quantity,class:"mb-0px!"},{default:p(()=>[d(e,{modelValue:r.quantity,"onUpdate:modelValue":c=>r.quantity=c,placeholder:"\u8BF7\u8F93\u5165\u6570\u91CF"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1032,["prop","rules"])]),_:1}),d(a,{label:"\u89C4\u683C\u6570\u91CF","min-width":"150",prop:"plannedSpecQuantity"},{default:p(({row:r,$index:v})=>[d(Q,{prop:`${v}.plannedSpecQuantity`,rules:f(k).plannedSpecQuantity,class:"mb-0px!"},{default:p(()=>[d(e,{modelValue:r.plannedSpecQuantity,"onUpdate:modelValue":c=>r.plannedSpecQuantity=c,placeholder:"\u8BF7\u8F93\u5165\u89C4\u683C\u6570\u91CF"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1032,["prop","rules"])]),_:1}),d(a,{label:"\u8BF4\u660E","min-width":"150"},{default:p(({row:r,$index:v})=>[d(Q,{prop:`${v}.note`,rules:f(k).note,class:"mb-0px!"},{default:p(()=>[d(e,{modelValue:r.note,"onUpdate:modelValue":c=>r.note=c,placeholder:"\u8BF7\u8F93\u5165\u8BF4\u660E"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1032,["prop","rules"])]),_:1}),d(a,{align:"center",fixed:"right",label:"\u64CD\u4F5C",width:"60"},{default:p(({$index:r})=>[d(u,{icon:"ep:delete",color:"#f56c6c",style:{},onClick:v=>{return c=r,y.value.splice(c,1),void D();var c}},null,8,["onClick"])]),_:1})]),_:1},8,["data"])]),_:1},8,["model","rules"])),[[ia,f(U)]]),d(ea,{justify:"center",class:"mt-3"},{default:p(()=>[d(ta,{onClick:J,round:""},{default:p(()=>n[3]||(n[3]=[$a(" + \u6DFB\u52A0\u4EFB\u52A1\u5355\u660E\u7EC6 ")])),_:1})]),_:1})],64)}}}),[["__scopeId","data-v-2403223c"]]);export{ja as default};
