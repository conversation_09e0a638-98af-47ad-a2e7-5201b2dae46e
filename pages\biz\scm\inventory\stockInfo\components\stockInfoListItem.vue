<template>
	<view class="list-item-wrapper">
		<view class="stock-card">
			<!-- 物料基本信息 -->
			<view class="material-info">
				<view class="material-header">
					<text class="material-name">{{ item.materialName }}</text>
					<view class="material-type" :class="item.materialTypeClass">
						{{ getMaterialTypeText(item.materialType) }}
					</view>
				</view>
				<view class="material-details">
					<text class="material-code">编码：{{ item.materialCode }}</text>
					<text class="material-source">来源：{{ getMaterialSourceText(item.materialSource) }}</text>
					<view class="material-status">
						<text>状态：</text>
						<view class="status-tag" :class="item.statusClass">
							{{ getStatusText(item.status) }}
						</view>
					</view>
				</view>
			</view>

			<!-- 库存数量信息 -->
			<view class="quantity-section">
				<view class="quantity-row">
					<view class="quantity-item primary">
						<text class="quantity-label">库存数量</text>
						<text class="quantity-value">{{ formatQuantity(item.quantity) }} {{ getUnitName(item.quantityUnit) }}</text>
					</view>
					<view class="quantity-item">
						<text class="quantity-label">锁定数量</text>
						<text class="quantity-value lock">{{ formatQuantity(item.lockQuantity) }} {{ getUnitName(item.quantityUnit) }}</text>
					</view>
				</view>
				<view class="quantity-row">
					<view class="quantity-item">
						<text class="quantity-label">未锁数量</text>
						<text class="quantity-value available">{{ formatQuantity(item.unlockQuantity) }} {{ getUnitName(item.quantityUnit) }}</text>
					</view>
					<view class="quantity-item">
						<text class="quantity-label">在途数量</text>
						<text class="quantity-value transit">{{ formatQuantity(item.transitQuantity) }} {{ getUnitName(item.quantityUnit) }}</text>
					</view>
				</view>
				<view class="quantity-row">
					<view class="quantity-item">
						<text class="quantity-label">在途已锁</text>
						<text class="quantity-value transit-lock">{{ formatQuantity(item.transitLockQuantity || 0) }} {{ getUnitName(item.quantityUnit) }}</text>
					</view>
					<view class="quantity-item">
						<text class="quantity-label">在途未锁</text>
						<text class="quantity-value transit-unlock">{{ formatQuantity(item.transitUnlockQuantity || 0) }} {{ getUnitName(item.quantityUnit) }}</text>
					</view>
				</view>
			</view>

			<!-- 价格和仓库信息 -->
			<view class="detail-section">
				<view class="detail-row">
					<text class="detail-item">价格：{{ formatAmount(item.price) }}</text>
					<text class="detail-item">总价值：{{ formatAmount(item.totalCost) }}</text>
					<text class="detail-item">仓库：{{ item.warehouseName }}</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import { getDictLabel, DICT_TYPE } from '../../../../../../utils/dict.js'

export default {
	name: 'StockInfoListItem',
	props: {
		// 库存项数据
		item: {
			type: Object,
			required: true
		},
		// 物料类型选项
		materialTypeOptions: {
			type: Array,
			default: () => []
		},
		// 状态选项
		statusOptions: {
			type: Array,
			default: () => []
		},
		// 单位选项
		unitOptions: {
			type: Array,
			default: () => []
		},
		// 字典数据
		dictData: {
			type: Object,
			default: () => ({})
		}
	},
	methods: {
		// 格式化数量
		formatQuantity(value) {
			if (!value && value !== 0) return '0'
			return Number(value).toLocaleString()
		},

		// 格式化金额
		formatAmount(value) {
			if (!value && value !== 0) return '¥0.00'
			return '¥' + Number(value).toFixed(2)
		},

		// 获取单位名称
		getUnitName(unitId) {
			if (!unitId && unitId !== 0) return ''

			// 处理数字类型
			if (typeof unitId === 'number') {
				const unit = this.unitOptions.find(unit => unit.id === unitId)
				if (unit && unit.name) return unit.name
			}

			// 处理字符串类型
			if (typeof unitId === 'string') {
				// 先尝试按字符串查找
				let unit = this.unitOptions.find(unit => unit.id.toString() === unitId)
				if (unit && unit.name) return unit.name

				// 再尝试转换为数字查找
				const numId = parseInt(unitId)
				if (!isNaN(numId)) {
					unit = this.unitOptions.find(unit => unit.id === numId)
					if (unit && unit.name) return unit.name
				}
			}

			return unitId.toString()
		},

		// 获取物料类型文本
		getMaterialTypeText(type) {
			return getDictLabel(this.materialTypeOptions, type) || type || '未知'
		},

		// 获取物料来源文本
		getMaterialSourceText(source) {
			const materialSourceOptions = this.dictData[DICT_TYPE.MATERIAL_SOURCE] || []
			return getDictLabel(materialSourceOptions, source) || source || '未知'
		},

		// 获取状态文本
		getStatusText(status) {
			return getDictLabel(this.statusOptions, status) || status || '未知'
		}
	}
}
</script>

<style lang="scss" scoped>
.list-item-wrapper {
	margin-bottom: 16rpx;
}

.stock-card {
	background-color: white;
	border-radius: 8rpx;
	padding: 20rpx;
	box-shadow: 0 1rpx 8rpx rgba(0, 0, 0, 0.08);
}

.material-info {
	border-bottom: 1px solid #f0f0f0;
	padding-bottom: 16rpx;
	margin-bottom: 16rpx;

	.material-header {
		display: flex;
		align-items: center;
		justify-content: space-between;
		margin-bottom: 8rpx;

		.material-name {
			font-size: 30rpx;
			font-weight: bold;
			color: #333;
			flex: 1;
			line-height: 1.3;
		}

		.material-type {
			padding: 4rpx 10rpx;
			border-radius: 12rpx;
			font-size: 20rpx;
			color: white;

			&.type-raw { background-color: #409EFF; }
			&.type-semi { background-color: #E6A23C; }
			&.type-finished { background-color: #67C23A; }
			&.type-auxiliary { background-color: #909399; }
			&.type-default { background-color: #C0C4CC; }
		}
	}

	.material-details {
		display: flex;
		align-items: center;
		justify-content: space-between;
		margin-bottom: 0;
		flex-wrap: wrap;
		gap: 16rpx;

		.material-code,
		.material-source {
			font-size: 24rpx;
			color: #666;
			line-height: 1.3;
			flex-shrink: 0;
		}

		.material-status {
			display: flex;
			align-items: center;
			font-size: 24rpx;
			color: #666;
			line-height: 1.3;
			margin-left: auto;
		}
	}
}

.quantity-section {
	margin-bottom: 16rpx;

	.quantity-row {
		display: flex;
		margin-bottom: 12rpx;

		&:last-child {
			margin-bottom: 0;
		}
	}

	.quantity-item {
		flex: 1;
		margin-right: 16rpx;

		&:last-child {
			margin-right: 0;
		}

		.quantity-label {
			font-size: 22rpx;
			color: #666;
			display: block;
			margin-bottom: 4rpx;
			line-height: 1.2;
		}

		.quantity-value {
			font-size: 26rpx;
			font-weight: bold;
			color: #333;
			line-height: 1.2;
		}
	}
}

.detail-section {
	.detail-row {
		display: flex;
		align-items: center;
		justify-content: space-between;
		flex-wrap: wrap;
		gap: 16rpx;

		.detail-item {
			font-size: 24rpx;
			color: #666;
			line-height: 1.3;
		}
	}
}

.status-tag {
	padding: 3rpx 10rpx;
	border-radius: 10rpx;
	font-size: 20rpx;
	color: white;
	line-height: 1.2;
	margin-left: 6rpx;

	&.status-normal { background-color: #67C23A; }
	&.status-locked { background-color: #E6A23C; }
	&.status-frozen { background-color: #409EFF; }
	&.status-damaged { background-color: #F56C6C; }
	&.status-default { background-color: #C0C4CC; }
}
</style>