<template>
	<view class="list-item-wrapper">
		<view class="stock-card">
			<!-- 物料基本信息 -->
			<view class="material-info">
				<view class="material-header">
					<text class="material-name">{{ item.materialName }}</text>
					<view class="material-type" :class="item.materialTypeClass">
						{{ getMaterialTypeText(item.materialType) }}
					</view>
				</view>
				<text class="material-code">编码：{{ item.materialCode }}</text>
				<text class="material-source">来源：{{ getMaterialSourceText(item.materialSource) }}</text>
			</view>

			<!-- 库存数量信息 -->
			<view class="quantity-section">
				<view class="quantity-row">
					<view class="quantity-item primary">
						<text class="quantity-label">库存数量</text>
						<text class="quantity-value">{{ formatQuantity(item.quantity) }}</text>
						<text class="quantity-unit">{{ getUnitName(item.quantityUnit) }}</text>
					</view>
					<view class="quantity-item">
						<text class="quantity-label">锁定数量</text>
						<text class="quantity-value lock">{{ formatQuantity(item.lockQuantity) }}</text>
					</view>
				</view>
				<view class="quantity-row">
					<view class="quantity-item">
						<text class="quantity-label">可用数量</text>
						<text class="quantity-value available">{{ formatQuantity(item.unlockQuantity) }}</text>
					</view>
					<view class="quantity-item">
						<text class="quantity-label">在途数量</text>
						<text class="quantity-value transit">{{ formatQuantity(item.transitQuantity) }}</text>
					</view>
				</view>
			</view>

			<!-- 价格和仓库信息 -->
			<view class="detail-section">
				<view class="detail-row">
					<text class="detail-label">价格：</text>
					<text class="detail-value">{{ formatAmount(item.price) }}</text>
				</view>
				<view class="detail-row">
					<text class="detail-label">总价值：</text>
					<text class="detail-value amount">{{ formatAmount(item.totalCost) }}</text>
				</view>
				<view class="detail-row">
					<text class="detail-label">仓库：</text>
					<text class="detail-value">{{ item.warehouseName }}</text>
				</view>
				<view class="detail-row">
					<text class="detail-label">状态：</text>
					<view class="status-tag" :class="item.statusClass">
						{{ getStatusText(item.status) }}
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import { getDictLabel, DICT_TYPE } from '../../../../../../utils/dict.js'

export default {
	name: 'StockInfoListItem',
	props: {
		// 库存项数据
		item: {
			type: Object,
			required: true
		},
		// 物料类型选项
		materialTypeOptions: {
			type: Array,
			default: () => []
		},
		// 状态选项
		statusOptions: {
			type: Array,
			default: () => []
		},
		// 单位选项
		unitOptions: {
			type: Array,
			default: () => []
		},
		// 字典数据
		dictData: {
			type: Object,
			default: () => ({})
		}
	},
	methods: {
		// 格式化数量
		formatQuantity(value) {
			if (!value && value !== 0) return '0'
			return Number(value).toLocaleString()
		},

		// 格式化金额
		formatAmount(value) {
			if (!value && value !== 0) return '¥0.00'
			return '¥' + Number(value).toFixed(2)
		},

		// 获取单位名称
		getUnitName(unitId) {
			if (!unitId) return ''
			const unit = this.unitOptions.find(unit => unit.id === unitId)
			return unit ? unit.name : unitId.toString()
		},

		// 获取物料类型文本
		getMaterialTypeText(type) {
			return getDictLabel(this.materialTypeOptions, type) || type || '未知'
		},

		// 获取物料来源文本
		getMaterialSourceText(source) {
			const materialSourceOptions = this.dictData[DICT_TYPE.MATERIAL_SOURCE] || []
			return getDictLabel(materialSourceOptions, source) || source || '未知'
		},

		// 获取状态文本
		getStatusText(status) {
			return getDictLabel(this.statusOptions, status) || status || '未知'
		}
	}
}
</script>

<style lang="scss" scoped>
.list-item-wrapper {
	margin-bottom: 20rpx;
}

.stock-card {
	background-color: white;
	border-radius: 12rpx;
	padding: 24rpx;
	box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

.material-info {
	border-bottom: 1px solid #f0f0f0;
	padding-bottom: 20rpx;
	margin-bottom: 20rpx;

	.material-header {
		display: flex;
		align-items: center;
		justify-content: space-between;
		margin-bottom: 12rpx;

		.material-name {
			font-size: 32rpx;
			font-weight: bold;
			color: #333;
			flex: 1;
		}

		.material-type {
			padding: 6rpx 12rpx;
			border-radius: 16rpx;
			font-size: 22rpx;
			color: white;

			&.type-raw { background-color: #409EFF; }
			&.type-semi { background-color: #E6A23C; }
			&.type-finished { background-color: #67C23A; }
			&.type-auxiliary { background-color: #909399; }
			&.type-default { background-color: #C0C4CC; }
		}
	}

	.material-code,
	.material-source {
		font-size: 26rpx;
		color: #666;
		margin-bottom: 8rpx;

		&:last-child {
			margin-bottom: 0;
		}
	}
}

.quantity-section {
	margin-bottom: 20rpx;

	.quantity-row {
		display: flex;
		margin-bottom: 16rpx;

		&:last-child {
			margin-bottom: 0;
		}
	}

	.quantity-item {
		flex: 1;
		margin-right: 20rpx;

		&:last-child {
			margin-right: 0;
		}

		&.primary {
			background-color: #f0f9ff;
			padding: 16rpx;
			border-radius: 8rpx;
			border: 2rpx solid #409EFF;
		}

		.quantity-label {
			font-size: 24rpx;
			color: #666;
			display: block;
			margin-bottom: 8rpx;
		}

		.quantity-value {
			font-size: 28rpx;
			font-weight: bold;
			color: #333;

			&.lock { color: #E6A23C; }
			&.available { color: #67C23A; }
			&.transit { color: #909399; }
		}

		.quantity-unit {
			font-size: 22rpx;
			color: #999;
			margin-left: 8rpx;
		}
	}
}

.detail-section {
	.detail-row {
		display: flex;
		align-items: center;
		margin-bottom: 12rpx;

		&:last-child {
			margin-bottom: 0;
		}

		.detail-label {
			font-size: 26rpx;
			color: #666;
			min-width: 120rpx;
		}

		.detail-value {
			font-size: 26rpx;
			color: #333;

			&.amount {
				color: #E6A23C;
				font-weight: bold;
			}
		}
	}

	.status-tag {
		padding: 4rpx 12rpx;
		border-radius: 12rpx;
		font-size: 22rpx;
		color: white;

		&.status-normal { background-color: #67C23A; }
		&.status-locked { background-color: #E6A23C; }
		&.status-frozen { background-color: #409EFF; }
		&.status-damaged { background-color: #F56C6C; }
		&.status-default { background-color: #C0C4CC; }
	}
}
</style>