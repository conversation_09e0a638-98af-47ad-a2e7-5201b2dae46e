<template>
	<view class="formulaMainPage">
		<view class="top-tabs">
			<uv-tabs :list="tabsList" @click="changeStatus" keyName="name"></uv-tabs>
		</view>
		<view class="top-search">
			<uv-search 
				shape="round" 
				v-model="searchKeyword" 
				placeholder="请输入配方名称" 
				clearable 
				bgColor="#f5f5f5"
				@search="handleSearch"
				@custom="handleSearch"
				@clear="handleClearSearch"
			></uv-search>
		</view>
		<scroll-view 
			scroll-y="true" 
			class="formula-list-scroll" 
			@scrolltolower="loadMoreFormulas"
			refresher-enabled
			:refresher-triggered="isRefreshing"
			@refresherrefresh="onPullDownRefresh"
			enable-flex="true"
		>
			<!-- 有数据时显示列表 -->
			<block v-if="formulaList.length > 0">
				<uni-list>
					<uni-list-item v-for="item in formulaList" :key="item.id" :clickable="false" class="formula-list-container">
						<template #body>
							<!-- 配方列表项组件 -->
							<formula-list-item :item="item" @delete="handleDeleteFormula" @update="handleEditFormula" @submit="handleSubmitFormula" @approve="handleApproveFormula" class="formula-item"></formula-list-item>
						</template>
					</uni-list-item>
				</uni-list>
				<uni-load-more :status="loadMoreStatus" @clickLoadMore="loadMoreFormulas"></uni-load-more>
			</block>
			
			<!-- 无数据时显示暂无数据提示 -->
			<view v-if="formulaList.length === 0 && loadMoreStatus !== 'loading'" class="empty-data">
				<text>暂无数据</text>
			</view>
		</scroll-view>
		<uni-popup ref="approvePopup" type="bottom" background-color="#fff" :mask-click="false">
			<view class="approve-popup-container">
				<!-- 弹出层标题栏 -->
				<view class="approve-header">
					<view class="header-title">配方审批</view>
					<view class="header-close" @click="closeApprove">
						<uni-icons type="close" size="20" color="#999"></uni-icons>
					</view>
				</view>
				
				<!-- 表单内容 -->
				<view class="approve-form">
					<uv-form label-position="left" :model="approveFormula" class="approveForm" :rules="approveRules">
						<uv-form-item label="配方名称" label-width="90">
							<view class="form-field-container">
								<input type="text" disabled v-model="approveFormula.name" class="input-approve disabled-input"/>
							</view>
						</uv-form-item>
						<uv-form-item label="审批状态" label-width="90" required>
							<view class="form-field-container">
								<picker 
									:value="approveStatusIndex" 
									:range="approvePickerRange" 
									range-key="label" 
									@change="handleApproveStatusChange"
									class="status-picker"
								>
									<view class="picker-box">
										<text class="picker-text">{{ getApproveStatusText() }}</text>
										<uni-icons type="bottom" size="16" color="#666"></uni-icons>
									</view>
								</picker>
							</view>
						</uv-form-item>
						<uv-form-item label="审批意见" label-width="90" required>
							<view class="form-field-container">
								<textarea 
									v-model="approveFormula.approveDesc" 
									placeholder="请输入审批意见（必填）" 
									class="textarea-approve"
									maxlength="200"
									show-count
								/>
							</view>
						</uv-form-item>
					</uv-form>
				</view>
				
				<!-- 底部按钮 -->
				<view class="approve-bottom">
					<view class="button-group">
						<button class="cancel-button" @click="closeApprove">取消</button>
						<button class="confirm-button" @click="confirmApprove">确认审批</button>
					</view>
				</view>
			</view>
		</uni-popup>
		<uni-fab 
			:pattern="fabConfig"
			horizontal="right"
			vertical="bottom"
			direction="vertical"
			@fabClick="handleAddFormula"
			v-if="this.$auth.hasPermi('quote:formula:create')"
		></uni-fab>
	</view>
</template>

<script>
import { getDictOptions } from "../../../../../utils/dict";
import { getFormulaPageApi, deleteFormuaApi, getFormulaApi, approveFormulaApi } from "../../../../../api/scm/rd/formula/index"; // 引入配方API
// 引入配方列表项组件
import FormulaListItem from "./components/FormulaListItem.vue"; 

export default {
	components: {
		FormulaListItem 
	},
	data() {
		// 创建一个本地fab配置对象，不直接修改pattern prop
		const localFabConfig = {
			color: '#007AFF',
			backgroundColor: '#FFF',
			buttonColor: '#007AFF',
			iconColor: '#fff'
		};
		
		return {
			tabsList: [{ name: '全部', value: '' }], // 初始化tabs，'全部'选项的value为空字符串或其他约定值
			approveStatusOptions: [], // 存储从字典获取的原始审批状态数据 {label, value}
			currentApproveStatusValue: '', // 当前选中的审批状态值，默认为空（全部）
			
			formulaList: [], 
			searchKeyword: '', 
			approveFormula:{
				name: '',
				status: '',
				approveDesc: ''
			},
			approveStatusIndex: 0, // 当前选中的审批状态索引
			
			formulaPageNo: 1,
			pageSize: 10, 
			loadMoreStatus: 'more', 
			isRefreshing: false, 
			
			// 悬浮按钮配置
			fabConfig: localFabConfig,
			needRefresh: false, 
			approveRules:{
				'status':{
					type:"string",
					required:true,
					message:'请选择审批状态',
					trigger:['blur','change']
				},
				'approveDesc':{
					type:'string',
					required:true,
					message:'请输入审批意见',
					trigger:['blur','change']
				}
			}
		}
	},
	computed: {
		// 审批状态选择器数据源
		approvePickerRange() {
			// 过滤掉"待提交"和"待审批"状态，因为这些不是可选的审批结果
			if (!this.approveStatusOptions || this.approveStatusOptions.length === 0) {
				return [];
			}
			return this.approveStatusOptions.filter(function(item) {
				return parseInt(item.value) > 1;
			});
		}
	},
	methods: {
		// 获取审批状态文本显示
		getApproveStatusText() {
			if (this.approvePickerRange && 
				this.approvePickerRange.length > 0 && 
				this.approveStatusIndex >= 0 && 
				this.approveStatusIndex < this.approvePickerRange.length) {
				return this.approvePickerRange[this.approveStatusIndex].label;
			}
			return '请选择审批状态';
		},
		
		// 获取审批状态并设置Tabs
		async getActiveTabsList() {
			try {
				const response = await getDictOptions('approve_status');
				if (response && response.length > 0) {
					this.approveStatusOptions = response; // 存储原始数据
					// 构建tabs，已有"全部"，追加其他状态
					const statusTabs = response.map(item => ({ name: item.label, value: item.value }));
					// 创建新数组而不是修改原数组
					this.tabsList = [{ name: '全部', value: '' }, ...statusTabs];
				}
			} catch (error) {
				console.error("获取审批状态字典失败:", error);
				this.$modal.msgError("获取审批状态失败");
			}
		},
		
		// 获取配方分页数据
		async fetchFormulaPage(isLoadMore = false) {
			if (!isLoadMore) { // 如果不是加载更多操作（即切换tab或首次加载或下拉刷新）
				this.formulaPageNo = 1; // 重置页码
				this.formulaList = []; // 清空列表
				this.loadMoreStatus = 'more'; // 重置加载状态
			}
			
			// 如果已经是loading或者noMore状态，且不是下拉刷新强制请求，则不再请求
			if ((this.loadMoreStatus === 'loading' || this.loadMoreStatus === 'noMore') && !this.isRefreshing && isLoadMore) {
				return;
			}
			
			this.loadMoreStatus = 'loading';
			
			try {
				const params = {
					pageNo: this.formulaPageNo,
					pageSize: this.pageSize,
					status: this.currentApproveStatusValue,
					productName: this.searchKeyword // 配方名称搜索关键词，后端API需要支持此参数
				};
				const response = await getFormulaPageApi(params);
				
				if (response && response.code === 0 && response.data && response.data.list) {
					const newList = response.data.list;
					// 创建新数组而不是直接修改
					this.formulaList = isLoadMore ? [...this.formulaList, ...newList] : [...newList];
					
					if (newList.length < this.pageSize) {
						this.loadMoreStatus = 'noMore';
					} else {
						this.loadMoreStatus = 'more';
					}
				} else {
					this.$modal.msgError(response.msg || '获取配方列表失败');
					this.loadMoreStatus = 'noMore'; // 获取失败也设置为noMore
				}
			} catch (error) {
				console.error("获取配方列表失败:", error);
				this.$modal.msgError("获取配方列表网络错误");
				this.loadMoreStatus = 'noMore';
			} finally {
				if (this.isRefreshing) {
					this.isRefreshing = false; // 结束下拉刷新状态
					uni.stopPullDownRefresh(); // uni-app API 停止下拉刷新动画
				}
			}
		},
		//开启审批流程
		async handleApproveFormula(item){
			console.log(item)
			if(item && item.id){
				const response = await getFormulaApi(item.id,true)
				if(!response || response.code !== 0 || !response.data){
					uni.showToast({
						title:"获取配方信息失败",
						icon:'none'
					})
					return
				}
				
				// 安全初始化审批对象
				this.approveFormula = response.data || {};
				// 确保approveDesc始终是字符串，防止null值导致split错误
				this.approveFormula.approveDesc = this.approveFormula.approveDesc || '';
				
				// 重置状态选择
				this.approveStatusIndex = 0;
				this.$refs.approvePopup.open('bottom')
			} else {
				uni.showToast({
					title:"请选择需要审核的配方",
					icon:'none'
				})
			}
		},
		closeApprove(){
			this.$refs.approvePopup.close()
		},
		// 选择审批状态
		handleApproveStatusChange(e) {
			this.approveStatusIndex = e.detail.value;
			if (this.approvePickerRange && 
				this.approvePickerRange.length > 0 && 
				this.approveStatusIndex >= 0 && 
				this.approveStatusIndex < this.approvePickerRange.length) {
				this.approveFormula.status = this.approvePickerRange[this.approveStatusIndex].value;
			}
		},
		// 确认审批
		async confirmApprove() {
			// 表单验证
			if (!this.approveFormula.status) {
				uni.showToast({
					title: '请选择审批状态',
					icon: 'none'
				});
				return;
			}
			
			if (!this.approveFormula.approveDesc) {
				uni.showToast({
					title: '请输入审批意见',
					icon: 'none'
				});
				return;
			}
			
			try {
				// 创建提交数据对象
				var submitData = Object.assign({}, this.approveFormula);
				submitData.status = this.approveFormula.status;
				submitData.approveDesc = this.approveFormula.approveDesc || ''; // 确保不为null
				
				uni.showLoading({
					title: '提交中...',
					mask: true
				});
				
				const response = await approveFormulaApi(submitData);
				
				uni.hideLoading();
				
				if (response.code === 0) {
					uni.showToast({
						title: '审批成功',
						icon: 'success'
					});
					this.$refs.approvePopup.close();
					this.fetchFormulaPage(false); // 刷新列表
				} else {
					uni.showToast({
						title: response.msg || '审批失败',
						icon: 'none'
					});
				}
			} catch (error) {
				uni.hideLoading();
				console.error('审批失败:', error);
				uni.showToast({
					title: '审批操作失败',
					icon: 'none'
				});
			}
		},
		// Tab切换
		changeStatus(tab) {
			let clickedStatusValue = ''; // 默认为'全部'
			if (tab.name === '全部') {
				clickedStatusValue = ''; // "全部"选项对应的值
			} else {
				const foundStatus = this.approveStatusOptions.find(opt => opt.label === tab.name);
				if (foundStatus) {
					clickedStatusValue = foundStatus.value;
				}
			}

			if (this.currentApproveStatusValue !== clickedStatusValue) {
				this.currentApproveStatusValue = clickedStatusValue;
				this.fetchFormulaPage(false); // 切换Tab时，不是加载更多
			}
		},
		
		// 搜索处理
		handleSearch() {
			this.fetchFormulaPage(false); // 搜索时，从第一页开始
		},
		
		// 清除搜索处理
		handleClearSearch() {
			this.searchKeyword = '';
			this.fetchFormulaPage(false); // 清除搜索时，也从第一页开始
		},
		
		// 上拉加载更多
		loadMoreFormulas() {
			if (this.loadMoreStatus === 'more') {
				this.formulaPageNo++;
				this.fetchFormulaPage(true); // 是加载更多
			}
		},
		
		// 下拉刷新
		async onPullDownRefresh() {
			if(this.isRefreshing) return; // 防止重复触发
			this.isRefreshing = true;
			this.searchKeyword = ''; // 通常下拉刷新会清空搜索条件，并回到"全部"或当前tab的第一页
			await this.fetchFormulaPage(false); // 不是加载更多，会重置页码和列表
		},
		
		// 新增配方 (FAB按钮点击)
		handleAddFormula() {
			// 跳转到新增配方页面
			uni.navigateTo({
				url: '/pages/biz/scm/rd/formula/FormulaDetail/FormulaDetail',
				success: (res) => {
					// 通过eventChannel向被打开页面传送数据
					res.eventChannel.emit('acceptDataFromOpener', {
						type: 'add',
						isApply: false
					});
				}
			});
		},
		
		// 编辑配方 (列表项点击)
		handleEditFormula(item) {
			// 跳转到配方详情/编辑页面，并传递配方ID
			uni.navigateTo({
				url: '/pages/biz/scm/rd/formula/FormulaDetail/FormulaDetail',
				success: (res) => {
					// 通过eventChannel向被打开页面传送数据
					res.eventChannel.emit('acceptDataFromOpener', {
						id: item.id,
						type: 'edit',
						isApply: false
					});
				}
			});
		},
		
		// 删除配方 (由列表项组件触发)
		handleDeleteFormula(item) {
			uni.showModal({
				title: '提示',
				content: `确定要删除配方 "${item.productName || item.id}" 吗？`, // 假设item有productName
				cancelText:'取消',
				confirmText:'确定',
				success: async (res) => {
					if (res.confirm) {
						try {
							const response = await deleteFormuaApi(item.id);
							if (response && response.code === 0) {
								this.$modal.msgSuccess('删除成功');
								// 使用filter创建新数组而不是直接修改
								this.formulaList = this.formulaList.filter(f => f.id !== item.id);
								if(this.formulaList.length === 0 && this.formulaPageNo > 1) { // 如果当前页删空了且不是第一页
									this.formulaPageNo--;
									this.fetchFormulaPage(false);
								}
							} else {
								this.$modal.msgError(response.msg || '删除失败');
							}
						} catch (error) {
							console.error("删除配方失败:", error);
							this.$modal.msgError('删除操作失败');
						}
					}
				}
			});
		},
		
		// 提交配方
		async handleSubmitFormula(item) {
			// 实现提交逻辑
			const _formulaId = item.id;
			if(!_formulaId){
				uni.showToast({
					title:'请选择要提交的记录',
					icon:'none'
				})
				return
			}
			const response = await getFormulaApi(_formulaId,true)
			if(response.code === 0){
				response.data.status = 1
				const res = await approveFormulaApi(response.data);
				if(res.code == 0){
					uni.showToast({
						title:'提交成功',
						icon:'none'
					})
					this.fetchFormulaPage();
					return
				}
			}
		}
	},
	mounted() {
		this.getActiveTabsList(); // 首先获取Tab状态
		this.fetchFormulaPage();     // 然后获取第一页数据
		
		// 监听配方更新事件
		uni.$on('refreshFormulaList', (needRefresh) => {
			if (needRefresh) {
				this.fetchFormulaPage(false); // 重新获取第一页数据
			}
		});
	},
	beforeDestroy() {
		// 组件销毁前移除事件监听，避免内存泄漏
		uni.$off('refreshFormulaList');
	},
	onShow() {
		// 如果从其他页面返回且标记了需要刷新
		if (this.needRefresh) {
			this.fetchFormulaPage(false);
			this.needRefresh = false; // 重置标记
		}
	}
};
</script>

<style lang="scss">
.formulaMainPage {
	display: flex;
	flex-direction: column;
	height: 100vh; /* 或者 calc(100vh - var(--window-top)) 如果有自定义导航栏 */
	background-color: #f5f5f5;

	.top-tabs {
		background-color: #fff;
		// border-bottom: 1px solid #eee;
	}

	.top-search {
		padding: 10px;
		background-color: #fff;
		// border-bottom: 1px solid #eee;
	}

	.formula-list-scroll {
		flex: 1;
		height: 0;
		background-color: #f8f8f8;
		padding: 8px 0;
	}
	
	.empty-data {
		display: flex;
		justify-content: center;
		align-items: center;
		height: 200px; // 或者根据实际内容调整
		color: #999;
		font-size: 14px;
	}

	// 配方列表项样式
	.formula-item {
		width: 100%;
		margin: 0;
		border-radius: 8px;
		box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
		background-color: #999;
		overflow: hidden;
	}
}

/* 审批弹出层样式 */
.approve-popup-container {
	background-color: #fff;
	border-radius: 16px 16px 0 0;
	max-height: 80vh;
	min-height: 400px;
	overflow: hidden;
}

/* 弹出层标题栏 */
.approve-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 20px 20px 15px 20px;
	border-bottom: 1px solid #f0f0f0;
	background-color: #fafafa;
}

.header-title {
	font-size: 18px;
	font-weight: 600;
	color: #333;
}

.header-close {
	width: 32px;
	height: 32px;
	display: flex;
	align-items: center;
	justify-content: center;
	border-radius: 50%;
	background-color: #f5f5f5;
	transition: all 0.2s ease;
}

.header-close:active {
	background-color: #e8e8e8;
	transform: scale(0.95);
}

/* 表单样式 */
.approve-form {
	padding: 25px 20px 20px 20px;
	max-height: 50vh;
	overflow-y: auto;
}

.form-field-container {
	width: 100%;
	margin-top: 8px;
}

/* 输入框样式 */
.input-approve {
	width: 100%;
	height: 44px;
	padding: 0 12px;
	border: 1px solid #e0e0e0;
	border-radius: 8px;
	background-color: #fff;
	font-size: 15px;
	color: #333;
	transition: all 0.2s ease;
	box-sizing: border-box;
}

.input-approve:focus {
	border-color: #2979ff;
	background-color: #fafbff;
}

.disabled-input {
	background-color: #f8f9fa !important;
	color: #666 !important;
	cursor: not-allowed;
}

/* 选择器样式 */
.status-picker {
	width: 100%;
}

.picker-box {
	display: flex;
	justify-content: space-between;
	align-items: center;
	height: 44px;
	padding: 0 12px;
	background-color: #fff;
	border: 1px solid #e0e0e0;
	border-radius: 8px;
	transition: all 0.2s ease;
}

.picker-box:active {
	border-color: #2979ff;
	background-color: #fafbff;
}

.picker-text {
	flex: 1;
	font-size: 15px;
	color: #333;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

/* 文本域样式 */
.textarea-approve {
	width: 100%;
	min-height: 100px;
	max-height: 150px;
	padding: 12px;
	border: 1px solid #e0e0e0;
	border-radius: 8px;
	background-color: #fff;
	font-size: 15px;
	color: #333;
	line-height: 1.5;
	resize: none;
	transition: all 0.2s ease;
	box-sizing: border-box;
}

.textarea-approve:focus {
	border-color: #2979ff;
	background-color: #fafbff;
	outline: none;
}

/* 底部按钮区域 */
.approve-bottom {
	padding: 20px;
	border-top: 1px solid #f0f0f0;
	background-color: #fafafa;
}

.button-group {
	display: flex;
	gap: 12px;
}

.cancel-button,
.confirm-button {
	flex: 1;
	height: 48px;
	border: none;
	border-radius: 24px;
	font-size: 16px;
	font-weight: 500;
	transition: all 0.3s ease;
	cursor: pointer;
}

.cancel-button {
	background-color: #f5f5f5;
	color: #666;
	border: 1px solid #e0e0e0;
}

.cancel-button:active {
	background-color: #e8e8e8;
	transform: scale(0.98);
}

.confirm-button {
	background: linear-gradient(135deg, #2979ff 0%, #1565c0 100%);
	color: #fff;
	box-shadow: 0 2px 8px rgba(41, 121, 255, 0.3);
}

.confirm-button:active {
	background: linear-gradient(135deg, #1976d2 0%, #0d47a1 100%);
	transform: scale(0.98);
	box-shadow: 0 1px 4px rgba(41, 121, 255, 0.3);
}

::v-deep .uni-list-item{
	background-color: #f5f5f5 !important;
}
// // 选择器样式
// .picker-box {
// 	display: flex;
// 	justify-content: space-between;
// 	align-items: center;
// 	height: 36px;
// 	padding: 0 12px;
// 	background-color: #f8f8f8;
// 	border-radius: 4px;
// 	border: 1px solid #e5e5e5;
	
// 	text {
// 		flex: 1;
// 		overflow: hidden;
// 		text-overflow: ellipsis;
// 		white-space: nowrap;
// 	}
// }
</style>