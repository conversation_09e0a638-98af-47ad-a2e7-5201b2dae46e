import{_ as M,aw as S,ax as Q}from"./index-Byekp3Iv.js";import{_ as Z}from"./Dialog.vue_vue_type_style_index_0_lang-DhV_HFpU.js";import{_ as q}from"./index.vue_vue_type_script_setup_true_lang-BeMNDf6p.js";import{_ as A}from"./ContentWrap.vue_vue_type_script_setup_true_lang-D3mNNG_R.js";import{k as B,r as p,P as G,y as g,m as d,z as o,H as e,u as l,Z as K,l as N,G as $,$ as J,E as m,A as W,F as X,h as ee,n as ae}from"./form-create-B86qX0W_.js";import{b as le}from"./formatTime-HVkyL6Kg.js";import{P as te}from"./index-1HLiV7Gt.js";import{S as oe}from"./index-Dth2jxQN.js";import{h as re,aj as se,k as ne,x as pe,Q as ue,F as de,f as me,Z as ie,_ as ce,a6 as fe,ak as ge}from"./form-designer-C0ARe9Dh.js";const be={key:0},_e=B({name:"SaleOutReceiptEnableList",__name:"SaleOutReceiptEnableList",emits:["success"],setup(we,{expose:I,emit:T}){const w=p([]),v=p(0),b=p(!1),u=p(!1),r=G({pageNo:1,pageSize:10,no:void 0,productId:void 0,outTime:[],receiptEnable:!0,customerId:void 0}),y=p(),h=p([]),i=p([]),U=n=>{i.value=n};I({open:async n=>{u.value=!0,await ae(),r.customerId=n,await V(),h.value=await te.getProductSimpleList()}});const C=T,z=()=>{try{C("success",i.value)}finally{u.value=!1}},x=async()=>{b.value=!0;try{const n=await oe.getSaleOutPage(r);w.value=n.list,v.value=n.total}finally{b.value=!1}},V=()=>{y.value.resetFields(),_()},_=()=>{r.pageNo=1,i.value=[],x()};return(n,a)=>{const D=ne,c=se,E=ue,F=pe,H=de,k=M,f=me,Y=re,P=A,s=ce,L=fe,O=q,R=Z,j=ge;return d(),g(R,{title:"\u9009\u62E9\u9500\u552E\u51FA\u5E93\uFF08\u4EC5\u5C55\u793A\u53EF\u6536\u6B3E\uFF09",modelValue:l(u),"onUpdate:modelValue":a[6]||(a[6]=t=>ee(u)?u.value=t:null),appendToBody:!0,scroll:!0,width:"1080"},{footer:o(()=>[e(f,{disabled:!l(i).length,type:"primary",onClick:z},{default:o(()=>a[9]||(a[9]=[m(" \u786E \u5B9A ")])),_:1},8,["disabled"]),e(f,{onClick:a[5]||(a[5]=t=>u.value=!1)},{default:o(()=>a[10]||(a[10]=[m("\u53D6 \u6D88")])),_:1})]),default:o(()=>[e(P,null,{default:o(()=>[e(Y,{class:"-mb-15px",model:l(r),ref_key:"queryFormRef",ref:y,inline:!0,"label-width":"68px"},{default:o(()=>[e(c,{label:"\u51FA\u5E93\u5355\u53F7",prop:"no"},{default:o(()=>[e(D,{modelValue:l(r).no,"onUpdate:modelValue":a[0]||(a[0]=t=>l(r).no=t),placeholder:"\u8BF7\u8F93\u5165\u51FA\u5E93\u5355\u53F7",clearable:"",onKeyup:K(_,["enter"]),class:"!w-160px"},null,8,["modelValue"])]),_:1}),e(c,{label:"\u4EA7\u54C1",prop:"productId"},{default:o(()=>[e(F,{modelValue:l(r).productId,"onUpdate:modelValue":a[1]||(a[1]=t=>l(r).productId=t),clearable:"",filterable:"",placeholder:"\u8BF7\u9009\u62E9\u4EA7\u54C1",class:"!w-160px"},{default:o(()=>[(d(!0),N($,null,J(l(h),t=>(d(),g(E,{key:t.id,label:t.name,value:t.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(c,{label:"\u51FA\u5E93\u65F6\u95F4",prop:"orderTime"},{default:o(()=>[e(H,{modelValue:l(r).outTime,"onUpdate:modelValue":a[2]||(a[2]=t=>l(r).outTime=t),"value-format":"YYYY-MM-DD HH:mm:ss",type:"daterange","start-placeholder":"\u5F00\u59CB\u65E5\u671F","end-placeholder":"\u7ED3\u675F\u65E5\u671F","default-time":[new Date("1 00:00:00"),new Date("1 23:59:59")],class:"!w-160px"},null,8,["modelValue","default-time"])]),_:1}),e(c,null,{default:o(()=>[e(f,{onClick:_},{default:o(()=>[e(k,{icon:"ep:search",class:"mr-5px"}),a[7]||(a[7]=m(" \u641C\u7D22"))]),_:1}),e(f,{onClick:V},{default:o(()=>[e(k,{icon:"ep:refresh",class:"mr-5px"}),a[8]||(a[8]=m(" \u91CD\u7F6E"))]),_:1})]),_:1})]),_:1},8,["model"])]),_:1}),e(P,null,{default:o(()=>[W((d(),g(l(ie),{data:l(w),"show-overflow-tooltip":!0,stripe:!0,onSelectionChange:U},{default:o(()=>[e(s,{width:"30",label:"\u9009\u62E9",type:"selection"}),e(s,{"min-width":"180",label:"\u51FA\u5E93\u5355\u53F7",align:"center",prop:"no"}),e(s,{label:"\u5BA2\u6237",align:"center",prop:"customerName"}),e(s,{label:"\u4EA7\u54C1\u4FE1\u606F",align:"center",prop:"productNames","min-width":"200"}),e(s,{label:"\u51FA\u5E93\u65F6\u95F4",align:"center",prop:"outTime",formatter:l(le),width:"120px"},null,8,["formatter"]),e(s,{label:"\u521B\u5EFA\u4EBA",align:"center",prop:"creatorName"}),e(s,{label:"\u5E94\u6536\u91D1\u989D",align:"center",prop:"totalPrice",formatter:l(S)},null,8,["formatter"]),e(s,{label:"\u5DF2\u6536\u91D1\u989D",align:"center",prop:"receiptPrice",formatter:l(S)},null,8,["formatter"]),e(s,{label:"\u672A\u6536\u91D1\u989D",align:"center"},{default:o(t=>[t.row.receiptPrice===t.row.totalPrice?(d(),N("span",be,"0")):(d(),g(L,{key:1,type:"danger"},{default:o(()=>[m(X(l(Q)(t.row.totalPrice-t.row.receiptPrice)),1)]),_:2},1024))]),_:1})]),_:1},8,["data"])),[[j,l(b)]]),e(O,{limit:l(r).pageSize,"onUpdate:limit":a[3]||(a[3]=t=>l(r).pageSize=t),page:l(r).pageNo,"onUpdate:page":a[4]||(a[4]=t=>l(r).pageNo=t),total:l(v),onPagination:x},null,8,["limit","page","total"])]),_:1})]),_:1},8,["modelValue"])}}});export{_e as _};
