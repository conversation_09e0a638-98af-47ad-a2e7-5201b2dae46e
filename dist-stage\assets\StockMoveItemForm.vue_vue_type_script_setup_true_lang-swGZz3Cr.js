import{aM as B,ax as N,aL as Z,ay as D}from"./index-Byekp3Iv.js";import{P as J}from"./index-1HLiV7Gt.js";import{W as K}from"./index-C8NGFT36.js";import{S as O}from"./index-boBBTifs.js";import{ak as T,h as X,Z as Y,_ as ee,aj as ae,x as le,Q as oe,k as de,l as te,f as re,i as ue}from"./form-designer-C0ARe9Dh.js";import{k as ie,r as h,P as se,b as S,e as ne,l as x,m as n,G as I,A as me,y as _,C as pe,u as s,z as o,H as e,$ as k,E}from"./form-create-B86qX0W_.js";const ce=ie({__name:"StockMoveItemForm",props:{items:{},disabled:{type:Boolean}},setup(L,{expose:j}){const G=L,M=h(!1),c=h([]),V=se({inId:[{required:!0,message:"\u8C03\u5EA6\u7F16\u53F7\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],fromWarehouseId:[{required:!0,message:"\u8C03\u51FA\u4ED3\u5E93\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],toWarehouseId:[{required:!0,message:"\u8C03\u5165\u4ED3\u5E93\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],productId:[{required:!0,message:"\u4EA7\u54C1\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],count:[{required:!0,message:"\u4EA7\u54C1\u6570\u91CF\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}]}),y=h([]),U=h([]),g=h([]),C=h(void 0);S(()=>G.items,async d=>{c.value=d},{immediate:!0}),S(()=>c.value,d=>{d&&d.length!==0&&d.forEach(r=>{r.totalPrice=D(r.productPrice,r.count)})},{deep:!0});const z=d=>{const{columns:r,data:i}=d,m=[];return r.forEach((f,u)=>{if(u!==0)if(["count","totalPrice"].includes(f.property)){const p=Z(i.map(v=>Number(v[f.property])));m[u]=f.property==="count"?B(p):N(p)}else m[u]="";else m[u]="\u5408\u8BA1"}),m},P=()=>{var r;const d={id:void 0,fromWarehouseId:(r=C.value)==null?void 0:r.id,toWarehouseId:void 0,productId:void 0,productUnitName:void 0,productBarCode:void 0,productPrice:void 0,stockCount:void 0,count:1,totalPrice:void 0,remark:void 0};c.value.push(d)},W=async d=>{if(!d.productId||!d.fromWarehouseId)return;const r=await O.getStock2(d.productId,d.fromWarehouseId);d.stockCount=r?r.count:0};return j({validate:()=>y.value.validate()}),ne(async()=>{U.value=await J.getProductSimpleList(),g.value=await K.getWarehouseSimpleList(),C.value=g.value.find(d=>d.defaultStatus),c.value.length===0&&P()}),(d,r)=>{const i=ee,m=oe,f=le,u=ae,p=de,v=te,$=re,A=Y,F=X,H=ue,Q=T;return n(),x(I,null,[me((n(),_(F,{ref_key:"formRef",ref:y,model:s(c),rules:s(V),"label-width":"0px","inline-message":!0,disabled:d.disabled},{default:o(()=>[e(A,{data:s(c),"show-summary":"","summary-method":z,class:"-mt-10px"},{default:o(()=>[e(i,{label:"\u5E8F\u53F7",type:"index",align:"center",width:"60"}),e(i,{label:"\u8C03\u51FA\u4ED3\u5E93","min-width":"125"},{default:o(({row:a,$index:t})=>[e(u,{prop:`${t}.fromWarehouseId`,rules:s(V).fromWarehouseId,class:"mb-0px!"},{default:o(()=>[e(f,{modelValue:a.fromWarehouseId,"onUpdate:modelValue":l=>a.fromWarehouseId=l,clearable:"",filterable:"",placeholder:"\u8BF7\u9009\u62E9\u8C03\u51FA\u4ED3\u5E93",onChange:l=>((q,b)=>{W(b)})(0,a)},{default:o(()=>[(n(!0),x(I,null,k(s(g),l=>(n(),_(m,{key:l.id,label:l.name,value:l.id},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue","onChange"])]),_:2},1032,["prop","rules"])]),_:1}),e(i,{label:"\u8C03\u5165\u4ED3\u5E93","min-width":"125"},{default:o(({row:a,$index:t})=>[e(u,{prop:`${t}.toWarehouseId`,rules:s(V).toWarehouseId,class:"mb-0px!"},{default:o(()=>[e(f,{modelValue:a.toWarehouseId,"onUpdate:modelValue":l=>a.toWarehouseId=l,clearable:"",filterable:"",placeholder:"\u8BF7\u9009\u62E9\u8C03\u5165\u4ED3\u5E93"},{default:o(()=>[(n(!0),x(I,null,k(s(g),l=>(n(),_(m,{key:l.id,label:l.name,value:l.id},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue"])]),_:2},1032,["prop","rules"])]),_:1}),e(i,{label:"\u4EA7\u54C1\u540D\u79F0","min-width":"180"},{default:o(({row:a,$index:t})=>[e(u,{prop:`${t}.productId`,rules:s(V).productId,class:"mb-0px!"},{default:o(()=>[e(f,{modelValue:a.productId,"onUpdate:modelValue":l=>a.productId=l,clearable:"",filterable:"",onChange:l=>((q,b)=>{const w=U.value.find(R=>R.id===q);w&&(b.productUnitName=w.unitName,b.productBarCode=w.barCode,b.productPrice=w.minPrice),W(b)})(l,a),placeholder:"\u8BF7\u9009\u62E9\u4EA7\u54C1"},{default:o(()=>[(n(!0),x(I,null,k(s(U),l=>(n(),_(m,{key:l.id,label:l.name,value:l.id},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue","onChange"])]),_:2},1032,["prop","rules"])]),_:1}),e(i,{label:"\u5E93\u5B58","min-width":"100"},{default:o(({row:a})=>[e(u,{class:"mb-0px!"},{default:o(()=>[e(p,{disabled:"",modelValue:a.stockCount,"onUpdate:modelValue":t=>a.stockCount=t,formatter:s(B)},null,8,["modelValue","onUpdate:modelValue","formatter"])]),_:2},1024)]),_:1}),e(i,{label:"\u6761\u7801","min-width":"150"},{default:o(({row:a})=>[e(u,{class:"mb-0px!"},{default:o(()=>[e(p,{disabled:"",modelValue:a.productBarCode,"onUpdate:modelValue":t=>a.productBarCode=t},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1024)]),_:1}),e(i,{label:"\u5355\u4F4D","min-width":"80"},{default:o(({row:a})=>[e(u,{class:"mb-0px!"},{default:o(()=>[e(p,{disabled:"",modelValue:a.productUnitName,"onUpdate:modelValue":t=>a.productUnitName=t},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1024)]),_:1}),e(i,{label:"\u6570\u91CF",prop:"count",fixed:"right","min-width":"140"},{default:o(({row:a,$index:t})=>[e(u,{prop:`${t}.count`,rules:s(V).count,class:"mb-0px!"},{default:o(()=>[e(v,{modelValue:a.count,"onUpdate:modelValue":l=>a.count=l,"controls-position":"right",min:.001,precision:3,class:"!w-100%"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1032,["prop","rules"])]),_:1}),e(i,{label:"\u4EA7\u54C1\u5355\u4EF7",fixed:"right","min-width":"120"},{default:o(({row:a,$index:t})=>[e(u,{prop:`${t}.productPrice`,class:"mb-0px!"},{default:o(()=>[e(v,{modelValue:a.productPrice,"onUpdate:modelValue":l=>a.productPrice=l,"controls-position":"right",min:.01,precision:2,class:"!w-100%"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1032,["prop"])]),_:1}),e(i,{label:"\u5408\u8BA1\u91D1\u989D",prop:"totalPrice",fixed:"right","min-width":"100"},{default:o(({row:a,$index:t})=>[e(u,{prop:`${t}.totalPrice`,class:"mb-0px!"},{default:o(()=>[e(p,{disabled:"",modelValue:a.totalPrice,"onUpdate:modelValue":l=>a.totalPrice=l,formatter:s(N)},null,8,["modelValue","onUpdate:modelValue","formatter"])]),_:2},1032,["prop"])]),_:1}),e(i,{label:"\u5907\u6CE8","min-width":"150"},{default:o(({row:a,$index:t})=>[e(u,{prop:`${t}.remark`,class:"mb-0px!"},{default:o(()=>[e(p,{modelValue:a.remark,"onUpdate:modelValue":l=>a.remark=l,placeholder:"\u8BF7\u8F93\u5165\u5907\u6CE8"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1032,["prop"])]),_:1}),e(i,{align:"center",fixed:"right",label:"\u64CD\u4F5C",width:"60"},{default:o(({$index:a})=>[e($,{onClick:t=>{return l=a,void c.value.splice(l,1);var l},link:""},{default:o(()=>r[0]||(r[0]=[E("\u2014")])),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])]),_:1},8,["model","rules","disabled"])),[[Q,s(M)]]),d.disabled?pe("",!0):(n(),_(H,{key:0,justify:"center",class:"mt-3"},{default:o(()=>[e($,{onClick:P,round:""},{default:o(()=>r[1]||(r[1]=[E("+ \u6DFB\u52A0\u8C03\u5EA6\u4EA7\u54C1")])),_:1})]),_:1}))],64)}}});export{ce as _};
