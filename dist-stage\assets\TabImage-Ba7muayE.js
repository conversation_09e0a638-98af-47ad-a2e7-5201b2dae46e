import{d as U,aE as A,_ as B,c as F}from"./index-Byekp3Iv.js";import{W as H}from"./main-CgM-XNl9.js";import{u as P,U as S}from"./useUpload-ClgG934H.js";import{i as T,f as W,j as X,v as Z,z as q}from"./form-designer-C0ARe9Dh.js";import{k as D,c as G,r as y,P as J,l as r,m as u,y as K,u as i,v as c,C as L,H as l,F as N,z as s,E as I,h as O}from"./form-create-B86qX0W_.js";import"./index.vue_vue_type_script_setup_true_lang-BeMNDf6p.js";import"./main-Gr8bO50b.js";import"./main-C_b7Mv-e.js";import"./main.vue_vue_type_script_setup_true_lang-B8TlAAct.js";import"./index-CnYMp6Ej.js";import"./index-DZDuYCM_.js";import"./formatTime-HVkyL6Kg.js";const Q={key:0,class:"select-item"},R=["src"],Y={key:0,class:"item-name"},$=F(D({__name:"TabImage",props:{modelValue:{}},emits:["update:modelValue"],setup(h,{emit:k}){const V=U(),b={Authorization:"Bearer "+A()},j=h,w=k,e=G({get:()=>j.modelValue,set:a=>w("update:modelValue",a)}),o=y(!1),n=y([]),d=J({accountId:e.value.accountId,type:"image",title:"",introduction:""}),x=a=>P(S.Image,2)(a),z=a=>{if(a.code!==0)return V.error("\u4E0A\u4F20\u51FA\u9519\uFF1A"+a.msg),!1;n.value=[],d.title="",d.introduction="",p(a.data)},C=()=>{e.value.mediaId=null,e.value.url=null,e.value.name=null},p=a=>{o.value=!1,e.value.mediaId=a.mediaId,e.value.url=a.url,e.value.name=a.name};return(a,t)=>{const f=B,m=W,v=T,E=Z,_=X,M=q;return u(),r("div",null,[i(e).url?(u(),r("div",Q,[c("img",{class:"material-img",src:i(e).url},null,8,R),i(e).name?(u(),r("p",Y,N(i(e).name),1)):L("",!0),l(v,{class:"ope-row",justify:"center"},{default:s(()=>[l(m,{type:"danger",circle:"",onClick:C},{default:s(()=>[l(f,{icon:"ep:delete"})]),_:1})]),_:1})])):(u(),K(v,{key:1,style:{"text-align":"center"},align:"middle"},{default:s(()=>[l(_,{span:12,class:"col-select"},{default:s(()=>[l(m,{type:"success",onClick:t[0]||(t[0]=g=>o.value=!0)},{default:s(()=>[t[2]||(t[2]=I(" \u7D20\u6750\u5E93\u9009\u62E9 ")),l(f,{icon:"ep:circle-check"})]),_:1}),l(E,{title:"\u9009\u62E9\u56FE\u7247",modelValue:i(o),"onUpdate:modelValue":t[1]||(t[1]=g=>O(o)?o.value=g:null),width:"90%","append-to-body":"","destroy-on-close":""},{default:s(()=>[l(i(H),{type:"image","account-id":i(e).accountId,onSelectMaterial:p},null,8,["account-id"])]),_:1},8,["modelValue"])]),_:1}),l(_,{span:12,class:"col-add"},{default:s(()=>[l(M,{action:"https://optest.hbfarmx.com/admin-api/mp/material/upload-temporary",headers:b,multiple:"",limit:1,"file-list":i(n),data:i(d),"before-upload":x,"on-success":z},{tip:s(()=>t[4]||(t[4]=[c("span",null,[c("div",{class:"el-upload__tip"},"\u652F\u6301 bmp/png/jpeg/jpg/gif \u683C\u5F0F\uFF0C\u5927\u5C0F\u4E0D\u8D85\u8FC7 2M")],-1)])),default:s(()=>[l(m,{type:"primary"},{default:s(()=>t[3]||(t[3]=[I("\u4E0A\u4F20\u56FE\u7247")])),_:1})]),_:1},8,["file-list","data"])]),_:1})]),_:1}))])}}}),[["__scopeId","data-v-b669ca58"]]);export{$ as default};
