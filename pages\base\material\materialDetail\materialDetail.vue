<template>
	<view class="material-detail-page">
		<view class="material-detail-card" ref="scrollContainer" @scroll="handleScroll">
			<uni-card 
			:title="materialDetailForm.name ? materialDetailForm.name + '详情' : '详情'" 
			:isFull="true"
			>
			<view class="material_form_box">
				<uv-form 
					labelPosition="top" 
					:model="materialDetailForm" 
					:rules="formRules" 
					ref="materialForm"
					:modelValue="materialDetailForm"
				>
					<uv-form-item label="商品类型" required labelWidth="80px" prop="categoryCode">
						<uni-data-picker
							v-model="categoryValue"
							:localdata="formattedCategoryList"
							popup-title="选择商品类型"
							@change="onCategoryChange"
							placeholder="请选择商品类型"
						></uni-data-picker>
					</uv-form-item>
					<uv-form-item label="物料编码" labelWidth="80px">
						<input type="text" placeholder="请输入物料编码" v-model="materialDetailForm.code" @input="updateFull" class="border-input" disabled/>
					</uv-form-item>
					<uv-form-item label="物料名称" labelWidth="80px">
						<input type="text" placeholder="请输入物料名称" v-model="materialDetailForm.name" @input="updateFull" class="border-input"/>
					</uv-form-item>
					<uv-form-item label="物料全称" labelWidth="80px">
						<input type="text" placeholder="(自动生成)" v-model="materialDetailForm.fullName" disabled class="border-input"/>
					</uv-form-item>
					<uv-form-item label="物料编码" labelWidth="80px">
						<input type="text" placeholder="(自动生成)" v-model="materialDetailForm.fullCode" disabled class="border-input"/>
					</uv-form-item>
					<uv-form-item label="规格">
						<input type="text" placeholder="请输入物料规格" v-model="materialDetailForm.spec" class="border-input"/>
					</uv-form-item>
					<uv-form-item label="主单位" labelWidth="60px">
						<picker
							@change="onUnitChange"
							:value="getUnitIndex(materialDetailForm.unit)"
							:range="unitList"
							range-key="name"
							class="unit-picker"
						>
							<view class="picker-value border-input">
								<text>{{ getUnitName(materialDetailForm.unit) || '请选择主单位' }}</text>
								<text class="picker-arrow">▼</text>
							</view>
						</picker>
					</uv-form-item>
					<uv-form-item label="销售单位" labelWidth="80px">
						<picker
							@change="onSaleUnitChange"
							:value="getUnitIndex(materialDetailForm.saleUnit)"
							:range="unitList"
							range-key="name"
							class="unit-picker"
						>
							<view class="picker-value border-input">
								<text>{{ getUnitName(materialDetailForm.saleUnit) || '请选择销售单位' }}</text>
								<text class="picker-arrow">▼</text>
							</view>
						</picker>
					</uv-form-item>
					<uv-form-item label="采购单位" labelWidth="80px">
						<picker
							@change="onPurchaseUnitChange"
							:value="getUnitIndex(materialDetailForm.purchaseUnit)"
							:range="unitList"
							range-key="name"
							class="unit-picker"
						>
							<view class="picker-value border-input">
								<text>{{ getUnitName(materialDetailForm.purchaseUnit) || '请选择采购单位' }}</text>
								<text class="picker-arrow">▼</text>
							</view>
						</picker>
					</uv-form-item>
					<uv-form-item label="库存单位" labelWidth="80px">
						<picker
							@change="onInventoryUnitChange"
							:value="getUnitIndex(materialDetailForm.inventoryUnit)"
							:range="unitList"
							range-key="name"
							class="unit-picker"
						>
							<view class="picker-value border-input">
								<text>{{ getUnitName(materialDetailForm.inventoryUnit) || '请选择库存单位' }}</text>
								<text class="picker-arrow">▼</text>
							</view>
						</picker>
					</uv-form-item>
					<uv-form-item label="生产单位" labelWidth="80px">
						<picker
							@change="onMfgUnitChange"
							:value="getUnitIndex(materialDetailForm.mfgUnit)"
							:range="unitList"
							range-key="name"
							class="unit-picker"
						>
							<view class="picker-value border-input">
								<text>{{ getUnitName(materialDetailForm.mfgUnit) || '请选择生产单位' }}</text>
								<text class="picker-arrow">▼</text>
							</view>
						</picker>
					</uv-form-item>
					<uv-form-item label="采购价格" labelWidth="80">
						<input type="number" placeholder="请输入采购价格" v-model="materialDetailForm.purchasePrice" class="border-input"/>
					</uv-form-item>
					<uv-form-item label="销售价格" labelWidth="80">
						<input type="number" placeholder="请输入销售价格" v-model="materialDetailForm.salePrice" class="border-input"/>
					</uv-form-item>
					<uv-form-item label="加权采购价格" labelWidth="120">
						<input type="number" placeholder="请输入加权采购价格" v-model="materialDetailForm.averagePurchasePrice" class="border-input"/>
					</uv-form-item>
					<uv-form-item label="价格单位" labelWidth="80" v-if="materialDetailForm.priceUnit">
						<input type="text" placeholder="请输入价格单位"  v-model="materialDetailForm.priceUnit" class="border-input"/>
					</uv-form-item>
					<uv-form-item label="物料图片" labelWidth="120">
						<view class="image-upload-container">
							<view class="image-content-wrapper">
								<view class="image-preview-section">
									<image
										v-if="materialDetailForm.url"
										:src="materialDetailForm.url"
										mode="aspectFill"
										class="preview-image"
										@click="previewImage"
										@error="handleImageError"
										@load="handleImageLoad"
									/>
									<view v-else class="default-image-placeholder" @click="selectImage">
										<uni-icons type="camera" size="30" color="#ccc"></uni-icons>
										<text class="placeholder-text">点击选择图片</text>
									</view>
								</view>

								<!-- 图片右侧操作按钮 -->
								<view v-if="materialDetailForm.url" class="image-side-actions">
									<view class="side-action-btn" @click="selectImage" title="更换图片">
										<uni-icons type="camera" size="18" color="#2979ff"></uni-icons>
									</view>
									<view class="side-action-btn delete-action" @click="deleteImage" title="删除图片">
										<uni-icons type="trash" size="18" color="#f56c6c"></uni-icons>
									</view>
								</view>
							</view>
							<view class="upload-progress" v-if="uploadProgress > 0 && uploadProgress < 100">
								<progress :percent="uploadProgress" stroke-width="3" backgroundColor="#f0f0f0" activeColor="#007aff" />
								<text class="progress-text">上传中 {{uploadProgress}}%</text>
							</view>
						</view>
					</uv-form-item>
					<uv-form-item label="来源" labelWidth="60px">
						<picker
							@change="onSourceChange"
							:value="getSourceIndex(materialDetailForm.source)"
							:range="sourceList"
							range-key="label"
							class="unit-picker"
						>
							<view class="picker-value border-input">
								<text>{{ getSourceName(materialDetailForm.source) || '请选择来源' }}</text>
								<text class="picker-arrow">▼</text>
							</view>
						</picker>
					</uv-form-item>
					<uv-form-item label="备注">
						<input type="text" v-model="materialDetailForm.remark" placeholder="请输入备注" class="border-input"/>
					</uv-form-item>
					<uv-form-item label="二维码" labelWidth="120">
						<input type="text" v-model="materialDetailForm.qrCode" placeholder="请输入二维码" class="border-input" />
					</uv-form-item>
					<uv-divider text="更多信息"></uv-divider>
					<view class="tabs_container">
						<uv-tabs
						:list="tabsList"
						@change="onTabChange"
						></uv-tabs>
					</view>
					<view class="supplier_box" v-if="nowTab === '采购' && nowTabIndex === 2">
						<uv-form-item label="其他信息" labelWidth="80">
							<input
							type="text"
							v-model="materialDetailForm.otherInfo"
							placeholder="请输入其他信息"
							class="border-input"
							/>
						</uv-form-item>
					</view>
					<view class="elemtn_box" v-if="nowTab === '元素含量' && nowTabIndex === 0">
						<uv-form-item label="元素组成" label-width="80">
							<view class="table_box">
								<uni-table border stripe emptyText="暂无元素">
									<uni-tr>
										<uni-th align="center" width="60">元素</uni-th>
										<uni-th align="center" width="60">含量</uni-th>
										<uni-th align="center" width="60">单位</uni-th>
										<uni-th align="center" width="60">操作</uni-th>
									</uni-tr>
									<uni-tr v-for="(item,index) in materialDetailForm.elements" :key="index">
										<uni-td align="center">{{getDictLabel(elementList,item.element)}}</uni-td>
										<uni-td align="center">{{item.quantity}}</uni-td>
										<uni-td align="center">{{item.unit}}</uni-td>
										<uni-td align="center">
											<view class="delete_btn_box" @click="deleteElement(index)">
												<uv-icon name="trash" color="#ff0000"></uv-icon>
											</view>
										</uni-td>
									</uni-tr>
								</uni-table>
								<view class="add_element_box">
									<button class="add_element_btn" @click="openAddElementPopup">
										<uv-icon name="plus" color="#fff" label="添加元素" labelPos="right" space="5" labelColor="#fff" labelSize="16"></uv-icon>
									</button>
								</view>
							</view>
						</uv-form-item>
					</view>
					<view class="finance_box" v-if="nowTab === '会计' && nowTabIndex === 1">
						<uv-form-item label="存货科目" labelWidth="80">
							<uni-data-picker
								v-model="inventoryAccountValue"
								:localdata="formattedAccountSubjects"
								popup-title="选择存货科目"
								@change="onInventoryAccountChange"
								placeholder="请选择存货科目"
							></uni-data-picker>
						</uv-form-item>
						<uv-form-item label="销售收入科目" labelWidth="120">
							<uni-data-picker
								v-model="saleAccountValue"
								:localdata="formattedAccountSubjects"
								popup-title="选择销售收入科目"
								@change="onSaleAccountChange"
								placeholder="请选择销售收入科目"
							></uni-data-picker>
						</uv-form-item>
						<uv-form-item label="销售成本科目" labelWidth="120">
							<uni-data-picker
								v-model="saleCostAccountValue"
								:localdata="formattedAccountSubjects"
								popup-title="选择销售成本科目"
								@change="onSaleCostAccountChange"
								placeholder="请选择销售成本科目"
							></uni-data-picker>
						</uv-form-item>
						<uv-form-item label="成本差异科目" labelWidth="120">
							<uni-data-picker
								v-model="costDiffAccountValue"
								:localdata="formattedAccountSubjects"
								popup-title="选择成本差异科目"
								@change="onCostDiffAccountChange"
								placeholder="请选择成本差异科目"
							></uni-data-picker>
						</uv-form-item>
						<uv-form-item label="代管科目" labelWidth="120">
							<uni-data-picker
								v-model="adminAccountValue"
								:localdata="formattedAccountSubjects"
								popup-title="选择代管科目"
								@change="onAdminAccountChange"
								placeholder="请选择代管科目"
							></uni-data-picker>
						</uv-form-item>
						<uv-form-item label="税目科目" labelWidth="120">
							<uni-data-picker
								v-model="rateAccountValue"
								:localdata="formattedAccountSubjects"
								popup-title="选择税目科目"
								@change="onRateAccountChange"
								placeholder="请选择税目科目"
							></uni-data-picker>
						</uv-form-item>
						<uv-form-item label="成本科目" labelWidth="120">
							<input type="number" v-model="materialDetailForm.costProjectId" placeholder="请输入成本科目" class="border-input" />
						</uv-form-item>
					</view>
					<view class="package_box" v-if="nowTab === '包装方案' && nowTabIndex === 3">
						<uv-form-item label="包装信息" labelWidth="80">
							<input
							type="text"
							v-model="materialDetailForm.package"
							placeholder="请输入包装信息"
							class="border-input"
							/>
						</uv-form-item>
					</view>
					<view class="bom_box" v-if="nowTab === '物料清单' && nowTabIndex === 4">
						<uv-form-item label="物料清单" labelWidth="80">
							<input
							type="text"
							v-model="materialDetailForm.bomList"
							placeholder="请输入物料清单"
							class="border-input"
							/>
						</uv-form-item>
					</view>
					<view class="replace_box" v-if="nowTab === '替换物料' && nowTabIndex === 5">
						<uv-form-item label="替换物料" labelWidth="80">
							<input
							type="text"
							v-model="materialDetailForm.replaceList"
							placeholder="请输入替换物料"
							class="border-input"
							/>
						</uv-form-item>
					</view>
					<view class="production_box" v-if="nowTab === '生产' && nowTabIndex === 6">
						<uv-form-item label="生产信息" labelWidth="80">
							<input
							type="text"
							v-model="materialDetailForm.productionInfo"
							placeholder="请输入生产信息"
							class="border-input"
							/>
						</uv-form-item>
					</view>
				</uv-form>
			</view>
			</uni-card>
		</view>
		<view class="confirm_btn_box">
			<button type="primary" @click="submitForm">确定</button>
		</view>
		<!-- 返回顶部按钮 -->
		<view v-if="showBackTop" class="back-to-top" @click="backToTop">
			<uv-icon name="arrow-up" color="#fff" size="18"></uv-icon>
		</view>
		<!-- 添加元素弹出层 -->
		<uni-popup ref="elementPopup" type="center">
			<view class="element-popup-content">
				<view class="popup-title">添加元素</view>
				<view class="popup-form">
					<view class="form-item">
						<view class="form-label">元素</view>
						<view class="form-content">
							<picker @change="onElementChange" :value="elementIndex" :range="elementList" range-key="label" class="element-picker">
								<view class="picker-value border-input">
									<text>{{ elementSelected ? elementSelected.label : '请选择元素' }}</text>
									<text class="picker-arrow">▼</text>
								</view>
							</picker>
						</view>
					</view>
					<view class="form-item">
						<view class="form-label">含量</view>
						<view class="form-content">
							<input type="number" v-model="newElement.quantity" class="border-input" placeholder="请输入含量"/>
						</view>
					</view>
					<view class="form-item">
						<view class="form-label">单位</view>
						<view class="form-content">
							<picker @change="onElementUnitChange" :value="elementUnitIndex" :range="elementUnitList" range-key="label" class="element-picker">
								<view class="picker-value border-input">
									<text>{{ newElement.unit || '请选择单位' }}</text>
									<text class="picker-arrow">▼</text>
								</view>
							</picker>
						</view>
					</view>
					<view class="popup-buttons">
						<button class="cancel-btn" @click="closeAddElementPopup">取消</button>
						<button class="confirm-btn" @click="addElement">确定</button>
					</view>
				</view>
			</view>
		</uni-popup>
	</view>
</template>

<script>
import { createMaterialApi, getMaterialApi, updateMaterialApi } from '../../../../api/scm/base/material'
import { getMaterialPricePageApi } from '../../../../api/scm/base/materialPrice';
import { getUnitPageApi } from '../../../../api/scm/base/unit';
import { getDictOptions, getDictLabel, getBatchDictOptions } from '../../../../utils/dict'
import { getAllSimpleAccountSubjectList } from '../../../../api/scm/base/finance/accountSubject/index'

	export default {
		data() {
			return {
					isUpdate: false,
					materialDetail: null,
					materialDetailForm: {
						companyName: null,
						code: null,
						categoryCode: null,
						fullCode: null,
						type: null,
						form: null,
						subType: null,
						name: null,
						fullName: null,
						spec: null,
						specQuantity: null,
						specUnit: null,
						inventory: null,
						unit: null,
						saleUnit: null,
						purchaseUnit: null,
						inventoryUnit: null,
						mfgUnit: null,
						purchasePrice: null,
						salePrice: null,
						averagePurchasePrice: null,
						priceUnit: null,
						elements: [],
						url: null,
						source: null,
						remark: null,
						inventoryAccountId: null,
						saleAccountId: null,
						saleCostAccountId: null,
						costDiffAccountId: null,
						adminAccountId: null,
						rateAccountId: null,
						costProjectId: null,
						qrCode: null,
						otherInfo: null,
						package: null,
						bomList: null,
						replaceList: null,
						productionInfo: null,

					}, // 用于表单绑定的数据
					formRules: {
						// 添加表单验证规则
						categoryCode: {
							required: true,
							message: '请选择商品类型',
							trigger: ['change', 'blur']
						}
					},
					tabsList:[{
						name:'元素含量'
					},{
						name:'会计'
					},{
						name:'采购'
					},{
						name:'包装方案'
					},{
						name:'物料清单'
					},{
						name:'替换物料'
					},{
						name:'生产'
					}],
					materialId: null,
					rawCategoryList: [], // 原始分类数据
					formattedCategoryList: [], // 格式化后的分类数据，专用于uni-data-picker
					selectedCategory: null, // 存储选中的分类完整信息
					categoryPath: '', // 存储分类选择路径
					categoryValue: '', // 专门用于uni-data-picker的值
					unitList: [], // 单位列表
					unitGroups: {}, // 按组分类的单位

					elementList:[], // 元素列表
					// 新增元素弹出层相关数据
					elementIndex: 0, // 选中的元素索引
					elementSelected: null, // 选中的元素对象
					elementUnitList: [], // 元素单位列表（从字典获取）
					elementUnitOptions: [], // 完整的单位选项数组
					elementUnitIndex: 0, // 选中的单位索引
					newElement: { // 新元素对象
						element: '',
						quantity: '',
						unit: ''
					},
					showBackTop: false, // 是否显示返回顶部按钮
					scrollTop: 0, // 滚动条位置
					isInitialized: false, // 标记是否已初始化，防止重复初始化
					nowTabIndex:0,
					nowTab:'元素含量',
					// 图片上传相关
					uploadProgress: 0, // 上传进度
					isUploading: false, // 是否正在上传
					imageLoadError: false, // 图片加载失败标记
					// 科目相关数据
					accountSubjects: [], // 原始科目数据
					formattedAccountSubjects: [], // 格式化后的科目数据，专用于uni-data-picker
					// 科目选择器的值
					inventoryAccountValue: '', // 存货科目选择值
					saleAccountValue: '', // 销售收入科目选择值
					saleCostAccountValue: '', // 销售成本科目选择值
					costDiffAccountValue: '', // 成本差异科目选择值
					adminAccountValue: '', // 代管科目选择值
					rateAccountValue: '', // 税目科目选择值
					// 来源相关数据
					sourceList: [] // 来源选项列表
			}
		},

		methods: {
			// 从utils/dict中导入的getDictLabel不会被Vue实例自动识别为方法
			// 需要在methods中添加此方法作为代理
			getDictLabel(dictList, value) {
				return getDictLabel(dictList, value);
			},
			async initMatrialDetail(id){
				// 防止重复初始化
				if (this.isInitialized) {
					return
				}
				
				if(id){
					try {
						// 显示加载提示
						uni.showLoading({
							title: '加载中...'
						})
						
						const response = await getMaterialApi(id)
						
						// 隐藏加载提示
						uni.hideLoading()
						
						if(response.code !== 0){
							uni.showToast({
								title:'初始化商品数据失败',
								icon:'error'
							})
							return
						}
						this.materialDetail = response.data
						// 将获取到的数据复制到表单数据对象中
						this.materialDetailForm = JSON.parse(JSON.stringify(this.materialDetail))
						
						// 如果有分类编码，查找并设置分类路径
						if (this.materialDetailForm.categoryCode) {
							this.categoryValue = this.materialDetailForm.categoryCode
							this.findSelectedCategory(this.formattedCategoryList)
							this.updateCategoryPath()
						}
						// 手动触发表单验证更新
						this.$nextTick(() => {
							this.$refs.materialForm && this.$refs.materialForm.setRules(this.formRules)
						})
						
						// 标记已初始化
						this.isInitialized = true
						// 设置科目选择器的值
						this.setAccountSubjectValues()
					} catch (error) {
						// 隐藏加载提示
						uni.hideLoading()
						
						uni.showToast({
							title: '初始化商品详情异常',
							icon: 'error'
						})
					}
				}
			},
			//tab切换方法
			onTabChange(event){
				if(event){
					this.nowTab = event.name
					this.nowTabIndex = event.index
				}
			},
			// 初始化元素和单位字典数据
			async initElementAndUnitDictData(){
				try {
					// 批量获取字典数据
					const dictData = await getBatchDictOptions([
						'all_element',
						'quality_unit'
					]);
					
					// 处理元素列表
					if (dictData.all_element && Array.isArray(dictData.all_element)) {
						this.elementList = dictData.all_element;
					}
					
					// 处理单位列表
					if (dictData.quality_unit && Array.isArray(dictData.quality_unit) && dictData.quality_unit.length > 0) {
						this.elementUnitOptions = [...dictData.quality_unit];
						this.elementUnitList = dictData.quality_unit.map(item => ({
							label: item.label,
							value: item.value
						}));
					} else {
						this.elementUnitOptions = [];
						this.elementUnitList = [];
					}
				} catch (error) {
					console.error('获取元素和单位字典数据失败:', error);
					this.elementList = [];
					this.elementUnitList = [];
					this.elementUnitOptions = [];
				}
			},
			// 这个方法已经不需要了，因为我们改用来源选择器
			//删除元素
			deleteElement(index){
				// 使用splice而不是slice来删除数组元素
				if(index !== undefined && this.materialDetailForm.elements && this.materialDetailForm.elements.length !== 0){
					this.materialDetailForm.elements.splice(index, 1)
				}
			},
			// 打开添加元素弹出层
			openAddElementPopup(){
				// 重置新元素数据
				this.elementIndex = 0
				this.elementSelected = null
				this.elementUnitIndex = 0
				this.newElement = {
					element: '',
					quantity: '',
					unit: ''
				}
				// 打开弹出层
				this.$refs.elementPopup.open()
			},
			// 关闭添加元素弹出层
			closeAddElementPopup(){
				this.$refs.elementPopup.close()
			},
			// 处理元素选择变化
			onElementChange(e){
				const index = e.detail.value
				if(this.elementList && index >= 0 && index < this.elementList.length){
					this.elementIndex = index
					this.elementSelected = this.elementList[index]
					this.newElement.element = this.elementList[index].value
				}
			},
			// 处理元素单位选择变化
			onElementUnitChange(e){
				const index = e.detail.value
				if(this.elementUnitList && index >= 0 && index < this.elementUnitList.length){
					this.elementUnitIndex = index
					this.newElement.unit = this.elementUnitList[index].value
				}
			},
			// 添加元素
			addElement(){
				// 验证必填项
				if(!this.newElement.element){
					uni.showToast({
						title: '请选择元素',
						icon: 'none'
					})
					return
				}
				if(!this.newElement.quantity){
					uni.showToast({
						title: '请输入含量',
						icon: 'none'
					})
					return
				}
				if(!this.newElement.unit){
					uni.showToast({
						title: '请选择单位',
						icon: 'none'
					})
					return
				}
				
				// 检查是否已初始化elements数组
				if(!this.materialDetailForm.elements){
					this.materialDetailForm.elements = []
				}
				
				// 添加新元素
				this.materialDetailForm.elements.push({
					element: this.newElement.element,
					quantity: this.newElement.quantity,
					unit: this.newElement.unit
				})
				
				// 关闭弹出层
				this.closeAddElementPopup()
			},
			// 处理分类选择变化
			onCategoryChange(e) {
				if (e && e.detail && e.detail.value) {
					// 获取选中的完整路径
					const selectedPath = e.detail.value
					// 如果是层级数据，取最后一个节点的categoryCode作为选中值
					if (Array.isArray(selectedPath)) {
						const lastNode = selectedPath[selectedPath.length - 1]
						if (lastNode && typeof lastNode === 'object') {
							// 如果是对象，取其value属性
							this.materialDetailForm.categoryCode = lastNode.value
						} else {
							// 如果直接是值
							this.materialDetailForm.categoryCode = lastNode
						}
						
						// 查找并处理分类信息
						const category = this.findSelectedCategory(this.formattedCategoryList)
						
						if (category) {
							this.updateWithCategoryInfo(category)
						}
					} else if (typeof selectedPath === 'object') {
						// 如果返回的是对象
						this.materialDetailForm.categoryCode = selectedPath.value
						
						const category = this.findSelectedCategory(this.formattedCategoryList)
						if (category) {
							this.updateWithCategoryInfo(category)
						}
					} else {
						// 如果是直接返回的值
						this.materialDetailForm.categoryCode = selectedPath
						
						const category = this.findSelectedCategory(this.formattedCategoryList)
						if (category) {
							this.updateWithCategoryInfo(category)
						}
					}
					
					// 主动触发表单验证
					this.$refs.materialForm && this.$refs.materialForm.validateField('categoryCode')
				}
			},
			
			// 根据选择的类别更新相关字段
			updateWithCategoryInfo(category) {
				if (!category) return
				// 更新分类路径显示
				this.updateCategoryPath()
				this.updateFull()
				this.materialDetailForm.type = category.fullCode.split('.')[0]
				this.materialDetailForm.form = category.fullCode.split('.')[1]
				this.materialDetailForm.subType = category.fullCode.split('.')[2]
				// 强制更新视图
				this.$forceUpdate()
			},
			
			updateFull(){
				this.materialDetailForm.fullCode = this.categoryValue + (this.materialDetailForm.code ? `.${this.materialDetailForm.code}` : '')
				this.materialDetailForm.fullName = this.categoryPath + (this.materialDetailForm.name ? `_${this.materialDetailForm.name}` : '')
			},
			
			// 更新分类路径显示
			updateCategoryPath() {
				if (this.selectedCategory && this.selectedCategory.fullName) {
					this.categoryPath = this.selectedCategory.fullName.replace(/_/g, ' _ ')
				} else {
					this.categoryPath = ''
				}
			},
			
			// 递归查找选中的分类
			findSelectedCategory(categories) {
				const code = this.materialDetailForm.categoryCode
				
				const findCategory = (items, code) => {
					if (!items || !Array.isArray(items)) return null
					
					for (const item of items) {
						if (item.categoryCode === code) {
							this.selectedCategory = item
							return item
						}
						
						if (item.children && item.children.length > 0) {
							const found = findCategory(item.children, code)
							if (found) return found
						}
					}
					
					return null
				}
				
				// 开始递归查找
				return findCategory(categories, code)
			},
			// 供应商相关方法已移除，因为采购tab内容已调整
			// 获取单位名称
			getUnitName(unitId) {
				// 如果unitId为null，直接返回空字符串
				if (unitId === null || unitId === undefined) {
					return '';
				}
				
				if (!this.unitList || this.unitList.length === 0) {
					return '';
				}
				
				// 将unitId转换为数字类型进行比较
				const unitIdNum = Number(unitId);
				const unit = this.unitList.find(item => {
					// 如果item.id为null，跳过比较
					if (item.id === null) return false;
					return Number(item.id) === unitIdNum;
				});
				
				return unit ? unit.name : '';
			},
			
			// 获取单位索引
			getUnitIndex(unitId) {
				// 如果unitId为null，返回0（"暂不选择"选项的索引）
				if (unitId === null || unitId === undefined) {
					return 0;
				}
				
				if (!this.unitList || this.unitList.length === 0) {
					return -1;
				}
				
				// 将unitId转换为数字类型进行比较
				const unitIdNum = Number(unitId);
				const index = this.unitList.findIndex(item => {
					// 如果item.id为null，跳过比较
					if (item.id === null) return false;
					return Number(item.id) === unitIdNum;
				});
				
				// 如果找不到，返回0（"暂不选择"选项的索引）
				return index === -1 ? 0 : index;
			},
			
			// 处理单位数据，添加"暂不选择"选项
			processUnitList() {
				// 添加"暂不选择"选项
				const emptyOption = {
					id: null,
					name: '暂不选择',
					code: '',
					coefficient: 0
				};
				
				// 确保unitList已经初始化
				if (!this.unitList) {
					this.unitList = [];
				}
				
				// 在列表前添加"暂不选择"选项
				this.unitList = [emptyOption, ...this.unitList];
			},
			
			// 主单位选择处理
			onUnitChange(e) {
				const index = e.detail.value
				if (index >= 0 && index < this.unitList.length) {
					const selectedUnit = this.unitList[index]
					// 处理"暂不选择"选项
					if (selectedUnit.id === null) {
						this.materialDetailForm.unit = null;
					} else {
						// 保存为number类型
						this.materialDetailForm.unit = Number(selectedUnit.id)
					}
				}
			},
			
			// 销售单位选择处理
			onSaleUnitChange(e) {
				const index = e.detail.value
				if (index >= 0 && index < this.unitList.length) {
					const selectedUnit = this.unitList[index]
					// 处理"暂不选择"选项
					if (selectedUnit.id === null) {
						this.materialDetailForm.saleUnit = null;
					} else {
						// 保存为number类型
						this.materialDetailForm.saleUnit = Number(selectedUnit.id)
					}
				}
			},

			// 采购单位选择处理
			onPurchaseUnitChange(e) {
				const index = e.detail.value
				if (index >= 0 && index < this.unitList.length) {
					const selectedUnit = this.unitList[index]
					// 处理"暂不选择"选项
					if (selectedUnit.id === null) {
						this.materialDetailForm.purchaseUnit = null;
					} else {
						// 保存为number类型
						this.materialDetailForm.purchaseUnit = Number(selectedUnit.id)
					}
				}
			},

			// 库存单位选择处理
			onInventoryUnitChange(e) {
				const index = e.detail.value
				if (index >= 0 && index < this.unitList.length) {
					const selectedUnit = this.unitList[index]
					// 处理"暂不选择"选项
					if (selectedUnit.id === null) {
						this.materialDetailForm.inventoryUnit = null;
					} else {
						// 保存为number类型
						this.materialDetailForm.inventoryUnit = Number(selectedUnit.id)
					}
				}
			},

			// 生产单位选择处理
			onMfgUnitChange(e) {
				const index = e.detail.value
				if (index >= 0 && index < this.unitList.length) {
					const selectedUnit = this.unitList[index]
					// 处理"暂不选择"选项
					if (selectedUnit.id === null) {
						this.materialDetailForm.mfgUnit = null;
					} else {
						// 保存为number类型
						this.materialDetailForm.mfgUnit = Number(selectedUnit.id)
					}
				}
			},
			
			// 组织单位按组显示
			organizeUnitsByGroup() {
				const groups = {};

				this.unitList.forEach(unit => {
					if (!groups[unit.groupCode]) {
						groups[unit.groupCode] = {
							groupCode: unit.groupCode,
							groupName: unit.groupName || `组 ${unit.groupCode}`,
							units: []
						};
					}

					groups[unit.groupCode].units.push(unit);
				});

				this.unitGroups = groups;
			},

			// 来源相关方法
			// 初始化来源选项
			async initSourceList() {
				try {
					const response = await getDictOptions('material_source')
					if (response) {
						this.sourceList = response
					}
				} catch (error) {
					console.error('加载来源选项失败:', error)
				}
			},

			// 获取来源名称
			getSourceName(sourceValue) {
				if (!sourceValue || !this.sourceList || this.sourceList.length === 0) {
					return '';
				}

				const source = this.sourceList.find(item => item.value === sourceValue);
				return source ? source.label : '';
			},

			// 获取来源索引
			getSourceIndex(sourceValue) {
				if (!sourceValue || !this.sourceList || this.sourceList.length === 0) {
					return 0;
				}

				const index = this.sourceList.findIndex(item => item.value === sourceValue);
				return index === -1 ? 0 : index;
			},

			// 来源选择处理
			onSourceChange(e) {
				const index = e.detail.value
				if (index >= 0 && index < this.sourceList.length) {
					const selectedSource = this.sourceList[index]
					this.materialDetailForm.source = selectedSource.value
				}
			},
			
			// 格式化分类数据以适应uni-data-picker组件
			formatCategoryList(data) {
				// 如果不是数组则返回空数组
				if (!Array.isArray(data)) return []
				
				const formatNode = (node) => {
					// 创建新对象，避免修改原始数据
					const formattedNode = {
						...node,
						text: node.categoryName,
						value: node.categoryCode
					}
					
					// 处理子节点
					if (node.children && Array.isArray(node.children)) {
						formattedNode.children = node.children.map(child => formatNode(child))
					}
					
					return formattedNode
				}
				
				// 处理每个顶级节点
				return data.map(item => formatNode(item))
			},
			// 表单提交
			submitForm() {
				this.$refs.materialForm.validate().then(async res => {
					if(this.isUpdate){
						const res = await updateMaterialApi(JSON.stringify(this.materialDetailForm))
						if(res.code !== 0){
							uni.showToast({
								title:'保存失败',
								icon:'error'
							})
							return
						}
						uni.showToast({
							title:'修改成功',
							icon:'success'
						})
						// 使用全局事件通知列表页刷新
						uni.$emit('material_list_refresh', {isRefresh: true})
						uni.navigateBack({
							delta:1
						})
					}else{
						const res = await createMaterialApi(JSON.stringify(this.materialDetailForm))
						if(res.code !== 0){
							uni.showToast({
								title:'新增失败',
								icon:'error'
							})
							return
						}
						uni.showToast({
							title:'新增成功',
							icon:'success'
						})
						// 使用全局事件通知列表页刷新
						uni.$emit('material_list_refresh', {isRefresh: true})
						uni.navigateBack({
							delta:1
						})
					}
				})
			},
			// 监听滚动事件
			handleScroll(e) {
				// 获取滚动条位置
				if (e && e.detail) {
					this.scrollTop = e.detail.scrollTop
					// 当滚动距离超过300时显示返回顶部按钮
					this.showBackTop = this.scrollTop > 300
				}
			},
			
			// 返回顶部
			backToTop() {
				// 使用uni的API实现平滑滚动到顶部
				if (this.$refs.scrollContainer) {
					this.$refs.scrollContainer.scrollTop = 0
				}
			},

			// 图片上传相关方法
			selectImage() {
				uni.chooseImage({
					count: 1,
					sizeType: ['compressed'],
					sourceType: ['album', 'camera'],
					success: (res) => {
						const tempFilePath = res.tempFilePaths[0];

						// 先显示临时图片
						this.$set(this.materialDetailForm, 'url', tempFilePath);
						this.$forceUpdate();

						// 然后上传图片
						this.uploadImage(tempFilePath);
					},
					fail: (err) => {
						console.error('选择图片失败:', err);
						uni.showToast({
							title: '选择图片失败',
							icon: 'none'
						});
					}
				});
			},

			async uploadImage(filePath) {
				this.isUploading = true;
				this.uploadProgress = 0;

				try {
					// 导入上传工具
					const upload = (await import('@/utils/upload')).default;

					const result = await upload({
						url: '/infra/file/upload',
						filePath: filePath,
						name: 'file',
						formData: {
							directory: 'material'
						}
					});

					// 根据上传工具的返回结构解析图片URL
					if (result && result.code === 200 && result.data) {
						const imageUrl = result.data;

						// 上传成功，更新图片URL
						this.$set(this.materialDetailForm, 'url', imageUrl);
						this.imageLoadError = false;
						this.uploadProgress = 100;

						// 强制更新视图
						this.$forceUpdate();

						uni.showToast({
							title: '图片上传成功',
							icon: 'success'
						});
					} else {
						throw new Error('上传失败: ' + (result && result.msg ? result.msg : '未知错误'));
					}

				} catch (error) {
					let errorMessage = '图片上传失败';
					if (error && error.message) {
						errorMessage += ': ' + error.message;
					}
					uni.showToast({
						title: errorMessage,
						icon: 'none'
					});
				} finally {
					this.isUploading = false;
					setTimeout(() => {
						this.uploadProgress = 0;
					}, 1000);
				}
			},

			previewImage() {
				if (this.materialDetailForm.url) {
					uni.previewImage({
						urls: [this.materialDetailForm.url],
						current: 0
					});
				}
			},

			deleteImage() {
				uni.showModal({
					title: '确认删除',
					content: '确定要删除当前图片吗？',
					confirmText:'确定',
					cancelText:'取消',
					success: (res) => {
						if (res.confirm) {
							this.$set(this.materialDetailForm, 'url', null);
							this.$forceUpdate();
							uni.showToast({
								title: '图片已删除',
								icon: 'success'
							});
						}
					}
				});
			},

			handleImageError() {
				this.imageLoadError = true;
			},

			handleImageLoad() {
				this.imageLoadError = false;
			},

			// 科目相关方法
			// 加载科目数据
			async loadAccountSubjects() {
				try {
					const response = await getAllSimpleAccountSubjectList()
					if (response.code === 0) {
						const flatData = response.data || []
						// 将扁平数据转换为树形结构
						this.accountSubjects = this.buildAccountSubjectTree(flatData)
						this.formattedAccountSubjects = this.formatAccountSubjectList(this.accountSubjects)
						// 如果有已选择的科目，设置对应的值
						this.setAccountSubjectValues()
					}
				} catch (error) {
					console.error('加载科目数据失败:', error)
					uni.showToast({
						title: '加载科目数据失败',
						icon: 'none'
					})
				}
			},

			// 将扁平的科目数据构建为树形结构
			buildAccountSubjectTree(flatData) {
				if (!Array.isArray(flatData)) return []

				// 1. 构建节点映射
				const nodeMap = {}
				flatData.forEach(item => {
					nodeMap[item.subjectId] = {
						...item,
						children: [] // 初始化children
					}
				})

				// 2. 构建树形结构
				const treeData = []
				flatData.forEach(item => {
					if (item.parentId === 0 || !item.parentId) {
						treeData.push(nodeMap[item.subjectId]) // 根节点
					} else {
						const parent = nodeMap[item.parentId]
						if (parent) {
							parent.children.push(nodeMap[item.subjectId]) // 添加到父节点的children
						}
					}
				})

				return treeData
			},

			// 格式化科目数据以适应uni-data-picker组件
			formatAccountSubjectList(data) {
				if (!Array.isArray(data)) return []

				const formatNode = (node) => {
					const formattedNode = {
						...node,
						text: node.subjectName,
						value: node.subjectId
					}

					if (node.children && Array.isArray(node.children)) {
						formattedNode.children = node.children.map(child => formatNode(child))
					}

					return formattedNode
				}

				return data.map(item => formatNode(item))
			},

			// 设置科目选择器的值（用于编辑时回显）
			setAccountSubjectValues() {
				if (this.materialDetailForm.inventoryAccountId) {
					this.inventoryAccountValue = this.materialDetailForm.inventoryAccountId
				}
				if (this.materialDetailForm.saleAccountId) {
					this.saleAccountValue = this.materialDetailForm.saleAccountId
				}
				if (this.materialDetailForm.saleCostAccountId) {
					this.saleCostAccountValue = this.materialDetailForm.saleCostAccountId
				}
				if (this.materialDetailForm.costDiffAccountId) {
					this.costDiffAccountValue = this.materialDetailForm.costDiffAccountId
				}
				if (this.materialDetailForm.adminAccountId) {
					this.adminAccountValue = this.materialDetailForm.adminAccountId
				}
				if (this.materialDetailForm.rateAccountId) {
					this.rateAccountValue = this.materialDetailForm.rateAccountId
				}
			},

			// 科目选择变化处理方法
			onInventoryAccountChange(e) {
				if (e && e.detail && e.detail.value) {
					const selectedPath = e.detail.value
					const subjectId = Array.isArray(selectedPath) ? selectedPath[selectedPath.length - 1].value : selectedPath.value
					this.materialDetailForm.inventoryAccountId = subjectId
				}
			},

			onSaleAccountChange(e) {
				if (e && e.detail && e.detail.value) {
					const selectedPath = e.detail.value
					const subjectId = Array.isArray(selectedPath) ? selectedPath[selectedPath.length - 1].value : selectedPath.value
					this.materialDetailForm.saleAccountId = subjectId
				}
			},

			onSaleCostAccountChange(e) {
				if (e && e.detail && e.detail.value) {
					const selectedPath = e.detail.value
					const subjectId = Array.isArray(selectedPath) ? selectedPath[selectedPath.length - 1].value : selectedPath.value
					this.materialDetailForm.saleCostAccountId = subjectId
				}
			},

			onCostDiffAccountChange(e) {
				if (e && e.detail && e.detail.value) {
					const selectedPath = e.detail.value
					const subjectId = Array.isArray(selectedPath) ? selectedPath[selectedPath.length - 1].value : selectedPath.value
					this.materialDetailForm.costDiffAccountId = subjectId
				}
			},

			onAdminAccountChange(e) {
				if (e && e.detail && e.detail.value) {
					const selectedPath = e.detail.value
					const subjectId = Array.isArray(selectedPath) ? selectedPath[selectedPath.length - 1].value : selectedPath.value
					this.materialDetailForm.adminAccountId = subjectId
				}
			},

			onRateAccountChange(e) {
				if (e && e.detail && e.detail.value) {
					const selectedPath = e.detail.value
					const subjectId = Array.isArray(selectedPath) ? selectedPath[selectedPath.length - 1].value : selectedPath.value
					this.materialDetailForm.rateAccountId = subjectId
				}
			},
		},
		// 添加onShow生命周期，某些小程序环境可能在这里才准备就绪
		onShow() {
			
			// 如果已经初始化，不再重复处理
			if (this.isInitialized) return

			// 尝试从页面栈获取参数
			try {
				const pages = getCurrentPages()
				if (pages.length > 0) {
					const currentPage = pages[pages.length - 1]
					
					// 获取页面参数（在某些平台下可能是通过这种方式获取）
					if (currentPage.options && currentPage.options.id) {
						this.materialId = currentPage.options.id
						this.isUpdate = true
						this.initMatrialDetail(this.materialId)
						return
					}
					
					// 微信小程序特有的参数获取方式
					if (currentPage.$page && currentPage.$page.fullPath) {
						const fullPath = currentPage.$page.fullPath
						
						// 解析路径中的参数
						const queryMatch = fullPath.match(/\?(.+)$/)
						if (queryMatch && queryMatch[1]) {
							const queryStr = queryMatch[1]
							const params = new URLSearchParams(queryStr)
							if (params.has('id')) {
								const id = params.get('id')
								this.materialId = id
								this.isUpdate = true
								this.initMatrialDetail(this.materialId)
								return
							}
						}
					}
				}
			} catch (error) {
				console.error('获取页面参数异常')
			}
			
			// 如果仍未初始化但materialId存在，尝试初始化
			if (!this.isInitialized && this.materialId) {
				this.initMatrialDetail(this.materialId)
			}
		},
		onLoad(){
			const eventChannel = this.getOpenerEventChannel()
			if(!eventChannel){
				return
			}
			eventChannel.on('acceptDataFormOpener',(data) => {
				if(data){
					console.log(data.categoryList)
					this.unitList = JSON.parse(JSON.stringify(data.unitList))
					this.processUnitList()
					this.organizeUnitsByGroup()
					this.rawCategoryList = JSON.parse(JSON.stringify(data.categoryList))
						// 转换为格式化数据用于组件
					this.formattedCategoryList = this.formatCategoryList(data.categoryList.children || [])
				}
			})
		},
		mounted(){
			this.initElementAndUnitDictData()
			this.loadAccountSubjects()
			this.initSourceList()
		}
	}
</script>

<style>
.material-detail-page{
	width: 100%;
	display: flex;
	flex-direction: column;
	height: 100%;
}
.material-detail-card{
	width: 100%;
	flex: 1;
	overflow-y: auto;
	height: calc(100% - 60px);
	padding-bottom: 70px; /* 添加底部内边距，防止内容被固定按钮遮挡 */
}
.category-path {
	margin-top: 5px;
	font-size: 12px;
	color: #666;
}
.category-path-text {
	line-height: 16px;
}
.debug-info {
	margin-top: 5px;
	font-size: 12px;
	color: #ff5500;
	background-color: #f8f8f8;
	padding: 5px;
	border: 1px dashed #ccc;
}
.debug-info text {
	display: block;
}
.border-input{
	border: 1px solid #DCDFE6;
	border-radius: 4px;
	padding: 8px 12px;
	width: 100%;
	box-sizing: border-box;
	font-size: 14px;
	color: #606266;
	height: 40px;
	line-height: 40px;
	transition: border-color 0.2s ease;
	background-color: #FFFFFF;
}

.border-input:focus{
	border-color: #2979FF;
	outline: none;
}

.border-input::placeholder{
	color: #C0C4CC;
}

.unit-picker {
	width: 100%;
}

.picker-value {
	position: relative;
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.picker-arrow {
	font-size: 12px;
	color: #909399;
}
.table_box{
	width: 100%;
}

/* 添加元素按钮样式 */
.add_element_box {
	margin-top: 15px;
	display: flex;
	justify-content: center;
}

.add_element_btn {
	background-color: #2979FF;
	color: white;
	padding: 8px 20px;
	border-radius: 4px;
	font-size: 14px;
	border: none;
	display: flex;
	align-items: center;
	justify-content: center;
	box-shadow: 0 2px 4px rgba(41, 121, 255, 0.2);
	transition: all 0.3s ease;
}

.add_element_btn:active {
	background-color: #1c69e8;
	transform: translateY(1px);
	box-shadow: 0 1px 2px rgba(41, 121, 255, 0.2);
}

/* 删除按钮样式 */
.delete_btn_box {
	cursor: pointer;
	padding: 5px;
	display: flex;
	justify-content: center;
	align-items: center;
}

/* 元素弹出层样式 */
.element-popup-content {
	background-color: #fff;
	border-radius: 8px;
	width: 80vw;
	max-width: 300px;
	padding: 20px;
}

.popup-title {
	font-size: 16px;
	font-weight: bold;
	margin-bottom: 15px;
	text-align: center;
	color: #303133;
}

.popup-form {
	margin-bottom: 15px;
}

.form-item {
	margin-bottom: 15px;
}

.form-label {
	font-size: 14px;
	color: #606266;
	margin-bottom: 5px;
}

.form-content {
	width: 100%;
}

.element-picker {
	width: 100%;
}

.popup-buttons {
	display: flex;
	justify-content: space-between;
	margin-top: 25px;
	gap: 15px;
}

.cancel-btn, .confirm-btn {
	flex: 1;
	height: 42px;
	line-height: 42px;
	padding: 0;
	border-radius: 21px;
	font-size: 15px;
	font-weight: 500;
	text-align: center;
	transition: all 0.3s ease;
	border: none;
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
	cursor: pointer;
}

.cancel-btn {
	background-color: #f8f9fa;
	color: #6c757d;
	border: 1px solid #e9ecef;
}

.cancel-btn:hover {
	background-color: #e9ecef;
	color: #495057;
	transform: translateY(-1px);
	box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.cancel-btn:active {
	background-color: #dee2e6;
	transform: translateY(0) scale(0.98);
	box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
}

.confirm-btn {
	background: linear-gradient(135deg, #2979FF 0%, #1565C0 100%);
	color: white;
	font-weight: 600;
}

.confirm-btn:hover {
	background: linear-gradient(135deg, #1976D2 0%, #0D47A1 100%);
	transform: translateY(-1px);
	box-shadow: 0 4px 16px rgba(41, 121, 255, 0.4);
}

.confirm-btn:active {
	background: linear-gradient(135deg, #1565C0 0%, #0D47A1 100%);
	transform: translateY(0) scale(0.98);
	box-shadow: 0 2px 8px rgba(41, 121, 255, 0.3);
}

/* 单选按钮样式 */
.radio-group {
	display: flex;
	align-items: center;
}

.radio {
	margin-right: 20px;
	font-size: 14px;
	color: #606266;
}
.tabs_container{
	width: 100%;
}
/* 客供选项样式 */
.radio-group radio-group {
	display: flex;
	flex-direction: row;
	align-items: center;
}

.radio-group label {
	display: flex;
	align-items: center;
	margin-right: 20px;
	padding: 5px 0;
}

.radio-group radio {
	transform: scale(0.9);
	margin-right: 4px;
}

.radio-group text {
	font-size: 14px;
	color: #606266;
	margin-left: 4px;
}
.confirm_btn_box{
	position: fixed;
	bottom: 0;
	left: 0;
	width: 100%;
	background-color: #fff;
	height: 60px;
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 0 20px;
	box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
	z-index: 100;
	box-sizing: border-box;
}

.confirm_btn_box button {
	width: 90%;
	height: 40px;
	line-height: 40px;
	border-radius: 4px;
	font-size: 16px;
	border-radius: 20px;
}

/* 返回顶部按钮样式 */
.back-to-top {
	position: fixed;
	right: 20px;
	bottom: 80px;
	width: 40px;
	height: 40px;
	background-color: rgba(41, 121, 255, 0.8);
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
	z-index: 999;
	transition: all 0.3s;
}

.back-to-top:active {
	transform: scale(0.95);
	background-color: rgba(41, 121, 255, 1);
}

/* 图片上传组件样式 */
.image-upload-container {
	width: 100%;
}

.image-content-wrapper {
	display: flex;
	align-items: flex-start;
	gap: 12px;
}

.image-preview-section {
	width: 120px;
	height: 120px;
	border: 1px dashed #dcdfe6;
	border-radius: 6px;
	display: flex;
	align-items: center;
	justify-content: center;
	overflow: hidden;
	background-color: #fafafa;
	flex-shrink: 0;
}

.preview-image {
	width: 100%;
	height: 100%;
	object-fit: cover;
	cursor: pointer;
	transition: transform 0.2s ease;
}

.preview-image:hover {
	transform: scale(1.02);
}

.preview-image:active {
	transform: scale(0.98);
}

.default-image-placeholder {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	width: 100%;
	height: 100%;
	cursor: pointer;
	transition: all 0.3s ease;
}

.default-image-placeholder:hover {
	border-color: #2979ff;
	background-color: #f0f7ff;
}

.placeholder-text {
	font-size: 12px;
	color: #ccc;
	margin-top: 5px;
}

.image-side-actions {
	display: flex;
	flex-direction: column;
	gap: 8px;
	margin-top: 8px;
}

.side-action-btn {
	width: 36px;
	height: 36px;
	border: 1px solid #dcdfe6;
	border-radius: 6px;
	display: flex;
	align-items: center;
	justify-content: center;
	cursor: pointer;
	transition: all 0.3s ease;
	background-color: #fff;
}

.side-action-btn:hover {
	border-color: #2979ff;
	background-color: #f0f7ff;
	transform: translateY(-1px);
	box-shadow: 0 2px 8px rgba(41, 121, 255, 0.2);
}

.side-action-btn:active {
	transform: translateY(0);
	box-shadow: 0 1px 4px rgba(41, 121, 255, 0.2);
}

.delete-action:hover {
	border-color: #f56c6c;
	background-color: #fef0f0;
	box-shadow: 0 2px 8px rgba(245, 108, 108, 0.2);
}

.upload-progress {
	margin-top: 10px;
	padding: 8px;
	background-color: #f5f5f5;
	border-radius: 4px;
}

.progress-text {
	font-size: 12px;
	color: #666;
	text-align: center;
	display: block;
	margin-top: 4px;
}
</style>
