import{_ as r}from"./CountTo.vue_vue_type_script_setup_true_lang-QUNNIqef.js";import{p as s}from"./index-Byekp3Iv.js";import{k as t,l as i,m,v as e,F as n,H as o}from"./form-create-B86qX0W_.js";const p={class:"flex flex-col gap-2 bg-[var(--el-bg-color-overlay)] p-6"},u={class:"flex items-center justify-between text-gray-500"},d={class:"flex flex-row items-baseline justify-between"},f=t({name:"ErpSummaryCard",__name:"SummaryCard",props:{title:s.string.def("").isRequired,value:s.number.def(0).isRequired},setup:a=>(c,v)=>{const l=r;return m(),i("div",p,[e("div",u,[e("span",null,n(a.title),1)]),e("div",d,[o(l,{prefix:"\uFFE5","end-val":a.value,decimals:2,duration:500,class:"text-3xl"},null,8,["end-val"])])])}});export{f as _};
