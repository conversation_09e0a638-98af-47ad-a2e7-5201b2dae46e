<template>
	<view class="formula-detail">
		<view v-if="formulaDetail">
			<uni-card :is-full="true" :is-shadow="true">
				<template #title>
					<view class="card-header">
						<text class="card-title">{{localFormulaForm.name}}信息</text>
					</view>
				</template>
				<uv-tabs :list='getActiveList()' @change="changeActiveTab" lineWidth="50"></uv-tabs>
				<uv-form label-position="top" v-if="activeTab === '基本信息'" >
					<uv-form-item label="配方名称" label-width="80" labelPosition="top">
						<view class="formulaName">
							<input type="text" v-model="localFormulaForm.name" :disabled="isAutoChangeName || isDisabled">
							<uv-text @click="customizeName" type="primary" :text="customizeButtonName" size="12" lines="1"></uv-text>
						</view>
					</uv-form-item>
					<uv-form-item label="产品形态" label-width="80" labelPosition="top">
						<view class="picker-view-container">
							<picker :range="product_state" @change="changeFormulaProductState" :disabled="isDisabled">
								<view class="picker-value">
									<text class="value-text">{{ selectedProductStateLabel || '请选择产品形态' }}</text>
									<text class="factory-arrow">▼</text>
								</view>
							</picker>
						</view>
					</uv-form-item>
					<uv-form-item label="通用名" label-width="60" labelPosition="top">
						<view class="picker-view-container">
							<picker :range="product_category" @change="changeFormulaCategory" :disabled="isDisabled">
								<view class="picker-value">
									<text class="value-text">{{ selectedCategoryLabel || '请选择通用名' }}</text>
									<text class="factory-arrow">▼</text>
								</view>
							</picker>
						</view>
					</uv-form-item>
					<uv-form-item label="企业" labelPosition="top">
						<view class="picker-view-container">
							<select-picker
								:options="companyList"
								:value="selectedCompany"
								:title="'选择企业'"
								:labelField="'name'"
								:valueField="'id'"
								:placeholder="'请选择企业'"
								:enableLoadMore="true"
								:pageSize="10"
								:disabled="isDisabled"
								@input="handleCompanySelect"
								@loadMore="loadMoreCompanies"
								@search="searchCompanies"
								@resetSearch="resetCompanySearch"
							></select-picker>
						</view>
					</uv-form-item>
					<uv-form-item label="氮磷钾含量" label-width="90" labelPosition="top">
						<view class="form-content">
							<view class="element-row">
								<view class="element-inline-group">
									<view class="unit-text element-label">N</view>
									<input 
										type="digit" 
										v-model="localFormulaForm.npk.N" 
										class="element-input" 
										placeholder="0"
										@input="() => updateFormulaName()"
										:disabled="isDisabled"
									/>
									<picker 
										:range="quality_unit" 
										@change="(e) => changeNpkUnit('n_unit', e)" 
										:value="getQuantityUnitIndex(localFormulaForm.npk.n_unit)"
										:disabled="isDisabled"
									>
										<view class="unit-text">
											{{ localFormulaForm.npk.n_unit || '选择单位' }} ▼
										</view>
									</picker>
								</view>
							</view>
							
							<view class="element-row">
								<view class="element-inline-group">
									<picker 
										:range="p_elements" 
										range-key="name"
										@change="changePType" 
										:value="getPTypeIndex(localFormulaForm.npk.PType)"
										:disabled="isDisabled"
									>
										<view class="unit-text">
											{{ getSelectedPTypeName() }} ▼
										</view>
									</picker>
									<input 
										type="digit" 
										v-model="localFormulaForm.npk.P" 
										class="element-input" 
										placeholder="0"
										@input="() => updateFormulaName()"
										:disabled="isDisabled"
									/>
									<picker 
										:range="quality_unit" 
										@change="(e) => changeNpkUnit('p_unit', e)" 
										:value="getQuantityUnitIndex(localFormulaForm.npk.p_unit)"
										:disabled="isDisabled"
									>
										<view class="unit-text">
											{{ localFormulaForm.npk.p_unit || '选择单位' }} ▼
										</view>
									</picker>
								</view>
							</view>
							
							<view class="element-row">
								<view class="element-inline-group">
									<view class="unit-text element-label">K</view>
									<input 
										type="digit" 
										v-model="localFormulaForm.npk.K" 
										class="element-input" 
										placeholder="0"
										@input="() => updateFormulaName()"
										:disabled="isDisabled"
									/>
									<picker 
										:range="quality_unit" 
										@change="(e) => changeNpkUnit('k_unit', e)" 
										:value="getQuantityUnitIndex(localFormulaForm.npk.k_unit)"
										:disabled="isDisabled"
									>
										<view class="unit-text">
											{{ localFormulaForm.npk.k_unit || '选择单位' }} ▼
										</view>
									</picker>
								</view>
							</view>
						</view>
					</uv-form-item>
					
					<!-- 中微量元素含量 -->
					<uv-form-item label="中微量元素" label-width="90" labelPosition="top">
						<view class="form-content">
							<view v-for="(item, index) in localFormulaForm.microElement" :key="index" class="element-row">
								<view class="element-inline-group">
									<picker 
										:range="mediumTraceElementOptions" 
										range-key="label"
										@change="(e) => changeMicroElement(index, e)" 
										:value="getMediumTraceElementIndex(item.element)"
										:disabled="isDisabled"
									>
										<view class="unit-text">
											{{ getMediumTraceElementLabel(item.element) || '选择元素' }} ▼
										</view>
									</picker>
									<input 
										type="digit" 
										v-model="item.quantity" 
										class="element-input" 
										placeholder="0"
										:disabled="isDisabled"
									/>
									<picker 
										:range="quality_unit" 
										@change="(e) => changeMicroElementUnit(index, e)" 
										:value="getQuantityUnitIndex(item.unit)"
										:disabled="isDisabled"
									>
										<view class="unit-text">
											{{ item.unit || '选择单位' }} ▼
										</view>
									</picker>
								</view>
								<text class="delete-icon" @click="deleteMicroElement(index)">×</text>
							</view>
							
							<view class="add-button-row">
								<text class="add-icon" @click="addMicroElement">+</text>
							</view>
						</view>
					</uv-form-item>
					
					<!-- 其他原料 -->
					<uv-form-item label="其他原料" label-width="90" labelPosition="top">
						<view class="form-content">
							<view v-for="(item, index) in localFormulaForm.otherMaterial" :key="index" class="element-row">
								<view class="element-inline-group">
									<picker 
										:range="productNameAbbrOptions" 
										range-key="label"
										@change="(e) => changeOtherMaterial(index, e)" 
										:value="getProductNameAbbrIndex(item.element)"
										:disabled="isDisabled"
									>
										<view class="unit-text">
											{{ getProductNameAbbrLabel(item.element) || '选择原料' }} ▼
										</view>
									</picker>
									<input 
										type="digit" 
										v-model="item.quantity" 
										class="element-input" 
										placeholder="0"
										:disabled="isDisabled"
									/>
									<picker 
										:range="quality_unit" 
										@change="(e) => changeOtherMaterialUnit(index, e)" 
										:value="getQuantityUnitIndex(item.unit)"
										:disabled="isDisabled"
									>
										<view class="unit-text">
											{{ item.unit || '选择单位' }} ▼
										</view>
									</picker>
								</view>
								<text class="delete-icon" @click="deleteOtherMaterial(index)">×</text>
							</view>
							
							<view class="add-button-row">
								<text class="add-icon" @click="addOtherMaterial">+</text>
							</view>
						</view>
					</uv-form-item>
					<uv-form-item label="工艺说明" label-width="80">
						<view class="form-content">
							<textarea 
								v-model="localFormulaForm.processDesc" 
								class="textarea-input" 
								placeholder="请输入工艺说明" 
								@input="updateFormField('processDesc', $event.detail.value)"
								:disabled="this.isDisabled"
							></textarea>
						</view>
					</uv-form-item>
					<uv-form-item label="备注">
						<view class="form-content">
							<textarea 
								v-model="localFormulaForm.remark" 
								class="textarea-input" 
								placeholder="请输入备注信息" 
								@input="updateFormField('remark', $event.detail.value)"
								:disabled="this.isDisabled"
							></textarea>
						</view>
					</uv-form-item>
				</uv-form>
				<view class="detail" v-if="activeTab === '配方明细' || activeTab === '包装材料'">
					<!-- 原料列表预览 - 仅在配方明细Tab显示 -->
					<view class="material-preview" v-if="activeTab === '配方明细'">
						<view class="preview-header">
							<text class="preview-title">原料列表</text>
						</view>
						
						<view class="material-list-preview">
							<uni-table border :loading="loading" emptyText="暂无原料数据" stripe>
								<uni-tr>
									<uni-th align="center" width="110">名称</uni-th>
									<uni-th align="center" width="60">用量</uni-th>
									<uni-th align="center" width="60">单价</uni-th>
									<uni-th align="center" width="70">投入成本</uni-th>
								</uni-tr>
								<uni-tr v-for="(item,index) in getMaterialItems()" :key="index">
									<uni-td align="center">
										<uv-text lines="1" :text="item.name">
										</uv-text>
									</uni-td>
									<uni-td align="center">{{item.quantity}}</uni-td>
									<uni-td align="center">{{item.price}}</uni-td>
									<uni-td align="center">{{item.cost}}</uni-td>
								</uni-tr>
							</uni-table>
						</view>
					</view>
					
					<!-- 包装材料预览 - 仅在包装材料Tab显示 -->
					<view class="material-preview" v-if="activeTab === '包装材料'">
						<view class="preview-header">
							<text class="preview-title">包装材料列表</text>
						</view>
						
						<view class="material-list-preview">
							<uni-table border :loading="loading" emptyText="暂无包装材料数据" stripe>
								<uni-tr>
									<uni-th align="center" width="100">名称</uni-th>
									<uni-th align="center" width="50">用量</uni-th>
									<uni-th align="center" width="60">单价</uni-th>
									<uni-th align="center" width="70">投入成本</uni-th>
								</uni-tr>
								<uni-tr v-for="(item,index) in getPackageItems()" :key="index">
									<uni-td align="center">
										<uv-text lines="1" :text="item.name"></uv-text>
									</uni-td>
									<uni-td align="center">{{item.quantity}}</uni-td>
									<uni-td align="center">{{item.price}}</uni-td>
									<uni-td align="center">{{item.cost}}</uni-td>
								</uni-tr>
							</uni-table>
						</view>
					</view>
					
					<!-- 根据Tab类型显示不同的按钮 -->
					<uv-text 
						v-if="activeTab === '配方明细'"
						type="success" 
						text="点击查看原料列表" 
						@click="viewMaterialDetail('material')"
					/>
					<uv-text 
						v-if="activeTab === '包装材料'"
						type="success" 
						text="点击查看包装材料列表" 
						@click="viewMaterialDetail('package')"
					/>
					
					<uv-divider text="其它信息"></uv-divider>
					<uv-form labelPosition="top" :model="localFormulaForm">
						<uv-form-item label="投入总份数" labelPosition="top" label-width="90">
							<view class="form-content">
								<input 
									type="text" 
									v-model="localFormulaForm.totalAmount" 
									placeholder="0" 
									disabled 
									class="standard-input"
								/>
							</view>
						</uv-form-item>
						
						<uv-form-item label="密度" labelPosition="top" label-width="90">
							<view class="form-content">
								<uv-number-box v-model="localFormulaForm.density" :min="0" :step="0.01" @change="updateDensity" :disabled="isDisabled"></uv-number-box>
							</view>
						</uv-form-item>
						
						<uv-form-item label="PH值" labelPosition="top" label-width="90">
							<view class="form-content">
								<input 
									type="digit" 
									v-model="localFormulaForm.ph" 
									placeholder="请输入PH值" 
									class="standard-input"
									@input="updateFormField('ph', $event.detail.value)"
									:disabled="isDisabled"
								/>
							</view>
						</uv-form-item>
						
						<uv-form-item label="EC" labelPosition="top" label-width="90">
							<view class="form-content">
								<input 
									type="digit" 
									v-model="localFormulaForm.ec" 
									placeholder="请输入EC值" 
									class="standard-input"
									@input="updateFormField('ec', $event.detail.value)"
									:disabled="isDisabled"
								/>
							</view>
						</uv-form-item>
						
						<uv-form-item label="水溶性" labelPosition="top" label-width="90">
							<view class="form-content">
								<input 
									type="text" 
									v-model="localFormulaForm.waterSoluble" 
									placeholder="请输入水溶性" 
									class="standard-input"
									@input="updateFormField('waterSoluble', $event.detail.value)"
									:disabled="isDisabled"
								/>
							</view>
						</uv-form-item>
						
						<uv-form-item label="外观" labelPosition="top" label-width="90">
							<view class="form-content">
								<input 
									type="text" 
									v-model="localFormulaForm.appearance" 
									placeholder="请输入外观" 
									class="standard-input"
									@input="updateFormField('appearance', $event.detail.value)"
									:disabled="isDisabled"
								/>
							</view>
						</uv-form-item>
						
						<uv-form-item label="元素含量" labelPosition="top" label-width="90">
							<view class="form-content">
								<view class="element-content-row" v-if="localFormulaForm.elements && Object.keys(localFormulaForm.elements).length > 0">
									<view class="element-content-item" v-for="(value, key) in localFormulaForm.elements" :key="key">
										<text class="element-name">{{key}}:</text>
										<text class="element-value">{{value}}</text>
										<text class="element-unit">%</text>
										<text class="separator" v-if="Object.keys(localFormulaForm.elements).indexOf(key) < Object.keys(localFormulaForm.elements).length - 1">|</text>
									</view>
								</view>
								<view v-else class="no-material-text">
									暂无元素含量数据
								</view>
							</view>
						</uv-form-item>
						
						<uv-form-item label="总成本" labelPosition="top" label-width="90">
							<view class="form-content">
								<view class="input-with-suffix">
									<input 
										type="digit" 
										v-model="localFormulaForm.totalCost" 
										placeholder="0" 
										disabled 
										class="standard-input"
									/>
									<text class="input-suffix">元</text>
								</view>
							</view>
						</uv-form-item>
						
						<uv-form-item label="总原料成本" labelPosition="top" label-width="90">
							<view class="form-content">
								<view class="input-with-suffix">
									<input 
										type="digit" 
										v-model="localFormulaForm.totalRawCost" 
										disabled 
										class="standard-input"
									/>
									<text class="input-suffix">元</text>
								</view>
							</view>
						</uv-form-item>
						
						<!-- 只在报价页面显示包装成本和封装成本 -->
						<template v-if="nowPage !== 'formula'">
							<uv-form-item label="包装成本" labelPosition="top" label-width="90">
								<view class="form-content">
									<view class="input-with-suffix">
										<input 
											type="digit" 
											v-model="localFormulaForm.totalAuxiliaryCost" 
											placeholder="请输入包装成本"
											class="standard-input"
											@input="updateFormField('totalAuxiliaryCost', $event.detail.value)"
											:disabled="isDisabled"
										/>
										<text class="input-suffix">元</text>
									</view>
								</view>
							</uv-form-item>
							
							<uv-form-item label="封装成本" labelPosition="top" label-width="90">
								<view class="form-content">
									<view class="input-with-suffix">
										<input 
											type="digit" 
											v-model="localFormulaForm.totalPackageCost" 
											placeholder="请输入封装成本"
											class="standard-input"
											@input="updateFormField('totalPackageCost', $event.detail.value)"
											:disabled="isDisabled"
										/>
										<text class="input-suffix">元</text>
									</view>
								</view>
							</uv-form-item>
						</template>
						
						<uv-form-item label="工艺说明" label-width="80">
							<view class="form-content">
								<textarea 
									v-model="localFormulaForm.processDesc" 
									class="textarea-input" 
									placeholder="请输入工艺说明" 
									@input="updateFormField('processDesc', $event.detail.value)"
								></textarea>
							</view>
						</uv-form-item>
						<uv-form-item label="备注">
							<view class="form-content">
								<textarea 
									v-model="localFormulaForm.remark" 
									class="textarea-input" 
									placeholder="请输入备注信息" 
									@input="updateFormField('remark', $event.detail.value)"
								></textarea>
							</view>
						</uv-form-item>
					</uv-form>
				</view>
			</uni-card>
		</view>
		<view v-else class="loading-container">
			<view class="loading-content">
				<uni-icons type="spinner-cycle" size="32" color="#2979ff" class="loading-icon"></uni-icons>
				<text class="loading-text">加载配方信息中...</text>
			</view>
		</view>
	</view>
</template>

<script>
import { getDictOptions, getDictLabel, getDictIndex, getBatchDictOptions, initSelectorDisplay } from '../../../../../../utils/dict';
import { getCompanyPageApi } from '../../../../../../api/scm/base/company';
import SelectPicker from '../../../../../../components/SelectPicker/SelectPicker.vue';

	export default {
		components: {
			SelectPicker
		},
		data(){
			// 默认NPK结构
			const defaultNpk = {
				N: null, P: null, K: null,
				n_unit: null, p_unit: null, k_unit: null,
				PType: null
			};
			
			return {
				// 本地表单数据，避免直接修改props
				localFormulaForm: {
					name: '',
					stateCode: null,
					typeCode: null,
					companyName: null,
					npk: {...defaultNpk},
					microElement: [],
					otherMaterial: [],
					materials: [],
					packages: [] // 增加packages数组初始化
				}, 
				customizeButtonName:'自定义名称',
				isAutoChangeName: true,
				activeList:[
					{ name:'基本信息' },
					{ name:'配方明细' },
					{ name:'包装材料' }
				],
				activeTab:'基本信息',
				// 产品形态相关
				product_state: [],
				productStateOptions: [], 
				selectedProductStateLabel: '', 
				// 通用名相关
				product_category: [],
				productCategoryOptions: [], 
				selectedCategoryLabel: '', 
				// 企业相关
				companyList: [], 
				selectedCompany: null, 
				companyParams: { 
					pageNo: 1,
					pageSize: 10,
					name: '',
				},
				p_elements:[
					{ code: "P", name: "磷", value: "P" },
					{ code: "KDP", name: "磷酸二氢钾", value: "KDP" },
					{ code: "BP", name: "膨化磷酸二氢钾", value: "BP" },
					{ code: "NOP", name: "硝酸钾", value: "NOP" },
				],
				// 单位相关
				quality_unit: [], 
				quantityUnitOptions: [],
				// 中微量元素相关
				medium_trace_element: [],
				mediumTraceElementOptions: [],
				// 其他原料相关
				product_name_abbr: [],
				productNameAbbrOptions: [],
				defaultNpk, // 保存默认NPK结构以便重用
				hasMaterialData: false,
				previewMaterials: [],
			}
		},
		props:{
			formulaId:{
				type:Number,
				required:false
			},
			isApply:{
				type:Boolean,
				required:false,
				default: false
			},
			formulaDetail:{
				type:Object,
				required:false
			},
			isAdd: {
				type: Boolean,
				required: false,
				default: false
			},
			nowPage: {
				type: String,
				required: false,
				default: 'quote' // 默认为报价页面，配方页面传 'formula'
			},
			isDisabled:{
				type:Boolean,
				default:false
			}
		},
		watch: {
			formulaDetail: {
				handler(newVal) {
					if (newVal) {
						this.initFormulaForm();
					}
				},
				deep: true,
				immediate: true
			},
			// 简化的NPK值监听，统一处理
			'localFormulaForm.npk': {
				handler() {
					if (this.isAutoChangeName) {
						this.updateFormulaName();
					}
				},
				deep: true
			},
			// 监听原料数据变化
			'localFormulaForm.materials': {
				handler() {
					this.updateMaterialsDisplay();
				},
				deep: true
			},
			// 监听密度变化，重新计算元素含量
			'localFormulaForm.density': {
				handler(newValue, oldValue) {
					if (newValue !== oldValue) {
						this.recalculateElementsAfterDensityChange();
					}
				}
			},
			// 监听封装成本变化，更新总成本
			'localFormulaForm.totalPackageCost': {
				handler() {
					this.calculateTotalCost();
				}
			},
			// 监听包装成本变化，更新总成本
			'localFormulaForm.totalAuxiliaryCost': {
				handler() {
					this.calculateTotalCost();
				}
			}
		},
		methods:{
			// 获取当前应该显示的tab列表
			getActiveList() {
				// 如果是配方页面，只显示"基本信息"和"配方明细"两个tab
				if (this.nowPage === 'formula') {
					return this.activeList.filter(tab => tab.name !== '包装材料');
				}
				// 否则显示全部tab
				return this.activeList;
			},
			
			// 初始化表单数据
			initFormulaForm() {
				if (this.formulaDetail) {
					// 深拷贝，避免直接修改props
					this.localFormulaForm = JSON.parse(JSON.stringify(this.formulaDetail));

					// 确保关键数组属性存在
					if (!this.localFormulaForm.microElement) {
						this.localFormulaForm.microElement = [];
					}
					if (!this.localFormulaForm.otherMaterial) {
						this.localFormulaForm.otherMaterial = [];
					}
					if (!this.localFormulaForm.materials) {
						this.localFormulaForm.materials = [];
					}
					if (!this.localFormulaForm.packages) {
						this.localFormulaForm.packages = [];
					}

					// 确保NPK属性存在
					if (!this.localFormulaForm.npk) {
						this.localFormulaForm.npk = {
							N: '',
							P: '',
							K: '',
							n_unit: 'g/kg',
							p_unit: 'g/kg',
							k_unit: 'g/kg',
							PType: 'P'
						};
					}

					// 确保文本字段不为null，防止textarea组件报错
					this.localFormulaForm.processDesc = this.localFormulaForm.processDesc || '';
					this.localFormulaForm.remark = this.localFormulaForm.remark || '';
					this.localFormulaForm.ph = this.localFormulaForm.ph || '';
					this.localFormulaForm.ec = this.localFormulaForm.ec || '';
					this.localFormulaForm.waterSoluble = this.localFormulaForm.waterSoluble || '';
					this.localFormulaForm.appearance = this.localFormulaForm.appearance || '';
					this.localFormulaForm.name = this.localFormulaForm.name || '';
					this.localFormulaForm.companyName = this.localFormulaForm.companyName || '';

					// 确保数值字段有默认值
					this.localFormulaForm.density = this.localFormulaForm.density || 1;
					this.localFormulaForm.totalAmount = this.localFormulaForm.totalAmount || 0;
					this.localFormulaForm.totalCost = this.localFormulaForm.totalCost || 0;
					this.localFormulaForm.totalRawCost = this.localFormulaForm.totalRawCost || 0;
					this.localFormulaForm.totalAuxiliaryCost = this.localFormulaForm.totalAuxiliaryCost || 0;
					this.localFormulaForm.totalPackageCost = this.localFormulaForm.totalPackageCost || 0;

					// 确保elements对象存在
					if (!this.localFormulaForm.elements) {
						this.localFormulaForm.elements = {};
					}
				} else {
					// 如果没有传入formulaDetail，则使用默认值
					this.localFormulaForm = {
						npk: {
							N: '',
							P: '',
							K: '',
							n_unit: 'g/kg',
							p_unit: 'g/kg',
							k_unit: 'g/kg',
							PType: 'P'
						},
						microElement: [],
						otherMaterial: [],
						materials: [],
						packages: [],
						name: '',
						density: 1,
						stateCode: null,
						typeCode: null,
						companyName: null,
						processDesc: '',
						remark: '',
						ph: '',
						ec: '',
						waterSoluble: '',
						appearance: '',
						totalAmount: 0,
						totalCost: 0,
						totalRawCost: 0,
						totalAuxiliaryCost: 0,
						totalPackageCost: 0,
						elements: {}
					};
				}
				
				// 更新选择器显示
				this.updateSelectorsDisplay();
				
				// 处理材料数据显示
				this.updateMaterialsDisplay();
			},
			
			// 更新选择器显示
			updateSelectorsDisplay() {
				// 更新产品形态选择器显示
				if (this.localFormulaForm.stateCode) {
					this.selectedProductStateLabel = getDictLabel(this.productStateOptions, this.localFormulaForm.stateCode);
				}
				
				// 更新通用名选择器显示
				if (this.localFormulaForm.typeCode) {
					this.selectedCategoryLabel = getDictLabel(this.productCategoryOptions, this.localFormulaForm.typeCode);
				}
				
				// 更新企业选择器显示
				if (this.localFormulaForm.companyName) {
					this.selectedCompany = { name: this.localFormulaForm.companyName };
				}
			},
			
			// 切换自定义/自动名称
			customizeName(){
				this.isAutoChangeName = !this.isAutoChangeName;
				this.customizeButtonName = this.isAutoChangeName ? '自定义名称' : '自动生成';
			},
			
			// 切换活动标签
			changeActiveTab(e){
				this.activeTab = e.name
			},
			
			// 改变产品形态
			changeFormulaProductState(e){
				const index = e.detail.value;
				if (index >= 0 && index < this.product_state.length) {
					this.selectedProductStateLabel = this.product_state[index];
					
					if (this.productStateOptions && this.productStateOptions[index]) {
						this.localFormulaForm.stateCode = this.productStateOptions[index].value;
						this.updateFormulaName();
					}
				}
			},
			
			// 改变产品通用名
			changeFormulaCategory(e){
				const index = e.detail.value;
				if (index >= 0 && index < this.product_category.length) {
					this.selectedCategoryLabel = this.product_category[index];
					
					if (this.productCategoryOptions && this.productCategoryOptions[index]) {
						this.localFormulaForm.typeCode = this.productCategoryOptions[index].value;
						this.updateFormulaName();
					}
				}
			},
			
			// 统一的NPK输入处理方法
			handleNpkInput(field, val) {
				// 设置对应字段的值
				if (this.localFormulaForm.npk) {
					// 更新对应字段的值
					this.$set(this.localFormulaForm.npk, field, val);
					
					// 如果是自动生成名称，更新配方名称
					if (this.isAutoChangeName) {
						this.updateFormulaName();
					}
				}
			},
			
			// 配方名称更新
			updateFormulaName() {
				if (!this.isAutoChangeName) return;
				
				const npk = this.localFormulaForm.npk || {};
				const n = npk.N ? `-${npk.N}` : '';
				const p = npk.P ? `-${npk.P}` : '';
				const k = npk.K ? `-${npk.K}` : '';
				
				this.localFormulaForm.name = 
					(this.localFormulaForm.stateCode || '') + 
					(this.localFormulaForm.typeCode || '') +
					(this.localFormulaForm.companyName ? `-${this.localFormulaForm.companyName}` : `-X`) +
					n + p + k;
				
				// 通知父组件配方数据已变更
				this.$emit('update:formulaData', this.localFormulaForm);
			},
			
			// 处理NPK单位变更
			changeNpkUnit(unitField, e) {
				const index = parseInt(e.detail.value, 10);
				if (index >= 0 && index < this.quality_unit.length) {
					// 更新对应的单位
					if (this.localFormulaForm.npk) {
						this.localFormulaForm.npk[unitField] = this.quality_unit[index];
					}
					
					// 更新配方名称
					this.updateFormulaName();
				}
			},
			
			// 获取量单位的索引
			getQuantityUnitIndex(unit) {
				return this.quality_unit.findIndex(item => item === unit);
			},
			
			// 改变磷类型
			changePType(e) {
				const index = e.detail.value;
				if (index >= 0 && index < this.p_elements.length) {
					this.localFormulaForm.npk.PType = this.p_elements[index].value;
					this.updateFormulaName();
				}
			},
			
			// 获取磷类型的索引
			getPTypeIndex(pType) {
				return this.p_elements.findIndex(item => item.value === pType);
			},
			
			// 获取选中的磷类型名称
			getSelectedPTypeName() {
				if (this.localFormulaForm.npk && this.localFormulaForm.npk.PType) {
					const pType = this.p_elements.find(item => item.value === this.localFormulaForm.npk.PType);
					return pType ? pType.name : '磷酸二氢钾';
				}
				return '磷酸二氢钾';
			},
			
			// 处理企业选择
			handleCompanySelect(company) {
				// 保存完整的公司对象，包含id和code
				this.selectedCompany = company;
				
				// 表单中只保存公司名称
				this.localFormulaForm.companyName = company.name;
				
				// 自动更新配方名称
				this.updateFormulaName();
			},
			
			// 企业相关方法 - 加载更多
			loadMoreCompanies(params) {
				const { page, pageSize, callback } = params;
				this.updateCompanyParams(page, pageSize);
				this.fetchCompanies(callback, false);
			},
			
			// 企业相关方法 - 搜索
			searchCompanies(params) {
				const { keyword, page, pageSize, callback } = params;
				this.updateCompanyParams(page, pageSize, keyword);
				this.fetchCompanies(callback, page === 1);
			},
			
			// 企业相关方法 - 重置搜索
			resetCompanySearch() {
				this.updateCompanyParams(1, this.companyParams.pageSize, '');
				this.fetchCompanies();
			},
			
			// 企业相关方法 - 更新参数
			updateCompanyParams(page, pageSize, keyword = this.companyParams.name) {
				this.companyParams = {
					...this.companyParams,
					pageNo: page,
					pageSize: pageSize || this.companyParams.pageSize,
					name: keyword
				};
			},
			
			// 获取企业数据
			async fetchCompanies(callback, isReset = true) {
				try {
					const response = await getCompanyPageApi(this.companyParams);
					
					if (response && response.code === 0 && response.data) {
						// 根据页码决定是替换还是追加数据
						if (isReset || this.companyParams.pageNo === 1) {
							this.companyList = response.data.list || [];
						} else {
							this.companyList = [...this.companyList, ...(response.data.list || [])];
						}
						
						// 处理回调
						if (callback) {
							const total = response.data.total || 0;
							const hasMore = this.companyList.length < total;
							
							callback({
								data: response.data.list || [],
								total: total,
								hasMore: hasMore
							});
						}
					} else if (callback) {
						callback({ data: [], total: 0, hasMore: false });
					}
				} catch (error) {
					if (callback) {
						callback({ data: [], total: 0, hasMore: false });
					}
				}
			},
			
			// 初始化企业列表
			initCompanyList() {
				this.updateCompanyParams(1, this.companyParams.pageSize, '');
				this.fetchCompanies();
			},
			
			// 手动触发配方名称更新
			changeFormulaName(){
				this.updateFormulaName();
			},
			
			// 获取表单数据方法，供父组件调用
			getFormData() {
				// 确保总成本计算最新
				this.calculateTotalCost();
				
				// 确保所有表单字段都包含在返回的数据中
				const formData = {
					...JSON.parse(JSON.stringify(this.localFormulaForm)),
					
					// 显式设置字段，确保它们被包含
					id: this.localFormulaForm.id,
					name: this.localFormulaForm.name || '',
					companyName: this.localFormulaForm.companyName || '',
					stateCode: this.localFormulaForm.stateCode || '',
					typeCode: this.localFormulaForm.typeCode || '',
					
					// 确保以下字段被包含
					ph: this.localFormulaForm.ph || '',
					ec: this.localFormulaForm.ec || '',
					waterSoluble: this.localFormulaForm.waterSoluble || '', 
					appearance: this.localFormulaForm.appearance || '',
					
					// 确保成本字段被包含
					totalAmount: this.localFormulaForm.totalAmount || 0,
					totalCost: this.localFormulaForm.totalCost || 0,
					totalRawCost: this.localFormulaForm.totalRawCost || 0,
					totalAuxiliaryCost: this.localFormulaForm.totalAuxiliaryCost || 0,
					totalPackageCost: this.localFormulaForm.totalPackageCost || 0,
					
					// 其他可能缺失的字段
					density: this.localFormulaForm.density || 1,
					processDesc: this.localFormulaForm.processDesc || '',
					remark: this.localFormulaForm.remark || '',
					
					// 确保NPK信息被包含且有默认值
					npk: {
						N: '',
						P: '',
						K: '',
						n_unit: 'g/kg',
						p_unit: 'g/kg',
						k_unit: 'g/kg',
						PType: 'P',
						...(this.localFormulaForm.npk || {})
					},
					
					// 确保材料信息被包含
					materials: Array.isArray(this.localFormulaForm.materials) ? this.localFormulaForm.materials : [],
					microElement: Array.isArray(this.localFormulaForm.microElement) ? this.localFormulaForm.microElement : [],
					otherMaterial: Array.isArray(this.localFormulaForm.otherMaterial) ? this.localFormulaForm.otherMaterial : [],
					packages: Array.isArray(this.localFormulaForm.packages) ? this.localFormulaForm.packages : []
				};
				
				// 确保materials和packages中的项目都有必要的字段
				if (formData.materials) {
					formData.materials = formData.materials.map(item => ({
						name: '',
						quantity: 0,
						price: 0,
						cost: 0,
						...(item || {})
					}));
				}
				
				if (formData.packages) {
					formData.packages = formData.packages.map(item => ({
						name: '',
						quantity: 0,
						price: 0,
						cost: 0,
						...(item || {})
					}));
				}
				
				// 确保microElement和otherMaterial中的项目都有必要的字段
				if (formData.microElement) {
					formData.microElement = formData.microElement.map(item => ({
						element: '',
						quantity: 0,
						unit: 'g/kg',
						...(item || {})
					}));
				}
				
				if (formData.otherMaterial) {
					formData.otherMaterial = formData.otherMaterial.map(item => ({
						element: '',
						quantity: 0,
						unit: 'g/kg',
						...(item || {})
					}));
				}
				
				return formData;
			},
			
			// 初始化字典数据
			async initDictData() {
				try {
					// 批量获取字典数据
					const dictData = await getBatchDictOptions([
						'product_state',
						'product_category',
						'quality_unit',
						'medium_trace_element',
						'product_name_abbr'
					]);
					
					// 处理产品形态
					if (dictData.product_state && Array.isArray(dictData.product_state)) {
						this.productStateOptions = [...dictData.product_state];
						this.product_state = dictData.product_state.map(item => item.label);
					}
					
					// 处理通用名
					if (dictData.product_category && Array.isArray(dictData.product_category)) {
						this.productCategoryOptions = [...dictData.product_category];
						this.product_category = dictData.product_category.map(item => item.label);
					}
					
					// 处理单位
					if (dictData.quality_unit && Array.isArray(dictData.quality_unit) && dictData.quality_unit.length > 0) {
						this.quantityUnitOptions = [...dictData.quality_unit];
						this.quality_unit = dictData.quality_unit.map(item => item.value);
						
						// 设置NPK单位默认值
						this.setDefaultNpkUnits();
					} else {
						this.quantityUnitOptions = [];
						this.quality_unit = [];
					}
					
					// 处理中微量元素
					if (dictData.medium_trace_element && Array.isArray(dictData.medium_trace_element)) {
						this.mediumTraceElementOptions = [...dictData.medium_trace_element];
						// 只保存value的数组，用于兼容旧代码
						this.medium_trace_element = dictData.medium_trace_element.map(item => item.value);
					}
					
					// 处理其他原料
					if (dictData.product_name_abbr && Array.isArray(dictData.product_name_abbr)) {
						this.productNameAbbrOptions = [...dictData.product_name_abbr];
						// 只保存value的数组，用于兼容旧代码
						this.product_name_abbr = dictData.product_name_abbr.map(item => item.value);
					}
					
					// 更新选择器显示
					this.updateSelectorsDisplay();
				} catch (error) {
					this.quantityUnitOptions = [];
					this.quality_unit = [];
				}
			},
			
			// 设置NPK单位默认值
			setDefaultNpkUnits() {
				if (this.localFormulaForm && this.localFormulaForm.npk && this.quality_unit.length > 0) {
					const npk = this.localFormulaForm.npk;
					const defaultUnit = this.quality_unit[0];
					
					// 为所有null单位设置默认值
					['n_unit', 'p_unit', 'k_unit'].forEach(field => {
						if (npk[field] === null) {
							this.$set(npk, field, defaultUnit);
						}
					});
				}
			},
			
			// 添加中微量元素
			addMicroElement() {
				if(this.isDisabled) return
				// 获取默认单位
				const defaultUnit = this.quality_unit.length > 0 ? this.quality_unit[0] : null;
				let defaultElement = null;
				
				// 从选项中获取第一个元素的value作为默认值
				if (this.mediumTraceElementOptions.length > 0) {
					defaultElement = this.mediumTraceElementOptions[0].value;
				}
				
				this.localFormulaForm.microElement.push({
					element: defaultElement,
					quantity: null,
					unit: defaultUnit
				});
			},
			
			// 删除中微量元素
			deleteMicroElement(index) {
				if(this.isDisabled) return
				this.localFormulaForm.microElement.splice(index, 1);
			},
			
			// 改变中微量元素
			changeMicroElement(index, e) {
				const selectedIndex = e.detail.value;
				if (selectedIndex >= 0 && selectedIndex < this.mediumTraceElementOptions.length) {
					const selectedOption = this.mediumTraceElementOptions[selectedIndex];
					// 存储value值到表单
					this.localFormulaForm.microElement[index].element = selectedOption.value;
				}
			},
			
			// 改变中微量元素单位
			changeMicroElementUnit(index, e) {
				const selectedIndex = e.detail.value;
				if (selectedIndex >= 0 && selectedIndex < this.quality_unit.length) {
					const unit = this.quality_unit[selectedIndex];
					this.localFormulaForm.microElement[index].unit = unit;
				}
			},
			
			// 获取中微量元素索引
			getMediumTraceElementIndex(value) {
				return getDictIndex(this.mediumTraceElementOptions, value);
			},
			
			// 获取其他原料索引
			getProductNameAbbrIndex(value) {
				return getDictIndex(this.productNameAbbrOptions, value);
			},
			
			// 添加其他原料
			addOtherMaterial() {
				if(this.isDisabled) return
				// 确保otherMaterial是一个数组
				if(!this.localFormulaForm.otherMaterial || !Array.isArray(this.localFormulaForm.otherMaterial)) {
					this.localFormulaForm.otherMaterial = [];
				}
				
				// 获取默认单位
				const defaultUnit = this.quality_unit.length > 0 ? this.quality_unit[0] : null;
				let defaultElement = null;
				
				// 从选项中获取第一个元素的value作为默认值
				if (this.productNameAbbrOptions.length > 0) {
					defaultElement = this.productNameAbbrOptions[0].value;
				}
				
				this.localFormulaForm.otherMaterial.push({
					element: defaultElement,
					quantity: null,
					unit: defaultUnit
				});
			},
			
			// 删除其他原料
			deleteOtherMaterial(index) {
				if(this.isDisabled) return
				// 确保otherMaterial是一个数组
				if(!this.localFormulaForm.otherMaterial || !Array.isArray(this.localFormulaForm.otherMaterial)) {
					this.localFormulaForm.otherMaterial = [];
					return;
				}
				
				// 确保索引有效
				if(index >= 0 && index < this.localFormulaForm.otherMaterial.length) {
					this.localFormulaForm.otherMaterial.splice(index, 1);
				}
			},
			
			// 改变其他原料
			changeOtherMaterial(index, e) {
				// 确保otherMaterial是一个数组
				if(!this.localFormulaForm.otherMaterial || !Array.isArray(this.localFormulaForm.otherMaterial)) {
					this.localFormulaForm.otherMaterial = [];
					return;
				}
				
				// 确保索引有效
				if(index < 0 || index >= this.localFormulaForm.otherMaterial.length) {
					return;
				}
				
				const selectedIndex = e.detail.value;
				if (selectedIndex >= 0 && selectedIndex < this.productNameAbbrOptions.length) {
					const selectedOption = this.productNameAbbrOptions[selectedIndex];
					// 存储value值到表单
					this.localFormulaForm.otherMaterial[index].element = selectedOption.value;
				}
			},
			
			// 改变其他原料单位
			changeOtherMaterialUnit(index, e) {
				// 确保otherMaterial是一个数组
				if(!this.localFormulaForm.otherMaterial || !Array.isArray(this.localFormulaForm.otherMaterial)) {
					this.localFormulaForm.otherMaterial = [];
					return;
				}
				
				// 确保索引有效
				if(index < 0 || index >= this.localFormulaForm.otherMaterial.length) {
					return;
				}
				
				const selectedIndex = e.detail.value;
				if (selectedIndex >= 0 && selectedIndex < this.quality_unit.length) {
					const unit = this.quality_unit[selectedIndex];
					this.localFormulaForm.otherMaterial[index].unit = unit;
				}
			},
			
			// 获取中微量元素标签
			getMediumTraceElementLabel(element) {
				return getDictLabel(this.mediumTraceElementOptions, element);
			},
			
			// 获取其他原料标签
			getProductNameAbbrLabel(element) {
				return getDictLabel(this.productNameAbbrOptions, element);
			},
			
			// 查看原料明细表格
			viewMaterialDetail(type = 'material') {
				try {
					// 检查包装材料或原料数据类型是否正确
					let materialData = [];
					let title = '';
					let detailType = '';
					let formDataField = '';
					
					if (type === 'material') {
						materialData = this.localFormulaForm.materials || []; // 即使没有数据也初始化为空数组
						title = '原料明细';
						detailType = 'material';
						formDataField = 'materials';
					} else if (type === 'package') {
						materialData = this.localFormulaForm.packages || []; // 即使没有数据也初始化为空数组
						title = '包装材料明细';
						detailType = 'package';
						formDataField = 'packages';
					}
					
					// 准备传递给MaterialDetail页面的数据
					const formulaData = {
						materials: JSON.parse(JSON.stringify(materialData)), // 确保materials始终存在
						density: this.localFormulaForm.density || 1,
						name: this.localFormulaForm.name || '配方',
						detailType: detailType, // 标记是原料还是包装材料
						materialType: type === 'package' ? 4 : 1, // 原料类型代码，1为原料，4为包装材料
						amountLabel: type === 'package' ? '数量' : '投入份数', // 数量的标签区分
						unitDefault: type === 'package' ? '个' : '份', // 默认单位区分
						isDisabled: this.isDisabled // 传递isDisabled状态
					};
					
					// 导航到明细页面并传递数据
					uni.navigateTo({
						url: '/pages/biz/scm/rd/formula/FormulaDetail/MaterialDetail',
						success: (res) => {
							// 通过eventChannel向被打开页面传送数据
							res.eventChannel.emit('formulaData', formulaData);
							
							// 监听子页面返回的数据
							res.eventChannel.on('updateFormulaMaterials', (data) => {
								
								// 更新配方表单中的数据
								if (data.materials && Array.isArray(data.materials)) {
									this.localFormulaForm[formDataField] = data.materials;
								}
								
								// 根据不同类型处理返回数据
								if (type === 'material') {
									// 更新配方表单中的总份数和总成本
									if (data.totalAmount !== undefined) {
										this.localFormulaForm.totalAmount = data.totalAmount;
									}
									
									if (data.totalCost !== undefined) {
										this.localFormulaForm.totalCost = data.totalCost;
										// 同时更新总原料成本
										this.localFormulaForm.totalRawCost = data.totalCost;
									}
									
									// 如果有元素汇总数据，也进行更新
									if (data.summarizedElements) {
										this.localFormulaForm.elements = data.summarizedElements;
									}
								} else if (type === 'package') {
									// 更新配方表单中的包装成本
									if (data.totalCost !== undefined) {
										this.localFormulaForm.totalAuxiliaryCost = data.totalCost;
									}
								}
								
								// 重新计算总成本 (原料成本 + 包装成本 + 封装成本)
								this.calculateTotalCost();
								
								// 更新预览数据
								this.updateMaterialsDisplay();
								
								// 通知父组件数据已更新
								this.$emit('formula-updated', this.localFormulaForm);
							});
						},
						fail: (err) => {
							uni.showToast({
								title: '页面跳转失败',
								icon: 'none',
								duration: 2000
							});
						}
					});
				} catch (error) {
					uni.showToast({
						title: '操作失败，请重试',
						icon: 'none',
						duration: 2000
					});
				}
			},
			
			// 获取包装材料列表项目
			getPackageItems() {
				// 从packages字段获取包装材料列表
				if (this.localFormulaForm && this.localFormulaForm.packages && Array.isArray(this.localFormulaForm.packages) && this.localFormulaForm.packages.length > 0) {
					return this.localFormulaForm.packages.map(item => ({
						name: item.materialName || '',
						quantity: item.amount || 0,
						cost: item.cost,
						price:item.price
					}));
				}
				return [];
			},
			
			// 检查是否有包装材料项目
			hasPackageItems() {
				return this.localFormulaForm.packages && 
					   Array.isArray(this.localFormulaForm.packages) && 
					   this.localFormulaForm.packages.length > 0 &&
					   this.localFormulaForm.packages.some(item => item.materialName);
			},
			
			// 获取总份数的单位
			getTotalUnit() {
				// 如果有原料，取第一个原料的单位作为默认单位
				if (this.localFormulaForm.materials && this.localFormulaForm.materials.length > 0) {
					const firstMaterial = this.localFormulaForm.materials[0];
					return firstMaterial.unit || 'kg';
				}
				return 'kg';
			},
			
			
			// 获取原料列表项目
			getMaterialItems() {
				// 直接从materials字段获取原料列表
				if (this.localFormulaForm && this.localFormulaForm.materials && Array.isArray(this.localFormulaForm.materials) && this.localFormulaForm.materials.length > 0) {
					return this.localFormulaForm.materials.map(item => ({
						name: item.materialName || '',
						quantity: item.amount || 0,
						cost: item.cost,
						price:item.price
					}));
				}
				return [];
			},
			
			// 检查是否有原料项目
			hasMaterialItems() {
				return this.localFormulaForm.materials && 
					   Array.isArray(this.localFormulaForm.materials) && 
					   this.localFormulaForm.materials.length > 0;
			},
			
			// 增加密度值
			increaseDensity() {
				if (!this.localFormulaForm.density) {
					this.localFormulaForm.density = 1;
				} else {
					// 转为数字并增加0.01，保留两位小数
					const density = parseFloat(this.localFormulaForm.density);
					this.localFormulaForm.density = (density + 0.01).toFixed(2);
				}
				
				// 重新计算元素含量
				this.recalculateElementsAfterDensityChange();
				
				// 通知父组件数据已更新
				this.$emit('formula-updated', this.localFormulaForm);
			},
			
			// 减少密度值
			decreaseDensity() {
				if (!this.localFormulaForm.density) {
					this.localFormulaForm.density = 1;
				} else {
					// 转为数字并减少0.01，但不低于0，保留两位小数
					const density = parseFloat(this.localFormulaForm.density);
					this.localFormulaForm.density = Math.max(0, density - 0.01).toFixed(2);
				}
				
				// 重新计算元素含量
				this.recalculateElementsAfterDensityChange();
				
				// 通知父组件数据已更新
				this.$emit('formula-updated', this.localFormulaForm);
			},
			
			// 处理密度值变化
			updateDensity(value) {
				// 确保密度值合法
				const density = parseFloat(value);
				if (isNaN(density) || density < 0) {
					this.localFormulaForm.density = 1;
				} else {
					this.localFormulaForm.density = parseFloat(density).toFixed(2);
				}
				
				// 如果存在元素含量数据，需要重新计算元素含量百分比
				this.recalculateElementsAfterDensityChange();
				
				// 通知父组件数据已更新
				this.$emit('formula-updated', this.localFormulaForm);
			},
			
			// 添加新方法：密度变化后重新计算元素含量
			recalculateElementsAfterDensityChange() {
				// 确保存在元素数据
				if (!this.localFormulaForm.elements || typeof this.localFormulaForm.elements !== 'object') {
					return;
				}
				
				// 仅在存在原料数据且总份数大于0的情况下执行计算
				if (!this.localFormulaForm.materials || 
					!Array.isArray(this.localFormulaForm.materials) || 
					this.localFormulaForm.materials.length === 0 || 
					!this.localFormulaForm.totalAmount || 
					this.localFormulaForm.totalAmount <= 0) {
					return;
				}
				
				// 获取新密度值
				const newDensity = parseFloat(this.localFormulaForm.density) || 1;
				
				// 计算每种元素在原料中的总量
				const elementTotals = {};
				const defaultRatio = 1000; // 与add.vue中保持一致的默认比率
				
				// 遍历所有原料
				this.localFormulaForm.materials.forEach(material => {
					if (material.element && typeof material.element === 'object') {
						// 遍历原料中的每个元素
						Object.keys(material.element).forEach(elementKey => {
							// 获取元素值和单位
							const elementValueWithUnit = material.element[elementKey];
							const numberMatch = elementValueWithUnit.match(/\d+(\.\d+)?/);
							let elementValue = numberMatch ? Number(numberMatch[0]) : 0;
							
							// 处理百分比单位
							const unit = elementValueWithUnit.replace(/\d+(\.\d+)?/, "").trim();
							if (unit === '%') {
								elementValue = elementValue / 100;
							}
							
							// 计算该原料对该元素总量的贡献
							const contribution = elementValue * Number(material.amount || 0);
							
							// 累加到元素总量
							if (!elementTotals[elementKey]) {
								elementTotals[elementKey] = 0;
							}
							elementTotals[elementKey] += contribution;
						});
					}
				});
				
				// 重新计算每个元素的百分比
				Object.keys(elementTotals).forEach(element => {
					const elementTotal = elementTotals[element];
					// 应用密度调整计算公式: (元素总量 * 默认比率) / (总原料量 / 密度)
					const value = (elementTotal * defaultRatio) / 
								  ((this.localFormulaForm.totalAmount === 0 ? 1 : this.localFormulaForm.totalAmount) / newDensity);
					
					// 更新元素含量值，保留4位小数
					this.localFormulaForm.elements[element] = this.unpadDecimal(value.toFixed(4));
				});
				
				// 重新计算N+P+K总和
				if (elementTotals['N'] !== undefined || elementTotals['P'] !== undefined || elementTotals['K'] !== undefined) {
					const nValue = Number(this.localFormulaForm.elements['N'] || 0);
					const pValue = Number(this.localFormulaForm.elements['P'] || 0);
					const kValue = Number(this.localFormulaForm.elements['K'] || 0);
					const npkTotal = nValue + pValue + kValue;
					
					this.localFormulaForm.elements["N+P+K"] = this.unpadDecimal(npkTotal.toFixed(4));
				}
			},
			
			// 工具方法：去除小数末尾多余的0
			unpadDecimal(num) {
				// 先转换为字符串，确保能够应用正则表达式
				const strNum = num.toString();
				// 使用正则表达式去除小数点后不必要的0
				return strNum.replace(/(\.\d*?)0+$/, "$1").replace(/\.$/, "");
			},
			
			// 计算总成本 (原料成本 + 包装成本 + 封装成本)
			calculateTotalCost() {
				// 确保所有成本值为数字类型
				const rawCost = Number(this.localFormulaForm.totalRawCost || 0);
				const auxCost = Number(this.localFormulaForm.totalAuxiliaryCost || 0);
				const packageCost = Number(this.localFormulaForm.totalPackageCost || 0);
				
				// 计算总成本
				const totalCost = rawCost + auxCost + packageCost;
				
				// 更新总成本，保留4位小数
				this.localFormulaForm.totalCost = totalCost.toFixed(4);
			},
			
			// 更新表单字段
			updateFormField(field, value) {
				// 使用$set确保响应式更新
				this.$set(this.localFormulaForm, field, value);
			},
			
			// 更新材料数据显示
			updateMaterialsDisplay() {
				// 检查是否有原料数据
				this.hasMaterialData = !!(this.localFormulaForm.materials && this.localFormulaForm.materials.length > 0);
				
				// 处理原料数据预览
				if (this.hasMaterialData) {
					this.previewMaterials = this.localFormulaForm.materials.map(item => ({
						name: item.materialName,
						amount: item.amount,
						unit: item.unit || '未知'
					}));
				} else {
					this.previewMaterials = [];
				}
			}
		},
		created() {
			// 确保localFormulaForm有初始值
			this.localFormulaForm = {
				npk: {
					N: '',
					P: '',
					K: '',
					n_unit: 'g/kg',
					p_unit: 'g/kg',
					k_unit: 'g/kg',
					PType: 'P'
				},
				name: '',
				stateCode: '',
				typeCode: '',
				companyName: '',
				microElement: [],
				otherMaterial: [],
				materials: [],
				packages: [],
				density: 1,
				totalAmount: 0,
				totalCost: 0,
				totalRawCost: 0,
				totalAuxiliaryCost: 0,
				totalPackageCost: 0,
				ph: '',
				ec: '',
				waterSoluble: '',
				appearance: '',
				processDesc: '',
				remark: '',
				elements: {}
			};
		},
		mounted() {
			// 初始化表单数据，确保关键数组属性都有默认值
			if (this.formulaDetail) {
				this.initFormulaForm();
			}
			
			// 获取字典数据
			this.initDictData();
			
			// 初始化企业列表
			this.initCompanyList();
		}
	}
</script>

<style>
.formula-detail{
	width: 100%;
}

.card-header {
	display: flex;
	align-items: center;
	width: 100%;
	padding:5px 0;
	color: #007aff;
}

.card-title {
	font-size: 16px;
	font-weight: bold;
	max-width: 80%;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

.formulaName {
	display: flex;
	align-items: center;
	justify-content: space-evenly;
	width: 100%;
}

.formulaName input {
	flex-shrink: 0;
	height: 38px;
	padding: 0 10px;
	border: 1px solid #eee;
	border-radius: 4px;
	margin-right: 10px;
	width:80%;
}

.formula-suffix {
	color: #999;
	font-size: 12px;
	margin-right: 10px;
}

/* 选择器容器样式 */
.picker-view-container {
	width: 100%;
}

/* 选择器值显示样式 */
.picker-value {
	height: 38px;
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 0 10px;
	border: 1px solid #eee;
	border-radius: 4px;
	background-color: #fff;
}

.value-text {
	flex: 1;
	overflow: hidden;
	white-space: nowrap;
	text-overflow: ellipsis;
}

.factory-arrow {
	font-size: 12px;
	color: #999;
	margin-left: 5px;
}

/* 表单内容样式 */
.form-content {
	width: 100%;
}

.element-row {
	display: flex;
	flex-direction: row;
	align-items: center;
	width: 100%;
	margin-bottom: 10px;
	flex-wrap: nowrap;
	position: relative;
}

.element-inline-group {
	display: flex;
	flex: 1;
	align-items: center;
	width: calc(100% - 40px);
}

.element-name {
	display: flex;
	align-items: center;
	justify-content: center;
	height: 34px;
	padding: 0 6px;
	font-weight: bold;
	font-size: 14px;
	/* background-color: #f5f7fa; */
	border: 1px solid #ddd;
	min-width: 80px;
	background-color: #fff;
}

.element-input {
	height: 34px;
	padding: 0 6px;
	border: 1px solid #ddd;
	background-color: #fff;
	font-size: 14px;
	margin: 0;
	flex: 1;
	min-width: 60px;
}

.unit-text {
	display: flex;
	align-items: center;
	height: 34px;
	padding: 0 6px;
	white-space: nowrap;
	font-size: 13px;
	color: #666;
	border: 1px solid #ddd;
	background-color: #fff;
	overflow: hidden;
	text-overflow: ellipsis;
	margin: 0;
	min-width: 80px;
	justify-content: space-between;
}

/* 氮和钾标签样式，与磷选择器保持一致但居中显示 */
.element-label {
	justify-content: center !important;
	font-weight: bold;
	font-size: 14px;
	color: #333;
}

/* 让控件连在一起 */
.element-inline-group > *:not(:first-child) {
	margin-left: -1px;
}

/* 第一个元素左上下圆角 */
.element-inline-group > *:first-child {
	border-top-left-radius: 4px;
	border-bottom-left-radius: 4px;
}

/* 最后一个元素右上下圆角 */
.element-inline-group > *:last-child {
	border-top-right-radius: 4px;
	border-bottom-right-radius: 4px;
}

.delete-icon {
	width: 30px;
	text-align: center;
	display: flex;
	justify-content: center;
	align-items: center;
	margin-left: 10px;
	color: #fa3534;
	font-size: 20px;
}

.add-icon {
	display: inline-flex;
	align-items: center;
	justify-content: center;
	width: 30px;
	height: 30px;
	border-radius: 50%;
	color: #2979ff;
	font-size: 22px;
}

.add-button-row {
	margin-top: 10px;
	display: flex;
	justify-content: center;
}
.textarea-input {
	width: 100%;
	min-height: 80px;
	padding: 8px 10px;
	border: 1px solid #ddd;
	border-radius: 4px;
	background-color: #fff;
	font-size: 14px;
	line-height: 1.5;
	box-sizing: border-box;
}

/* 更多响应式处理 */
@media screen and (max-width: 480px) {
	.element-inline-group {
		width: calc(100% - 30px);
	}
	
	.element-name {
		min-width: 30px;
		padding: 0 5px;
	}
	
	.unit-text {
		min-width: 70px;
	}
	
	.delete-icon {
		min-width: 30px;
	}
}

.material-btn {
	margin: 10px 0;
	width: 100%;
	font-size: 14px;
}

.material-preview {
	margin-bottom: 10px;
	padding: 10px;
	border: 1px solid #ddd;
	border-radius: 4px;
	background-color: #fff;
}

.preview-header {
	font-size: 16px;
	font-weight: bold;
	margin-bottom: 10px;
}

.preview-content {
	display: flex;
	flex-direction: column;
}

.preview-item {
	display: flex;
	flex-direction: row;
	align-items: center;
	margin-bottom: 5px;
}

.preview-label {
	font-size: 14px;
	font-weight: bold;
	margin-right: 10px;
}

.preview-value {
	font-size: 14px;
}

.material-list-preview {
	margin-top: 10px;
}

.material-preview-header {
	display: flex;
	flex-direction: row;
	align-items: center;
	margin-bottom: 5px;
}

.material-header-cell {
	font-size: 14px;
	font-weight: bold;
	margin-right: 10px;
}

.material-preview-item {
	display: flex;
	flex-direction: row;
	align-items: center;
	margin-bottom: 5px;
}

.material-item-cell {
	font-size: 14px;
	margin-right: 10px;
}

.no-material-text {
	font-size: 14px;
	color: #999;
	text-align: center;
}

.density-control {
	display: flex;
	flex-direction: row;
	align-items: center;
	width: 100%;
}

.density-btn {
	display: flex;
	align-items: center;
	justify-content: center;
	width: 32px;
	height: 32px;
	background-color: #f5f5f5;
	border: 1px solid #ddd;
	font-size: 16px;
	cursor: pointer;
}

.density-input {
	flex: 1;
	height: 32px;
	text-align: center;
	border-top: 1px solid #ddd;
	border-bottom: 1px solid #ddd;
	border-left: none;
	border-right: none;
}

.element-content-row {
	display: flex;
	flex-direction: row;
	align-items: center;
	flex-wrap: wrap;
	width: 100%;
	margin-bottom: 10px;
}

.element-content-item {
	display: flex;
	flex-direction: row;
	align-items: center;
	margin-right: 10px;
	margin-bottom: 5px;
}

.element-content-item .element-name {
	font-weight: bold;
	margin-right: 5px;
	border: 0;
}

.element-value {
	margin-right: 2px;
}

.element-unit {
	color: #666;
}

.separator {
	margin: 0 10px;
	color: #ddd;
}

.cost-input {
	display: flex;
	flex-direction: row;
	align-items: center;
	width: 100%;
}

.unit-text {
	margin-left: 5px;
	color: #666;
}

.element-input {
	width: 60px;
	height: 32px;
	text-align: center;
	border: 1px solid #ddd;
	margin: 0 5px;
}

.textarea-input {
	width: 100%;
	min-height: 80px;
	padding: 8px;
	border: 1px solid #ddd;
	border-radius: 4px;
}

.standard-input {
	height: 38px;
	width: 100%;
	padding: 0 10px;
	border: 1px solid #eee;
	border-radius: 4px;
	background-color: #fff;
}

.input-with-suffix {
	display: flex;
	align-items: center;
	width: 100%;
}

.input-suffix {
	margin-left: 5px;
	color: #666;
}

/* 加载状态指示器样式 */
.loading-container {
	display: flex;
	justify-content: center;
	align-items: center;
	height: 100vh;
	background-color: #f8f9fa;
}

.loading-content {
	display: flex;
	flex-direction: column;
	align-items: center;
	text-align: center;
}

.loading-icon {
	margin-bottom: 12px;
	animation: spin 1s linear infinite;
}

.loading-text {
	font-size: 14px;
	color: #666;
}

@keyframes spin {
	from {
		transform: rotate(0deg);
	}
	to {
		transform: rotate(360deg);
	}
}
</style>