import{aM as h,ax as x,aL as z,ay as k}from"./index-Byekp3Iv.js";import{S as A}from"./index-boBBTifs.js";import{W as F}from"./index-C8NGFT36.js";import{ak as G,Z as H,_ as M,aj as Q,x as Z,Q as D,k as J,l as K,f as O,h as T}from"./form-designer-C0ARe9Dh.js";import{k as X,r as V,P as Y,b as v,e as ee,A as le,u as n,y as w,m as b,z as a,H as e,C as y,l as ae,G as te,$ as oe,E as de}from"./form-create-B86qX0W_.js";const re=X({__name:"SaleReturnItemForm",props:{items:{},disabled:{type:Boolean}},setup(I,{expose:N}){const S=I,q=V(!1),m=V([]),P=Y({warehouseId:[{required:!0,message:"\u4ED3\u5E93\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],productId:[{required:!0,message:"\u4EA7\u54C1\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],count:[{required:!0,message:"\u4EA7\u54C1\u6570\u91CF\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}]}),g=V([]),_=V([]),U=V(void 0);v(()=>S.items,async i=>{i.forEach(o=>{var r;o.warehouseId==null&&(o.warehouseId=(r=U.value)==null?void 0:r.id),o.stockCount===null&&o.warehouseId!=null&&E(o)}),m.value=i},{immediate:!0}),v(()=>m.value,i=>{i&&i.length!==0&&i.forEach(o=>{o.totalProductPrice=k(o.productPrice,o.count),o.taxPrice=k(o.totalProductPrice,o.taxPercent/100),o.totalProductPrice!=null?o.totalPrice=o.totalProductPrice+(o.taxPrice||0):o.totalPrice=void 0})},{deep:!0});const B=i=>{const{columns:o,data:r}=i,p=[];return o.forEach((c,u)=>{if(u!==0)if(["count","totalProductPrice","taxPrice","totalPrice"].includes(c.property)){const s=z(r.map(f=>Number(f[c.property])));p[u]=c.property==="count"?h(s):x(s)}else p[u]="";else p[u]="\u5408\u8BA1"}),p},E=async i=>{if(!i.productId)return;const o=await A.getStockCount(i.productId);i.stockCount=o||0};return N({validate:()=>g.value.validate()}),ee(async()=>{_.value=await F.getWarehouseSimpleList(),U.value=_.value.find(i=>i.defaultStatus)}),(i,o)=>{const r=M,p=D,c=Z,u=Q,s=J,f=K,R=O,W=H,L=T,j=G;return le((b(),w(L,{ref_key:"formRef",ref:g,model:n(m),rules:n(P),"label-width":"0px","inline-message":!0,disabled:i.disabled},{default:a(()=>[e(W,{data:n(m),"show-summary":"","summary-method":B,class:"-mt-10px"},{default:a(()=>{var C,$;return[e(r,{label:"\u5E8F\u53F7",type:"index",align:"center",width:"60"}),e(r,{label:"\u4ED3\u5E93\u540D\u79F0","min-width":"125"},{default:a(({row:l,$index:t})=>[e(u,{prop:`${t}.warehouseId`,rules:n(P).warehouseId,class:"mb-0px!"},{default:a(()=>[e(c,{modelValue:l.warehouseId,"onUpdate:modelValue":d=>l.warehouseId=d,clearable:"",filterable:"",placeholder:"\u8BF7\u9009\u62E9\u4ED3\u5E93",onChange:d=>i.onChangeWarehouse(d,l)},{default:a(()=>[(b(!0),ae(te,null,oe(n(_),d=>(b(),w(p,{key:d.id,label:d.name,value:d.id},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue","onChange"])]),_:2},1032,["prop","rules"])]),_:1}),e(r,{label:"\u4EA7\u54C1\u540D\u79F0","min-width":"180"},{default:a(({row:l})=>[e(u,{class:"mb-0px!"},{default:a(()=>[e(s,{disabled:"",modelValue:l.productName,"onUpdate:modelValue":t=>l.productName=t},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1024)]),_:1}),e(r,{label:"\u5E93\u5B58","min-width":"100"},{default:a(({row:l})=>[e(u,{class:"mb-0px!"},{default:a(()=>[e(s,{disabled:"",modelValue:l.stockCount,"onUpdate:modelValue":t=>l.stockCount=t,formatter:n(h)},null,8,["modelValue","onUpdate:modelValue","formatter"])]),_:2},1024)]),_:1}),e(r,{label:"\u6761\u7801","min-width":"150"},{default:a(({row:l})=>[e(u,{class:"mb-0px!"},{default:a(()=>[e(s,{disabled:"",modelValue:l.productBarCode,"onUpdate:modelValue":t=>l.productBarCode=t},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1024)]),_:1}),e(r,{label:"\u5355\u4F4D","min-width":"80"},{default:a(({row:l})=>[e(u,{class:"mb-0px!"},{default:a(()=>[e(s,{disabled:"",modelValue:l.productUnitName,"onUpdate:modelValue":t=>l.productUnitName=t},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1024)]),_:1}),((C=n(m)[0])==null?void 0:C.outCount)!=null?(b(),w(r,{key:0,label:"\u5DF2\u51FA\u5E93",fixed:"right","min-width":"80"},{default:a(({row:l})=>[e(u,{class:"mb-0px!"},{default:a(()=>[e(s,{disabled:"",modelValue:l.outCount,"onUpdate:modelValue":t=>l.outCount=t,formatter:n(h)},null,8,["modelValue","onUpdate:modelValue","formatter"])]),_:2},1024)]),_:1})):y("",!0),(($=n(m)[0])==null?void 0:$.returnCount)!=null?(b(),w(r,{key:1,label:"\u5DF2\u9000\u8D27",fixed:"right","min-width":"80"},{default:a(({row:l})=>[e(u,{class:"mb-0px!"},{default:a(()=>[e(s,{disabled:"",modelValue:l.returnCount,"onUpdate:modelValue":t=>l.returnCount=t,formatter:n(h)},null,8,["modelValue","onUpdate:modelValue","formatter"])]),_:2},1024)]),_:1})):y("",!0),e(r,{label:"\u6570\u91CF",prop:"count",fixed:"right","min-width":"140"},{default:a(({row:l,$index:t})=>[e(u,{prop:`${t}.count`,rules:n(P).count,class:"mb-0px!"},{default:a(()=>[e(f,{modelValue:l.count,"onUpdate:modelValue":d=>l.count=d,"controls-position":"right",min:.001,precision:3,class:"!w-100%"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1032,["prop","rules"])]),_:1}),e(r,{label:"\u4EA7\u54C1\u5355\u4EF7",fixed:"right","min-width":"120"},{default:a(({row:l,$index:t})=>[e(u,{prop:`${t}.productPrice`,class:"mb-0px!"},{default:a(()=>[e(f,{modelValue:l.productPrice,"onUpdate:modelValue":d=>l.productPrice=d,"controls-position":"right",min:.01,precision:2,class:"!w-100%"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1032,["prop"])]),_:1}),e(r,{label:"\u91D1\u989D",prop:"totalProductPrice",fixed:"right","min-width":"100"},{default:a(({row:l,$index:t})=>[e(u,{prop:`${t}.totalProductPrice`,class:"mb-0px!"},{default:a(()=>[e(s,{disabled:"",modelValue:l.totalProductPrice,"onUpdate:modelValue":d=>l.totalProductPrice=d,formatter:n(x)},null,8,["modelValue","onUpdate:modelValue","formatter"])]),_:2},1032,["prop"])]),_:1}),e(r,{label:"\u7A0E\u7387\uFF08%\uFF09",fixed:"right","min-width":"115"},{default:a(({row:l,$index:t})=>[e(u,{prop:`${t}.taxPercent`,class:"mb-0px!"},{default:a(()=>[e(f,{modelValue:l.taxPercent,"onUpdate:modelValue":d=>l.taxPercent=d,"controls-position":"right",min:0,precision:2,class:"!w-100%"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1032,["prop"])]),_:1}),e(r,{label:"\u7A0E\u989D",prop:"taxPrice",fixed:"right","min-width":"120"},{default:a(({row:l,$index:t})=>[e(u,{prop:`${t}.taxPrice`,class:"mb-0px!"},{default:a(()=>[e(u,{prop:`${t}.taxPrice`,class:"mb-0px!"},{default:a(()=>[e(s,{disabled:"",modelValue:l.taxPrice,"onUpdate:modelValue":d=>l.taxPrice=d,formatter:n(x)},null,8,["modelValue","onUpdate:modelValue","formatter"])]),_:2},1032,["prop"])]),_:2},1032,["prop"])]),_:1}),e(r,{label:"\u7A0E\u989D\u5408\u8BA1",prop:"totalPrice",fixed:"right","min-width":"100"},{default:a(({row:l,$index:t})=>[e(u,{prop:`${t}.totalPrice`,class:"mb-0px!"},{default:a(()=>[e(s,{disabled:"",modelValue:l.totalPrice,"onUpdate:modelValue":d=>l.totalPrice=d,formatter:n(x)},null,8,["modelValue","onUpdate:modelValue","formatter"])]),_:2},1032,["prop"])]),_:1}),e(r,{label:"\u5907\u6CE8","min-width":"150"},{default:a(({row:l,$index:t})=>[e(u,{prop:`${t}.remark`,class:"mb-0px!"},{default:a(()=>[e(s,{modelValue:l.remark,"onUpdate:modelValue":d=>l.remark=d,placeholder:"\u8BF7\u8F93\u5165\u5907\u6CE8"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1032,["prop"])]),_:1}),e(r,{align:"center",fixed:"right",label:"\u64CD\u4F5C",width:"60"},{default:a(({$index:l})=>[e(R,{disabled:n(m).length===1,onClick:t=>{return d=l,void m.value.splice(d,1);var d},link:""},{default:a(()=>o[0]||(o[0]=[de(" \u2014 ")])),_:2},1032,["disabled","onClick"])]),_:1})]}),_:1},8,["data"])]),_:1},8,["model","rules","disabled"])),[[j,n(q)]])}}});export{re as _};
