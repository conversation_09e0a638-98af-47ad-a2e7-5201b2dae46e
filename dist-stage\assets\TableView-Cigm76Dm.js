import{u as Oe,_ as v,c as Pe}from"./index-Byekp3Iv.js";import{_ as Be}from"./ContentWrap.vue_vue_type_script_setup_true_lang-D3mNNG_R.js";import{f as je}from"./formatTime-HVkyL6Kg.js";import{f as Ue,Z as Ye,_ as Ee,P as Ae,a6 as Fe,ak as He,ay as Ve,M as S,O as We}from"./form-designer-C0ARe9Dh.js";import{k as Ze,r as I,c as V,e as Ge,y as j,m as M,z as c,v as t,l as Je,C as W,H as l,E as u,u as p,A as Ke,F as r,N as Le,B as f}from"./form-create-B86qX0W_.js";const Re={class:"table-header"},Xe={class:"header-content"},es={class:"header-actions"},ss={class:"table-container"},as={class:"order-cell"},ts={class:"order-no"},ls={class:"customer-name"},rs={class:"product-cell"},is={class:"product-name"},cs={class:"product-code"},os={class:"quantity-cell"},ns={class:"main-quantity"},ds={class:"completed-quantity"},us={class:"stock-cell"},ps={class:"stock-progress"},vs={class:"progress-bar"},gs={class:"progress-text"},fs={class:"stock-info"},ys={class:"process-cell"},hs={class:"process-info"},ms={class:"process-progress"},ws={class:"process-detail"},bs={class:"process-detail"},_s={class:"process-cell"},Ds={class:"process-info"},ks={class:"process-progress"},zs={class:"process-detail"},Cs={class:"process-detail"},Ns={class:"process-cell"},qs={class:"process-info"},xs={class:"process-progress"},Ss={class:"process-detail"},Ts={class:"process-detail"},Is={class:"process-cell"},Qs={class:"process-info"},$s={class:"process-progress"},Ms={class:"process-detail"},Os={class:"process-detail"},Ps={class:"process-cell"},Bs={class:"process-info"},js={class:"process-progress"},Us={class:"process-detail"},Ys={class:"process-detail"},Es={class:"process-cell"},As={class:"process-info"},Fs={class:"process-progress"},Hs={class:"process-detail"},Vs={class:"process-detail"},Ws={class:"process-cell"},Zs={class:"process-info"},Gs={class:"process-progress"},Js={class:"process-detail"},Ks={class:"process-detail"},Ls={class:"pagination-container"},Rs={key:0,class:"batch-actions"},Xs={class:"batch-content"},ea={class:"batch-info"},sa={class:"batch-buttons"},aa=Pe(Ze({__name:"TableView",props:{queryParams:{},loading:{type:Boolean,default:!1},tableData:{default:()=>[]},orderTotals:{default:()=>({saleTotal:0,purchaseTotal:0,workTotal:0,total:0})}},emits:["update:query-params","order-detail","page-change","order-type-change"],setup(Z,{emit:G}){const T=Z,O=G,J=Oe(),m=I([]),K=I(""),L=I(""),w=I(1),x=I(10),U=V(()=>(T.tableData||[]).filter(e=>e.orderType==="sale"||!e.orderType)),R=V(()=>{var e;return((e=T.orderTotals)==null?void 0:e.saleTotal)||U.value.length||0}),X=({row:e})=>e.isException?"exception-row":"",ee=()=>({backgroundColor:"#ffffff",color:"#374151",fontWeight:"600",fontSize:"13px",padding:"12px 8px",borderBottom:"2px solid #e5e7eb"}),se=()=>({padding:"12px 8px",fontSize:"13px"}),b=e=>e?je(e,"YYYY-MM-DD"):"-",Q=e=>{var s;return((s=e.orderDetails)==null?void 0:s[0])||{}},ae=e=>{const s=Q(e);return s.productName||s.materialName||e.productName||e.productInfo||""},te=e=>{const s=Q(e);return s.productCode||s.materialCode||e.productCode||e.materialCode||""},le=e=>{const s=Q(e),i=s.quantity||e.quantity||0;if(s.specQuantityTotal)return s.specQuantityTotal;const d=s.specUnit||s.unit||s.spec||e.unit||"";return i?`${i} ${d}`:"-"},y=(e,s)=>e?`${e}${s}`:"-",re=e=>{const s=e.completedQuantity||e.finishedQuantity||0,i=e.unit||e.unitName||"";return y(s,i)},P=e=>e.stockProgress||e.inventoryProgress||e.progress||0,ie=e=>{const s=P(e);return s>=80?"progress-green":s>=60?"progress-yellow":"progress-red"},ce=e=>{const s=e.stockQuantity||e.inventoryQuantity||0,i=e.unit||e.unitName||"";return y(s,i)},g="pending",_="in_progress",h="completed",oe="failed",o=(e,s)=>{const i=e.progress||0;return{plan:(e.approvalStatus||"0")==="1"?h:g,purchase:i>20?h:i>0?_:g,production:i>60?h:i>30?_:g,quality:i>80?h:i>50?_:g,warehouseIn:i>85?h:i>60?_:g,delivery:e.deliveryStatus==="1"?h:i>70?_:g,warehouseOut:i>=100?h:i>80?_:g}[s]||g},D=(e,s)=>{const i=e.progress||0;switch(s){case"plan":return i>0?100:0;case"purchase":return Math.min(.3*i,100);case"production":return Math.min(.8*i,100);case"quality":return Math.min(.9*i,100);case"warehouseIn":return Math.min(.95*i,100);case"delivery":return Math.min(.98*i,100);case"warehouseOut":return i;default:return 0}},Y={[h]:{type:"success",class:"status-completed",icon:"ep:check",label:"\u5B8C\u6210"},[_]:{type:"primary",class:"status-in-progress",icon:"ep:loading",label:"\u8FDB\u884C\u4E2D"},[g]:{type:"warning",class:"status-pending",icon:"ep:clock",label:"\u672A\u5F00\u59CB"},[oe]:{type:"danger",class:"status-failed",icon:"ep:close",label:"\u5931\u8D25"}},$=e=>Y[e]||Y[g],k=e=>$(e).type,z=e=>$(e).class,C=e=>$(e).icon,N=e=>$(e).label,ne=(e,s)=>e.estimatedDeliveryDate||e.deliveryDate||new Date,E=(e,s)=>e.completedDate||e.estimatedDeliveryDate,A=(e,s)=>s==="warehouseIn"?e.completedDate||e.estimatedDeliveryDate:s==="delivery"?e.deliveryDate||e.estimatedDeliveryDate:e.completedDate||e.estimatedDeliveryDate,F=(e,s)=>e.approvalStatus||"0",de=(e,s)=>!e||!s?"-":`${b(e).substring(5)}\u81F3${b(s).substring(5)}`,ue=e=>{const s=Q(e);return`${s.quantity||e.quantity||0}${s.specUnit||s.unit||e.unit||e.unitName||""}`},pe=e=>{const s=e.unit||e.unitName||"";return y(e.purchaseQuantity,s)},ve=e=>{const s=e.unit||e.unitName||"";return y(e.productionQuantity,s)},ge=e=>{const s=e.unit||e.unitName||"";return y(e.qualityQuantity,s)},fe=e=>{const s=e.unit||e.unitName||"";return y(e.inboundQuantity,s)},ye=e=>{switch(e){case"approved":case"passed":case"1":return"approval-success";case"rejected":case"failed":case"-1":return"approval-danger";default:return"approval-pending"}},he=e=>{switch(e){case"approved":case"passed":case"1":return"\u901A\u8FC7";case"rejected":case"failed":case"-1":return"\u62D2\u7EDD";default:return"\u5F85\u5BA1\u6838"}},me=e=>{const s=e.unit||e.unitName||"";return y(e.noticeQuantity,s)},we=e=>{const s=e.unit||e.unitName||"";return y(e.outboundQuantity,s)},be=({prop:e,order:s})=>{K.value=e,L.value=s,B()},_e=e=>{m.value=e},De=e=>{x.value=e,w.value=1,O("page-change",{pageNo:w.value,pageSize:x.value,orderType:"sale"})},ke=e=>{w.value=e,O("page-change",{pageNo:w.value,pageSize:x.value,orderType:"sale"})},H=e=>{const s=e.id||e.orderId;if(!s)return void S.warning("\u8BA2\u5355ID\u4E0D\u5B58\u5728\uFF0C\u65E0\u6CD5\u8DF3\u8F6C");const i=e.orderNo;J.push({name:"SaleOrderProcessDetail",params:{id:s.toString()},query:{title:i||"\u8BA2\u5355\u5C65\u7EA6\u8BE6\u60C5"}})},ze=()=>{B()},Ce=()=>{S.info("\u5BFC\u51FA\u529F\u80FD\u5F00\u53D1\u4E2D...")},Ne=()=>{S.info(`\u6279\u91CF\u5BFC\u51FA ${m.value.length} \u6761\u8BB0\u5F55`)},qe=()=>{S.info(`\u6279\u91CF\u6253\u5370 ${m.value.length} \u6761\u8BB0\u5F55`)},xe=async()=>{try{await We.confirm(`\u786E\u5B9A\u8981\u53D6\u6D88\u9009\u4E2D\u7684 ${m.value.length} \u4E2A\u8BA2\u5355\u5417\uFF1F`,"\u6279\u91CF\u53D6\u6D88\u786E\u8BA4",{confirmButtonText:"\u786E\u5B9A",cancelButtonText:"\u53D6\u6D88",type:"warning"}),S.success("\u6279\u91CF\u53D6\u6D88\u6210\u529F"),m.value=[],B()}catch{}},B=async()=>{};return Ge(()=>{T.tableData&&T.tableData.length!==0||O("page-change",{pageNo:w.value,pageSize:x.value,orderType:"sale"})}),(e,s)=>{const i=Ue,d=Ee,Se=Ae,q=Fe,Te=Ye,Ie=Ve,Qe=He;return M(),j(p(Be),null,{default:c(()=>[t("div",Re,[t("div",Xe,[s[4]||(s[4]=t("div",{class:"header-title"},[t("h3",{class:"title-text"},"\u9500\u552E\u8BA2\u5355\u5C65\u7EA6\u8DDF\u8E2A"),t("div",{class:"title-subtitle"},"\u5B9E\u65F6\u76D1\u63A7\u9500\u552E\u8BA2\u5355\u4ECE\u521B\u5EFA\u5230\u4EA4\u4ED8\u7684\u5168\u8FC7\u7A0B")],-1)),t("div",es,[l(i,{size:"small",onClick:Ce,class:"action-btn export-btn"},{default:c(()=>[l(p(v),{icon:"ep:download",class:"btn-icon"}),s[2]||(s[2]=u(" \u5BFC\u51FA "))]),_:1}),l(i,{size:"small",onClick:ze,class:"action-btn refresh-btn"},{default:c(()=>[l(p(v),{icon:"ep:refresh",class:"btn-icon"}),s[3]||(s[3]=u(" \u5237\u65B0 "))]),_:1})])])]),t("div",ss,[Ke((M(),j(Te,{data:U.value,border:"",stripe:"","show-overflow-tooltip":!0,"highlight-current-row":"",onSortChange:be,onSelectionChange:_e,class:"custom-table","row-class-name":X,"header-cell-style":ee,"cell-style":se,"max-height":600},{default:c(()=>[l(d,{type:"selection",width:"50",align:"center"}),l(d,{label:"\u8BA2\u5355\u4FE1\u606F",width:"200",fixed:"left",sortable:"custom",prop:"orderNo"},{default:c(({row:a})=>[t("div",as,[t("div",ts,[l(Se,{type:"primary",onClick:n=>H(a),class:"order-link"},{default:c(()=>[u(r(a.orderNo),1)]),_:2},1032,["onClick"])]),t("div",ls,r(a.customerName||"-"),1)])]),_:1}),l(d,{label:"\u4EA7\u54C1\u4FE1\u606F",width:"200","show-overflow-tooltip":""},{default:c(({row:a})=>[t("div",rs,[t("div",is,r(ae(a)),1),t("div",cs,"\u7F16\u7801\uFF1A"+r(te(a)),1)])]),_:1}),l(d,{label:"\u6570\u91CF",width:"100",align:"center",sortable:"custom"},{default:c(({row:a})=>[t("div",os,[t("div",ns,r(le(a)),1),t("div",ds,"\u5DF2\u5B8C\u6210\uFF1A"+r(re(a)),1)])]),_:1}),l(d,{label:"\u5E93\u5B58\u72B6\u6001",width:"130",align:"center","class-name":"hidden-on-mobile"},{default:c(({row:a})=>[t("div",us,[t("div",ps,[t("div",vs,[t("div",{class:f(["progress-fill",ie(a)]),style:Le({width:P(a)+"%"})},null,6)]),t("span",gs,r(P(a))+"%",1)]),t("div",fs,"\u5E93\u5B58\uFF1A"+r(ce(a)),1)])]),_:1}),l(d,{label:"\u8BA1\u5212",width:"120",align:"center"},{default:c(({row:a})=>{return[t("div",ys,[l(q,{type:k(o(a,"plan")),size:"small",class:f(["process-tag",z(o(a,"plan"))]),effect:"plain"},{default:c(()=>[l(p(v),{icon:C(o(a,"plan")),class:"tag-icon"},null,8,["icon"]),u(" "+r(N(o(a,"plan"))),1)]),_:2},1032,["type","class"]),t("div",hs,[t("div",ms,r(D(a,"plan"))+"%",1),s[5]||(s[5]=t("div",{class:"process-divider"},null,-1)),t("div",ws,r(de((n=a,n.orderDate||new Date),ne(a))),1),t("div",bs,"\u8BA1\u5212\uFF1A"+r(ue(a)),1)])])];var n}),_:1}),l(d,{label:"\u91C7\u8D2D",width:"120",align:"center"},{default:c(({row:a})=>{return[t("div",_s,[l(q,{type:k(o(a,"purchase")),size:"small",class:f(["process-tag",z(o(a,"purchase"))]),effect:"plain"},{default:c(()=>[l(p(v),{icon:C(o(a,"purchase")),class:"tag-icon"},null,8,["icon"]),u(" "+r(N(o(a,"purchase"))),1)]),_:2},1032,["type","class"]),t("div",Ds,[t("div",ks,r(D(a,"purchase"))+"%",1),s[6]||(s[6]=t("div",{class:"process-divider"},null,-1)),t("div",zs,"\u91C7\u8D2D\uFF1A"+r(pe(a)),1),t("div",Cs,r(b((n=a,n.completedDate||n.estimatedDeliveryDate))),1)])])];var n}),_:1}),l(d,{label:"\u751F\u4EA7",width:"120",align:"center"},{default:c(({row:a})=>[t("div",Ns,[l(q,{type:k(o(a,"production")),size:"small",class:f(["process-tag",z(o(a,"production"))]),effect:"plain"},{default:c(()=>[l(p(v),{icon:C(o(a,"production")),class:"tag-icon"},null,8,["icon"]),u(" "+r(N(o(a,"production"))),1)]),_:2},1032,["type","class"]),t("div",qs,[t("div",xs,r(D(a,"production"))+"%",1),s[7]||(s[7]=t("div",{class:"process-divider"},null,-1)),t("div",Ss,"\u62A5\u5DE5\uFF1A"+r(ve(a)),1),t("div",Ts,r(b(E(a))),1)])])]),_:1}),l(d,{label:"\u8D28\u68C0",width:"120",align:"center"},{default:c(({row:a})=>{return[t("div",Is,[l(q,{type:k(o(a,"quality")),size:"small",class:f(["process-tag",z(o(a,"quality"))]),effect:"plain"},{default:c(()=>[l(p(v),{icon:C(o(a,"quality")),class:"tag-icon"},null,8,["icon"]),u(" "+r(N(o(a,"quality"))),1)]),_:2},1032,["type","class"]),t("div",Qs,[t("div",$s,r(D(a,"quality"))+"%",1),s[8]||(s[8]=t("div",{class:"process-divider"},null,-1)),t("div",Ms,"\u5408\u683C\uFF1A"+r(ge(a)),1),t("div",Os,r((n=a,n.qualityInspector||n.inspector||"\u5F20\u5DE5")),1)])])];var n}),_:1}),l(d,{label:"\u5165\u5E93",width:"120",align:"center","class-name":"hidden-on-tablet"},{default:c(({row:a})=>[t("div",Ps,[l(q,{type:k(o(a,"warehouseIn")),size:"small",class:f(["process-tag",z(o(a,"warehouseIn"))]),effect:"plain"},{default:c(()=>[l(p(v),{icon:C(o(a,"warehouseIn")),class:"tag-icon"},null,8,["icon"]),u(" "+r(N(o(a,"warehouseIn"))),1)]),_:2},1032,["type","class"]),t("div",Bs,[t("div",js,r(D(a,"warehouseIn"))+"%",1),s[9]||(s[9]=t("div",{class:"process-divider"},null,-1)),t("div",Us,"\u5165\u5E93\uFF1A"+r(fe(a)),1),t("div",Ys,r(b(A(a,"warehouseIn"))),1),t("div",{class:f(["process-detail approval-status",ye(F(a))])}," \u5BA1\u6838\uFF1A"+r(he(F(a))),3)])])]),_:1}),l(d,{label:"\u53D1\u8D27",width:"120",align:"center","class-name":"hidden-on-mobile"},{default:c(({row:a})=>[t("div",Es,[l(q,{type:k(o(a,"delivery")),size:"small",class:f(["process-tag",z(o(a,"delivery"))]),effect:"plain"},{default:c(()=>[l(p(v),{icon:C(o(a,"delivery")),class:"tag-icon"},null,8,["icon"]),u(" "+r(N(o(a,"delivery"))),1)]),_:2},1032,["type","class"]),t("div",As,[t("div",Fs,r(D(a,"delivery"))+"%",1),s[10]||(s[10]=t("div",{class:"process-divider"},null,-1)),t("div",Hs,"\u901A\u77E5\uFF1A"+r(me(a)),1),t("div",Vs,r(b(A(a,"delivery"))),1)])])]),_:1}),l(d,{label:"\u51FA\u5E93",width:"120",align:"center"},{default:c(({row:a})=>[t("div",Ws,[l(q,{type:k(o(a,"warehouseOut")),size:"small",class:f(["process-tag",z(o(a,"warehouseOut"))]),effect:"plain"},{default:c(()=>[l(p(v),{icon:C(o(a,"warehouseOut")),class:"tag-icon"},null,8,["icon"]),u(" "+r(N(o(a,"warehouseOut"))),1)]),_:2},1032,["type","class"]),t("div",Zs,[t("div",Gs,r(D(a,"warehouseOut"))+"%",1),s[11]||(s[11]=t("div",{class:"process-divider"},null,-1)),t("div",Js,"\u51FA\u5E93\uFF1A"+r(we(a)),1),t("div",Ks,r(b(E(a))),1)])])]),_:1}),l(d,{label:"\u64CD\u4F5C",width:"120",fixed:"right",align:"center"},{default:c(({row:a})=>{return[l(i,{link:"",type:"primary",size:"small",onClick:$e=>H(a)},{default:c(()=>[l(p(v),{icon:"ep:view"}),s[12]||(s[12]=u(" \u8BE6\u60C5 "))]),_:2},1032,["onClick"]),(n=a,n.approvalStatus==="0"||n.approvalStatus==="1"&&n.deliveryStatus!=="1"?(M(),j(i,{key:0,link:"",type:"warning",size:"small",onClick:$e=>(Me=>{S.info(`\u7F16\u8F91\u8BA2\u5355: ${Me.orderNo}`)})(a)},{default:c(()=>[l(p(v),{icon:"ep:edit"}),s[13]||(s[13]=u(" \u7F16\u8F91 "))]),_:2},1032,["onClick"])):W("",!0))];var n}),_:1})]),_:1},8,["data"])),[[Qe,T.loading]])]),t("div",Ls,[l(Ie,{"current-page":w.value,"onUpdate:currentPage":s[0]||(s[0]=a=>w.value=a),"page-size":x.value,"onUpdate:pageSize":s[1]||(s[1]=a=>x.value=a),"page-sizes":[10,20,50,100],total:R.value,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:De,onCurrentChange:ke,class:"custom-pagination"},null,8,["current-page","page-size","total"])]),m.value.length>0?(M(),Je("div",Rs,[t("div",Xs,[t("span",ea," \u5DF2\u9009\u62E9 "+r(m.value.length)+" \u6761\u8BB0\u5F55 ",1),t("div",sa,[l(i,{size:"small",onClick:Ne,class:"batch-btn"},{default:c(()=>s[14]||(s[14]=[u(" \u6279\u91CF\u5BFC\u51FA ")])),_:1}),l(i,{size:"small",onClick:qe,class:"batch-btn"},{default:c(()=>s[15]||(s[15]=[u(" \u6279\u91CF\u6253\u5370 ")])),_:1}),l(i,{size:"small",type:"danger",onClick:xe,class:"batch-btn batch-btn-danger"},{default:c(()=>s[16]||(s[16]=[u(" \u6279\u91CF\u53D6\u6D88 ")])),_:1})])])])):W("",!0)]),_:1})}}}),[["__scopeId","data-v-41225dd2"]]);export{aa as default};
