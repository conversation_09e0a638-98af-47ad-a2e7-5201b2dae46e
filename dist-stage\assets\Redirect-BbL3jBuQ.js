import{u as m}from"./index-Byekp3Iv.js";import{k as c,u as i,l as n,m as u}from"./form-create-B86qX0W_.js";import"./form-designer-C0ARe9Dh.js";const d=c({name:"Redirect",__name:"Redirect",setup(y){const{currentRoute:p,replace:s}=m(),{params:e,query:a}=i(p),{path:t,_redirect_type:o="path"}=e;Reflect.deleteProperty(e,"_redirect_type"),Reflect.deleteProperty(e,"path");const r=Array.isArray(t)?t.join("/"):t;return s(o==="name"?{name:r,query:a,params:e}:{path:r.startsWith("/")?r:"/"+r,query:a}),(l,_)=>(u(),n("div"))}});export{d as default};
