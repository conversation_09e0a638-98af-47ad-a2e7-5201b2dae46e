import{W as I,e as j,u as A}from"./index-Byekp3Iv.js";import{k as E,r as L,P as b,c as G,u as l,b as H,A as J,I as M,l as y,m as p,H as n,v as N,z as c,E as S,G as P,$ as R,y as W,F as B,M as x}from"./form-create-B86qX0W_.js";import{u as D,L as T,_ as K}from"./LoginFormTitle.vue_vue_type_script_setup_true_lang-DDJek0nz.js";import{a0 as Q,$ as X,h as Y,aj as Z,q as ee,w as se,f as ae}from"./form-designer-C0ARe9Dh.js";const te={class:"form-cont"},oe={key:0},re={key:1},le=E({name:"SSOLogin",__name:"SSOLogin",setup(ne){const i=j(),{currentRoute:U}=A(),{getLoginState:z,setLoginState:C}=D(),k=L({name:"",logo:""}),a=b({responseType:"",clientId:"",redirectUri:"",state:"",scopes:[]}),V=G(()=>l(z)===T.SSO),u=b({scopes:[]}),f=L(!1),F=async()=>{if(i.query.client_id===void 0)return;if(a.responseType=i.query.response_type,a.clientId=i.query.client_id,a.redirectUri=i.query.redirect_uri,a.state=i.query.state,i.query.scope&&(a.scopes=i.query.scope.split(" ")),a.scopes.length>0){const s=await O(!0,a.scopes,[]);if(s)return void(location.href=s)}const t=await(e=a.clientId,I.get({url:"/system/oauth2/authorize?clientId="+e}));var e;let o;if(k.value=t.client,a.scopes.length>0){o=[];for(const s of t.scopes)a.scopes.indexOf(s.key)>=0&&o.push(s)}else{o=t.scopes;for(const s of o)a.scopes.push(s.key)}for(const s of o)s.value&&u.scopes.push(s.key)},q=async t=>{let e,o;t?(e=u.scopes,o=a.scopes.filter(s=>e.indexOf(s)===-1)):(e=[],o=a.scopes),f.value=!0;try{const s=await O(!1,e,o);if(!s)return;location.href=s}finally{f.value=!1}},O=(t,e,o)=>((s,_,v,d,m,g,r)=>{const h={};for(const w of g)h[w]=!0;for(const w of r)h[w]=!1;return I.post({url:"/system/oauth2/authorize",headers:{"Content-Type":"application/x-www-form-urlencoded"},params:{response_type:s,client_id:_,redirect_uri:v,state:d,auto_approve:m,scope:JSON.stringify(h)}})})(a.responseType,a.clientId,a.redirectUri,a.state,t,e,o),$=t=>{switch(t){case"user.read":return"\u8BBF\u95EE\u4F60\u7684\u4E2A\u4EBA\u4FE1\u606F";case"user.write":return"\u4FEE\u6539\u4F60\u7684\u4E2A\u4EBA\u4FE1\u606F";default:return t}};return H(()=>U.value,t=>{t.name==="SSOLogin"&&(C(T.SSO),F())},{immediate:!0}),(t,e)=>{const o=X,s=Q,_=se,v=ee,d=Z,m=ae,g=Y;return J((p(),y("div",te,[n(K,{class:"w-full"}),n(s,{class:"form",style:{float:"none"},value:"uname"},{default:c(()=>[n(o,{label:l(k).name,name:"uname"},null,8,["label"])]),_:1}),N("div",null,[n(g,{model:l(u),class:"login-form"},{default:c(()=>[e[4]||(e[4]=S(" \u6B64\u7B2C\u4E09\u65B9\u5E94\u7528\u8BF7\u6C42\u83B7\u5F97\u4EE5\u4E0B\u6743\u9650\uFF1A ")),n(d,{prop:"scopes"},{default:c(()=>[n(v,{modelValue:l(u).scopes,"onUpdate:modelValue":e[0]||(e[0]=r=>l(u).scopes=r)},{default:c(()=>[(p(!0),y(P,null,R(l(a).scopes,r=>(p(),W(_,{key:r,value:r,class:"block mb-[-10px]"},{default:c(()=>[S(B($(r)),1)]),_:2},1032,["value"]))),128))]),_:1},8,["modelValue"])]),_:1}),n(d,{class:"w-full"},{default:c(()=>[n(m,{loading:l(f),class:"w-3/5",type:"primary",onClick:e[1]||(e[1]=x(r=>q(!0),["prevent"]))},{default:c(()=>[l(f)?(p(),y("span",re,"\u6388 \u6743 \u4E2D...")):(p(),y("span",oe,"\u540C\u610F\u6388\u6743"))]),_:1},8,["loading"]),n(m,{class:"w-3/10",onClick:e[2]||(e[2]=x(r=>q(!1),["prevent"]))},{default:c(()=>e[3]||(e[3]=[S("\u62D2\u7EDD")])),_:1})]),_:1})]),_:1},8,["model"])])],512)),[[M,l(V)]])}}});export{le as _};
