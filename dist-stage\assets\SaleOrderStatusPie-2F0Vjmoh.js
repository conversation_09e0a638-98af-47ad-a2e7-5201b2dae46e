import{c as u}from"./index-Byekp3Iv.js";import{w as m}from"./echarts-B337Cxlk.js";import{k as d,r as c,e as f,y as h,m as p,z as i,v as n}from"./form-create-B86qX0W_.js";import{ad as g}from"./form-designer-C0ARe9Dh.js";const b=d({name:"SaleOrderStatusPie",setup(){const t=c(null),e=[{count:30,status:"\u5DF2\u5B8C\u6210"},{count:20,status:"\u8FDB\u884C\u4E2D"},{count:10,status:"\u903E\u671F"},{count:15,status:"\u672A\u5F00\u59CB"}];return f(()=>{if(t.value){const s=m(t.value),o={tooltip:{trigger:"item"},legend:{bottom:0},series:[{name:"\u8BA2\u5355\u72B6\u6001",type:"pie",radius:["40%","70%"],data:e.map((a,r)=>({value:a.count,name:a.status,itemStyle:{color:["#10b981","#2563eb","#ef4444","#f59e0b"][r]}})),emphasis:{itemStyle:{shadowBlur:10,shadowOffsetX:0,shadowColor:"rgba(0, 0, 0, 0.5)"}},label:{show:!1}}]};s.setOption(o)}}),{chartRef:t}}}),v={ref:"chartRef",style:{width:"100%",height:"20rem"}},w=u(b,[["render",function(t,e,s,o,a,r){const l=g;return p(),h(l,{shadow:"hover",style:{height:"23rem"}},{header:i(()=>e[0]||(e[0]=[n("div",{style:{"font-size":"1.0rem","font-weight":"bold","margin-right":"1rem"}},"\u8BA2\u5355\u72B6\u6001\u5206\u5E03",-1)])),default:i(()=>[n("div",v,null,512)]),_:1})}],["__scopeId","data-v-ad145b5c"]]);export{w as default};
