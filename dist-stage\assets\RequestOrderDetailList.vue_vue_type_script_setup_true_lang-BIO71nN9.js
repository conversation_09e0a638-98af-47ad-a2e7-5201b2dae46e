import{a as G,d as H,D as S}from"./index-Byekp3Iv.js";import{_ as Y}from"./ContentWrap.vue_vue_type_script_setup_true_lang-D3mNNG_R.js";import{_ as Z}from"./DictTag.vue_vue_type_script_lang-DdZ_pRVv.js";import{d as q}from"./formatTime-HVkyL6Kg.js";import{q as v,f as _}from"./formatter-CF7Ifi5S.js";import{R as D}from"./index-CqbWs1hs.js";import{B as j}from"./index-ChDRG3BK.js";import{W as J}from"./index-BC-xKroV.js";import{WarehouseApi as V}from"./index-DmNtGmQq.js";import{W as X}from"./index-DWM7TlB0.js";import{U as $}from"./index-C4fKXT07.js";import{ak as ee,Z as te,_ as ae,a6 as re}from"./form-designer-C0ARe9Dh.js";import{k as le,r as Q,c as T,b as L,e as ie,y as k,m,z as u,A as ne,u as l,l as N,C as g,G as I,H as r,E as O,F as f,v as oe,n as ue}from"./form-create-B86qX0W_.js";const se={key:0},de={key:0},pe={key:1},me={key:1},ce={key:1},ye=le({__name:"RequestOrderDetailList",props:{bizOrderId:{},bomId:{},detailType:{},warehouseMap:{},locationMap:{},unitMap:{}},setup(C){const{t:be}=G(),K=H(),i=C,M=Q(!1),b=Q([]),U=Q(new Map),R=Q(new Map),E=Q(new Map),P=T(()=>i.warehouseMap||U.value),x=T(()=>i.locationMap||R.value),z=T(()=>i.unitMap||E.value),W=Q(!1),h=T(()=>i.detailType||"requestOrder"),A=async()=>{M.value=!0;try{if(!i.bizOrderId&&i.detailType!=="bom")return void(b.value=[]);switch(i.detailType){case"requestOrder":default:i.bizOrderId?b.value=await D.getRequestOrderDetailListByBizOrderId(i.bizOrderId):b.value=[];break;case"bom":i.bomId?(b.value=await j.getBomMaterialListByBomId(i.bomId,""),b.value=b.value.map(c=>({...c,unit:c.materialUnit||c.unit,plannedQuantity:c.quantity||0,fulfilledQuantity:0,readyQuantity:0,lockedQuantity:0}))):b.value=[];break;case"workOrder":if(i.bizOrderId)try{const c=await D.getRequestOrder(i.bizOrderId),y=await J.getWorkOrderPage({orderNo:c.orderNo||""});y&&Array.isArray(y.list)?b.value=y.list:y&&Array.isArray(y)?b.value=y:b.value=[]}catch{b.value=[]}else b.value=[]}await ue()}catch{K.error("\u52A0\u8F7D\u6570\u636E\u5931\u8D25\uFF0C\u8BF7\u91CD\u8BD5"),b.value=[]}finally{M.value=!1}};L(()=>({bizOrderId:i.bizOrderId,bomId:i.bomId,detailType:i.detailType}),async()=>{(i.bizOrderId||i.bomId)&&(await B(),await A())},{immediate:!1});const B=async()=>{if(!W.value){const c=[];i.warehouseMap||c.push((async()=>{const y=await V.getWarehouseList({pageNo:1,pageSize:100});U.value=new Map(y.map(t=>[t.id,t.name]))})()),i.locationMap||c.push((async()=>{const y=await X.getWarehouseLocationPage({pageNo:1,pageSize:100,warehouseId:void 0});R.value=new Map(y.list.map(t=>[t.id,t.name]))})()),i.unitMap||c.push((async()=>{const y=await $.getUnitPage({pageNo:1,pageSize:100});E.value=new Map(y.list.map(t=>[t.id,t.name]))})()),c.length>0&&await Promise.all(c),W.value=!0}},F=c=>{const{columns:y,data:t}=c,n=[];return y.forEach((w,o)=>{if(o!==0)if(h.value==="bom")if(w.property==="quantity"||o===7){const s=t.map(a=>Number(a.quantity)||0);if(s.every(a=>Number.isNaN(a)))n[o]="";else{const a=s.reduce((e,d)=>{const p=Number(d);return Number.isNaN(p)?e:e+p},0);n[o]=_(a)}}else n[o]="";else if(h.value==="workOrder")if(w.property==="orderQuantity"){const s=t.map(a=>Number(a.orderQuantity)||0);if(s.every(a=>Number.isNaN(a)))n[o]="";else{const a=s.reduce((e,d)=>{const p=Number(d);return Number.isNaN(p)?e:e+p},0);n[o]=_(a)}}else if(w.property==="scheduleQuantity"){const s=t.map(a=>Number(a.scheduleQuantity)||0);if(s.every(a=>Number.isNaN(a)))n[o]="";else{const a=s.reduce((e,d)=>{const p=Number(d);return Number.isNaN(p)?e:e+p},0);n[o]=_(a)}}else n[o]="";else if(w.property==="plannedQuantity"||o===7){const s=t.map(a=>Number(a.plannedQuantity)||0);if(s.every(a=>Number.isNaN(a)))n[o]="";else{const a=s.reduce((e,d)=>{const p=Number(d);return Number.isNaN(p)?e:e+p},0);n[o]=_(a)}}else if(w.property==="fulfilledQuantity"||o===8){const s=t.map(a=>Number(a.fulfilledQuantity)||0);if(s.every(a=>Number.isNaN(a)))n[o]="";else{const a=s.reduce((e,d)=>{const p=Number(d);return Number.isNaN(p)?e:e+p},0);n[o]=_(a)}}else if(w.property==="readyQuantity"||o===9){const s=t.map(a=>Number(a.readyQuantity)||0);if(s.every(a=>Number.isNaN(a)))n[o]="";else{const a=s.reduce((e,d)=>{const p=Number(d);return Number.isNaN(p)?e:e+p},0);n[o]=_(a)}}else if(w.property==="lockedQuantity"||o===10){const s=t.map(a=>Number(a.lockedQuantity)||0);if(s.every(a=>Number.isNaN(a)))n[o]="";else{const a=s.reduce((e,d)=>{const p=Number(d);return Number.isNaN(p)?e:e+p},0);n[o]=_(a)}}else n[o]="";else n[o]="\u5408\u8BA1"}),n};return L(()=>[i.bizOrderId,i.bomId,i.detailType],async()=>{(i.bizOrderId||i.bomId)&&await A()},{immediate:!1}),ie(async()=>{await B(),(i.bizOrderId||i.bomId)&&await A()}),(c,y)=>{const t=ae,n=re,w=Z,o=te,s=Y,a=ee;return m(),k(s,null,{default:u(()=>[ne((m(),k(o,{data:l(b),stripe:!0,border:!0,"show-overflow-tooltip":!0,"show-summary":"","summary-method":F},{default:u(()=>[l(h)!=="workOrder"?(m(),N(I,{key:0},[r(t,{label:"\u5E8F\u53F7",align:"center",prop:"num",width:"60"}),r(t,{label:"\u4ED3\u5E93",align:"left",prop:"warehouseId",width:"110"},{default:u(e=>[e.row.warehouseId?(m(),k(n,{key:0},{default:u(()=>{var d;return[O(f(((d=l(P))==null?void 0:d.get(e.row.warehouseId))||"-"),1)]}),_:2},1024)):g("",!0)]),_:1}),r(t,{label:"\u5E93\u4F4D",align:"left",prop:"locationId",width:"60"},{default:u(e=>{var d;return[oe("span",null,f(((d=l(x))==null?void 0:d.get(e.row.locationId))||"-"),1)]}),_:1}),r(t,{label:"\u7269\u6599\u540D\u79F0",align:"left",prop:"materialName",width:"150"}),r(t,{label:"\u7269\u6599\u7F16\u53F7",align:"center",prop:"materialCode",width:"120"}),r(t,{label:"\u89C4\u683C",align:"center",prop:"spec",width:"80"}),r(t,{label:"\u5355\u4F4D",align:"center",prop:"unit",width:"80"},{default:u(e=>{var d;return[e.row.unit?(m(),N("span",se,f(((d=l(z))==null?void 0:d.get(Number(e.row.unit)))||"-"),1)):g("",!0)]}),_:1})],64)):g("",!0),l(h)!=="workOrder"?(m(),k(t,{key:1,label:"\u9700\u6C42\u6570\u91CF",align:"center",width:"100"},{default:u(e=>[l(h)==="bom"?(m(),N("span",de,f(l(v)(null,null,e.row.quantity||0,null)),1)):(m(),N("span",pe,f(l(v)(null,null,e.row.plannedQuantity||0,null)),1))]),_:1})):g("",!0),l(h)==="requestOrder"?(m(),N(I,{key:2},[r(t,{label:"\u5DF2\u8F6C\u6570\u91CF",align:"center",prop:"fulfilledQuantity",width:"100",formatter:l(v)},null,8,["formatter"]),r(t,{label:"\u4ED3\u5E93\u6570\u91CF",align:"center",prop:"stockQuantity",width:"100",formatter:l(v)},null,8,["formatter"]),r(t,{label:"\u53EF\u7528\u6570\u91CF",align:"center",prop:"readyQuantity",width:"100",formatter:l(v)},{default:u(e=>[e.row.readyStatus===1?(m(),k(n,{key:0,type:"success"},{default:u(()=>[O(f(e.row.readyQuantity),1)]),_:2},1024)):(m(),k(n,{key:1},{default:u(()=>[O(f(e.row.readyQuantity),1)]),_:2},1024))]),_:1},8,["formatter"]),r(t,{label:"\u5728\u9014\u6570\u91CF",align:"center",prop:"transitQuantity",width:"100",formatter:l(v)},{default:u(e=>[e.row.transitQuantity>0?(m(),k(n,{key:0,type:"success"},{default:u(()=>[O(f(e.row.transitQuantity),1)]),_:2},1024)):(m(),N("span",me,"-"))]),_:1},8,["formatter"]),r(t,{label:"\u5728\u9014\u53EF\u9501",align:"center",prop:"lockTransitQuantity",width:"100",formatter:l(v)},{default:u(e=>[e.row.lockTransitQuantity>0?(m(),k(n,{key:0,type:"success"},{default:u(()=>[O(f(e.row.lockTransitQuantity),1)]),_:2},1024)):(m(),N("span",ce,"-"))]),_:1},8,["formatter"]),r(t,{label:"\u51C6\u5907\u72B6\u6001",align:"center",prop:"readyStatus",width:"100"},{default:u(e=>[r(w,{type:l(S).MFG_MATERIAL_READY_STATUS,value:e.row.readyStatus},null,8,["type","value"])]),_:1}),r(t,{label:"\u5DF2\u8F6C\u91C7\u8D2D\u6570\u91CF",align:"center",prop:"purchaseQuantity",width:"110"})],64)):g("",!0),l(h)==="workOrder"?(m(),N(I,{key:3},[r(t,{label:"\u751F\u4EA7\u5355\u53F7",align:"center",prop:"workNo","min-width":"160"}),r(t,{label:"\u4EA7\u54C1\u540D\u79F0",align:"center",prop:"productName","min-width":"150"}),r(t,{label:"\u89C4\u683C",align:"center",prop:"spec","min-width":"120"}),r(t,{label:"\u8BA2\u5355\u6570\u91CF",align:"center",prop:"orderQuantity","min-width":"120"},{default:u(e=>[r(n,null,{default:u(()=>[O(f(e.row.orderQuantity||0)+" "+f(l(z).get(Number(e.row.orderUnit))),1)]),_:2},1024)]),_:1}),r(t,{label:"\u8BA1\u5212\u6570\u91CF",align:"center",prop:"scheduleQuantity","min-width":"120"},{default:u(e=>[r(n,null,{default:u(()=>[O(f(e.row.scheduleQuantity||0)+" "+f(l(z).get(Number(e.row.orderUnit))),1)]),_:2},1024)]),_:1}),r(t,{label:"\u72B6\u6001",align:"center",prop:"status","min-width":"100"},{default:u(e=>[r(w,{type:l(S).WORK_ORDER_STATUS,value:e.row.status},null,8,["type","value"])]),_:1}),r(t,{label:"\u8BA1\u5212\u5F00\u59CB",align:"center",prop:"scheduleStartTime",formatter:l(q),"min-width":"160"},null,8,["formatter"]),r(t,{label:"\u8BA1\u5212\u7ED3\u675F",align:"center",prop:"scheduleEndTime",formatter:l(q),"min-width":"160"},null,8,["formatter"]),r(t,{label:"\u4EA4\u671F",align:"center",prop:"deliverDate",formatter:l(q),"min-width":"160"},null,8,["formatter"]),r(t,{label:"\u9886\u6599\u72B6\u6001",align:"center",prop:"pickingStatus","min-width":"100"},{default:u(e=>[r(w,{type:l(S).COMMON_TASK_STATUS,value:e.row.pickingStatus},null,8,["type","value"])]),_:1}),r(t,{label:"\u5165\u5E93\u72B6\u6001",align:"center",prop:"inStockStatus","min-width":"100"},{default:u(e=>[r(w,{type:l(S).COMMON_TASK_STATUS,value:e.row.inStockStatus},null,8,["type","value"])]),_:1}),r(t,{label:"\u62A5\u5DE5\u72B6\u6001",align:"center",prop:"reportStatus","min-width":"100"},{default:u(e=>[r(w,{type:l(S).COMMON_TASK_STATUS,value:e.row.reportStatus},null,8,["type","value"])]),_:1}),r(t,{label:"\u5907\u6CE8",align:"center",prop:"remark","min-width":"150"})],64)):g("",!0),l(h)==="bom"?(m(),N(I,{key:4},[r(t,{label:"\u635F\u8017\u7387",align:"center",prop:"lossRate",width:"80"}),r(t,{label:"\u8F85\u52A9\u6570\u91CF",align:"center",prop:"auxiliaryQuantity",width:"90",formatter:l(v)},null,8,["formatter"]),r(t,{label:"\u8F85\u52A9\u5355\u4F4D",align:"center",prop:"auxiliaryUnit",width:"90"})],64)):g("",!0),l(h)!=="workOrder"?(m(),N(I,{key:5},[r(t,{label:"\u5907\u6CE8",align:"center",prop:"remark",width:"100"}),r(t,{label:"\u8BF4\u660E",align:"center",prop:"note"}),r(t,{label:"\u6279\u53F7",align:"center",prop:"batchNo"})],64)):g("",!0),l(h)==="workOrder"?(m(),N(I,{key:6},[],64)):g("",!0)]),_:1},8,["data"])),[[a,l(M)]])]),_:1})}}});export{ye as _};
