import{u,_ as c}from"./index-Byekp3Iv.js";import{C as d}from"./CardTitle-q3QPfAon.js";import{ad as g}from"./form-designer-C0ARe9Dh.js";import{k as p,y as f,m as a,z as t,v as r,l as b,G as C,$ as x,B as h,H as n,F as w,u as v}from"./form-create-B86qX0W_.js";const N={class:"flex flex-row flex-wrap gap-8 p-4"},y=["onClick"],k=p({name:"ShortcutCard",__name:"ShortcutCard",setup(B){const l=u(),i=[{name:"\u7528\u6237\u7BA1\u7406",icon:"ep:user-filled",bgColor:"bg-red-400",routerName:"MemberUser"},{name:"\u4EA7\u54C1\u7BA1\u7406",icon:"fluent-mdl2:product",bgColor:"bg-orange-400",routerName:"ProductSpu"},{name:"\u8BA2\u5355\u7BA1\u7406",icon:"ep:list",bgColor:"bg-yellow-500",routerName:"TradeOrder"},{name:"\u91C7\u8D2D\u7BA1\u7406",icon:"ri:refund-2-line",bgColor:"bg-green-600",routerName:"TradeAfterSale"},{name:"\u751F\u4EA7\u7BA1\u7406",icon:"fa-solid:project-diagram",bgColor:"bg-cyan-500",routerName:"TradeBrokerageUser"},{name:"\u4ED3\u5E93\u7BA1\u7406",icon:"ep:ticket",bgColor:"bg-blue-500",routerName:"PromotionCoupon"},{name:"\u914D\u65B9\u7BA1\u7406",icon:"fa:group",bgColor:"bg-purple-500",routerName:"PromotionBargainActivity"},{name:"\u8D22\u52A1\u7BA1\u7406",icon:"vaadin:money-withdraw",bgColor:"bg-rose-500",routerName:"TradeBrokerageWithdraw"}];return(S,T)=>{const s=c,m=g;return a(),f(m,{shadow:"never"},{header:t(()=>[n(v(d),{title:"\u5FEB\u6377\u5165\u53E3"})]),default:t(()=>[r("div",N,[(a(),b(C,null,x(i,e=>r("div",{key:e.name,class:"h-20 w-20% flex flex-col cursor-pointer items-center justify-center gap-2",onClick:_=>{return o=e.routerName,void l.push({name:o});var o}},[r("div",{class:h([e.bgColor,"h-48px w-48px flex items-center justify-center rounded text-white"])},[n(s,{icon:e.icon,class:"text-7.5!"},null,8,["icon"])],2),r("span",null,w(e.name),1)],8,y)),64))])]),_:1})}}});export{k as default};
