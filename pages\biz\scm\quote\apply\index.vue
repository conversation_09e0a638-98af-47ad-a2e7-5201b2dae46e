<template>
	<view class="applyMainPage">
		<view class="top-tabs">
			<uv-tabs :list="tabs" @click="changeStatus" v-if="tabs && tabs.length"></uv-tabs>
		</view>
		<view class="top-search">
			<uv-search 
				shape="round" 
				v-model="productName" 
				placeholder="请输入配方名称" 
				clearable 
				bgColor="#f5f5f5"
				@search="handleSearch"
				@custom="handleSearch"
				@clear="handleClear"
			></uv-search>
		</view>
		<scroll-view 
			scroll-y="true" 
			class="applyMainList" 
			@scrolltolower="loadMoreList"
			refresher-enabled
			:refresher-triggered="isRefreshing"
			@refresherrefresh="onPullDownRefresh"
			enable-flex="true"
		>
			<!-- 有数据时显示列表 -->
			<block v-if="applyList && applyList.length > 0">
				<view class="list-container">
					<view class="list-item-wrapper" v-for="item in applyList" :key="item.id">
						<view class="list-item-card">
							<apply-list-item :item="item" @delete="handleDelete" @confirm="handleConfirm" @update="handleUpdate"></apply-list-item>
						</view>
					</view>
				</view>
				<!-- 有数据时才显示加载更多相关内容 -->
				<view class="load-more-container">
					<uni-load-more :status="loadMoreStatus" @clickLoadMore="loadMoreList"></uni-load-more>
					<view v-if="loadMoreStatus === 'more'" class="load-more-btn" @click="loadMoreList">
						<text>点击加载更多</text>
					</view>
				</view>
			</block>
			
			<!-- 无数据时显示暂无数据提示 -->
			<view v-if="!applyList || applyList.length === 0" class="empty-data">
				<text>暂无数据</text>
			</view>
		</scroll-view>
		<uni-fab 
			:pattern="fabPattern"
			horizontal="right"
			vertical="bottom"
			direction="vertical"
			@fabClick="handleUpdate"
			v-if="this.$auth && this.$auth.hasPermi('quote:apply:create')"
		></uni-fab>
	</view>
</template>

<script>
import { getApplyPageApi, deleteApplyApi, confirmApplyApi } from "../../../../../api/scm/quote/apply"
import { getDictDataPage } from "../../../../../api/system/dict/dict.data"
import { getDictType} from "../../../../../api/system/dict/dict.type"
import { getDictOptions } from "../../../../../utils/dict"
import ApplyListItem from "./components/ApplyListItem.vue"

export default {
	components:{
		ApplyListItem
	},
	data() {
		return {
			applyList: [],
			tabs: [{
				name: '全部'
			}],
			quote_status_list: [],
			applyPageNo: 1,
			loadMoreStatus: 'more', // 加载更多状态：more-加载更多，loading-加载中，noMore-没有更多了
			currentListStatus: '', // 当前选中的状态值
			pageSize: 10, // 每页数据条数
			isRefreshing: false, // 下拉刷新状态
			productName: '', // 配方名称搜索关键词
			// 悬浮按钮配置
			fabPattern: {
				color: '#007AFF',
				backgroundColor: '#FFF',
				buttonColor: '#007AFF'
			},
			needRefresh: false,
		}
	},
	methods:{
		async getApplyPage(statusValue, isLoadMoreOperation = false) {
			// 非加载更多操作（切换标签页或首次加载）时，重置页码和列表
			if (!isLoadMoreOperation) {
				this.applyPageNo = 1;
				this.applyList = [];
			}
			
			// 设置加载状态
			this.loadMoreStatus = 'loading';
			
			try {
				const response = await getApplyPageApi({
					pageNo: this.applyPageNo,
					pageSize: this.pageSize,
					status: statusValue,
					productName: this.productName
				});
				
				if (!response || response.code !== 0) {
					this.$model.msgError('未获取到报价信息');
					this.loadMoreStatus = 'noMore';
					return;
				}
				
				// 获取新数据
				const newList = response.data && response.data.list ? response.data.list : [];
				
				// 判断是否有更多数据
				if (newList.length < this.pageSize) {
					this.loadMoreStatus = 'noMore';
				} else {
					this.loadMoreStatus = 'more';
				}
				
				// 如果是加载更多操作，则追加数据；否则，替换数据
				if (isLoadMoreOperation) {
					this.applyList = [...this.applyList, ...newList];
				} else {
					this.applyList = newList;
					// 如果初次加载某状态无数据，也设为没有更多
					if (newList.length === 0) {
						this.loadMoreStatus = 'noMore';
					}
				}
				
				// 完成下拉刷新
				if (this.isRefreshing) {
					this.isRefreshing = false;
				}
			} catch (error) {
				this.loadMoreStatus = 'noMore';
				this.$model.msgError('获取报价列表失败');
				
				// 完成下拉刷新
				if (this.isRefreshing) {
					this.isRefreshing = false;
				}
			}
		},
		async getTabsList() {
			this.quote_status_list = await getDictOptions('quote_status') || [];
			if (this.quote_status_list && this.quote_status_list.length > 0) {
				this.quote_status_list.forEach(item => {
					this.tabs.push({
						name: item.label
					});
				});
			}
		},
		// 修改handleUpdate方法，完善参数传递
		handleUpdate(params) {
			// 跳转到报价单详情/编辑页面
			uni.navigateTo({
				url: '/pages/biz/scm/quote/apply/applyDetail/applyDetail',
				success: (res) => {
					// 通过eventChannel向被打开页面传送数据
					if (params) {
						// 如果有传递参数，通过事件通道传递给详情页
						res.eventChannel.emit('acceptDataFromOpener', { 
							data: params,
							nowPage: 'apply'
						});
					} else {
						// 如果没有参数，表示新增模式
						res.eventChannel.emit('acceptDataFromOpener', { 
							data: null, 
							type: 'add',
							nowPage: 'apply'
						});
					}
				}
			});
		},
		changeStatus(status) {
			// 获取当前选中状态的值
			const nowStatus = this.quote_status_list && this.quote_status_list.length > 0 ? 
				this.quote_status_list.filter(item => { 
					return item.label === status.name
				}) : [];
			
			// 更新当前状态值
			if (nowStatus.length !== 0) {
				this.currentListStatus = nowStatus[0].value;
			} else {
				this.currentListStatus = ''; // 全部
			}
			
			// 重置加载更多状态
			this.loadMoreStatus = 'more';
			
			// 加载新状态的数据
			this.getApplyPage(this.currentListStatus, false);
		},
		loadMoreList() {
			// 如果正在加载或没有更多数据，则不执行操作
			if (this.loadMoreStatus === 'loading' || this.loadMoreStatus === 'noMore') {
				return;
			}
			
			// 页码自增
			this.applyPageNo++;
			
			// 加载下一页数据
			this.getApplyPage(this.currentListStatus, true);
		},
		// 下拉刷新
		async onPullDownRefresh() {
			// 设置刷新状态
			this.isRefreshing = true;
			
			// 重置页码和加载状态
			this.applyPageNo = 1;
			this.loadMoreStatus = 'more';
			
			// 重新加载当前状态的数据
			await this.getApplyPage(this.currentListStatus, false);
		},
		
		// 处理删除操作
		handleDelete(item) {
			uni.showModal({
				title: '提示',
				content: '确定要删除该报价申请吗？',
				cancelText:'取消',
				confirmText:'确定',
				success: async (res) => {
					if (res.confirm) {
							// 这里需要实现删除API，以下是示例
							const response = await deleteApplyApi(item.id);
							if(response.code === 0){
								uni.showToast({
									title: '删除成功',
									icon: 'success'
								});
								// 重新加载列表
								this.getApplyPage(this.currentListStatus, false);
							}else{
								uni.showToast({
									title:'删除失败',
									icon:'none'
								})
							}
					}
				}
			});
		},
		
		// 处理审核操作
		handleConfirm(item) {
			uni.showModal({
				title:'提示',
				content:'是否要确认该报价申请',
				cancelText:'取消',
				confirmText:'确定',
				success:async (res) => {
					if (res.confirm){
							const query = {
								id:item.id
							}
							const response = await confirmApplyApi(query);
							if(response.code !== 0){
								uni.showToast({
									title:'确认失败',
									icon:'none',
								})
								return
							}
							uni.showToast({
								title:'确认成功',
								icon:'success'
							})
							this.getApplyPage(this.currentListStatus,false);
					}
				}
			})
		},
		handleSearch() {
			// 使用当前的搜索关键词和状态值重新加载数据
			this.getApplyPage(this.currentListStatus, false);
		},
		handleClear() {
			// 清空搜索关键词
			this.productName = '';
			// 重新加载数据
			this.getApplyPage(this.currentListStatus, false);
		},
		// 刷新列表数据 - 供其他页面调用
		refreshList() {
			this.getApplyPage(this.currentListStatus, false);
		}
	},
	async mounted() {
		// 先获取标签页列表
		await this.getTabsList();
		// 设置初始状态为"全部"
		this.currentListStatus = '';
		
		// 加载初始数据
		this.getApplyPage(this.currentListStatus, false);
	},
	onLoad() {
		// onLoad中已经初始化过，不需要重复加载
		// 由mounted处理初始加载
	},
	
	// 每次页面显示时检查是否需要刷新
	onShow() {
		// 检查是否有刷新标记
		if (this.needRefresh) {
			// 重置标记
			this.needRefresh = false;
			// 刷新列表
			this.refreshList();
		}
	}
}
</script>
<style>
	.applyMainPage{
		display: flex;
		flex-direction: column;
		height: 100vh;
	}
	.top-tabs{
		background-color: #fff;
		/* height: 40px; */
		z-index: 10;
	}
	.top-search {
		background-color: #fff;
		padding: 8px 12px;
		border-bottom: 1px solid #f0f0f0;
		z-index: 9;
	}
	.applyMainList{
		flex: 1;
		background-color: #f5f7fa;
		width: 100%;
		height: 0; /* 使用flex:1自动填充剩余空间 */
		overflow: hidden; /* 确保使用scroll-view的滚动 */
	}
	
	/* 列表容器样式 */
	.list-container {
		padding: 16px 12px;
		min-height: 100%;
	}
	
	/* 列表项包装器 */
	.list-item-wrapper {
		margin-bottom: 16px;
	}
	
	.list-item-wrapper:last-child {
		margin-bottom: 0;
	}
	
	/* 列表项卡片样式 */
	.list-item-card {
		background-color: #ffffff;
		border-radius: 12px;
		box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
		overflow: hidden;
		border: 1px solid rgba(0, 0, 0, 0.04);
		transition: all 0.2s ease;
	}
	
	.list-item-card:active {
		transform: scale(0.98);
		box-shadow: 0 1px 6px rgba(0, 0, 0, 0.1);
	}
	.empty-data {
		display: flex;
		justify-content: center;
		align-items: center;
		padding: 60px 20px;
		color: #999;
		font-size: 14px;
		margin: 16px 12px;
		background-color: #ffffff;
		border-radius: 12px;
		box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
	}
	
	.load-more-btn {
		display: flex;
		justify-content: center;
		align-items: center;
		height: 50px;
		background-color: #ffffff;
		color: #007AFF;
		font-size: 14px;
		border-radius: 12px;
		box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
		border: 1px solid rgba(0, 0, 0, 0.04);
		transition: all 0.3s ease;
		cursor: pointer;
	}
	
	.load-more-btn:active {
		background-color: #f8f9fa;
		transform: scale(0.98);
	}
	
	/* 加载更多容器 */
	.load-more-container {
		padding: 0 12px 16px 12px;
	}
</style>