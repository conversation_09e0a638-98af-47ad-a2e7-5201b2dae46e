import{p as z,d as E,at as G}from"./index-Byekp3Iv.js";import{g as H}from"./index-DxWi2Nwj.js";import{_ as q}from"./ProductAttributes.vue_vue_type_script_setup_true_lang-E-luaJpa.js";import{_ as I}from"./ProductPropertyAddForm.vue_vue_type_script_setup_true_lang-CZ4JTLNP.js";import{r as P}from"./formRules-MbITpNi6.js";import{h as J,s as K,u as M,aj as Q,f as W,ak as X}from"./form-designer-C0ARe9Dh.js";import{_ as b}from"./SkuList.vue_vue_type_script_setup_true_lang-D1OJWIwD.js";import{k as Y,r as u,P as h,b as Z,l as D,m as i,A as $,H as s,y as n,u as e,z as o,C as y,E as d,G as w}from"./form-create-B86qX0W_.js";const ee=Y({name:"ProductSpuSkuForm",__name:"SkuForm",props:{propFormData:{type:Object,default:()=>{}},isDetail:z.bool.def(!1)},emits:["update:activeName"],setup(m,{expose:F,emit:V}){const g=[{name:"stock",rule:a=>a>=0,message:"\u5546\u54C1\u5E93\u5B58\u5FC5\u987B\u5927\u4E8E\u7B49\u4E8E 1 \uFF01\uFF01\uFF01"},{name:"price",rule:a=>a>=.01,message:"\u5546\u54C1\u9500\u552E\u4EF7\u683C\u5FC5\u987B\u5927\u4E8E\u7B49\u4E8E 0.01 \u5143\uFF01\uFF01\uFF01"},{name:"marketPrice",rule:a=>a>=.01,message:"\u5546\u54C1\u5E02\u573A\u4EF7\u683C\u5FC5\u987B\u5927\u4E8E\u7B49\u4E8E 0.01 \u5143\uFF01\uFF01\uFF01"},{name:"costPrice",rule:a=>a>=.01,message:"\u5546\u54C1\u6210\u672C\u4EF7\u683C\u5FC5\u987B\u5927\u4E8E\u7B49\u4E8E 0.00 \u5143\uFF01\uFF01\uFF01"}],x=E(),S=u(!1),v=m,T=u(),k=u(),l=u([]),c=u(),r=h({specType:!1,subCommissionType:!1,skus:[]}),B=h({specType:[P],subCommissionType:[P]});Z(()=>v.propFormData,a=>{a&&(G(r,a),l.value=H(a))},{immediate:!0});const L=V;F({validate:async()=>{if(k)try{c.value.validateSku(),await e(k).validate(),Object.assign(v.propFormData,r)}catch(a){throw x.error("\u3010\u5E93\u5B58\u4EF7\u683C\u3011\u4E0D\u5B8C\u5584\uFF0C\u8BF7\u586B\u5199\u76F8\u5173\u4FE1\u606F"),L("update:activeName","sku"),a}}});const R=()=>{for(const a of r.skus)a.firstBrokeragePrice=0,a.secondBrokeragePrice=0},j=()=>{l.value=[],r.skus=[{price:0,marketPrice:0,costPrice:0,barCode:"",picUrl:"",stock:0,weight:0,volume:0,firstBrokeragePrice:0,secondBrokeragePrice:0}]},U=a=>{c.value.generateTableData(a)};return(a,t)=>{const f=M,C=K,p=Q,A=W,N=J,O=X;return i(),D(w,null,[$((i(),n(N,{ref_key:"formRef",ref:k,disabled:m.isDetail,model:e(r),rules:e(B),"label-width":"120px"},{default:o(()=>[s(p,{label:"\u5206\u9500\u7C7B\u578B",prop:"subCommissionType"},{default:o(()=>[s(C,{modelValue:e(r).subCommissionType,"onUpdate:modelValue":t[0]||(t[0]=_=>e(r).subCommissionType=_),class:"w-80",onChange:R},{default:o(()=>[s(f,{value:!1},{default:o(()=>t[2]||(t[2]=[d("\u9ED8\u8BA4\u8BBE\u7F6E")])),_:1}),s(f,{value:!0,class:"radio"},{default:o(()=>t[3]||(t[3]=[d("\u5355\u72EC\u8BBE\u7F6E")])),_:1})]),_:1},8,["modelValue"])]),_:1}),s(p,{label:"\u5546\u54C1\u89C4\u683C",prop:"specType"},{default:o(()=>[s(C,{modelValue:e(r).specType,"onUpdate:modelValue":t[1]||(t[1]=_=>e(r).specType=_),class:"w-80",onChange:j},{default:o(()=>[s(f,{value:!1,class:"radio"},{default:o(()=>t[4]||(t[4]=[d("\u5355\u89C4\u683C")])),_:1}),s(f,{value:!0},{default:o(()=>t[5]||(t[5]=[d("\u591A\u89C4\u683C")])),_:1})]),_:1},8,["modelValue"])]),_:1}),e(r).specType?y("",!0):(i(),n(p,{key:0},{default:o(()=>[s(e(b),{ref_key:"skuListRef",ref:c,"prop-form-data":e(r),"property-list":e(l),"rule-config":g},null,8,["prop-form-data","property-list"])]),_:1})),e(r).specType?(i(),n(p,{key:1,label:"\u5546\u54C1\u5C5E\u6027"},{default:o(()=>[s(A,{class:"mb-10px mr-15px",onClick:e(T).open},{default:o(()=>t[6]||(t[6]=[d("\u6DFB\u52A0\u5C5E\u6027")])),_:1},8,["onClick"]),s(q,{"is-detail":m.isDetail,"property-list":e(l),onSuccess:U},null,8,["is-detail","property-list"])]),_:1})):y("",!0),e(r).specType&&e(l).length>0?(i(),D(w,{key:2},[m.isDetail?y("",!0):(i(),n(p,{key:0,label:"\u6279\u91CF\u8BBE\u7F6E"},{default:o(()=>[s(e(b),{"is-batch":!0,"prop-form-data":e(r),"property-list":e(l)},null,8,["prop-form-data","property-list"])]),_:1})),s(p,{label:"\u89C4\u683C\u5217\u8868"},{default:o(()=>[s(e(b),{ref_key:"skuListRef",ref:c,"is-detail":m.isDetail,"prop-form-data":e(r),"property-list":e(l),"rule-config":g},null,8,["is-detail","prop-form-data","property-list"])]),_:1})],64)):y("",!0)]),_:1},8,["disabled","model","rules"])),[[O,e(S)]]),s(I,{ref_key:"attributesAddFormRef",ref:T,propertyList:e(l)},null,8,["propertyList"])],64)}}});export{ee as _};
