import{a as W,e as X,m as Y,n as ee,u as ae,q as te,o as T,r as le,s as se,t as z,v as oe,w as ne,x as re,y as ie,z as pe,A as de,c as me}from"./index-Byekp3Iv.js";import{_ as ce}from"./Verify-cinTggqv.js";import{_ as ue}from"./XButton-g2ipiZNp.js";import{k as xe,r as h,c as fe,u as e,P as ge,e as he,l as we,m as b,v as n,B as I,F as x,H as a,z as l,ac as ye,A as _e,y as M,C as U,Z as ve,E as q,I as be,T as Fe}from"./form-create-B86qX0W_.js";import{_ as C}from"./logo-Des-IPiV.js";import{_ as ke}from"./login-box-bg-CgotIC_L.js";import{u as F}from"./useIcon-DDXPB-la.js";import{T as Ve,_ as Ne}from"./LocaleDropdown.vue_vue_type_script_setup_true_lang-DRaoyBOw.js";import{u as Pe,L as je,_ as Le,a as Ee}from"./LoginFormTitle.vue_vue_type_script_setup_true_lang-DDJek0nz.js";import{r as k}from"./formRules-MbITpNi6.js";import{h as Se,i as Te,j as ze,aj as Ie,k as Me,w as Ue,P as qe,d as Ce}from"./form-designer-C0ARe9Dh.js";import"./_commonjs-dynamic-modules-BHR_E30J.js";const Re={class:"relative mx-auto h-full flex"},Ae={class:"relative flex items-center text-white"},Oe={class:"text-20px font-bold"},Be={class:"h-[calc(100%-60px)] flex items-center justify-center"},Ge={key:"2",class:"text-3xl text-white"},He={key:"3",class:"mt-5 text-14px font-normal text-white"},Ke={class:"relative flex-1 p-30px dark:bg-[var(--login-bg-color)] lt-sm:p-10px overflow-x-hidden overflow-y-auto"},Ze={class:"flex items-center justify-between text-white at-2xl:justify-end at-xl:justify-end"},$e={class:"flex items-center at-2xl:hidden at-xl:hidden"},De={class:"text-20px font-bold"},Je={class:"flex items-center justify-end space-x-10px h-48px"},Qe={class:"m-auto h-[calc(100%-60px)] w-[100%] flex items-center at-2xl:max-w-500px at-lg:max-w-500px at-md:max-w-500px at-xl:max-w-500px"},We=me(xe({name:"SocialLogin",__name:"SocialLogin",setup(Xe){const{t:m}=W(),i=X(),V=Y(),{getPrefixCls:R}=ee(),N=R("login"),A=F({icon:"ep:house"}),O=F({icon:"ep:avatar"}),B=F({icon:"ep:lock"}),P=h(),{validForm:G}=Ee(P),{getLoginState:H}=Pe(),{push:K}=ae(),Z=te(),_=h(!1),j=h(),$=h("blockPuzzle"),D=fe(()=>e(H)===je.LOGIN),J={tenantName:[k],username:[k],password:[k]},s=ge({isShowPassword:!1,captchaEnable:!0,tenantEnable:!0,loginForm:{tenantName:"\u6CD5\u9EA6\u514B\u65AF",username:"",password:"",captchaVerification:"",rememberMe:!1}}),L=async()=>{s.captchaEnable?j.value.show():await S({})},E=h();function w(o){return new URL(decodeURIComponent(location.href)).searchParams.get(o)??""}const S=async o=>{var t,p;_.value=!0;try{if(await(async()=>{if(s.tenantEnable){const v=await pe(s.loginForm.tenantName);de(v)}})(),!await G())return;let r=w("redirect");const c=w("type"),f=(t=i==null?void 0:i.query)==null?void 0:t.code,g=(p=i==null?void 0:i.query)==null?void 0:p.state,u={...s.loginForm},y=await ne({username:u.username,password:u.password,captchaVerification:o.captchaVerification,socialCode:f,socialState:g,socialType:c});if(!y)return;E.value=Ce.service({lock:!0,text:"\u6B63\u5728\u52A0\u8F7D\u7CFB\u7EDF\u4E2D...",background:"rgba(0, 0, 0, 0.7)"}),u.rememberMe?re(u):ie(),z(y),r||(r="/"),r.indexOf("sso")!==-1?window.location.href=window.location.href.replace("/login?redirect=",""):K({path:r||Z.addRouters[0].path})}finally{_.value=!1,E.value.close()}};return he(()=>{(()=>{const o=le();o&&(s.loginForm={...s.loginForm,username:o.username?o.username:s.loginForm.username,password:o.password?o.password:s.loginForm.password,rememberMe:!!o.rememberMe,tenantName:o.tenantName?o.tenantName:s.loginForm.tenantName})})(),(async()=>{var o,t;try{const p=w("type"),r=w("redirect"),c=(o=i==null?void 0:i.query)==null?void 0:o.code,f=(t=i==null?void 0:i.query)==null?void 0:t.state,g=await se(p,c,f);z(g),oe.push({path:r||"/"})}catch{}})()}),(o,t)=>{const p=Ie,r=ze,c=Me,f=Ue,g=qe,u=Te,y=ue,v=ce,Q=Se;return b(),we("div",{class:I([e(N),"relative h-[100%] lt-md:px-10px lt-sm:px-10px lt-xl:px-10px lt-xl:px-10px"])},[n("div",Re,[n("div",{class:I(`${e(N)}__left flex-1 bg-gray-500 bg-opacity-20 relative p-30px lt-xl:hidden overflow-x-hidden overflow-y-auto`)},[n("div",Ae,[t[6]||(t[6]=n("img",{alt:"",class:"mr-10px h-48px w-48px",src:C},null,-1)),n("span",Oe,x(e(T)(e(V).getTitle)),1)]),n("div",Be,[a(ye,{appear:"","enter-active-class":"animate__animated animate__bounceInLeft",tag:"div"},{default:l(()=>[t[7]||(t[7]=n("img",{key:"1",alt:"",class:"w-350px",src:ke},null,-1)),n("div",Ge,x(e(m)("login.welcome")),1),n("div",He,x(e(m)("login.message")),1)]),_:1})])],2),n("div",Ke,[n("div",Ze,[n("div",$e,[t[8]||(t[8]=n("img",{alt:"",class:"mr-10px h-48px w-48px",src:C},null,-1)),n("span",De,x(e(T)(e(V).getTitle)),1)]),n("div",Je,[a(e(Ve)),a(e(Ne),{class:"dark:text-white lt-xl:text-white"})])]),a(Fe,{appear:"","enter-active-class":"animate__animated animate__bounceInRight"},{default:l(()=>[n("div",Qe,[_e(a(Q,{ref_key:"formLogin",ref:P,model:e(s).loginForm,rules:J,class:"login-form","label-position":"top","label-width":"120px",size:"large"},{default:l(()=>[a(u,{style:{"margin-right":"-10px","margin-left":"-10px"}},{default:l(()=>[a(r,{span:24,style:{"padding-right":"10px","padding-left":"10px"}},{default:l(()=>[a(p,null,{default:l(()=>[a(Le,{style:{width:"100%"}})]),_:1})]),_:1}),a(r,{span:24,style:{"padding-right":"10px","padding-left":"10px"}},{default:l(()=>[e(s).tenantEnable?(b(),M(p,{key:0,prop:"tenantName"},{default:l(()=>[a(c,{modelValue:e(s).loginForm.tenantName,"onUpdate:modelValue":t[0]||(t[0]=d=>e(s).loginForm.tenantName=d),placeholder:e(m)("login.tenantNamePlaceholder"),"prefix-icon":e(A),link:"",type:"primary"},null,8,["modelValue","placeholder","prefix-icon"])]),_:1})):U("",!0)]),_:1}),a(r,{span:24,style:{"padding-right":"10px","padding-left":"10px"}},{default:l(()=>[a(p,{prop:"username"},{default:l(()=>[a(c,{modelValue:e(s).loginForm.username,"onUpdate:modelValue":t[1]||(t[1]=d=>e(s).loginForm.username=d),placeholder:e(m)("login.usernamePlaceholder"),"prefix-icon":e(O)},null,8,["modelValue","placeholder","prefix-icon"])]),_:1})]),_:1}),a(r,{span:24,style:{"padding-right":"10px","padding-left":"10px"}},{default:l(()=>[a(p,{prop:"password"},{default:l(()=>[a(c,{modelValue:e(s).loginForm.password,"onUpdate:modelValue":t[2]||(t[2]=d=>e(s).loginForm.password=d),placeholder:e(m)("login.passwordPlaceholder"),"prefix-icon":e(B),"show-password":"",type:"password",onKeyup:t[3]||(t[3]=ve(d=>L(),["enter"]))},null,8,["modelValue","placeholder","prefix-icon"])]),_:1})]),_:1}),a(r,{span:24,style:{"padding-right":"10px","padding-left":"10px","margin-top":"-20px","margin-bottom":"-20px"}},{default:l(()=>[a(p,null,{default:l(()=>[a(u,{justify:"space-between",style:{width:"100%"}},{default:l(()=>[a(r,{span:6},{default:l(()=>[a(f,{modelValue:e(s).loginForm.rememberMe,"onUpdate:modelValue":t[4]||(t[4]=d=>e(s).loginForm.rememberMe=d)},{default:l(()=>[q(x(e(m)("login.remember")),1)]),_:1},8,["modelValue"])]),_:1}),a(r,{offset:6,span:12},{default:l(()=>[a(g,{style:{float:"right"},type:"primary"},{default:l(()=>[q(x(e(m)("login.forgetPassword")),1)]),_:1})]),_:1})]),_:1})]),_:1})]),_:1}),a(r,{span:24,style:{"padding-right":"10px","padding-left":"10px"}},{default:l(()=>[a(p,null,{default:l(()=>[a(y,{loading:e(_),title:e(m)("login.login"),class:"w-[100%]",type:"primary",onClick:t[5]||(t[5]=d=>L())},null,8,["loading","title"])]),_:1})]),_:1}),e(s).captchaEnable==="true"?(b(),M(v,{key:0,ref_key:"verify",ref:j,captchaType:e($),imgSize:{width:"400px",height:"200px"},mode:"pop",onSuccess:S},null,8,["captchaType"])):U("",!0)]),_:1})]),_:1},8,["model"]),[[be,e(D)]])])]),_:1})])])],2)}}}),[["__scopeId","data-v-38dae6da"]]);export{We as default};
