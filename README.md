**严肃声明：现在、未来都不会有商业版本，所有代码全部开源!！**

**「我喜欢写代码，乐此不疲」**  
**「我喜欢做开源，以此为乐」**

我 🐶 在上海艰苦奋斗，早中晚在 top3 大厂认真搬砖，夜里为开源做贡献。

如果这个项目让你有所收获，记得 Star 关注哦，这对我是非常不错的鼓励与支持。

## 🐶 新手必读

* 演示地址【Vue3 + element-plus】：<http://dashboard-vue3.yudao.iocoder.cn>
* 演示地址【Vue3 + vben(ant-design-vue)】：<http://dashboard-vben.yudao.iocoder.cn>
* 演示地址【Vue2 + element-ui】：<http://dashboard.yudao.iocoder.cn>
* 启动文档：<https://doc.iocoder.cn/quick-start/>
* 视频教程：<https://doc.iocoder.cn/video/>

## 🐯 平台简介

**芋道**，以开发者为中心，打造中国第一流的快速开发平台，全部开源，个人与企业可 100% 免费使用。

![](/.image/admin-uniapp/02.png)

* 采用 uni-app 框架，一套代码多端适配，支持 App、小程序、H5！
* 已经实现登录、我的、工作台、编辑资料、头像修改、密码修改、常见问题、关于我们等基础功能。

## 技术栈

| 框架                                              | 说明                 | 版本     |
|-------------------------------------------------|--------------------|--------|
| [uni-app](hhttps://github.com/dcloudio/uni-app) | 跨平台框架              | 2.0.0  |
| [uni-ui](https://github.com/dcloudio/uni-ui)    | 基于 uni-app 的 UI 框架 | 1.4.20 |

## 🔥 后端架构

支持 Spring Boot、Spring Cloud 两种架构：

① Spring Boot 单体架构：<https://doc.iocoder.cn>

![架构图](/.image/common/ruoyi-vue-pro-architecture.png)

② Spring Cloud 微服务架构：<https://cloud.iocoder.cn>

![架构图](/.image/common/yudao-cloud-architecture.png)

## 📋 项目更新记录

### 2024年功能更新

#### 库存管理模块
- **实时库存页面**: 完成小程序端实时库存列表页面开发
  - 📍 位置: `pages/biz/scm/inventory/stockInfo/stockInfo/stockInfo.vue`
  - 🔍 **搜索功能**: 支持物料名称模糊搜索
  - 🏷️ **筛选功能**: 物料类型、仓库、物料编码、库存状态多维度筛选
  - 📋 **列表展示**: 物料基本信息、库存数量、价格、状态等核心字段展示
  - 📱 **移动端优化**: 响应式设计，适配小程序交互体验
  - 🔄 **分页加载**: 下拉刷新、上拉加载更多功能
  - 🎨 **UI设计**: 卡片式布局，清晰的数据层次展示
  
- **API优化**: 更新库存相关API接口
  - 📍 位置: `api/scm/inventory/stockInfo/index.js`
  - 🔗 **接口完善**: 参考PC端实现，统一接口规范
  - 📊 **数据支持**: 支持单位、仓库等基础数据获取

#### 技术特点
- 🎯 **参考PC端设计**: 严格按照PC端功能需求进行移动端适配
- 📱 **uni-app最佳实践**: 使用数组语法解决动态类名兼容性问题
- 🔧 **代码规范**: 遵循项目代码规范，注释完善，结构清晰
- 🎨 **用户体验**: 完善的加载状态、错误处理、空数据提示

## 🐷 演示图

| biu                              | biu                              | biu                              |
|----------------------------------|----------------------------------|----------------------------------|
| ![](/.image/admin-uniapp/01.png) | ![](/.image/admin-uniapp/02.png) | ![](/.image/admin-uniapp/03.png) |
| ![](/.image/admin-uniapp/04.png) | ![](/.image/admin-uniapp/05.png) | ![](/.image/admin-uniapp/06.png) |
| ![](/.image/admin-uniapp/07.png) | ![](/.image/admin-uniapp/08.png) | ![](/.image/admin-uniapp/09.png) |
