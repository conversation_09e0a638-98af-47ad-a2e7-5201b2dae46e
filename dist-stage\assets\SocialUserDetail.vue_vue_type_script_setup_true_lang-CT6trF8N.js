import{_ as b}from"./Dialog.vue_vue_type_style_index_0_lang-DhV_HFpU.js";import{D as x}from"./index-Byekp3Iv.js";import{_ as V}from"./DictTag.vue_vue_type_script_lang-DdZ_pRVv.js";import{a as k}from"./index-Ctv_CVf0.js";import{am as v,an as U,al as I,k as T}from"./form-designer-C0ARe9Dh.js";import{k as S,r,y as z,m as E,z as t,H as a,u as e,E as u,F as m,h as D}from"./form-create-B86qX0W_.js";const R=S({__name:"SocialUserDetail",setup(Y,{expose:w}){const d=r(!1),_=r(!1),l=r({});return w({open:async i=>{d.value=!0;try{l.value=await k(i)}finally{_.value=!1}}}),(i,n)=>{const c=V,o=U,h=I,f=v,p=T,y=b;return E(),z(y,{modelValue:e(d),"onUpdate:modelValue":n[2]||(n[2]=s=>D(d)?d.value=s:null),title:"\u8BE6\u60C5",width:"800"},{default:t(()=>[a(f,{column:1,border:""},{default:t(()=>[a(o,{label:"\u793E\u4EA4\u5E73\u53F0","min-width":"160"},{default:t(()=>[a(c,{type:e(x).SYSTEM_SOCIAL_TYPE,value:e(l).type},null,8,["type","value"])]),_:1}),a(o,{label:"\u7528\u6237\u6635\u79F0","min-width":"120"},{default:t(()=>[u(m(e(l).nickname),1)]),_:1}),a(f,{label:"\u7528\u6237\u5934\u50CF","min-width":"120"},{default:t(()=>[a(h,{src:e(l).avatar,class:"h-30px w-30px"},null,8,["src"])]),_:1}),a(o,{label:"\u793E\u4EA4 token","min-width":"120"},{default:t(()=>[u(m(e(l).token),1)]),_:1}),a(o,{label:"\u539F\u59CB Token \u6570\u636E","min-width":"120"},{default:t(()=>[a(p,{modelValue:e(l).rawTokenInfo,"onUpdate:modelValue":n[0]||(n[0]=s=>e(l).rawTokenInfo=s),autosize:{maxRows:20},readonly:!0,type:"textarea"},null,8,["modelValue"])]),_:1}),a(o,{label:"\u539F\u59CB User \u6570\u636E","min-width":"120"},{default:t(()=>[a(p,{modelValue:e(l).rawUserInfo,"onUpdate:modelValue":n[1]||(n[1]=s=>e(l).rawUserInfo=s),autosize:{maxRows:20},readonly:!0,type:"textarea"},null,8,["modelValue"])]),_:1}),a(o,{label:"\u6700\u540E\u4E00\u6B21\u7684\u8BA4\u8BC1 code","min-width":"120"},{default:t(()=>[u(m(e(l).code),1)]),_:1}),a(o,{label:"\u6700\u540E\u4E00\u6B21\u7684\u8BA4\u8BC1 state","min-width":"120"},{default:t(()=>[u(m(e(l).state),1)]),_:1})]),_:1})]),_:1},8,["modelValue"])}}});export{R as _};
