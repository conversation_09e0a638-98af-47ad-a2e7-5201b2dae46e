import{aC as G,M as k}from"./index-Byekp3Iv.js";import I from"./RewardRuleCouponSelect-C2xIm5iw.js";import{l as U}from"./constants-C3gLHYOK.js";import{i as T,j as z,f as S,h as $,aj as F,l as H,k as L,D as M,a6 as q}from"./form-designer-C0ARe9Dh.js";import{k as A,r as B,y as i,m as s,z as a,l as J,C as R,H as l,u as d,G as K,$ as N,v as f,F as h,E as t}from"./form-create-B86qX0W_.js";const O={class:"font-bold"},Q=A({name:"RewardRule",__name:"RewardRule",props:{modelValue:{}},emits:["update:modelValue","deleteRule"],setup(w,{expose:C,emit:b}){const p=G(w,"modelValue",b),c=B(),g=()=>{k(p.value.rules)&&(p.value.rules=[]),p.value.rules.push({limit:0,discountPrice:0,freeDelivery:!1,point:0})};return C({setRuleCoupon:()=>{var V;k(c.value)||((V=c.value)==null||V.forEach(e=>e.setGiveCouponList()))}}),(V,e)=>{const _=S,y=H,x=L,r=F,n=z,P=M,D=$,E=q,j=T;return s(),i(j,null,{default:a(()=>[d(p).rules?(s(!0),J(K,{key:0},N(d(p).rules,(o,m)=>(s(),i(n,{key:m,span:24},{default:a(()=>[f("span",O,"\u6D3B\u52A8\u5C42\u7EA7"+h(m+1),1),m!==0?(s(),i(_,{key:0,link:"",type:"danger",onClick:u=>{return v=m,void p.value.rules.splice(v,1);var v}},{default:a(()=>e[0]||(e[0]=[t(" \u5220\u9664 ")])),_:2},1032,["onClick"])):R("",!0),l(D,{ref_for:!0,ref:"formRef",model:o},{default:a(()=>[l(r,{label:"\u4F18\u60E0\u95E8\u69DB:","label-width":"100px",prop:"limit"},{default:a(()=>[e[1]||(e[1]=t(" \u6EE1 ")),d(U).PRICE.type===d(p).conditionType?(s(),i(y,{key:0,modelValue:o.limit,"onUpdate:modelValue":u=>o.limit=u,min:0,precision:2,step:.1,class:"w-150px! p-x-20px!",placeholder:"",type:"number","controls-position":"right"},null,8,["modelValue","onUpdate:modelValue"])):(s(),i(x,{key:1,modelValue:o.limit,"onUpdate:modelValue":u=>o.limit=u,min:0,class:"w-150px! p-x-20px!",placeholder:"",type:"number"},null,8,["modelValue","onUpdate:modelValue"])),t(" "+h(d(U).PRICE.type===d(p).conditionType?"\u5143":"\u4EF6"),1)]),_:2},1024),l(r,{label:"\u4F18\u60E0\u5185\u5BB9:","label-width":"100px"},{default:a(()=>[l(n,{span:24},{default:a(()=>[e[4]||(e[4]=t(" \u8BA2\u5355\u91D1\u989D\u4F18\u60E0 ")),l(r,null,{default:a(()=>[e[2]||(e[2]=t(" \u51CF ")),l(y,{modelValue:o.discountPrice,"onUpdate:modelValue":u=>o.discountPrice=u,min:0,precision:2,step:.1,class:"w-150px! p-x-20px!","controls-position":"right"},null,8,["modelValue","onUpdate:modelValue"]),e[3]||(e[3]=t(" \u5143 "))]),_:2},1024)]),_:2},1024),l(n,{span:24},{default:a(()=>[e[5]||(e[5]=f("span",null,"\u5305\u90AE\uFF1A",-1)),l(P,{modelValue:o.freeDelivery,"onUpdate:modelValue":u=>o.freeDelivery=u,"active-text":"\u662F","inactive-text":"\u5426","inline-prompt":""},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1024),l(n,{span:24},{default:a(()=>[e[8]||(e[8]=f("span",null,"\u9001\u79EF\u5206\uFF1A",-1)),l(r,null,{default:a(()=>[e[6]||(e[6]=t(" \u9001 ")),l(x,{modelValue:o.point,"onUpdate:modelValue":u=>o.point=u,class:"w-150px! p-x-20px!",placeholder:"",type:"number"},null,8,["modelValue","onUpdate:modelValue"]),e[7]||(e[7]=t(" \u79EF\u5206 "))]),_:2},1024)]),_:2},1024),l(n,{span:24},{default:a(()=>[e[9]||(e[9]=f("span",null,"\u9001\u4F18\u60E0\u5238\uFF1A",-1)),l(I,{ref_for:!0,ref_key:"rewardRuleCouponSelectRef",ref:c,modelValue:o,"onUpdate:modelValue":u=>o=u},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1024)]),_:2},1024)]),_:2},1032,["model"])]),_:2},1024))),128)):R("",!0),l(n,{span:24,class:"mt-10px"},{default:a(()=>[l(_,{type:"primary",onClick:g},{default:a(()=>e[10]||(e[10]=[t("\u6DFB\u52A0\u4F18\u60E0\u89C4\u5219")])),_:1})]),_:1}),l(n,{span:24},{default:a(()=>[l(E,{type:"warning"},{default:a(()=>e[11]||(e[11]=[t(" \u8D60\u9001\u79EF\u5206\u4E3A 0 \u65F6\u4E0D\u8D60\u9001\u3002\u672A\u9009\u4F18\u60E0\u5238\u65F6\u4E0D\u8D60\u9001\u3002")])),_:1})]),_:1})]),_:1})}}});export{Q as _};
