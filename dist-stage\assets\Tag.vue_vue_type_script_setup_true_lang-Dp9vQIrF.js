import{k as p,l as a,m as l,G as u,$ as m,B as n,F as x}from"./form-create-B86qX0W_.js";const i={class:"flex flex-wrap gap-[8px]"},b=["onClick"],c=p({__name:"Tag",props:{tags:{default:()=>[]},modelValue:{}},emits:["update:modelValue"],setup(o,{emit:r}){const s=o,t=r;return(d,g)=>(l(),a("div",i,[(l(!0),a(u,null,m(s.tags,e=>(l(),a("span",{key:e.value,class:n(["tag mb-2 border-[2px] border-solid border-[#DDDFE3] px-2 leading-6 text-[12px] bg-[#DDDFE3] rounded-[4px] cursor-pointer",d.modelValue===e.value&&"!border-[#846af7] text-[#846af7]"]),onClick:f=>t("update:modelValue",e.value)},x(e.label),11,b))),128))]))}});export{c as _};
