import{p as r,c as N}from"./index-Byekp3Iv.js";import{g as m}from"./tsxHelper-DyKb0U4O.js";import{ak as Q,Z as R,ay as V,ai as X,_ as x}from"./form-designer-C0ARe9Dh.js";import{k as Y,r as v,e as ee,u as a,c as I,b as z,A as ne,H as s,t as O}from"./form-create-B86qX0W_.js";const ae=N(Y({name:"Table",props:{pageSize:r.number.def(10),currentPage:r.number.def(1),selection:r.bool.def(!1),showOverflowTooltip:r.bool.def(!0),columns:{type:Array,default:()=>[]},expand:r.bool.def(!1),pagination:{type:Object,default:()=>{}},reserveSelection:r.bool.def(!1),loading:r.bool.def(!1),reserveIndex:r.bool.def(!1),align:r.string.validate(i=>["left","center","right"].includes(i)).def("center"),headerAlign:r.string.validate(i=>["left","center","right"].includes(i)).def("center"),data:{type:Array,default:()=>[]}},emits:["update:pageSize","update:currentPage","register"],setup(i,{attrs:q,slots:u,emit:P,expose:B}){const T=v();ee(()=>{const e=a(T);P("register",e==null?void 0:e.$parent,T.value)});const w=v(i.pageSize),A=v(i.currentPage),D=v({}),j=v({}),l=I(()=>{const e={...i};return Object.assign(e,a(j)),e}),C=(e,c)=>{var o;const{columns:p}=a(l);for(const g of c||p)for(const f of e)g.field===f.field?X(g,f.path,f.value):(o=g.children)!=null&&o.length&&C(e,g.children)},U=v([]),E=e=>{U.value=e};B({setProps:(e={})=>{j.value=Object.assign(a(j),e),D.value=e},setColumn:C,selections:U});const F=I(()=>Object.assign({small:!1,background:!0,pagerCount:document.body.clientWidth<992?5:7,layout:"total, sizes, prev, pager, next, jumper",pageSizes:[10,20,30,50,100],disabled:!1,hideOnSinglePage:!1,total:10},a(l).pagination));z(()=>a(l).pageSize,e=>{w.value=e}),z(()=>a(l).currentPage,e=>{A.value=e}),z(()=>w.value,e=>{P("update:pageSize",e)}),z(()=>A.value,e=>{P("update:currentPage",e)});const G=I(()=>{const e={...q,...i};return delete e.columns,delete e.data,e}),J=()=>{const{selection:e,reserveSelection:c,align:p,headerAlign:o}=a(l);return e?s(x,{type:"selection",reserveSelection:c,align:p,headerAlign:o,width:"50"},null):void 0},K=()=>{const{align:e,headerAlign:c,expand:p}=a(l);return p?s(x,{type:"expand",align:e,headerAlign:c},{default:o=>m(u,"expand",o)}):void 0},H=e=>{const{columns:c,reserveIndex:p,pageSize:o,currentPage:g,align:f,headerAlign:W,showOverflowTooltip:L}=a(l);return[K(),J()].concat((e||c).map(n=>{if(n.type==="index")return s(x,{type:"index",index:n.index?n.index:b=>((d,S,$,_)=>{const y=S+1;return d?$*(_-1)+y:y})(p,b,o,g),align:n.align||f,headerAlign:n.headerAlign||W,label:n.label,width:"65px"},null);{const b={...n};return b.children&&delete b.children,s(x,O({showOverflowTooltip:L,align:f,headerAlign:W},b,{prop:n.field}),{default:d=>{var S;return n.children&&n.children.length?($=>{const{align:_,headerAlign:y,showOverflowTooltip:M}=a(l);return $.map(t=>{const k={...t};return k.children&&delete k.children,s(x,O({showOverflowTooltip:M,align:_,headerAlign:y},k,{prop:t.field}),{default:h=>{var Z;return t.children&&t.children.length?H(t.children):m(u,t.field,h)||((Z=t==null?void 0:t.formatter)==null?void 0:Z.call(t,h.row,h.column,h.row[t.field],h.$index))||h.row[t.field]},header:m(u,`${t.field}-header`)})})})(n.children):m(u,n.field,d)||((S=n==null?void 0:n.formatter)==null?void 0:S.call(n,d.row,d.column,d.row[n.field],d.$index))||d.row[n.field]},header:()=>m(u,`${n.field}-header`)||n.label})}}))};return()=>ne(s("div",null,[s(R,O({ref:T,data:a(l).data,"onSelection-change":E},a(G)),{default:()=>H(),append:()=>m(u,"append")}),a(l).pagination?s(V,O({pageSize:w.value,"onUpdate:pageSize":e=>w.value=e,currentPage:A.value,"onUpdate:currentPage":e=>A.value=e,class:"float-right mb-15px mt-15px"},a(F)),null):void 0]),[[Q,a(l).loading]])}}),[["__scopeId","data-v-5be60b0f"]]);export{ae as _};
