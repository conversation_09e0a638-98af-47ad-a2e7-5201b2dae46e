import{c as l}from"./index-Byekp3Iv.js";import{f as n}from"./form-designer-C0ARe9Dh.js";import{k as p,l as t,m as s,G as y,$ as m,H as d,z as u,E as f,F as g}from"./form-create-B86qX0W_.js";const C={class:"category-list"},k=l(p({__name:"RoleCategoryList",props:{categoryList:{type:Array,required:!0},active:{type:String,required:!1,default:"\u5168\u90E8"}},emits:["onCategoryClick"],setup(r,{emit:e}){const o=e;return(v,_)=>{const i=n;return s(),t("div",C,[(s(!0),t(y,null,m(r.categoryList,a=>(s(),t("div",{class:"category",key:a},[d(i,{plain:"",round:"",size:"small",type:a===r.active?"primary":"",onClick:L=>(async c=>{o("onCategoryClick",c)})(a)},{default:u(()=>[f(g(a),1)]),_:2},1032,["type","onClick"])]))),128))])}}}),[["__scopeId","data-v-275d5faa"]]);export{k as default};
