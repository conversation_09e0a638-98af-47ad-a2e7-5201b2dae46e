import{aC as _,d as k,M as o,c as N}from"./index-Byekp3Iv.js";import{D as z}from"./config-NlBZD0Kw.js";import{aj as S,k as U,f as C}from"./form-designer-C0ARe9Dh.js";import{k as M,y as $,m as c,z as t,v as n,l as f,H as u,G as q,$ as A,E as v,u as D}from"./form-create-B86qX0W_.js";const T={class:"flex flex-col"},Z=N(M({name:"ThingModelEnumDataSpecs",__name:"ThingModelEnumDataSpecs",props:{modelValue:{}},emits:["update:modelValue"],setup(g,{emit:w}){const s=_(g,"modelValue",w),E=k(),x=()=>{s.value.push({dataType:z.<PERSON>NUM,name:"",value:void 0})},V=(p,a,e)=>{if(o(a))return void e(new Error("\u679A\u4E3E\u503C\u4E0D\u80FD\u4E3A\u7A7A"));if(isNaN(Number(a)))return void e(new Error("\u679A\u4E3E\u503C\u5FC5\u987B\u662F\u6570\u5B57"));s.value.map(l=>l.value).filter(l=>l===a).length>1?e(new Error("\u679A\u4E3E\u503C\u4E0D\u80FD\u91CD\u590D")):e()},b=(p,a,e)=>{o(a)?e(new Error("\u679A\u4E3E\u63CF\u8FF0\u4E0D\u80FD\u4E3A\u7A7A")):/^[\u4e00-\u9fa5a-zA-Z0-9]/.test(a)?/^[\u4e00-\u9fa5a-zA-Z0-9][a-zA-Z0-9\u4e00-\u9fa5_-]*$/.test(a)?a.length>20?e(new Error("\u679A\u4E3E\u63CF\u8FF0\u957F\u5EA6\u4E0D\u80FD\u8D85\u8FC720\u4E2A\u5B57\u7B26")):e():e(new Error("\u679A\u4E3E\u63CF\u8FF0\u53EA\u80FD\u5305\u542B\u4E2D\u6587\u3001\u82F1\u6587\u5B57\u6BCD\u3001\u6570\u5B57\u3001\u4E0B\u5212\u7EBF\u548C\u77ED\u5212\u7EBF")):e(new Error("\u679A\u4E3E\u63CF\u8FF0\u5FC5\u987B\u4EE5\u4E2D\u6587\u3001\u82F1\u6587\u5B57\u6BCD\u6216\u6570\u5B57\u5F00\u5934"))},h=(p,a,e)=>{if(o(s.value))return void e(new Error("\u8BF7\u81F3\u5C11\u6DFB\u52A0\u4E00\u4E2A\u679A\u4E3E\u9879"));if(s.value.some(r=>o(r.value)||o(r.name)))return void e(new Error("\u5B58\u5728\u672A\u586B\u5199\u7684\u679A\u4E3E\u503C\u6216\u63CF\u8FF0"));if(s.value.some(r=>isNaN(Number(r.value))))return void e(new Error("\u5B58\u5728\u975E\u6570\u5B57\u7684\u679A\u4E3E\u503C"));const l=s.value.map(r=>r.value),i=new Set(l);l.length===i.size?e():e(new Error("\u5B58\u5728\u91CD\u590D\u7684\u679A\u4E3E\u503C"))};return(p,a)=>{const e=U,l=S,i=C;return c(),$(l,{rules:[{required:!0,validator:h,trigger:"change"}],label:"\u679A\u4E3E\u9879"},{default:t(()=>[n("div",T,[a[3]||(a[3]=n("div",{class:"flex items-center"},[n("span",{class:"flex-1"}," \u53C2\u6570\u503C "),n("span",{class:"flex-1"}," \u53C2\u6570\u63CF\u8FF0 ")],-1)),(c(!0),f(q,null,A(D(s),(r,d)=>(c(),f("div",{key:d,class:"flex items-center justify-between mb-5px"},[u(l,{prop:`property.dataSpecsList[${d}].value`,rules:[{required:!0,message:"\u679A\u4E3E\u503C\u4E0D\u80FD\u4E3A\u7A7A"},{validator:V,trigger:"blur"}],class:"flex-1 mb-0"},{default:t(()=>[u(e,{modelValue:r.value,"onUpdate:modelValue":m=>r.value=m,placeholder:"\u8BF7\u8F93\u5165\u679A\u4E3E\u503C,\u5982'0'"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1032,["prop","rules"]),a[1]||(a[1]=n("span",{class:"mx-2"},"~",-1)),u(l,{prop:`property.dataSpecsList[${d}].name`,rules:[{required:!0,message:"\u679A\u4E3E\u63CF\u8FF0\u4E0D\u80FD\u4E3A\u7A7A"},{validator:b,trigger:"blur"}],class:"flex-1 mb-0"},{default:t(()=>[u(e,{modelValue:r.name,"onUpdate:modelValue":m=>r.name=m,placeholder:"\u5BF9\u8BE5\u679A\u4E3E\u9879\u7684\u63CF\u8FF0"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1032,["prop","rules"]),u(i,{class:"ml-10px",link:"",type:"primary",onClick:m=>(y=>{s.value.length!==1?s.value.splice(y,1):E.warning("\u81F3\u5C11\u9700\u8981\u4E00\u4E2A\u679A\u4E3E\u9879")})(d)},{default:t(()=>a[0]||(a[0]=[v("\u5220\u9664")])),_:2},1032,["onClick"])]))),128)),u(i,{link:"",type:"primary",onClick:x},{default:t(()=>a[2]||(a[2]=[v("+\u6DFB\u52A0\u679A\u4E3E\u9879")])),_:1})])]),_:1},8,["rules"])}}}),[["__scopeId","data-v-c58cb9f8"]]);export{Z as default};
