import{W as n,a as z,d as A}from"./index-Byekp3Iv.js";import{_ as E}from"./Dialog.vue_vue_type_style_index_0_lang-DhV_HFpU.js";import{g as B}from"./commonBiz-BtTo-n4b.js";import{h as D,aj as G,k as H,x as W,Q as $,ak as q,f as J}from"./form-designer-C0ARe9Dh.js";import{k as K,r as i,P as M,y as w,m as v,z as r,A as O,u as a,H as s,l as T,G as X,$ as Y,E as h,h as Z}from"./form-create-B86qX0W_.js";const f={getReportLossPage:async t=>await n.get({url:"/scm/mfg/report-loss/page",params:t}),getReportLoss:async t=>await n.get({url:"/scm/mfg/report-loss/get?id="+t}),createReportLoss:async t=>await n.post({url:"/scm/mfg/report-loss/create",data:t}),updateReportLoss:async t=>await n.put({url:"/scm/mfg/report-loss/update",data:t}),deleteReportLoss:async t=>await n.delete({url:"/scm/mfg/report-loss/delete?id="+t}),exportReportLoss:async t=>await n.download({url:"/scm/mfg/report-loss/export-excel",params:t})},ee=K({name:"ReportLossForm",__name:"ReportLossForm",emits:["success"],setup(t,{expose:L,emit:U}){const{t:y}=z(),g=A(),m=i(!1),_=i(""),p=i(!1),b=i(""),k=i([]),o=i({id:void 0,reportId:void 0,workId:void 0,materialId:void 0,materialName:void 0,materialCode:void 0,spec:void 0,lossQuantity:void 0,lossUnit:void 0,remark:void 0}),x=M({}),V=i();L({open:async(u,e)=>{if(m.value=!0,_.value=y("action."+u),b.value=u,Q(),await(async()=>{try{const d=await B();d&&d.length>0&&(k.value=d)}catch{}})(),e){p.value=!0;try{o.value=await f.getReportLoss(e)}finally{p.value=!1}}}});const C=U,I=async()=>{await V.value.validate(),p.value=!0;try{const u=o.value;b.value==="create"?(await f.createReportLoss(u),g.success(y("common.createSuccess"))):(await f.updateReportLoss(u),g.success(y("common.updateSuccess"))),m.value=!1,C("success")}finally{p.value=!1}},Q=()=>{var u;o.value={id:void 0,reportId:void 0,workId:void 0,materialId:void 0,materialName:void 0,materialCode:void 0,spec:void 0,lossQuantity:void 0,lossUnit:void 0,remark:void 0},(u=V.value)==null||u.resetFields()};return(u,e)=>{const d=H,c=G,N=$,F=W,P=D,R=J,S=E,j=q;return v(),w(S,{title:a(_),modelValue:a(m),"onUpdate:modelValue":e[7]||(e[7]=l=>Z(m)?m.value=l:null)},{footer:r(()=>[s(R,{onClick:I,type:"primary",disabled:a(p)},{default:r(()=>e[8]||(e[8]=[h("\u786E \u5B9A")])),_:1},8,["disabled"]),s(R,{onClick:e[6]||(e[6]=l=>m.value=!1)},{default:r(()=>e[9]||(e[9]=[h("\u53D6 \u6D88")])),_:1})]),default:r(()=>[O((v(),w(P,{ref_key:"formRef",ref:V,model:a(o),rules:a(x),"label-width":"100px"},{default:r(()=>[s(c,{label:"\u7269\u6599\u540D\u79F0",prop:"materialName"},{default:r(()=>[s(d,{modelValue:a(o).materialName,"onUpdate:modelValue":e[0]||(e[0]=l=>a(o).materialName=l),placeholder:"\u8BF7\u8F93\u5165\u7269\u6599\u540D\u79F0",disabled:""},null,8,["modelValue"])]),_:1}),s(c,{label:"\u7269\u6599\u7F16\u7801",prop:"materialCode"},{default:r(()=>[s(d,{modelValue:a(o).materialCode,"onUpdate:modelValue":e[1]||(e[1]=l=>a(o).materialCode=l),placeholder:"\u8BF7\u8F93\u5165\u7269\u6599\u7F16\u7801"},null,8,["modelValue"])]),_:1}),s(c,{label:"\u7269\u6599\u89C4\u683C",prop:"spec"},{default:r(()=>[s(d,{modelValue:a(o).spec,"onUpdate:modelValue":e[2]||(e[2]=l=>a(o).spec=l),placeholder:"\u8BF7\u8F93\u5165\u7269\u6599\u89C4\u683C"},null,8,["modelValue"])]),_:1}),s(c,{label:"\u635F\u8017\u6570\u91CF",prop:"lossQuantity"},{default:r(()=>[s(d,{modelValue:a(o).lossQuantity,"onUpdate:modelValue":e[3]||(e[3]=l=>a(o).lossQuantity=l),placeholder:"\u8BF7\u8F93\u5165\u635F\u8017\u6570\u91CF"},null,8,["modelValue"])]),_:1}),s(c,{label:"\u635F\u8017\u5355\u4F4D",prop:"lossUnit"},{default:r(()=>[s(F,{modelValue:a(o).lossUnit,"onUpdate:modelValue":e[4]||(e[4]=l=>a(o).lossUnit=l),placeholder:"\u8BF7\u9009\u62E9\u635F\u8017\u5355\u4F4D",clearable:"",filterable:"",style:{width:"100%"}},{default:r(()=>[(v(!0),T(X,null,Y(a(k),l=>(v(),w(N,{key:l.id,label:l.name,value:l.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),s(c,{label:"\u5907\u6CE8",prop:"remark"},{default:r(()=>[s(d,{modelValue:a(o).remark,"onUpdate:modelValue":e[5]||(e[5]=l=>a(o).remark=l),placeholder:"\u8BF7\u8F93\u5165\u5907\u6CE8"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])),[[j,a(p)]])]),_:1},8,["title","modelValue"])}}});export{f as R,ee as _};
