import{W as r,a as D,d as J,X as Y}from"./index-Byekp3Iv.js";import{_ as Z}from"./Dialog.vue_vue_type_style_index_0_lang-DhV_HFpU.js";import{_ as K}from"./ContentWrap.vue_vue_type_script_setup_true_lang-D3mNNG_R.js";import{_ as L}from"./StockCheckItemForm.vue_vue_type_script_setup_true_lang-DFMq7bML.js";import{h as M,i as N,j as O,aj as Q,k as ee,F as ae,ak as le,a0 as te,$ as se,f as oe}from"./form-designer-C0ARe9Dh.js";import{k as ce,r as u,P as de,c as ue,y as V,m as S,z as t,A as re,H as l,u as a,h as T,C as ie,E as F}from"./form-create-B86qX0W_.js";const n={getStockCheckPage:async s=>await r.get({url:"/erp/stock-check/page",params:s}),getStockCheck:async s=>await r.get({url:"/erp/stock-check/get?id="+s}),createStockCheck:async s=>await r.post({url:"/erp/stock-check/create",data:s}),updateStockCheck:async s=>await r.put({url:"/erp/stock-check/update",data:s}),updateStockCheckStatus:async(s,f)=>await r.put({url:"/erp/stock-check/update-status",params:{id:s,status:f}}),deleteStockCheck:async s=>await r.delete({url:"/erp/stock-check/delete",params:{ids:s.join(",")}}),exportStockCheck:async s=>await r.download({url:"/erp/stock-check/export-excel",params:s})},me=ce({name:"StockCheckForm",__name:"StockCheckForm",emits:["success"],setup(s,{expose:f,emit:j}){const{t:h}=D(),b=J(),i=u(!1),C=u(""),m=u(!1),v=u(""),o=u({id:void 0,customerId:void 0,checkTime:void 0,remark:void 0,fileUrl:"",items:[]}),I=de({checkTime:[{required:!0,message:"\u76D8\u70B9\u65F6\u95F4\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}]}),_=ue(()=>v.value==="detail"),y=u(),w=u("item"),g=u();f({open:async(d,e)=>{if(i.value=!0,C.value=h("action."+d),v.value=d,A(),e){m.value=!0;try{o.value=await n.getStockCheck(e)}finally{m.value=!1}}}});const P=j,R=async()=>{await y.value.validate(),await g.value.validate(),m.value=!0;try{const d=o.value;v.value==="create"?(await n.createStockCheck(d),b.success(h("common.createSuccess"))):(await n.updateStockCheck(d),b.success(h("common.updateSuccess"))),i.value=!1,P("success")}finally{m.value=!1}},A=()=>{var d;o.value={id:void 0,customerId:void 0,checkTime:void 0,remark:void 0,fileUrl:void 0,items:[]},(d=y.value)==null||d.resetFields()};return(d,e)=>{const U=ee,p=Q,k=O,$=ae,q=Y,z=N,E=M,G=se,H=te,W=K,x=oe,X=Z,B=le;return S(),V(X,{title:a(C),modelValue:a(i),"onUpdate:modelValue":e[6]||(e[6]=c=>T(i)?i.value=c:null),width:"1080"},{footer:t(()=>[a(_)?ie("",!0):(S(),V(x,{key:0,onClick:R,type:"primary",disabled:a(m)},{default:t(()=>e[7]||(e[7]=[F(" \u786E \u5B9A ")])),_:1},8,["disabled"])),l(x,{onClick:e[5]||(e[5]=c=>i.value=!1)},{default:t(()=>e[8]||(e[8]=[F("\u53D6 \u6D88")])),_:1})]),default:t(()=>[re((S(),V(E,{ref_key:"formRef",ref:y,model:a(o),rules:a(I),"label-width":"100px",disabled:a(_)},{default:t(()=>[l(z,{gutter:20},{default:t(()=>[l(k,{span:8},{default:t(()=>[l(p,{label:"\u76D8\u70B9\u5355\u53F7",prop:"no"},{default:t(()=>[l(U,{disabled:"",modelValue:a(o).no,"onUpdate:modelValue":e[0]||(e[0]=c=>a(o).no=c),placeholder:"\u4FDD\u5B58\u65F6\u81EA\u52A8\u751F\u6210"},null,8,["modelValue"])]),_:1})]),_:1}),l(k,{span:8},{default:t(()=>[l(p,{label:"\u76D8\u70B9\u65F6\u95F4",prop:"checkTime"},{default:t(()=>[l($,{modelValue:a(o).checkTime,"onUpdate:modelValue":e[1]||(e[1]=c=>a(o).checkTime=c),type:"date","value-format":"x",placeholder:"\u9009\u62E9\u76D8\u70B9\u65F6\u95F4",class:"!w-1/1"},null,8,["modelValue"])]),_:1})]),_:1}),l(k,{span:16},{default:t(()=>[l(p,{label:"\u5907\u6CE8",prop:"remark"},{default:t(()=>[l(U,{type:"textarea",modelValue:a(o).remark,"onUpdate:modelValue":e[2]||(e[2]=c=>a(o).remark=c),rows:1,placeholder:"\u8BF7\u8F93\u5165\u5907\u6CE8"},null,8,["modelValue"])]),_:1})]),_:1}),l(k,{span:8},{default:t(()=>[l(p,{label:"\u9644\u4EF6",prop:"fileUrl"},{default:t(()=>[l(q,{"is-show-tip":!1,modelValue:a(o).fileUrl,"onUpdate:modelValue":e[3]||(e[3]=c=>a(o).fileUrl=c),limit:1},null,8,["modelValue"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model","rules","disabled"])),[[B,a(m)]]),l(W,null,{default:t(()=>[l(H,{modelValue:a(w),"onUpdate:modelValue":e[4]||(e[4]=c=>T(w)?w.value=c:null),class:"-mt-15px -mb-10px"},{default:t(()=>[l(G,{label:"\u76D8\u70B9\u4EA7\u54C1\u6E05\u5355",name:"item"},{default:t(()=>[l(L,{ref_key:"itemFormRef",ref:g,items:a(o).items,disabled:a(_)},null,8,["items","disabled"])]),_:1})]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["title","modelValue"])}}});export{n as S,me as _};
