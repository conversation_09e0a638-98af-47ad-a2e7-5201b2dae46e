import{_ as q,aN as _,aw as T}from"./index-Byekp3Iv.js";import{_ as A}from"./Dialog.vue_vue_type_style_index_0_lang-DhV_HFpU.js";import{_ as G}from"./index.vue_vue_type_script_setup_true_lang-BeMNDf6p.js";import{_ as K}from"./ContentWrap.vue_vue_type_script_setup_true_lang-D3mNNG_R.js";import{k as Q,r as d,P as $,y as w,m as f,z as o,H as e,u as l,Z as J,l as W,G as X,$ as ee,E as p,A as ae,h as I,n as le}from"./form-create-B86qX0W_.js";import{S as te}from"./index-D8pULT6U.js";import{b as oe}from"./formatTime-HVkyL6Kg.js";import{P as re}from"./index-1HLiV7Gt.js";import{h as ne,aj as de,k as ie,x as pe,Q as se,F as me,f as ue,Z as ce,_ as fe,u as ve,ak as ge}from"./form-designer-C0ARe9Dh.js";const be=Q({name:"SaleOrderReturnEnableList",__name:"SaleOrderReturnEnableList",emits:["success"],setup(_e,{expose:z,emit:D}){const h=d([]),y=d(0),v=d(!1),i=d(!1),r=$({pageNo:1,pageSize:10,no:void 0,productId:void 0,orderTime:[],returnEnable:!0}),V=d(),x=d([]),s=d(void 0),m=d(void 0);z({open:async()=>{i.value=!0,await le(),await C(),x.value=await re.getProductSimpleList()}});const E=D,Y=()=>{try{E("success",m.value)}finally{i.value=!1}},k=async()=>{v.value=!0;try{const b=await te.getSaleOrderPage(r);h.value=b.list,y.value=b.total}finally{v.value=!1}},C=()=>{V.value.resetFields(),g()},g=()=>{r.pageNo=1,s.value=void 0,m.value=void 0,k()};return(b,a)=>{const F=ie,u=de,H=se,L=pe,O=me,N=q,c=ue,R=ne,P=K,j=ve,n=fe,B=G,M=A,Z=ge;return f(),w(M,{title:"\u9009\u62E9\u9500\u552E\u8BA2\u5355\uFF08\u4EC5\u5C55\u793A\u53EF\u9000\u8D27\uFF09",modelValue:l(i),"onUpdate:modelValue":a[7]||(a[7]=t=>I(i)?i.value=t:null),appendToBody:!0,scroll:!0,width:"1080"},{footer:o(()=>[e(c,{disabled:!l(m),type:"primary",onClick:Y},{default:o(()=>a[11]||(a[11]=[p("\u786E \u5B9A")])),_:1},8,["disabled"]),e(c,{onClick:a[6]||(a[6]=t=>i.value=!1)},{default:o(()=>a[12]||(a[12]=[p("\u53D6 \u6D88")])),_:1})]),default:o(()=>[e(P,null,{default:o(()=>[e(R,{class:"-mb-15px",model:l(r),ref_key:"queryFormRef",ref:V,inline:!0,"label-width":"68px"},{default:o(()=>[e(u,{label:"\u8BA2\u5355\u5355\u53F7",prop:"no"},{default:o(()=>[e(F,{modelValue:l(r).no,"onUpdate:modelValue":a[0]||(a[0]=t=>l(r).no=t),placeholder:"\u8BF7\u8F93\u5165\u8BA2\u5355\u5355\u53F7",clearable:"",onKeyup:J(g,["enter"]),class:"!w-160px"},null,8,["modelValue"])]),_:1}),e(u,{label:"\u4EA7\u54C1",prop:"productId"},{default:o(()=>[e(L,{modelValue:l(r).productId,"onUpdate:modelValue":a[1]||(a[1]=t=>l(r).productId=t),clearable:"",filterable:"",placeholder:"\u8BF7\u9009\u62E9\u4EA7\u54C1",class:"!w-160px"},{default:o(()=>[(f(!0),W(X,null,ee(l(x),t=>(f(),w(H,{key:t.id,label:t.name,value:t.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(u,{label:"\u8BA2\u5355\u65F6\u95F4",prop:"orderTime"},{default:o(()=>[e(O,{modelValue:l(r).orderTime,"onUpdate:modelValue":a[2]||(a[2]=t=>l(r).orderTime=t),"value-format":"YYYY-MM-DD HH:mm:ss",type:"daterange","start-placeholder":"\u5F00\u59CB\u65E5\u671F","end-placeholder":"\u7ED3\u675F\u65E5\u671F","default-time":[new Date("1 00:00:00"),new Date("1 23:59:59")],class:"!w-160px"},null,8,["modelValue","default-time"])]),_:1}),e(u,null,{default:o(()=>[e(c,{onClick:g},{default:o(()=>[e(N,{icon:"ep:search",class:"mr-5px"}),a[8]||(a[8]=p(" \u641C\u7D22"))]),_:1}),e(c,{onClick:C},{default:o(()=>[e(N,{icon:"ep:refresh",class:"mr-5px"}),a[9]||(a[9]=p(" \u91CD\u7F6E"))]),_:1})]),_:1})]),_:1},8,["model"])]),_:1}),e(P,null,{default:o(()=>[ae((f(),w(l(ce),{data:l(h),"show-overflow-tooltip":!0,stripe:!0},{default:o(()=>[e(n,{align:"center",width:"65"},{default:o(t=>[e(j,{value:t.row.id,modelValue:l(s),"onUpdate:modelValue":a[3]||(a[3]=S=>I(s)?s.value=S:null),onChange:S=>{return U=t.row,void(m.value=U);var U}},{default:o(()=>a[10]||(a[10]=[p(" \xA0 ")])),_:2},1032,["value","modelValue","onChange"])]),_:1}),e(n,{"min-width":"180",label:"\u8BA2\u5355\u5355\u53F7",align:"center",prop:"no"}),e(n,{label:"\u5BA2\u6237",align:"center",prop:"customerName"}),e(n,{label:"\u4EA7\u54C1\u4FE1\u606F",align:"center",prop:"productNames","min-width":"200"}),e(n,{label:"\u8BA2\u5355\u65F6\u95F4",align:"center",prop:"orderTime",formatter:l(oe),width:"120px"},null,8,["formatter"]),e(n,{label:"\u521B\u5EFA\u4EBA",align:"center",prop:"creatorName"}),e(n,{label:"\u603B\u6570\u91CF",align:"center",prop:"totalCount",formatter:l(_)},null,8,["formatter"]),e(n,{label:"\u51FA\u5E93\u6570\u91CF",align:"center",prop:"outCount",formatter:l(_)},null,8,["formatter"]),e(n,{label:"\u9000\u8D27\u6570\u91CF",align:"center",prop:"returnCount",formatter:l(_)},null,8,["formatter"]),e(n,{label:"\u91D1\u989D\u5408\u8BA1",align:"center",prop:"totalProductPrice",formatter:l(T)},null,8,["formatter"]),e(n,{label:"\u542B\u7A0E\u91D1\u989D",align:"center",prop:"totalPrice",formatter:l(T)},null,8,["formatter"])]),_:1},8,["data"])),[[Z,l(v)]]),e(B,{limit:l(r).pageSize,"onUpdate:limit":a[4]||(a[4]=t=>l(r).pageSize=t),page:l(r).pageNo,"onUpdate:page":a[5]||(a[5]=t=>l(r).pageNo=t),total:l(y),onPagination:k},null,8,["limit","page","total"])]),_:1})]),_:1},8,["modelValue"])}}});export{be as _};
