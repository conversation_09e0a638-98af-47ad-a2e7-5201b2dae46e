import{a as A,d as E}from"./index-Byekp3Iv.js";import{_ as G}from"./Dialog.vue_vue_type_style_index_0_lang-DhV_HFpU.js";import{b as H}from"./index-vD1LnedY.js";import{g as M,u as P}from"./index-DBpQoWT7.js";import{h as Q,aj as R,k as D,x as S,Q as $,ak as q,f as B}from"./form-designer-C0ARe9Dh.js";import{k as J,r as m,P as K,y as c,m as n,z as o,A as L,u as l,H as u,l as N,G as O,$ as T,E as _,h as W}from"./form-create-B86qX0W_.js";const X=J({name:"MpUserForm",__name:"UserForm",emits:["success"],setup(Y,{expose:V,emit:y}){const{t:b}=A(),g=E(),d=m(!1),r=m(!1),s=m({id:void 0,nickname:void 0,remark:void 0,tagIds:[]}),I=K({}),i=m(),f=m([]);V({open:async t=>{if(d.value=!0,U(),t){r.value=!0;try{s.value=await M(t)}finally{r.value=!1}}f.value=await H()}});const h=y,w=async()=>{if(i&&await i.value.validate()){r.value=!0;try{await P(s.value),g.success(b("common.updateSuccess")),d.value=!1,h("success")}finally{r.value=!1}}},U=()=>{var t;s.value={id:void 0,nickname:void 0,remark:void 0,tagIds:[]},(t=i.value)==null||t.resetFields()};return(t,e)=>{const v=D,p=R,x=$,F=S,C=Q,k=B,j=G,z=q;return n(),c(j,{modelValue:l(d),"onUpdate:modelValue":e[4]||(e[4]=a=>W(d)?d.value=a:null),title:"\u4FEE\u6539"},{footer:o(()=>[u(k,{disabled:l(r),type:"primary",onClick:w},{default:o(()=>e[5]||(e[5]=[_("\u786E \u5B9A")])),_:1},8,["disabled"]),u(k,{onClick:e[3]||(e[3]=a=>d.value=!1)},{default:o(()=>e[6]||(e[6]=[_("\u53D6 \u6D88")])),_:1})]),default:o(()=>[L((n(),c(C,{ref_key:"formRef",ref:i,model:l(s),rules:l(I),"label-width":"80px"},{default:o(()=>[u(p,{label:"\u6635\u79F0",prop:"nickname"},{default:o(()=>[u(v,{modelValue:l(s).nickname,"onUpdate:modelValue":e[0]||(e[0]=a=>l(s).nickname=a),placeholder:"\u8BF7\u8F93\u5165\u6635\u79F0"},null,8,["modelValue"])]),_:1}),u(p,{label:"\u5907\u6CE8",prop:"remark"},{default:o(()=>[u(v,{modelValue:l(s).remark,"onUpdate:modelValue":e[1]||(e[1]=a=>l(s).remark=a),placeholder:"\u8BF7\u8F93\u5165\u5907\u6CE8"},null,8,["modelValue"])]),_:1}),u(p,{label:"\u6807\u7B7E",prop:"tagIds"},{default:o(()=>[u(F,{modelValue:l(s).tagIds,"onUpdate:modelValue":e[2]||(e[2]=a=>l(s).tagIds=a),clearable:"",multiple:"",placeholder:"\u8BF7\u9009\u62E9\u6807\u7B7E"},{default:o(()=>[(n(!0),N(O,null,T(l(f),a=>(n(),c(x,{key:a.tagId,label:a.name,value:a.tagId},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])),[[z,l(r)]])]),_:1},8,["modelValue"])}}});export{X as _};
