<template>
	<view class="FormulaListItem">
		<uni-swipe-action>
			<uni-swipe-action-item :right-options="swipeOptions" @click="handleSwipeAction">
				<view class="item-container">
					<view class="main-content" @click.stop="handleContentClick">
						<!-- Row 1: 配方名称 -->
						<view class="title-row">
							<view class="product-name text-ellipsis">{{ item.name || item.productName || '未知配方' }}</view>
							<view class="formula-status" :class="'status-' + item.status">{{ getStatusLabel(item.status) }}</view>
						</view>

						<!-- Row 2: 公司名称 -->
						<view class="info-row" v-if="item.companyName">
							<text class="label">公司:</text>
							<text class="value text-ellipsis">{{ item.companyName }}</text>
						</view>

						<!-- Row 3: NPK -->
						<view class="info-row important-row" v-if="formattedNpk">
							<text class="label">NPK:</text>
							<text class="value important-value">{{ formattedNpk }}</text>
						</view>

						<!-- Row 4: 其他成分 -->
						<view class="info-row" v-if="formattedOtherElements">
							<text class="label">其他成分:</text>
							<text class="value text-ellipsis">{{ formattedOtherElements }}</text>
						</view>

						<!-- Row 5: 总成本 -->
						<view class="info-row important-row">
							<text class="label">总成本:</text>
							<text class="value cost-value">{{ item.totalCost !== null ? item.totalCost : '--' }}</text>
						</view>
						
						<!-- 版本 -->
						<view class="info-row" v-if="item.version">
							<text class="label">版本:</text>
							<text class="value">{{ item.version }}</text>
						</view>

						<!-- Row 6: 理化指标 -->
						<view class="info-row" v-if="item.density !== null">
							<text class="label">密度:</text>
							<text class="value">{{ item.density }}</text>
						</view>
						<view class="info-row" v-if="item.ph !== null">
							<text class="label">PH:</text>
							<text class="value">{{ item.ph }}</text>
						</view>

						<!-- Row 7: 时间 和 更新人 -->
						<view class="info-row footer-row">
							<text class="label">{{ item.updateTime ? '更新于:' : '创建于:' }}</text>
							<text class="value">{{ formatTime(item.updateTime || item.createTime) }}</text>
						</view>
					</view>
				</view>
			</uni-swipe-action-item>
		</uni-swipe-action>
	</view>
</template>

<script>
import { getDictLabel, getBatchDictOptions,DICT_TYPE } from '../../../../../../utils/dict';

export default {
	name: "FormulaListItem",
	props: {
		item: {
			type: Object,
			default: () => ({})
		},
		// approveStatusOptions 仍然保留，以防未来需要在其他地方显示转换后的状态文本
		approveStatusOptions: {
			type: Array,
			default: () => [
				{ label: '待提交', value: '0' },
				{ label: '待审批', value: '1' },
				{ label: '审批中', value: '2' },
				{ label: '通过', value: '3' },
				{ label: '不通过', value: '4' }
			]
		}
	},
	data() {
		return {
			baseOptions: [
				{
					text: '删除',
					style: {
						backgroundColor: '#dd524d'
					}
				}
			],
			isActionTriggered: false, // 添加标志跟踪操作触发状态
			// 字典数据
			dictOptions: {
				medium_trace_element: [],
				product_name_abbr: [],
				approve_status: [] // 添加审批状态字典
			}
		};
	},
	computed: {
		swipeOptions() {
			// 根据item状态动态计算滑动选项
			const options = [...this.baseOptions];
			if (this.item.status == 0 && this.$auth.hasPermi('quote:formula:approve')) {
				options.push({
					text: '提交',
					style: {
						backgroundColor: '#55aaff'
					}
				});
			}else if(this.item.status == 1 &&this.$auth.hasPermi('quote:formula:approve')){
				options.push({
					text:'审批',
					style:{
						backgroundColor:'#0055ff'
					}
				})
			}else if(this.item.status >= 3){
				options.shift()
			}
			return options;
		},
		formattedNpk() {
			if (!this.item || !this.item.npk) return '';
			const npk = this.item.npk;
			const parts = [];
			if (npk.N) parts.push(`N: ${npk.N}${npk.n_unit || ''}`);
			if (npk.P) parts.push(`P: ${npk.P}${npk.p_unit || ''}`);
			if (npk.K) parts.push(`K: ${npk.K}${npk.k_unit || ''}`);
			return parts.join(', ');
		},
		formattedOtherElements() {
			if (!this.item) return '';
			const parts = [];

			// 处理中微量元素，使用字典转换
			if (this.item.microElement && this.item.microElement.length > 0) {
				this.item.microElement.forEach(el => {
					if (el.element && el.quantity) {
						const elementLabel = getDictLabel(this.dictOptions.medium_trace_element, el.element);
						parts.push(`${elementLabel}: ${el.quantity}${el.unit || ''}`);
					}
				});
			}

			// 处理其他原料，使用字典转换
			if (this.item.otherMaterial && this.item.otherMaterial.length > 0) {
				this.item.otherMaterial.forEach(el => {
					if (el.element && el.quantity) {
						const elementLabel = getDictLabel(this.dictOptions.product_name_abbr, el.element);
						parts.push(`${elementLabel}: ${el.quantity}${el.unit || ''}`);
					}
				});
			}

			// 处理元素含量（这部分通常已经是可读的元素符号）
			if (this.item.elements) {
				for (const key in this.item.elements) {
					if (Object.hasOwnProperty.call(this.item.elements, key)) {
						parts.push(`${key}: ${this.item.elements[key]}`);
					}
				}
			}
			return parts.join(', ') || '无';
		}
	},
	methods: {
		// formatApproveStatus 方法，优先使用字典数据
		formatApproveStatus(statusValue) {
			// 优先使用字典数据
			if (this.dictOptions.approve_status && this.dictOptions.approve_status.length > 0) {
				return getDictLabel(this.dictOptions.approve_status, statusValue);
			}
			// 备用方案：使用props中的approveStatusOptions
			const statusOption = this.approveStatusOptions.find(opt => opt.value == statusValue);
			return statusOption ? statusOption.label : (statusValue !== null ? `状态(${statusValue})` : '未知');
		},
		handleSwipeAction(e) {
			// 标记操作已触发
			this.isActionTriggered = true;
			// 设置一个延迟，确保标志在点击事件处理后重置
			setTimeout(() => {
				this.isActionTriggered = false;
			}, 300);
			
			if (e.content.text === '删除') {
				this.$emit('delete', this.item);
			} else if (e.content.text === '提交') {
				this.$emit('submit', this.item);
			}else if(e.content.text === '审批') {
				this.$emit('approve',this.item)
			}
		},
		// 处理内容区域点击
		handleContentClick() {
			// 只有当没有触发滑动操作时，才触发更新事件
			if (!this.isActionTriggered && this.$auth.hasPermi('quote:formula:update')) {
				this.$emit('update', this.item);
			}
		},
		formatTime(timestamp) {
			if (!timestamp) return '--';
			const date = new Date(timestamp);
			const Y = date.getFullYear() + '-';
			const M = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1) + '-';
			const D = (date.getDate() < 10 ? '0' + date.getDate() : date.getDate());
			return Y + M + D;
		},
		// 获取状态标签
		getStatusLabel(status) {
			// 优先使用字典数据，如果字典数据为空则使用props中的备用数据
			if (this.dictOptions.approve_status && this.dictOptions.approve_status.length > 0) {
				return getDictLabel(this.dictOptions.approve_status, status);
			}
			// 备用方案：使用props中的approveStatusOptions
			const statusOption = this.approveStatusOptions.find(option => option.value === status.toString());
			return statusOption ? statusOption.label : '未知状态';
		},

		// 初始化字典数据
		async initDictData() {
			try {
				const dictData = await getBatchDictOptions([
					DICT_TYPE.MEDIUM_TRACE_ELEMENT,
					DICT_TYPE.PRODUCT_NAME_ABBR,
					DICT_TYPE.APPROVE_STATUS
				]);

				this.dictOptions = {
					medium_trace_element: dictData.medium_trace_element || [],
					product_name_abbr: dictData.product_name_abbr || [],
					approve_status: dictData.approve_status || []
				};
			} catch (error) {
				console.error('获取字典数据失败:', error);
				// 如果获取失败，保持空数组，getDictLabel会返回原始值
				this.dictOptions = {
					medium_trace_element: [],
					product_name_abbr: [],
					approve_status: []
				};
			}
		}
	},

	mounted() {
		// 初始化字典数据
		this.initDictData();
	}
};
</script>

<style>
.FormulaListItem {
	width: 100%;
	/* background-color: #fff; */
	border-bottom: none;
	box-sizing: border-box;
	max-width: 100%;
	overflow-x: hidden;
	flex: 0 0 auto;
	border-radius: 8px;
	border: 1px solid #e8e8e8;
}

.item-container {
	background-color: #fff;
	width: 100%;
	overflow: hidden;
	padding: 12px 16px;
	box-sizing: border-box;
	max-width: 100vw;
	position: relative;
}

.main-content {
	width: 100%;
	overflow: hidden;
	box-sizing: border-box;
	max-width: 100%;
	flex-shrink: 0;
	display: block;
}

.title-row {
	display: flex;
	flex-direction: row;
	align-items: center;
	justify-content: space-between;
	width: 100%;
	margin-bottom: 8px;
}

.product-name {
	font-size: 18px;
	font-weight: bold;
	color: #303133;
	flex: 1;
	line-height: 1.4;
}

.formula-status {
	font-size: 13px;
	padding: 4px 8px;
	border-radius: 12px;
	margin-left: 12px;
	white-space: nowrap;
	font-weight: 500;
}



.info-row {
	display: flex;
	align-items: flex-start;
	margin-top: 6px;
	font-size: 14px;
	width: 100%;
	overflow: hidden;
	line-height: 1.6;
	box-sizing: border-box;
	flex-wrap: wrap;
	max-width: 100%;
}

.footer-row {
	margin-top: 8px;
	align-items: center;
	padding-top: 6px;
	border-top: 1px solid #f5f5f5;
}

.label {
	color: #666666;
	margin-right: 6px;
	flex-shrink: 0;
	white-space: nowrap;
	min-width: 55px;
	font-size: 13px;
	width: 65px;
	text-align: left;
	font-weight: 500;
}

.value {
	color: #333333;
	margin-right: 8px;
	font-size: 14px;
	flex: 1;
	min-width: 0;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
	max-width: calc(100% - 65px); /* 为标签留出空间 */
	width: calc(100% - 70px);
	text-align: left;
	font-weight: 400;
}

.text-ellipsis {
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
	min-width: 0;
	max-width: 100%;
}

/* 重要信息行样式 */
.important-row {
	background-color: #fafafa;
	padding: 4px 8px;
	margin: 6px -8px;
	border-radius: 6px;
}

.important-value {
	/* color: #409eff !important; */
	font-weight: 600 !important;
}

.cost-value {
	/* color: #e6a23c !important; */
	font-weight: 600 !important;
	font-size: 15px !important;
}

/* 增强状态样式的对比度 */
.status-0 {
	background-color: #f4f4f5;
	color: #909399;
	border: 1px solid #e4e7ed;
}

.status-1 {
	background-color: #e8f4ff;
	color: #409eff;
	border: 1px solid #b3d8ff;
}

.status-2 {
	background-color: #fdf6ec;
	color: #e6a23c;
	border: 1px solid #f5dab1;
}

.status-3 {
	background-color: #f0f9eb;
	color: #67c23a;
	border: 1px solid #c2e7b0;
}

.status-4 {
	background-color: #e1f3d8;
	color: #67c23a;
	border: 1px solid #c2e7b0;
}

.status-5 {
	background-color: #fef0f0;
	color: #f56c6c;
	border: 1px solid #fbc4c4;
}
</style> 