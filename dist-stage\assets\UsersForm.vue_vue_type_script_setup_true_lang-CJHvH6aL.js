import{W as n,a as E,d as H}from"./index-Byekp3Iv.js";import{_ as R}from"./Dialog.vue_vue_type_style_index_0_lang-DhV_HFpU.js";import{h as W,aj as B,k as G,s as J,u as K,F as L,ak as M,f as N}from"./form-designer-C0ARe9Dh.js";import{k as O,r as c,P as Q,y as h,m as w,z as s,A as T,u as l,H as o,E as v,h as X}from"./form-create-B86qX0W_.js";const V={getUsersPage:async t=>await n.get({url:"/system/users/page",params:t}),getUsers:async t=>await n.get({url:"/system/users/get?id="+t}),createUsers:async t=>await n.post({url:"/system/users/create",data:t}),updateUsers:async t=>await n.put({url:"/system/users/update",data:t}),deleteUsers:async t=>await n.delete({url:"/system/users/delete?id="+t}),exportUsers:async t=>await n.download({url:"/system/users/export-excel",params:t})},Y=O({name:"UsersForm",__name:"UsersForm",emits:["success"],setup(t,{expose:x,emit:D}){const{t:f}=E(),U=H(),i=c(!1),_=c(""),p=c(!1),b=c(""),a=c({id:void 0,username:void 0,nickname:void 0,remark:void 0,deptId:void 0,postIds:void 0,email:void 0,mobile:void 0,sex:void 0,avatar:void 0,status:void 0,loginIp:void 0,loginDate:void 0}),P=Q({username:[{required:!0,message:"\u7528\u6237\u8D26\u53F7\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],nickname:[{required:!0,message:"\u7528\u6237\u6635\u79F0\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],status:[{required:!0,message:"\u5E10\u53F7\u72B6\u6001\uFF080\u6B63\u5E38 1\u505C\u7528\uFF09\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}]}),g=c();x({open:async(r,e)=>{if(i.value=!0,_.value=f("action."+r),b.value=r,C(),e){p.value=!0;try{a.value=await V.getUsers(e)}finally{p.value=!1}}}});const F=D,q=async()=>{await g.value.validate(),p.value=!0;try{const r=a.value;b.value==="create"?(await V.createUsers(r),U.success(f("common.createSuccess"))):(await V.updateUsers(r),U.success(f("common.updateSuccess"))),i.value=!1,F("success")}finally{p.value=!1}},C=()=>{var r;a.value={id:void 0,username:void 0,nickname:void 0,remark:void 0,deptId:void 0,postIds:void 0,email:void 0,mobile:void 0,sex:void 0,avatar:void 0,status:void 0,loginIp:void 0,loginDate:void 0},(r=g.value)==null||r.resetFields()};return(r,e)=>{const m=G,u=B,y=K,I=J,S=L,j=W,k=N,z=R,A=M;return w(),h(z,{title:l(_),modelValue:l(i),"onUpdate:modelValue":e[13]||(e[13]=d=>X(i)?i.value=d:null)},{footer:s(()=>[o(k,{onClick:q,type:"primary",disabled:l(p)},{default:s(()=>e[16]||(e[16]=[v("\u786E \u5B9A")])),_:1},8,["disabled"]),o(k,{onClick:e[12]||(e[12]=d=>i.value=!1)},{default:s(()=>e[17]||(e[17]=[v("\u53D6 \u6D88")])),_:1})]),default:s(()=>[T((w(),h(j,{ref_key:"formRef",ref:g,model:l(a),rules:l(P),"label-width":"100px"},{default:s(()=>[o(u,{label:"\u7528\u6237\u8D26\u53F7",prop:"username"},{default:s(()=>[o(m,{modelValue:l(a).username,"onUpdate:modelValue":e[0]||(e[0]=d=>l(a).username=d),placeholder:"\u8BF7\u8F93\u5165\u7528\u6237\u8D26\u53F7"},null,8,["modelValue"])]),_:1}),o(u,{label:"\u7528\u6237\u6635\u79F0",prop:"nickname"},{default:s(()=>[o(m,{modelValue:l(a).nickname,"onUpdate:modelValue":e[1]||(e[1]=d=>l(a).nickname=d),placeholder:"\u8BF7\u8F93\u5165\u7528\u6237\u6635\u79F0"},null,8,["modelValue"])]),_:1}),o(u,{label:"\u5907\u6CE8",prop:"remark"},{default:s(()=>[o(m,{modelValue:l(a).remark,"onUpdate:modelValue":e[2]||(e[2]=d=>l(a).remark=d),placeholder:"\u8BF7\u8F93\u5165\u5907\u6CE8"},null,8,["modelValue"])]),_:1}),o(u,{label:"\u90E8\u95E8ID",prop:"deptId"},{default:s(()=>[o(m,{modelValue:l(a).deptId,"onUpdate:modelValue":e[3]||(e[3]=d=>l(a).deptId=d),placeholder:"\u8BF7\u8F93\u5165\u90E8\u95E8ID"},null,8,["modelValue"])]),_:1}),o(u,{label:"\u5C97\u4F4D\u7F16\u53F7\u6570\u7EC4",prop:"postIds"},{default:s(()=>[o(m,{modelValue:l(a).postIds,"onUpdate:modelValue":e[4]||(e[4]=d=>l(a).postIds=d),placeholder:"\u8BF7\u8F93\u5165\u5C97\u4F4D\u7F16\u53F7\u6570\u7EC4"},null,8,["modelValue"])]),_:1}),o(u,{label:"\u7528\u6237\u90AE\u7BB1",prop:"email"},{default:s(()=>[o(m,{modelValue:l(a).email,"onUpdate:modelValue":e[5]||(e[5]=d=>l(a).email=d),placeholder:"\u8BF7\u8F93\u5165\u7528\u6237\u90AE\u7BB1"},null,8,["modelValue"])]),_:1}),o(u,{label:"\u624B\u673A\u53F7\u7801",prop:"mobile"},{default:s(()=>[o(m,{modelValue:l(a).mobile,"onUpdate:modelValue":e[6]||(e[6]=d=>l(a).mobile=d),placeholder:"\u8BF7\u8F93\u5165\u624B\u673A\u53F7\u7801"},null,8,["modelValue"])]),_:1}),o(u,{label:"\u7528\u6237\u6027\u522B",prop:"sex"},{default:s(()=>[o(I,{modelValue:l(a).sex,"onUpdate:modelValue":e[7]||(e[7]=d=>l(a).sex=d)},{default:s(()=>[o(y,{value:"1"},{default:s(()=>e[14]||(e[14]=[v("\u8BF7\u9009\u62E9\u5B57\u5178\u751F\u6210")])),_:1})]),_:1},8,["modelValue"])]),_:1}),o(u,{label:"\u5934\u50CF\u5730\u5740",prop:"avatar"},{default:s(()=>[o(m,{modelValue:l(a).avatar,"onUpdate:modelValue":e[8]||(e[8]=d=>l(a).avatar=d),placeholder:"\u8BF7\u8F93\u5165\u5934\u50CF\u5730\u5740"},null,8,["modelValue"])]),_:1}),o(u,{label:"\u5E10\u53F7\u72B6\u6001\uFF080\u6B63\u5E38 1\u505C\u7528\uFF09",prop:"status"},{default:s(()=>[o(I,{modelValue:l(a).status,"onUpdate:modelValue":e[9]||(e[9]=d=>l(a).status=d)},{default:s(()=>[o(y,{value:"1"},{default:s(()=>e[15]||(e[15]=[v("\u8BF7\u9009\u62E9\u5B57\u5178\u751F\u6210")])),_:1})]),_:1},8,["modelValue"])]),_:1}),o(u,{label:"\u6700\u540E\u767B\u5F55IP",prop:"loginIp"},{default:s(()=>[o(m,{modelValue:l(a).loginIp,"onUpdate:modelValue":e[10]||(e[10]=d=>l(a).loginIp=d),placeholder:"\u8BF7\u8F93\u5165\u6700\u540E\u767B\u5F55IP"},null,8,["modelValue"])]),_:1}),o(u,{label:"\u6700\u540E\u767B\u5F55\u65F6\u95F4",prop:"loginDate"},{default:s(()=>[o(S,{modelValue:l(a).loginDate,"onUpdate:modelValue":e[11]||(e[11]=d=>l(a).loginDate=d),type:"date","value-format":"x",placeholder:"\u9009\u62E9\u6700\u540E\u767B\u5F55\u65F6\u95F4"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])),[[A,l(p)]])]),_:1},8,["title","modelValue"])}}});export{V as U,Y as _};
