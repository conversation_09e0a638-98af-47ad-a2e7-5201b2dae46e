import{d as H,_ as g}from"./index-Byekp3Iv.js";import{K as O}from"./index-sJcIszpZ.js";import{K as S}from"./index-1_dPwbTU.js";import{o as Q,f as R,aj as B,l as J,h as W,a2 as X,a4 as Y,a3 as Z,aQ as ee}from"./form-designer-C0ARe9Dh.js";import{k as ae,i as le,c as se,r as M,e as te,l as d,m as i,v as s,E as x,H as o,z as u,u as f,y,C,F as v,G as j,$ as K,g as L}from"./form-create-B86qX0W_.js";const ne={class:"mb-20px"},ie={class:"mb-20px flex justify-between items-center"},oe={class:"text-16px font-bold flex items-center"},ue={class:"segment-settings mb-20px"},pe={class:"mb-10px"},me={class:"file-selector mb-10px"},de={class:"flex items-center cursor-pointer"},ve={key:0,class:"ml-5px text-gray-500 text-12px"},ce={key:0,class:"ml-5px text-gray-500 text-12px"},re={key:1,class:"text-gray-400"},xe={class:"file-preview bg-gray-50 p-15px rounded-md max-h-600px overflow-y-auto"},ge={key:0,class:"flex justify-center items-center py-20px"},fe={class:"text-gray-500 text-12px mb-5px"},ye={class:"bg-white p-10px rounded-md"},ke={class:"mt-20px flex justify-between"},we=ae({__name:"SplitStep",props:{modelValue:{type:Object,required:!0}},emits:["update:modelValue"],setup(z,{emit:D}){const E=z,I=D,k=H(),V=le("parent",null),e=se({get:()=>E.modelValue,set:t=>I("update:modelValue",t)}),w=M(!1),a=M(null),b=M(!1),h=async t=>{if(t&&t.url){w.value=!0;try{t.segments=await O.splitContent(t.url,e.value.segmentMaxTokens)}catch{}finally{w.value=!1}}else k.warning("\u6587\u4EF6 URL \u4E0D\u5B58\u5728")},N=async()=>{!a.value&&e.value.list&&e.value.list.length>0&&(a.value=e.value.list[0]),a.value?await h(a.value):k.warning("\u8BF7\u5148\u9009\u62E9\u6587\u4EF6")},P=()=>{var l,c;const t=V||((l=L())==null?void 0:l.parent);t&&typeof((c=t.exposed)==null?void 0:c.goToPrevStep)=="function"&&t.exposed.goToPrevStep()},U=async()=>{var t,l,c;if((t=a==null?void 0:a.value)!=null&&t.segments&&a.value.segments.length!==0){b.value=!0;try{if(e.value.id)await S.updateKnowledgeDocument({id:e.value.id,segmentMaxTokens:e.value.segmentMaxTokens});else{const T=await S.createKnowledgeDocumentList({knowledgeId:e.value.knowledgeId,segmentMaxTokens:e.value.segmentMaxTokens,list:e.value.list.map(r=>({name:r.name,url:r.url}))});e.value.list.forEach((r,_)=>{r.id=T[_]})}const p=V||((l=L())==null?void 0:l.parent);p&&typeof((c=p.exposed)==null?void 0:c.goToNextStep)=="function"&&p.exposed.goToNextStep()}catch{}finally{b.value=!1}}else k.warning("\u8BF7\u5148\u9884\u89C8\u5206\u6BB5\u5185\u5BB9")};return te(async()=>{e.value.segmentMaxTokens||(e.value.segmentMaxTokens=500),!a.value&&e.value.list&&e.value.list.length>0&&(a.value=e.value.list[0]),a.value&&await h(a.value)}),(t,l)=>{const c=Q,p=R,T=J,r=B,_=W,$=Y,q=Z,A=X,F=ee;return i(),d("div",null,[s("div",ne,[s("div",ie,[s("div",oe,[l[1]||(l[1]=x(" \u5206\u6BB5\u8BBE\u7F6E ")),o(c,{content:"\u7CFB\u7EDF\u4F1A\u81EA\u52A8\u5C06\u6587\u6863\u5185\u5BB9\u5206\u5272\u6210\u591A\u4E2A\u6BB5\u843D\uFF0C\u60A8\u53EF\u4EE5\u6839\u636E\u9700\u8981\u8C03\u6574\u5206\u6BB5\u65B9\u5F0F\u548C\u5185\u5BB9\u3002",placement:"top"},{default:u(()=>[o(f(g),{icon:"ep:warning",class:"ml-5px text-gray-400"})]),_:1})]),s("div",null,[o(p,{type:"primary",plain:"",size:"small",onClick:N},{default:u(()=>l[2]||(l[2]=[x(" \u9884\u89C8\u5206\u6BB5 ")])),_:1})])]),s("div",ue,[o(_,{"label-width":"120px"},{default:u(()=>[o(r,{label:"\u6700\u5927 Token \u6570"},{default:u(()=>[o(T,{modelValue:e.value.segmentMaxTokens,"onUpdate:modelValue":l[0]||(l[0]=n=>e.value.segmentMaxTokens=n),min:1,max:2048},null,8,["modelValue"])]),_:1})]),_:1})])]),s("div",pe,[l[4]||(l[4]=s("div",{class:"text-16px font-bold mb-10px"},"\u5206\u6BB5\u9884\u89C8",-1)),s("div",me,[e.value.list&&e.value.list.length>0?(i(),y(A,{key:0,trigger:"click"},{dropdown:u(()=>[o(q,null,{default:u(()=>[(i(!0),d(j,null,K(e.value.list,(n,m)=>(i(),y($,{key:m,onClick:be=>(async G=>{a.value=e.value.list[G],await h(a.value)})(m)},{default:u(()=>[x(v(n.name)+" ",1),n.segments?(i(),d("span",ce," ("+v(n.segments.length)+"\u4E2A\u5206\u7247) ",1)):C("",!0)]),_:2},1032,["onClick"]))),128))]),_:1})]),default:u(()=>{var n,m;return[s("div",de,[o(f(g),{icon:"ep:document",class:"text-danger mr-5px"}),s("span",null,v(((n=a.value)==null?void 0:n.name)||"\u8BF7\u9009\u62E9\u6587\u4EF6"),1),(m=a.value)!=null&&m.segments?(i(),d("span",ve," ("+v(a.value.segments.length)+"\u4E2A\u5206\u7247) ",1)):C("",!0),o(f(g),{icon:"ep:arrow-down",class:"ml-5px"})])]}),_:1})):(i(),d("div",re,"\u6682\u65E0\u4E0A\u4F20\u6587\u4EF6"))]),s("div",xe,[w.value?(i(),d("div",ge,[o(f(g),{icon:"ep:loading",class:"is-loading"}),l[3]||(l[3]=s("span",{class:"ml-10px"},"\u6B63\u5728\u52A0\u8F7D\u5206\u6BB5\u5185\u5BB9...",-1))])):a.value&&a.value.segments&&a.value.segments.length>0?(i(!0),d(j,{key:1},K(a.value.segments,(n,m)=>(i(),d("div",{key:m,class:"mb-10px"},[s("div",fe," \u5206\u7247-"+v(m+1)+" \xB7 "+v(n.contentLength||0)+" \u5B57\u7B26\u6570 \xB7 "+v(n.tokens||0)+" Token ",1),s("div",ye,v(n.content),1)]))),128)):(i(),y(F,{key:2,description:"\u6682\u65E0\u9884\u89C8\u5185\u5BB9"}))])]),s("div",ke,[s("div",null,[e.value.id?C("",!0):(i(),y(p,{key:0,onClick:P},{default:u(()=>l[5]||(l[5]=[x("\u4E0A\u4E00\u6B65")])),_:1}))]),s("div",null,[o(p,{type:"primary",loading:b.value,onClick:U},{default:u(()=>l[6]||(l[6]=[x(" \u4FDD\u5B58\u5E76\u5904\u7406 ")])),_:1},8,["loading"])])])])}}});export{we as _};
