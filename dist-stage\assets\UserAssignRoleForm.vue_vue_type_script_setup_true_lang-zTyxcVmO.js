import{a as C,d as S}from"./index-Byekp3Iv.js";import{_ as j}from"./Dialog.vue_vue_type_style_index_0_lang-DhV_HFpU.js";import{c as z,d as E}from"./index-Nk6p0_SU.js";import{g as G}from"./index-CU_lrk1T.js";import{h as H,aj as D,k as Q,x as $,Q as q,ak as B,f as J}from"./form-designer-C0ARe9Dh.js";import{k as K,r as n,y as c,m as i,z as u,A as L,u as s,H as o,l as M,G as N,$ as O,E as _,h as P}from"./form-create-B86qX0W_.js";const T=K({name:"SystemUserAssignRoleForm",__name:"UserAssignRoleForm",emits:["success"],setup(W,{expose:y,emit:V}){const{t:b}=C(),I=S(),m=n(!1),r=n(!1),e=n({id:-1,nickname:"",username:"",roleIds:[]}),t=n(),v=n([]);y({open:async d=>{m.value=!0,h(),e.value.id=d.id,e.value.username=d.username,e.value.nickname=d.nickname,r.value=!0;try{e.value.roleIds=await z(d.id)}finally{r.value=!1}v.value=await G()}});const w=V,U=async()=>{if(t&&await t.value.validate()){r.value=!0;try{await E({userId:e.value.id,roleIds:e.value.roleIds}),I.success(b("common.updateSuccess")),m.value=!1,w("success",!0)}finally{r.value=!1}}},h=()=>{var d;e.value={id:-1,nickname:"",username:"",roleIds:[]},(d=t.value)==null||d.resetFields()};return(d,a)=>{const p=Q,f=D,x=q,A=$,g=H,k=J,F=j,R=B;return i(),c(F,{modelValue:s(m),"onUpdate:modelValue":a[4]||(a[4]=l=>P(m)?m.value=l:null),title:"\u5206\u914D\u89D2\u8272"},{footer:u(()=>[o(k,{disabled:s(r),type:"primary",onClick:U},{default:u(()=>a[5]||(a[5]=[_("\u786E \u5B9A")])),_:1},8,["disabled"]),o(k,{onClick:a[3]||(a[3]=l=>m.value=!1)},{default:u(()=>a[6]||(a[6]=[_("\u53D6 \u6D88")])),_:1})]),default:u(()=>[L((i(),c(g,{ref_key:"formRef",ref:t,model:s(e),"label-width":"80px"},{default:u(()=>[o(f,{label:"\u7528\u6237\u540D\u79F0"},{default:u(()=>[o(p,{modelValue:s(e).username,"onUpdate:modelValue":a[0]||(a[0]=l=>s(e).username=l),disabled:!0},null,8,["modelValue"])]),_:1}),o(f,{label:"\u7528\u6237\u6635\u79F0"},{default:u(()=>[o(p,{modelValue:s(e).nickname,"onUpdate:modelValue":a[1]||(a[1]=l=>s(e).nickname=l),disabled:!0},null,8,["modelValue"])]),_:1}),o(f,{label:"\u89D2\u8272"},{default:u(()=>[o(A,{modelValue:s(e).roleIds,"onUpdate:modelValue":a[2]||(a[2]=l=>s(e).roleIds=l),multiple:"",placeholder:"\u8BF7\u9009\u62E9\u89D2\u8272"},{default:u(()=>[(i(!0),M(N,null,O(s(v),l=>(i(),c(x,{key:l.id,label:l.name,value:l.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"])),[[R,s(r)]])]),_:1},8,["modelValue"])}}});export{T as _};
