import{H as u,c as m}from"./index-Byekp3Iv.js";import"./Descriptions.vue_vue_type_style_index_0_scoped_74d4336e_lang-DL7daZUP.js";import{D as s}from"./DescriptionsItemLabel-DuFHeS62.js";import{an as i,am as b}from"./form-designer-C0ARe9Dh.js";import{k as p,y as _,m as d,z as e,H as l,E as t,F as c,u as a,B as f}from"./form-create-B86qX0W_.js";import"./el-collapse-transition-l0sNRNKZ.js";import"./DictTag.vue_vue_type_script_lang-DdZ_pRVv.js";const v=m(p({__name:"UserAccountInfo",props:{user:{},wallet:{},column:{default:2}},setup:g=>(n,w)=>{const o=i,r=b;return d(),_(r,{class:f({"kefu-descriptions":n.column===1}),column:n.column},{default:e(()=>[l(o,null,{label:e(()=>[l(a(s),{icon:"svg-icon:member_level",label:" \u7B49\u7EA7 "})]),default:e(()=>[t(" "+c(n.user.levelName||"\u65E0"),1)]),_:1}),l(o,null,{label:e(()=>[l(a(s),{icon:"ep:suitcase",label:" \u6210\u957F\u503C "})]),default:e(()=>[t(" "+c(n.user.experience||0),1)]),_:1}),l(o,null,{label:e(()=>[l(a(s),{icon:"ep:coin",label:" \u5F53\u524D\u79EF\u5206 "})]),default:e(()=>[t(" "+c(n.user.point||0),1)]),_:1}),l(o,null,{label:e(()=>[l(a(s),{icon:"ep:coin",label:" \u603B\u79EF\u5206 "})]),default:e(()=>[t(" "+c(n.user.totalPoint||0),1)]),_:1}),l(o,null,{label:e(()=>[l(a(s),{icon:"svg-icon:member_balance",label:" \u5F53\u524D\u4F59\u989D "})]),default:e(()=>[t(" "+c(a(u)(n.wallet.balance||0)),1)]),_:1}),l(o,null,{label:e(()=>[l(a(s),{icon:"svg-icon:member_expenditure_balance",label:" \u652F\u51FA\u91D1\u989D "})]),default:e(()=>[t(" "+c(a(u)(n.wallet.totalExpense||0)),1)]),_:1}),l(o,null,{label:e(()=>[l(a(s),{icon:"svg-icon:member_recharge_balance",label:" \u5145\u503C\u91D1\u989D "})]),default:e(()=>[t(" "+c(a(u)(n.wallet.totalRecharge||0)),1)]),_:1})]),_:1},8,["class","column"])}}),[["__scopeId","data-v-cda83eb1"]]);export{v as default};
