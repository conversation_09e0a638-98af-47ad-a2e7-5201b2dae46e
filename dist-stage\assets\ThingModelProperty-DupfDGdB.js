import{aC as x,M as R,c as Y}from"./index-Byekp3Iv.js";import{D as s,a as K,d as N,v as Z,e as E}from"./config-NlBZD0Kw.js";import ee from"./ThingModelEnumDataSpecs-DFyLC04r.js";import ae from"./ThingModelNumberDataSpecs-VnS8Qs6I.js";import{_ as le}from"./Dialog.vue_vue_type_style_index_0_lang-DhV_HFpU.js";import{aj as w,f as te,V as de,h as se,k as I,ak as pe,s as F,u as q,x as ue,Q as oe}from"./form-designer-C0ARe9Dh.js";import{k as j,r as M,e as re,n as ie,l as T,m as i,G as V,H as u,z as o,$ as O,v as A,F as C,E as v,u as e,h as ne,A as ce,y,C as f,c as me,b as ye}from"./form-create-B86qX0W_.js";const Se={class:"btn"},B=Y(j({name:"ThingModelStructDataSpecs",__name:"ThingModelStructDataSpecs",props:{modelValue:{}},emits:["update:modelValue"],setup(h,{emit:k}){const r=x(h,"modelValue",k),a=M(!1),b=M("\u65B0\u589E\u53C2\u6570"),S=M(!1),c=M(),t=M({property:{dataType:s.INT,dataSpecs:{dataType:s.INT}}}),_=p=>{a.value=!0,n(),R(p)||(t.value={identifier:p.identifier,name:p.name,description:p.description,property:{dataType:p.childDataType,dataSpecs:p.dataSpecs,dataSpecsList:p.dataSpecsList}})},g=async()=>{await c.value.validate();try{const p=e(t),d={identifier:p.identifier,name:p.name,description:p.description,dataType:s.STRUCT,childDataType:p.property.dataType,dataSpecs:p.property.dataSpecs&&Object.keys(p.property.dataSpecs).length>1?p.property.dataSpecs:void 0,dataSpecsList:R(p.property.dataSpecsList)?void 0:p.property.dataSpecsList},l=r.value.findIndex(L=>L.identifier===p.identifier);l>-1?r.value[l]=d:r.value.push(d)}finally{a.value=!1}},n=()=>{var p;t.value={property:{dataType:s.INT,dataSpecs:{dataType:s.INT}}},(p=c.value)==null||p.resetFields()},U=(p,d,l)=>{R(r.value)?l(new Error("struct \u4E0D\u80FD\u4E3A\u7A7A")):l()};return re(async()=>{await ie(),R(r.value)&&(r.value=[])}),(p,d)=>{const l=te,L=de,D=w,z=I,G=se,H=le,J=pe;return i(),T(V,null,[u(D,{rules:[{required:!0,validator:U,trigger:"change"}],label:"JSON \u5BF9\u8C61"},{default:o(()=>[(i(!0),T(V,null,O(e(r),(m,$)=>(i(),T("div",{key:$,class:"w-1/1 struct-item flex justify-between px-10px mb-10px"},[A("span",null,"\u53C2\u6570\u540D\u79F0\uFF1A"+C(m.name),1),A("div",Se,[u(l,{link:"",type:"primary",onClick:Q=>_(m)},{default:o(()=>d[6]||(d[6]=[v("\u7F16\u8F91")])),_:2},1032,["onClick"]),u(L,{direction:"vertical"}),u(l,{link:"",type:"danger",onClick:Q=>(X=>{r.value.splice(X,1)})($)},{default:o(()=>d[7]||(d[7]=[v("\u5220\u9664")])),_:2},1032,["onClick"])])]))),128)),u(l,{link:"",type:"primary",onClick:d[0]||(d[0]=m=>_(null))},{default:o(()=>d[8]||(d[8]=[v("+\u65B0\u589E\u53C2\u6570")])),_:1})]),_:1},8,["rules"]),u(H,{modelValue:e(a),"onUpdate:modelValue":d[5]||(d[5]=m=>ne(a)?a.value=m:null),title:e(b),"append-to-body":""},{footer:o(()=>[u(l,{disabled:e(S),type:"primary",onClick:g},{default:o(()=>d[9]||(d[9]=[v("\u786E \u5B9A")])),_:1},8,["disabled"]),u(l,{onClick:d[4]||(d[4]=m=>a.value=!1)},{default:o(()=>d[10]||(d[10]=[v("\u53D6 \u6D88")])),_:1})]),default:o(()=>[ce((i(),y(G,{ref_key:"structFormRef",ref:c,model:e(t),rules:e(K),"label-width":"100px"},{default:o(()=>[u(D,{label:"\u53C2\u6570\u540D\u79F0",prop:"name"},{default:o(()=>[u(z,{modelValue:e(t).name,"onUpdate:modelValue":d[1]||(d[1]=m=>e(t).name=m),placeholder:"\u8BF7\u8F93\u5165\u529F\u80FD\u540D\u79F0"},null,8,["modelValue"])]),_:1}),u(D,{label:"\u6807\u8BC6\u7B26",prop:"identifier"},{default:o(()=>[u(z,{modelValue:e(t).identifier,"onUpdate:modelValue":d[2]||(d[2]=m=>e(t).identifier=m),placeholder:"\u8BF7\u8F93\u5165\u6807\u8BC6\u7B26"},null,8,["modelValue"])]),_:1}),u(P,{modelValue:e(t).property,"onUpdate:modelValue":d[3]||(d[3]=m=>e(t).property=m),"is-struct-data-specs":""},null,8,["modelValue"])]),_:1},8,["model","rules"])),[[J,e(S)]])]),_:1},8,["modelValue","title"])],64)}}}),[["__scopeId","data-v-f7ce3adb"]]),Te=Object.freeze(Object.defineProperty({__proto__:null,default:B},Symbol.toStringTag,{value:"Module"})),W=j({name:"ThingModelArrayDataSpecs",__name:"ThingModelArrayDataSpecs",props:{modelValue:{}},emits:["update:modelValue"],setup(h,{emit:k}){const r=x(h,"modelValue",k),a=b=>{b===s.STRUCT&&(r.value.dataSpecsList=[])};return(b,S)=>{const c=q,t=F,_=w,g=I;return i(),T(V,null,[u(_,{label:"\u5143\u7D20\u7C7B\u578B",prop:"property.dataSpecs.childDataType"},{default:o(()=>[u(t,{modelValue:e(r).childDataType,"onUpdate:modelValue":S[0]||(S[0]=n=>e(r).childDataType=n),onChange:a},{default:o(()=>[(i(!0),T(V,null,O(e(N),n=>(i(),T(V,{key:n.value},[[e(s).ENUM,e(s).ARRAY,e(s).DATE].includes(n.value)?f("",!0):(i(),y(c,{key:0,value:n.value,class:"w-1/3"},{default:o(()=>[v(C(`${n.value}(${n.label})`),1)]),_:2},1032,["value"]))],64))),128))]),_:1},8,["modelValue"])]),_:1}),u(_,{label:"\u5143\u7D20\u4E2A\u6570",prop:"property.dataSpecs.size"},{default:o(()=>[u(g,{modelValue:e(r).size,"onUpdate:modelValue":S[1]||(S[1]=n=>e(r).size=n),placeholder:"\u8BF7\u8F93\u5165\u6570\u7EC4\u4E2D\u7684\u5143\u7D20\u4E2A\u6570"},null,8,["modelValue"])]),_:1}),e(r).childDataType===e(s).STRUCT?(i(),y(B,{key:0,modelValue:e(r).dataSpecsList,"onUpdate:modelValue":S[2]||(S[2]=n=>e(r).dataSpecsList=n)},null,8,["modelValue"])):f("",!0)],64)}}}),P=Y(j({name:"ThingModelProperty",__name:"ThingModelProperty",props:{modelValue:{},isStructDataSpecs:{type:Boolean},isParams:{type:Boolean}},emits:["update:modelValue"],setup(h,{emit:k}){const r=h,a=x(r,"modelValue",k),b=me(()=>r.isStructDataSpecs?N.filter(c=>![s.STRUCT,s.ARRAY].includes(c.value)):N),S=c=>{switch(a.value.dataSpecs={},a.value.dataSpecsList=[],![s.ENUM,s.BOOL,s.STRUCT].includes(c)&&(a.value.dataSpecs.dataType=c),c){case s.ENUM:a.value.dataSpecsList.push({dataType:s.ENUM,name:"",value:void 0});break;case s.BOOL:for(let t=0;t<2;t++)a.value.dataSpecsList.push({dataType:s.BOOL,name:"",value:t})}};return ye(()=>a.value.accessMode,c=>{r.isStructDataSpecs||r.isParams||R(c)&&(a.value.accessMode=E.READ_WRITE.value)},{immediate:!0}),(c,t)=>{const _=oe,g=ue,n=w,U=I,p=q,d=F;return i(),T(V,null,[u(n,{rules:[{required:!0,message:"\u8BF7\u9009\u62E9\u6570\u636E\u7C7B\u578B",trigger:"change"}],label:"\u6570\u636E\u7C7B\u578B",prop:"property.dataType"},{default:o(()=>[u(g,{modelValue:e(a).dataType,"onUpdate:modelValue":t[0]||(t[0]=l=>e(a).dataType=l),placeholder:"\u8BF7\u9009\u62E9\u6570\u636E\u7C7B\u578B",onChange:S},{default:o(()=>[(i(!0),T(V,null,O(e(b),l=>(i(),y(_,{key:l.value,label:`${l.value}(${l.label})`,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),[e(s).INT,e(s).DOUBLE,e(s).FLOAT].includes(e(a).dataType||"")?(i(),y(e(ae),{key:0,modelValue:e(a).dataSpecs,"onUpdate:modelValue":t[1]||(t[1]=l=>e(a).dataSpecs=l)},null,8,["modelValue"])):f("",!0),e(a).dataType===e(s).ENUM?(i(),y(e(ee),{key:1,modelValue:e(a).dataSpecsList,"onUpdate:modelValue":t[2]||(t[2]=l=>e(a).dataSpecsList=l)},null,8,["modelValue"])):f("",!0),e(a).dataType===e(s).BOOL?(i(),y(n,{key:2,label:"\u5E03\u5C14\u503C"},{default:o(()=>[(i(!0),T(V,null,O(e(a).dataSpecsList,(l,L)=>(i(),T("div",{key:l.value,class:"flex items-center justify-start w-1/1 mb-5px"},[A("span",null,C(l.value),1),t[7]||(t[7]=A("span",{class:"mx-2"},"-",-1)),u(n,{prop:`property.dataSpecsList[${L}].name`,rules:[{required:!0,message:"\u679A\u4E3E\u63CF\u8FF0\u4E0D\u80FD\u4E3A\u7A7A"},{validator:e(Z),trigger:"blur"}],class:"flex-1 mb-0"},{default:o(()=>[u(U,{modelValue:l.name,"onUpdate:modelValue":D=>l.name=D,placeholder:"\u5982\uFF1A"+(l.value===0?"\u5173":"\u5F00"),class:"w-255px!"},null,8,["modelValue","onUpdate:modelValue","placeholder"])]),_:2},1032,["prop","rules"])]))),128))]),_:1})):f("",!0),e(a).dataType===e(s).TEXT?(i(),y(n,{key:3,label:"\u6570\u636E\u957F\u5EA6",prop:"property.dataSpecs.length"},{default:o(()=>[u(U,{modelValue:e(a).dataSpecs.length,"onUpdate:modelValue":t[3]||(t[3]=l=>e(a).dataSpecs.length=l),class:"w-255px!",placeholder:"\u8BF7\u8F93\u5165\u6587\u672C\u5B57\u8282\u957F\u5EA6"},{append:o(()=>t[8]||(t[8]=[v("\u5B57\u8282")])),_:1},8,["modelValue"])]),_:1})):f("",!0),e(a).dataType===e(s).DATE?(i(),y(n,{key:4,label:"\u65F6\u95F4\u683C\u5F0F",prop:"date"},{default:o(()=>[u(U,{class:"w-255px!",disabled:"",placeholder:"String \u7C7B\u578B\u7684 UTC \u65F6\u95F4\u6233\uFF08\u6BEB\u79D2\uFF09"})]),_:1})):f("",!0),e(a).dataType===e(s).ARRAY?(i(),y(e(W),{key:5,modelValue:e(a).dataSpecs,"onUpdate:modelValue":t[4]||(t[4]=l=>e(a).dataSpecs=l)},null,8,["modelValue"])):f("",!0),e(a).dataType===e(s).STRUCT?(i(),y(e(B),{key:6,modelValue:e(a).dataSpecsList,"onUpdate:modelValue":t[5]||(t[5]=l=>e(a).dataSpecsList=l)},null,8,["modelValue"])):f("",!0),c.isStructDataSpecs||c.isParams?f("",!0):(i(),y(n,{key:7,label:"\u8BFB\u5199\u7C7B\u578B",prop:"property.accessMode"},{default:o(()=>[u(d,{modelValue:e(a).accessMode,"onUpdate:modelValue":t[6]||(t[6]=l=>e(a).accessMode=l)},{default:o(()=>[u(p,{label:e(E).READ_WRITE.value},{default:o(()=>[v(C(e(E).READ_WRITE.label),1)]),_:1},8,["label"]),u(p,{label:e(E).READ_ONLY.value},{default:o(()=>[v(C(e(E).READ_ONLY.label),1)]),_:1},8,["label"])]),_:1},8,["modelValue"])]),_:1}))],64)}}}),[["__scopeId","data-v-e01c5cbd"]]),fe=Object.freeze(Object.defineProperty({__proto__:null,default:P},Symbol.toStringTag,{value:"Module"}));export{P as T,W as _,Te as a,fe as b};
