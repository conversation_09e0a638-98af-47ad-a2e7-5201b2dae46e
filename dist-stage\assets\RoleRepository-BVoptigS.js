import{u as L,_ as T,c as W}from"./index-Byekp3Iv.js";import X from"./RoleHeader-DQiL36Fl.js";import V from"./RoleList-BBG_Jb7v.js";import{_ as $,C as d}from"./ChatRoleForm.vue_vue_type_script_setup_true_lang-DW1KFuWo.js";import j from"./RoleCategoryList-Bv8zKBHP.js";import{C as B}from"./index-D39PSukB.js";import{U as F,X as G,k as Y,aW as J,f as K,a0 as Q,$ as Z}from"./form-designer-C0ARe9Dh.js";import{k as aa,r as t,P as S,e as ea,y as P,m as U,z as i,H as l,v as la,C as ta,u as oa,E as sa}from"./form-create-B86qX0W_.js";import"./Dialog.vue_vue_type_style_index_0_lang-DhV_HFpU.js";import"./constants-C3gLHYOK.js";import"./index-B33xcmUV.js";import"./constants-CHvLs4wt.js";import"./index-Bgh1KIDe.js";import"./index-LYNnTnhv.js";const ia={class:"search-container"},na=W(aa({__name:"RoleRepository",setup(ra){const z=L(),u=t(!1),n=t("my-role"),c=t(""),g=S({pageNo:1,pageSize:50}),p=t([]),f=S({pageNo:1,pageSize:50}),m=t([]),y=t("\u5168\u90E8"),_=t([]),M=async a=>{n.value=a.paneName+"",await s()},C=async a=>{const e={...g,name:c.value,publicStatus:!1},{list:o}=await d.getMyPage(e);a?p.value.push.apply(p.value,o):p.value=o},h=async a=>{const e={...f,category:y.value==="\u5168\u90E8"?"":y.value,name:c.value,publicStatus:!0},{total:o,list:v}=await d.getMyPage(e);a?m.value.push.apply(m.value,v):m.value=v},s=async()=>{n.value==="my-role"?(g.pageNo=1,await C()):(f.pageNo=1,await h())},E=async a=>{y.value=a,await s()},w=t(),I=async()=>{w.value.open("my-create",null,"\u6DFB\u52A0\u89D2\u8272")},b=async a=>{w.value.open("my-update",a.id,"\u7F16\u8F91\u89D2\u8272")},R=async a=>{await s()},O=async a=>{await d.deleteMy(a.id),await s()},k=async a=>{try{u.value=!0,a==="public"?(f.pageNo++,await h(!0)):(g.pageNo++,await C(!0))}finally{u.value=!1}},x=async a=>{const e={roleId:a.id},o=await B.createChatConversationMy(e);await z.push({name:"AiChat",query:{conversationId:o}})};return ea(async()=>{await(async()=>{_.value=["\u5168\u90E8",...await d.getCategoryList()]})(),await s()}),(a,e)=>{const o=Y,v=T,D=K,N=Z,q=Q,A=G,H=F;return U(),P(H,{class:"role-container"},{default:i(()=>[l($,{ref_key:"formRef",ref:w,onSuccess:R},null,512),l(X,{title:"\u89D2\u8272\u4ED3\u5E93",class:"relative"}),l(A,{class:"role-main"},{default:i(()=>[la("div",ia,[l(o,{loading:u.value,modelValue:c.value,"onUpdate:modelValue":e[0]||(e[0]=r=>c.value=r),class:"search-input",size:"default",placeholder:"\u8BF7\u8F93\u5165\u641C\u7D22\u7684\u5185\u5BB9","suffix-icon":oa(J),onChange:s},null,8,["loading","modelValue","suffix-icon"]),n.value=="my-role"?(U(),P(D,{key:0,type:"primary",onClick:I,class:"ml-20px"},{default:i(()=>[l(v,{icon:"ep:user",style:{"margin-right":"5px"}}),e[4]||(e[4]=sa(" \u6DFB\u52A0\u89D2\u8272 "))]),_:1})):ta("",!0)]),l(q,{modelValue:n.value,"onUpdate:modelValue":e[3]||(e[3]=r=>n.value=r),class:"tabs",onTabClick:M},{default:i(()=>[l(N,{class:"role-pane",label:"\u6211\u7684\u89D2\u8272",name:"my-role"},{default:i(()=>[l(V,{loading:u.value,"role-list":p.value,"show-more":!0,onOnDelete:O,onOnEdit:b,onOnUse:x,onOnPage:e[1]||(e[1]=r=>k("my")),class:"mt-20px"},null,8,["loading","role-list"])]),_:1}),l(N,{label:"\u516C\u5171\u89D2\u8272",name:"public-role"},{default:i(()=>[l(j,{class:"role-category-list","category-list":_.value,active:y.value,onOnCategoryClick:E},null,8,["category-list","active"]),l(V,{"role-list":m.value,onOnDelete:O,onOnEdit:b,onOnUse:x,onOnPage:e[2]||(e[2]=r=>k("public")),class:"mt-20px",loading:""},null,8,["role-list"])]),_:1})]),_:1},8,["modelValue"])]),_:1})]),_:1})}}}),[["__scopeId","data-v-05e056d9"]]);export{na as default};
