import{a as H,d as M,h as Q,D as T}from"./index-Byekp3Iv.js";import{_ as X}from"./Dialog.vue_vue_type_style_index_0_lang-DhV_HFpU.js";import{C as I}from"./constants-C3gLHYOK.js";import{d as Y,h as J}from"./tree-COGD3qag.js";import{g as K}from"./index-CL6LcTWy.js";import{g as O}from"./index-DC6LIg_n.js";import{b as P,c as W,u as Z}from"./index-DP9Sf6UT.js";import{h as ee,i as le,j as ae,aj as se,k as de,a1 as ue,x as oe,Q as te,ak as re,f as me}from"./form-designer-C0ARe9Dh.js";import{k as pe,r as m,P as ne,y as c,m as p,z as a,A as ie,u as l,H as e,C as E,l as S,G as C,$ as q,E as A,h as fe}from"./form-create-B86qX0W_.js";const ce=pe({name:"SystemUserForm",__name:"UserForm",emits:["success"],setup(_e,{expose:F,emit:N}){const{t:V}=H(),b=M(),n=m(!1),k=m(""),i=m(!1),g=m(""),u=m({nickname:"",deptId:"",mobile:"",email:"",id:void 0,username:"",password:"",sex:void 0,postIds:[],remark:"",status:I.ENABLE,roleIds:[]}),j=ne({username:[{required:!0,message:"\u7528\u6237\u540D\u79F0\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],nickname:[{required:!0,message:"\u7528\u6237\u6635\u79F0\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],password:[{required:!0,message:"\u7528\u6237\u5BC6\u7801\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],email:[{type:"email",message:"\u8BF7\u8F93\u5165\u6B63\u786E\u7684\u90AE\u7BB1\u5730\u5740",trigger:["blur","change"]}],mobile:[{pattern:/^(?:(?:\+|00)86)?1(?:3[\d]|4[5-79]|5[0-35-9]|6[5-7]|7[0-8]|8[\d]|9[189])\d{8}$/,message:"\u8BF7\u8F93\u5165\u6B63\u786E\u7684\u624B\u673A\u53F7\u7801",trigger:"blur"}]}),v=m(),y=m([]),h=m([]);F({open:async(o,s)=>{if(n.value=!0,k.value=V("action."+o),g.value=o,R(),s){i.value=!0;try{u.value=await P(s)}finally{i.value=!1}}y.value=J(await O()),h.value=await K()}});const B=N,L=async()=>{if(v&&await v.value.validate()){i.value=!0;try{const o=u.value;g.value==="create"?(await W(o),b.success(V("common.createSuccess"))):(await Z(o),b.success(V("common.updateSuccess"))),n.value=!1,B("success")}finally{i.value=!1}}},R=()=>{var o;u.value={nickname:"",deptId:"",mobile:"",email:"",id:void 0,username:"",password:"",sex:void 0,postIds:[],remark:"",status:I.ENABLE,roleIds:[]},(o=v.value)==null||o.resetFields()};return(o,s)=>{const f=de,t=se,r=ae,$=ue,_=le,w=te,U=oe,z=ee,x=me,D=X,G=re;return p(),c(D,{modelValue:l(n),"onUpdate:modelValue":s[10]||(s[10]=d=>fe(n)?n.value=d:null),title:l(k)},{footer:a(()=>[e(x,{disabled:l(i),type:"primary",onClick:L},{default:a(()=>s[11]||(s[11]=[A("\u786E \u5B9A")])),_:1},8,["disabled"]),e(x,{onClick:s[9]||(s[9]=d=>n.value=!1)},{default:a(()=>s[12]||(s[12]=[A("\u53D6 \u6D88")])),_:1})]),default:a(()=>[ie((p(),c(z,{ref_key:"formRef",ref:v,model:l(u),rules:l(j),"label-width":"80px"},{default:a(()=>[e(_,null,{default:a(()=>[e(r,{span:12},{default:a(()=>[e(t,{label:"\u7528\u6237\u6635\u79F0",prop:"nickname"},{default:a(()=>[e(f,{modelValue:l(u).nickname,"onUpdate:modelValue":s[0]||(s[0]=d=>l(u).nickname=d),placeholder:"\u8BF7\u8F93\u5165\u7528\u6237\u6635\u79F0"},null,8,["modelValue"])]),_:1})]),_:1}),e(r,{span:12},{default:a(()=>[e(t,{label:"\u5F52\u5C5E\u90E8\u95E8",prop:"deptId"},{default:a(()=>[e($,{modelValue:l(u).deptId,"onUpdate:modelValue":s[1]||(s[1]=d=>l(u).deptId=d),data:l(y),props:l(Y),"check-strictly":"","node-key":"id",placeholder:"\u8BF7\u9009\u62E9\u5F52\u5C5E\u90E8\u95E8"},null,8,["modelValue","data","props"])]),_:1})]),_:1})]),_:1}),e(_,null,{default:a(()=>[e(r,{span:12},{default:a(()=>[e(t,{label:"\u624B\u673A\u53F7\u7801",prop:"mobile"},{default:a(()=>[e(f,{modelValue:l(u).mobile,"onUpdate:modelValue":s[2]||(s[2]=d=>l(u).mobile=d),maxlength:"11",placeholder:"\u8BF7\u8F93\u5165\u624B\u673A\u53F7\u7801"},null,8,["modelValue"])]),_:1})]),_:1}),e(r,{span:12},{default:a(()=>[e(t,{label:"\u90AE\u7BB1",prop:"email"},{default:a(()=>[e(f,{modelValue:l(u).email,"onUpdate:modelValue":s[3]||(s[3]=d=>l(u).email=d),maxlength:"50",placeholder:"\u8BF7\u8F93\u5165\u90AE\u7BB1"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(_,null,{default:a(()=>[e(r,{span:12},{default:a(()=>[l(u).id===void 0?(p(),c(t,{key:0,label:"\u7528\u6237\u540D\u79F0",prop:"username"},{default:a(()=>[e(f,{modelValue:l(u).username,"onUpdate:modelValue":s[4]||(s[4]=d=>l(u).username=d),placeholder:"\u8BF7\u8F93\u5165\u7528\u6237\u540D\u79F0"},null,8,["modelValue"])]),_:1})):E("",!0)]),_:1}),e(r,{span:12},{default:a(()=>[l(u).id===void 0?(p(),c(t,{key:0,label:"\u7528\u6237\u5BC6\u7801",prop:"password"},{default:a(()=>[e(f,{modelValue:l(u).password,"onUpdate:modelValue":s[5]||(s[5]=d=>l(u).password=d),placeholder:"\u8BF7\u8F93\u5165\u7528\u6237\u5BC6\u7801","show-password":"",type:"password"},null,8,["modelValue"])]),_:1})):E("",!0)]),_:1})]),_:1}),e(_,null,{default:a(()=>[e(r,{span:12},{default:a(()=>[e(t,{label:"\u7528\u6237\u6027\u522B"},{default:a(()=>[e(U,{modelValue:l(u).sex,"onUpdate:modelValue":s[6]||(s[6]=d=>l(u).sex=d),placeholder:"\u8BF7\u9009\u62E9"},{default:a(()=>[(p(!0),S(C,null,q(l(Q)(l(T).SYSTEM_USER_SEX),d=>(p(),c(w,{key:d.value,label:d.label,value:d.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(r,{span:12},{default:a(()=>[e(t,{label:"\u5C97\u4F4D"},{default:a(()=>[e(U,{modelValue:l(u).postIds,"onUpdate:modelValue":s[7]||(s[7]=d=>l(u).postIds=d),multiple:"",placeholder:"\u8BF7\u9009\u62E9"},{default:a(()=>[(p(!0),S(C,null,q(l(h),d=>(p(),c(w,{key:d.id,label:d.name,value:d.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(_,null,{default:a(()=>[e(r,{span:24},{default:a(()=>[e(t,{label:"\u5907\u6CE8"},{default:a(()=>[e(f,{modelValue:l(u).remark,"onUpdate:modelValue":s[8]||(s[8]=d=>l(u).remark=d),placeholder:"\u8BF7\u8F93\u5165\u5185\u5BB9",type:"textarea"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model","rules"])),[[G,l(i)]])]),_:1},8,["modelValue","title"])}}});export{ce as _};
