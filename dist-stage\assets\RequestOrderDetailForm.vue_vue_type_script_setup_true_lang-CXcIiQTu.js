import{W as p,a as C,d as P,h as A,D as F}from"./index-Byekp3Iv.js";import{_ as E}from"./Dialog.vue_vue_type_style_index_0_lang-DhV_HFpU.js";import{h as T,aj as j,k as G,x as M,Q as H,ak as L,f as W}from"./form-designer-C0ARe9Dh.js";import{k as Y,r as c,P as $,y as b,m as V,z as t,A as B,u as a,H as o,l as J,G as K,$ as X,E as U,h as Z}from"./form-create-B86qX0W_.js";const v={getRequestOrderDetailPage:async i=>await p.get({url:"/scm/mfg/request-order-detail/page",params:i}),getRequestOrderDetail:async i=>await p.get({url:"/scm/mfg/request-order-detail/get?id="+i}),createRequestOrderDetail:async i=>await p.post({url:"/scm/mfg/request-order-detail/create",data:i}),updateRequestOrderDetail:async i=>await p.put({url:"/scm/mfg/request-order-detail/update",data:i}),deleteRequestOrderDetail:async i=>await p.delete({url:"/mfg/request-order-detail/delete?id="+i}),exportRequestOrderDetail:async i=>await p.download({url:"/scm/mfg/request-order-detail/export-excel",params:i})},ee=Y({name:"RequestOrderDetailForm",__name:"RequestOrderDetailForm",emits:["success"],setup(i,{expose:O,emit:R}){const{t:f}=C(),h=P(),n=c(!1),_=c(""),s=c(!1),I=c(""),d=c({id:void 0,num:void 0,bizOrderId:void 0,bizOrderNo:void 0,warehouseId:void 0,locationId:void 0,materialId:void 0,materialName:void 0,materialCode:void 0,spec:void 0,unit:void 0,unitPrice:void 0,amount:void 0,remark:void 0,plannedQuantity:void 0,fulfilledQuantity:void 0,note:void 0,batchNo:void 0,lossRate:void 0,lossQuantity:void 0,readyStatus:void 0,readyQuantity:void 0,lockedQuantity:void 0}),D=$({}),y=c();O({open:async(m,e)=>{if(n.value=!0,_.value=f("action."+m),I.value=m,w(),e){s.value=!0;try{d.value=await v.getRequestOrderDetail(e)}finally{s.value=!1}}}});const k=R,q=async()=>{await y.value.validate(),s.value=!0;try{const m=d.value;I.value==="create"?(await v.createRequestOrderDetail(m),h.success(f("common.createSuccess"))):(await v.updateRequestOrderDetail(m),h.success(f("common.updateSuccess"))),n.value=!1,k("success")}finally{s.value=!1}},w=()=>{var m;d.value={id:void 0,num:void 0,bizOrderId:void 0,bizOrderNo:void 0,warehouseId:void 0,locationId:void 0,materialId:void 0,materialName:void 0,materialCode:void 0,spec:void 0,unit:void 0,unitPrice:void 0,amount:void 0,remark:void 0,plannedQuantity:void 0,fulfilledQuantity:void 0,note:void 0,batchNo:void 0,lossRate:void 0,lossQuantity:void 0,readyStatus:void 0,readyQuantity:void 0,lockedQuantity:void 0},(m=y.value)==null||m.resetFields()};return(m,e)=>{const r=G,u=j,g=H,N=M,z=T,Q=W,S=E,x=L;return V(),b(S,{title:a(_),modelValue:a(n),"onUpdate:modelValue":e[23]||(e[23]=l=>Z(n)?n.value=l:null)},{footer:t(()=>[o(Q,{onClick:q,type:"primary",disabled:a(s)},{default:t(()=>e[24]||(e[24]=[U("\u786E \u5B9A")])),_:1},8,["disabled"]),o(Q,{onClick:e[22]||(e[22]=l=>n.value=!1)},{default:t(()=>e[25]||(e[25]=[U("\u53D6 \u6D88")])),_:1})]),default:t(()=>[B((V(),b(z,{ref_key:"formRef",ref:y,model:a(d),rules:a(D),"label-width":"100px"},{default:t(()=>[o(u,{label:"\u5E8F\u53F7",prop:"num"},{default:t(()=>[o(r,{modelValue:a(d).num,"onUpdate:modelValue":e[0]||(e[0]=l=>a(d).num=l),placeholder:"\u8BF7\u8F93\u5165\u5E8F\u53F7"},null,8,["modelValue"])]),_:1}),o(u,{label:"\u8BA2\u5355\u7F16\u53F7",prop:"bizOrderId"},{default:t(()=>[o(r,{modelValue:a(d).bizOrderId,"onUpdate:modelValue":e[1]||(e[1]=l=>a(d).bizOrderId=l),placeholder:"\u8BF7\u8F93\u5165\u8BA2\u5355\u7F16\u53F7"},null,8,["modelValue"])]),_:1}),o(u,{label:"\u5355\u53F7",prop:"bizOrderNo"},{default:t(()=>[o(r,{modelValue:a(d).bizOrderNo,"onUpdate:modelValue":e[2]||(e[2]=l=>a(d).bizOrderNo=l),placeholder:"\u8BF7\u8F93\u5165\u5355\u53F7"},null,8,["modelValue"])]),_:1}),o(u,{label:"\u4ED3\u5E93ID",prop:"warehouseId"},{default:t(()=>[o(r,{modelValue:a(d).warehouseId,"onUpdate:modelValue":e[3]||(e[3]=l=>a(d).warehouseId=l),placeholder:"\u8BF7\u8F93\u5165\u4ED3\u5E93ID"},null,8,["modelValue"])]),_:1}),o(u,{label:"\u5E93\u4F4DID",prop:"locationId"},{default:t(()=>[o(r,{modelValue:a(d).locationId,"onUpdate:modelValue":e[4]||(e[4]=l=>a(d).locationId=l),placeholder:"\u8BF7\u8F93\u5165\u5E93\u4F4DID"},null,8,["modelValue"])]),_:1}),o(u,{label:"\u7269\u6599ID",prop:"materialId"},{default:t(()=>[o(r,{modelValue:a(d).materialId,"onUpdate:modelValue":e[5]||(e[5]=l=>a(d).materialId=l),placeholder:"\u8BF7\u8F93\u5165\u7269\u6599ID"},null,8,["modelValue"])]),_:1}),o(u,{label:"\u7269\u6599\u540D\u79F0",prop:"materialName"},{default:t(()=>[o(r,{modelValue:a(d).materialName,"onUpdate:modelValue":e[6]||(e[6]=l=>a(d).materialName=l),placeholder:"\u8BF7\u8F93\u5165\u7269\u6599\u540D\u79F0"},null,8,["modelValue"])]),_:1}),o(u,{label:"\u7269\u6599\u7F16\u53F7",prop:"materialCode"},{default:t(()=>[o(r,{modelValue:a(d).materialCode,"onUpdate:modelValue":e[7]||(e[7]=l=>a(d).materialCode=l),placeholder:"\u8BF7\u8F93\u5165\u7269\u6599\u7F16\u53F7"},null,8,["modelValue"])]),_:1}),o(u,{label:"\u89C4\u683C",prop:"spec"},{default:t(()=>[o(r,{modelValue:a(d).spec,"onUpdate:modelValue":e[8]||(e[8]=l=>a(d).spec=l),placeholder:"\u8BF7\u8F93\u5165\u89C4\u683C"},null,8,["modelValue"])]),_:1}),o(u,{label:"\u5355\u4F4D",prop:"unit"},{default:t(()=>[o(r,{modelValue:a(d).unit,"onUpdate:modelValue":e[9]||(e[9]=l=>a(d).unit=l),placeholder:"\u8BF7\u8F93\u5165\u5355\u4F4D"},null,8,["modelValue"])]),_:1}),o(u,{label:"\u5355\u4EF7",prop:"unitPrice"},{default:t(()=>[o(r,{modelValue:a(d).unitPrice,"onUpdate:modelValue":e[10]||(e[10]=l=>a(d).unitPrice=l),placeholder:"\u8BF7\u8F93\u5165\u5355\u4EF7"},null,8,["modelValue"])]),_:1}),o(u,{label:"\u91D1\u989D",prop:"amount"},{default:t(()=>[o(r,{modelValue:a(d).amount,"onUpdate:modelValue":e[11]||(e[11]=l=>a(d).amount=l),placeholder:"\u8BF7\u8F93\u5165\u91D1\u989D"},null,8,["modelValue"])]),_:1}),o(u,{label:"\u5907\u6CE8",prop:"remark"},{default:t(()=>[o(r,{modelValue:a(d).remark,"onUpdate:modelValue":e[12]||(e[12]=l=>a(d).remark=l),placeholder:"\u8BF7\u8F93\u5165\u5907\u6CE8"},null,8,["modelValue"])]),_:1}),o(u,{label:"\u8BA1\u5212\u6570\u91CF",prop:"plannedQuantity"},{default:t(()=>[o(r,{modelValue:a(d).plannedQuantity,"onUpdate:modelValue":e[13]||(e[13]=l=>a(d).plannedQuantity=l),placeholder:"\u8BF7\u8F93\u5165\u8BA1\u5212\u6570\u91CF"},null,8,["modelValue"])]),_:1}),o(u,{label:"\u5C65\u7EA6\u6570\u91CF",prop:"fulfilledQuantity"},{default:t(()=>[o(r,{modelValue:a(d).fulfilledQuantity,"onUpdate:modelValue":e[14]||(e[14]=l=>a(d).fulfilledQuantity=l),placeholder:"\u8BF7\u8F93\u5165\u5C65\u7EA6\u6570\u91CF"},null,8,["modelValue"])]),_:1}),o(u,{label:"\u8BF4\u660E",prop:"note"},{default:t(()=>[o(r,{modelValue:a(d).note,"onUpdate:modelValue":e[15]||(e[15]=l=>a(d).note=l),placeholder:"\u8BF7\u8F93\u5165\u8BF4\u660E"},null,8,["modelValue"])]),_:1}),o(u,{label:"\u6279\u53F7",prop:"batchNo"},{default:t(()=>[o(r,{modelValue:a(d).batchNo,"onUpdate:modelValue":e[16]||(e[16]=l=>a(d).batchNo=l),placeholder:"\u8BF7\u8F93\u5165\u6279\u53F7"},null,8,["modelValue"])]),_:1}),o(u,{label:"\u635F\u8017\u7387",prop:"lossRate"},{default:t(()=>[o(r,{modelValue:a(d).lossRate,"onUpdate:modelValue":e[17]||(e[17]=l=>a(d).lossRate=l),placeholder:"\u8BF7\u8F93\u5165\u635F\u8017\u7387"},null,8,["modelValue"])]),_:1}),o(u,{label:"\u635F\u8017\u6570\u91CF",prop:"lossQuantity"},{default:t(()=>[o(r,{modelValue:a(d).lossQuantity,"onUpdate:modelValue":e[18]||(e[18]=l=>a(d).lossQuantity=l),placeholder:"\u8BF7\u8F93\u5165\u635F\u8017\u6570\u91CF"},null,8,["modelValue"])]),_:1}),o(u,{label:"\u51C6\u5907\u72B6\u6001",prop:"readyStatus"},{default:t(()=>[o(N,{modelValue:a(d).readyStatus,"onUpdate:modelValue":e[19]||(e[19]=l=>a(d).readyStatus=l),placeholder:"\u8BF7\u9009\u62E9\u51C6\u5907\u72B6\u6001"},{default:t(()=>[(V(!0),J(K,null,X(a(A)(a(F).MFG_MATERIAL_READY_STATUS),l=>(V(),b(g,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),o(u,{label:"\u51C6\u5907\u6570\u91CF",prop:"readyQuantity"},{default:t(()=>[o(r,{modelValue:a(d).readyQuantity,"onUpdate:modelValue":e[20]||(e[20]=l=>a(d).readyQuantity=l),placeholder:"\u8BF7\u8F93\u5165\u51C6\u5907\u6570\u91CF"},null,8,["modelValue"])]),_:1}),o(u,{label:"\u9501\u5E93\u6570\u91CF",prop:"lockedQuantity"},{default:t(()=>[o(r,{modelValue:a(d).lockedQuantity,"onUpdate:modelValue":e[21]||(e[21]=l=>a(d).lockedQuantity=l),placeholder:"\u8BF7\u8F93\u5165\u9501\u5E93\u6570\u91CF"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])),[[x,a(s)]])]),_:1},8,["title","modelValue"])}}});export{v as R,ee as _};
