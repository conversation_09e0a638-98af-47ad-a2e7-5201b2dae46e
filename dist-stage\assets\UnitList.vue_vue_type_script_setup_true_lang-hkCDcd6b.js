import{a as d,d as g}from"./index-Byekp3Iv.js";import{_ as b}from"./ContentWrap.vue_vue_type_script_setup_true_lang-D3mNNG_R.js";import{d as _}from"./formatTime-HVkyL6Kg.js";import{U as y}from"./index-B7p-mTFI.js";import{ak as v,Z as w,_ as k}from"./form-designer-C0ARe9Dh.js";import{k as I,r as l,e as U,y as p,m as n,z as s,A as h,u as t,H as e}from"./form-create-B86qX0W_.js";const x=I({__name:"UnitList",props:{groupId:{}},setup(i){const{t:L}=d();g();const m=i,r=l(!1),o=l([]);return U(()=>{(async()=>{r.value=!0;try{o.value=await y.getUnitListByGroupId(m.groupId)}finally{r.value=!1}})()}),(j,z)=>{const a=k,c=w,u=b,f=v;return n(),p(u,null,{default:s(()=>[h((n(),p(c,{data:t(o),stripe:!0,"show-overflow-tooltip":!0},{default:s(()=>[e(a,{label:"\u7F16\u53F7",align:"center",prop:"id"}),e(a,{label:"\u7F16\u7801",align:"center",prop:"code"}),e(a,{label:"\u540D\u79F0",align:"center",prop:"name"}),e(a,{label:"\u5206\u7EC4\u7F16\u7801",align:"center",prop:"groupCode"}),e(a,{label:"\u6362\u7B97\u7387",align:"center",prop:"coefficient"}),e(a,{label:"\u5907\u6CE8",align:"center",prop:"remark"}),e(a,{label:"\u516C\u53F8\u540D\u79F0",align:"center",prop:"companyName"}),e(a,{label:"\u521B\u5EFA\u65F6\u95F4",align:"center",prop:"createTime",formatter:t(_),width:"180px"},null,8,["formatter"])]),_:1},8,["data"])),[[f,t(r)]])]),_:1})}}});export{x as _};
