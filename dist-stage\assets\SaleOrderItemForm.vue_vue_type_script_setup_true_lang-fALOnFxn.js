import{aM as $,ax as V,aL as Q,ay as y}from"./index-Byekp3Iv.js";import{P as R}from"./index-1HLiV7Gt.js";import{S as Z}from"./index-boBBTifs.js";import{ak as D,h as J,Z as K,_ as T,aj as W,x as X,Q as Y,k as ee,l as ae,f as le,i as te}from"./form-designer-C0ARe9Dh.js";import{k as oe,r as h,P as de,b as C,e as re,l as I,m as P,G as N,A as ue,y as v,C as ie,u as c,z as l,H as e,$ as ce,E as B}from"./form-create-B86qX0W_.js";const ne=oe({__name:"SaleOrderItemForm",props:{items:{},disabled:{type:Boolean}},setup(S,{expose:E}){const j=S,q=h(!1),p=h([]),_=de({productId:[{required:!0,message:"\u4EA7\u54C1\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],count:[{required:!0,message:"\u4EA7\u54C1\u6570\u91CF\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}]}),U=h([]),w=h([]);C(()=>j.items,async i=>{p.value=i},{immediate:!0}),C(()=>p.value,i=>{i&&i.length!==0&&i.forEach(d=>{d.totalProductPrice=y(d.productPrice,d.count),d.taxPrice=y(d.totalProductPrice,d.taxPercent/100),d.totalProductPrice!=null?d.totalPrice=d.totalProductPrice+(d.taxPrice||0):d.totalPrice=void 0})},{deep:!0});const L=i=>{const{columns:d,data:u}=i,s=[];return d.forEach((m,r)=>{if(r!==0)if(["count","totalProductPrice","taxPrice","totalPrice"].includes(m.property)){const n=Q(u.map(f=>Number(f[m.property])));s[r]=m.property==="count"?$(n):V(n)}else s[r]="";else s[r]="\u5408\u8BA1"}),s},g=()=>{p.value.push({id:void 0,productId:void 0,productUnitName:void 0,productBarCode:void 0,productPrice:void 0,stockCount:void 0,count:1,totalProductPrice:void 0,taxPercent:void 0,taxPrice:void 0,totalPrice:void 0,remark:void 0})},z=async i=>{if(!i.productId)return;const d=await Z.getStockCount(i.productId);i.stockCount=d||0};return E({validate:()=>U.value.validate()}),re(async()=>{w.value=await R.getProductSimpleList(),p.value.length===0&&g()}),(i,d)=>{const u=T,s=Y,m=X,r=W,n=ee,f=ae,k=le,A=K,F=J,G=te,H=D;return P(),I(N,null,[ue((P(),v(F,{ref_key:"formRef",ref:U,model:c(p),rules:c(_),"label-width":"0px","inline-message":!0,disabled:i.disabled},{default:l(()=>[e(A,{data:c(p),"show-summary":"","summary-method":L,class:"-mt-10px"},{default:l(()=>[e(u,{label:"\u5E8F\u53F7",type:"index",align:"center",width:"60"}),e(u,{label:"\u4EA7\u54C1\u540D\u79F0","min-width":"180"},{default:l(({row:a,$index:o})=>[e(r,{prop:`${o}.productId`,rules:c(_).productId,class:"mb-0px!"},{default:l(()=>[e(m,{modelValue:a.productId,"onUpdate:modelValue":t=>a.productId=t,clearable:"",filterable:"",onChange:t=>((M,x)=>{const b=w.value.find(O=>O.id===M);b&&(x.productUnitName=b.unitName,x.productBarCode=b.barCode,x.productPrice=b.salePrice),z(x)})(t,a),placeholder:"\u8BF7\u9009\u62E9\u4EA7\u54C1"},{default:l(()=>[(P(!0),I(N,null,ce(c(w),t=>(P(),v(s,{key:t.id,label:t.name,value:t.id},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue","onChange"])]),_:2},1032,["prop","rules"])]),_:1}),e(u,{label:"\u5E93\u5B58","min-width":"100"},{default:l(({row:a})=>[e(r,{class:"mb-0px!"},{default:l(()=>[e(n,{disabled:"",modelValue:a.stockCount,"onUpdate:modelValue":o=>a.stockCount=o,formatter:c($)},null,8,["modelValue","onUpdate:modelValue","formatter"])]),_:2},1024)]),_:1}),e(u,{label:"\u6761\u7801","min-width":"150"},{default:l(({row:a})=>[e(r,{class:"mb-0px!"},{default:l(()=>[e(n,{disabled:"",modelValue:a.productBarCode,"onUpdate:modelValue":o=>a.productBarCode=o},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1024)]),_:1}),e(u,{label:"\u5355\u4F4D","min-width":"80"},{default:l(({row:a})=>[e(r,{class:"mb-0px!"},{default:l(()=>[e(n,{disabled:"",modelValue:a.productUnitName,"onUpdate:modelValue":o=>a.productUnitName=o},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1024)]),_:1}),e(u,{label:"\u6570\u91CF",prop:"count",fixed:"right","min-width":"140"},{default:l(({row:a,$index:o})=>[e(r,{prop:`${o}.count`,rules:c(_).count,class:"mb-0px!"},{default:l(()=>[e(f,{modelValue:a.count,"onUpdate:modelValue":t=>a.count=t,"controls-position":"right",min:.001,precision:3,class:"!w-100%"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1032,["prop","rules"])]),_:1}),e(u,{label:"\u4EA7\u54C1\u5355\u4EF7",fixed:"right","min-width":"120"},{default:l(({row:a,$index:o})=>[e(r,{prop:`${o}.productPrice`,class:"mb-0px!"},{default:l(()=>[e(f,{modelValue:a.productPrice,"onUpdate:modelValue":t=>a.productPrice=t,"controls-position":"right",min:.01,precision:2,class:"!w-100%"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1032,["prop"])]),_:1}),e(u,{label:"\u91D1\u989D",prop:"totalProductPrice",fixed:"right","min-width":"100"},{default:l(({row:a,$index:o})=>[e(r,{prop:`${o}.totalProductPrice`,class:"mb-0px!"},{default:l(()=>[e(n,{disabled:"",modelValue:a.totalProductPrice,"onUpdate:modelValue":t=>a.totalProductPrice=t,formatter:c(V)},null,8,["modelValue","onUpdate:modelValue","formatter"])]),_:2},1032,["prop"])]),_:1}),e(u,{label:"\u7A0E\u7387\uFF08%\uFF09",fixed:"right","min-width":"115"},{default:l(({row:a,$index:o})=>[e(r,{prop:`${o}.taxPercent`,class:"mb-0px!"},{default:l(()=>[e(f,{modelValue:a.taxPercent,"onUpdate:modelValue":t=>a.taxPercent=t,"controls-position":"right",min:0,precision:2,class:"!w-100%"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1032,["prop"])]),_:1}),e(u,{label:"\u7A0E\u989D",prop:"taxPrice",fixed:"right","min-width":"120"},{default:l(({row:a,$index:o})=>[e(r,{prop:`${o}.taxPrice`,class:"mb-0px!"},{default:l(()=>[e(r,{prop:`${o}.taxPrice`,class:"mb-0px!"},{default:l(()=>[e(n,{disabled:"",modelValue:a.taxPrice,"onUpdate:modelValue":t=>a.taxPrice=t,formatter:c(V)},null,8,["modelValue","onUpdate:modelValue","formatter"])]),_:2},1032,["prop"])]),_:2},1032,["prop"])]),_:1}),e(u,{label:"\u7A0E\u989D\u5408\u8BA1",prop:"totalPrice",fixed:"right","min-width":"100"},{default:l(({row:a,$index:o})=>[e(r,{prop:`${o}.totalPrice`,class:"mb-0px!"},{default:l(()=>[e(n,{disabled:"",modelValue:a.totalPrice,"onUpdate:modelValue":t=>a.totalPrice=t,formatter:c(V)},null,8,["modelValue","onUpdate:modelValue","formatter"])]),_:2},1032,["prop"])]),_:1}),e(u,{label:"\u5907\u6CE8","min-width":"150"},{default:l(({row:a,$index:o})=>[e(r,{prop:`${o}.remark`,class:"mb-0px!"},{default:l(()=>[e(n,{modelValue:a.remark,"onUpdate:modelValue":t=>a.remark=t,placeholder:"\u8BF7\u8F93\u5165\u5907\u6CE8"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1032,["prop"])]),_:1}),e(u,{align:"center",fixed:"right",label:"\u64CD\u4F5C",width:"60"},{default:l(({$index:a})=>[e(k,{onClick:o=>{return t=a,void p.value.splice(t,1);var t},link:""},{default:l(()=>d[0]||(d[0]=[B("\u2014")])),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])]),_:1},8,["model","rules","disabled"])),[[H,c(q)]]),i.disabled?ie("",!0):(P(),v(G,{key:0,justify:"center",class:"mt-3"},{default:l(()=>[e(k,{onClick:g,round:""},{default:l(()=>d[1]||(d[1]=[B("+ \u6DFB\u52A0\u91C7\u8D2D\u4EA7\u54C1")])),_:1})]),_:1}))],64)}}});export{ne as _};
