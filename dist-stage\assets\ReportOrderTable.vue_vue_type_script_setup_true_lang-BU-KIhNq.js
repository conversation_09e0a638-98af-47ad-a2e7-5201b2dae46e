import{D as v}from"./index-Byekp3Iv.js";import{_ as E}from"./ContentWrap.vue_vue_type_script_setup_true_lang-D3mNNG_R.js";import{_ as R}from"./DictTag.vue_vue_type_script_lang-DdZ_pRVv.js";import{d as y}from"./formatTime-HVkyL6Kg.js";import{R as H}from"./index-ByxWixVk.js";import{f as w}from"./formatter-CF7Ifi5S.js";import{ak as q,Z as C,_ as F}from"./form-designer-C0ARe9Dh.js";import{k as z,r as _,b as M,e as P,y as N,m as k,z as i,A as U,H as t,u as o,E as m,F as d}from"./form-create-B86qX0W_.js";const $=z({__name:"ReportOrderTable",props:{workOrderId:{}},setup(O,{expose:T}){const s=O,f=_(!1),p=_([]),h=async()=>{if(s.workOrderId){f.value=!0;try{const a=await H.getReportOrderPage({workId:s.workOrderId,pageNo:1,pageSize:100});a&&Array.isArray(a.list)?p.value=a.list:Array.isArray(a)?p.value=a:p.value=[]}catch{p.value=[]}finally{f.value=!1}}},A=a=>{const{columns:g,data:r}=a,l=[];return g.forEach((u,n)=>{if(n===0)return void(l[n]="\u5408\u8BA1");if(["costHeadcount","quantity","piece"].includes(u.property)){const c=r.map(e=>Number(e[u.property]));if(c.every(e=>Number.isNaN(e)))l[n]="";else{const e=c.reduce((b,I)=>{const x=Number(I);return Number.isNaN(x)?b:b+x},0);l[n]=w(e)}}else l[n]=""}),l};return M(()=>s.workOrderId,async a=>{a?await h():p.value=[]},{immediate:!0}),P(async()=>{s.workOrderId&&await h()}),T({refresh:h}),(a,g)=>{const r=F,l=R,u=C,n=E,c=q;return k(),N(n,null,{default:i(()=>[U((k(),N(u,{data:p.value,stripe:!0,border:"","show-overflow-tooltip":!0,"highlight-current-row":"","show-summary":"","summary-method":A,"max-height":p.value.length>0?600:200,style:{width:"100%"}},{default:i(()=>[t(r,{label:"\u751F\u4EA7\u7F16\u53F7",align:"center",prop:"workNo","min-width":"120px",fixed:"left"}),t(r,{label:"\u62A5\u5DE5\u7F16\u53F7",align:"center",prop:"reportCode","min-width":"120px",fixed:"left"}),t(r,{label:"\u4EFB\u52A1\u7C7B\u578B",align:"center",prop:"type","min-width":"100px"},{default:i(e=>[t(l,{type:o(v).MFG_WORK_TYPE,value:e.row.type},null,8,["type","value"])]),_:1}),t(r,{label:"\u5F00\u59CB\u65F6\u95F4",align:"center",prop:"startTime",formatter:o(y),"min-width":"160px"},null,8,["formatter"]),t(r,{label:"\u7ED3\u675F\u65F6\u95F4",align:"center",prop:"endTime",formatter:o(y),"min-width":"160px"},null,8,["formatter"]),t(r,{label:"\u7528\u65F6",align:"center",prop:"costTime","min-width":"100px"}),t(r,{label:"\u4EBA\u6570",align:"center",prop:"costHeadcount","min-width":"80px"},{default:i(e=>[m(d(o(w)(e.row.costHeadcount)),1)]),_:1}),t(r,{label:"\u751F\u4EA7\u7EBF",align:"center",prop:"line","min-width":"100px"},{default:i(e=>[t(l,{type:o(v).MANUFACTURE_LINE,value:e.row.line},null,8,["type","value"])]),_:1}),t(r,{label:"\u6570\u91CF",align:"center",prop:"quantity","min-width":"100px"},{default:i(e=>[m(d(o(w)(e.row.quantity)),1)]),_:1}),t(r,{label:"\u4EF6\u6570",align:"center",prop:"piece","min-width":"100px"},{default:i(e=>[m(d(o(w)(e.row.piece)),1)]),_:1}),t(r,{label:"\u6279\u53F7",align:"center",prop:"batchNo","min-width":"120px"}),t(r,{label:"\u6E29\u5EA6",align:"center",prop:"temperature","min-width":"80px"},{default:i(e=>[m(d(e.row.temperature?`${e.row.temperature}\xB0C`:""),1)]),_:1}),t(r,{label:"\u6E7F\u5EA6",align:"center",prop:"humidity","min-width":"80px"},{default:i(e=>[m(d(e.row.humidity?`${e.row.humidity}%`:""),1)]),_:1}),t(r,{label:"\u5907\u6CE8",align:"left",prop:"remark","min-width":"150px"}),t(r,{label:"\u521B\u5EFA\u65F6\u95F4",align:"center",prop:"createTime",formatter:o(y),"min-width":"160px"},null,8,["formatter"])]),_:1},8,["data","max-height"])),[[c,f.value]])]),_:1})}}});export{$ as _};
