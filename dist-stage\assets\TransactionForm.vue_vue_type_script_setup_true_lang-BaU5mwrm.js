import{W as _,a as te,d as oe,au as q,D as I}from"./index-Byekp3Iv.js";import{_ as ue}from"./Dialog.vue_vue_type_style_index_0_lang-DhV_HFpU.js";import{g as ie}from"./commonBiz-BtTo-n4b.js";import{_ as ne}from"./HistoryTransactionList.vue_vue_type_script_setup_true_lang-1HYT1eMf.js";import{h as de,i as re,j as me,aj as se,k as ce,x as pe,Q as ve,F as fe,ak as ye,a0 as be,$ as Ve,f as _e}from"./form-designer-C0ARe9Dh.js";import{k as Ne,r as c,P as he,y as p,m,z as u,A as Ue,C as Te,u as o,H as a,l as N,G as h,$ as U,h as B,E as S}from"./form-create-B86qX0W_.js";const k={getTransactionPage:async s=>await _.get({url:"/scm/inventory/transaction/page",params:s}),getTransaction:async s=>await _.get({url:"/scm/inventory/transaction/get?id="+s}),createTransaction:async s=>await _.post({url:"/scm/inventory/transaction/create",data:s}),updateTransaction:async s=>await _.put({url:"/scm/inventory/transaction/update",data:s}),deleteTransaction:async s=>await _.delete({url:"/scm/inventory/transaction/delete?id="+s}),exportTransaction:async s=>await _.download({url:"/scm/inventory/transaction/export-excel",params:s})},ge=Ne({name:"TransactionForm",__name:"TransactionForm",emits:["success"],setup(s,{expose:R,emit:Y}){const{t:T}=te(),L=oe(),v=c(!1),w=c(""),f=c(!1),Q=c(""),g=c("history"),C=c(!1),O=c(),A=q(I.SCM_BIZ_TYPE),P=q(I.MATERIAL_TYPE),F=q(I.INVENTORY_MOVE_TYPE),M=q(I.INVENTORY_TRANSACTION_DIRECTION),W=c([]),y=c(new Map),t=c({id:void 0,bizId:void 0,bizNo:void 0,transactionType:void 0,transactionDirection:void 0,materialId:void 0,materialCode:void 0,materialName:void 0,materialType:void 0,moveType:void 0,fromWarehouseName:void 0,fromLocationName:void 0,toWarehouseName:void 0,toLocationName:void 0,moveDate:void 0,sourceNo:void 0,inventoryBatchNo:void 0,quantity:void 0,quantityUnit:void 0,auxiliaryQuantity:void 0,auxiliaryUnit:void 0,remark:void 0,beforeQuantity:void 0,afterQuantity:void 0,costObjectCode:void 0,costObjectName:void 0,accountingVoucherNumber:void 0,tenantName:void 0}),$=he({beforeQuantity:[{required:!0,message:"\u51FA\u5165\u5E93\u524D\u5E93\u5B58\u6570\u91CF\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],afterQuantity:[{required:!0,message:"\u51FA\u5165\u5E93\u540E\u5E93\u5B58\u6570\u91CF\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}]}),D=c(),j=async()=>{try{const i=await ie();i&&i.length>0&&(W.value=i.map(e=>({label:e.name,value:e.id})),i.forEach(e=>{e&&e.id&&e.name&&y.value.set(e.id,e.name)}))}catch{}},x=i=>{if(!i&&i!==0)return"";if(typeof i=="number"){const e=y.value.get(i);if(e)return e}if(typeof i=="string"){for(const[n,d]of y.value)if(n.toString()===i)return d;const e=parseInt(i);if(!isNaN(e)){const n=y.value.get(e);if(n)return n}}return i.toString()};R({open:async(i,e)=>{if(v.value=!0,w.value=T("action."+i),Q.value=i,z(),await j(),e){f.value=!0;try{const n=await k.getTransaction(e);n.quantityUnit&&(n.quantityUnit=x(n.quantityUnit)),n.auxiliaryUnit&&(n.auxiliaryUnit=x(n.auxiliaryUnit)),t.value=n}finally{f.value=!1}}},openWithStockInfo:async(i,e)=>{v.value=!0,w.value=T("action."+i),Q.value=i,z(),await j(),e&&(t.value.materialId=e.materialId,t.value.materialCode=e.materialCode,t.value.materialName=e.materialName,t.value.materialType=e.materialType,t.value.inventoryId=e.id,t.value.inventoryBatchNo=e.batchNo,t.value.fromWarehouseId=e.warehouseId,t.value.fromWarehouseName=e.warehouseName,t.value.fromLocationId=e.locationId,t.value.fromLocationName=e.locationName,e.quantityUnit&&(t.value.quantityUnit=x(e.quantityUnit)),e.auxiliaryUnit&&(t.value.auxiliaryUnit=x(e.auxiliaryUnit)),t.value.beforeQuantity=e.quantity,t.value.moveDate=new Date().getTime(),e.tenantName&&(t.value.tenantName=e.tenantName),O.value=e.id,C.value=!0)}});const G=Y,H=async()=>{await D.value.validate(),f.value=!0;try{const i={...t.value};if(i.quantityUnit&&typeof i.quantityUnit=="string"){for(const[e,n]of y.value)if(n===i.quantityUnit){i.quantityUnit=e;break}}if(i.auxiliaryUnit&&typeof i.auxiliaryUnit=="string"){for(const[e,n]of y.value)if(n===i.auxiliaryUnit){i.auxiliaryUnit=e;break}}Q.value==="create"?(await k.createTransaction(i),L.success(T("common.createSuccess"))):(await k.updateTransaction(i),L.success(T("common.updateSuccess"))),v.value=!1,G("success")}finally{f.value=!1}},z=()=>{var i;t.value={id:void 0,bizId:void 0,bizNo:void 0,transactionType:void 0,transactionDirection:void 0,materialId:void 0,materialCode:void 0,materialName:void 0,materialType:void 0,moveType:void 0,fromWarehouseName:void 0,fromLocationName:void 0,toWarehouseName:void 0,toLocationName:void 0,moveDate:void 0,sourceNo:void 0,inventoryBatchNo:void 0,quantity:void 0,quantityUnit:void 0,auxiliaryQuantity:void 0,auxiliaryUnit:void 0,remark:void 0,beforeQuantity:void 0,afterQuantity:void 0,costObjectCode:void 0,costObjectName:void 0,accountingVoucherNumber:void 0,tenantName:void 0},C.value=!1,O.value=void 0,g.value="history",(i=D.value)==null||i.resetFields()};return(i,e)=>{const n=ce,d=se,r=me,b=ve,V=pe,J=fe,Z=re,K=de,X=Ve,ee=be,E=_e,ae=ue,le=ye;return m(),p(ae,{title:o(w),modelValue:o(v),"onUpdate:modelValue":e[27]||(e[27]=l=>B(v)?v.value=l:null),width:1200},{footer:u(()=>[a(E,{onClick:H,type:"primary",disabled:o(f)},{default:u(()=>e[28]||(e[28]=[S("\u786E \u5B9A")])),_:1},8,["disabled"]),a(E,{onClick:e[26]||(e[26]=l=>v.value=!1)},{default:u(()=>e[29]||(e[29]=[S("\u53D6 \u6D88")])),_:1})]),default:u(()=>[Ue((m(),p(K,{ref_key:"formRef",ref:D,model:o(t),rules:o($),"label-width":"auto"},{default:u(()=>[a(Z,{gutter:20},{default:u(()=>[a(r,{span:8},{default:u(()=>[a(d,{label:"\u4EA4\u6613\u7F16\u53F7",prop:"bizId"},{default:u(()=>[a(n,{modelValue:o(t).bizId,"onUpdate:modelValue":e[0]||(e[0]=l=>o(t).bizId=l),placeholder:"\u8BF7\u8F93\u5165\u4EA4\u6613\u7F16\u53F7"},null,8,["modelValue"])]),_:1})]),_:1}),a(r,{span:8},{default:u(()=>[a(d,{label:"\u4EA4\u6613\u5355\u53F7",prop:"bizNo"},{default:u(()=>[a(n,{modelValue:o(t).bizNo,"onUpdate:modelValue":e[1]||(e[1]=l=>o(t).bizNo=l),placeholder:"\u8BF7\u8F93\u5165\u4EA4\u6613\u5355\u53F7"},null,8,["modelValue"])]),_:1})]),_:1}),a(r,{span:8},{default:u(()=>[a(d,{label:"\u4EA4\u6613\u7C7B\u578B",prop:"transactionType"},{default:u(()=>[a(V,{modelValue:o(t).transactionType,"onUpdate:modelValue":e[2]||(e[2]=l=>o(t).transactionType=l),placeholder:"\u8BF7\u9009\u62E9\u4EA4\u6613\u7C7B\u578B",clearable:""},{default:u(()=>[(m(!0),N(h,null,U(o(A),l=>(m(),p(b,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),a(r,{span:8},{default:u(()=>[a(d,{label:"\u4EA4\u6613\u65B9\u5411",prop:"transactionDirection"},{default:u(()=>[a(V,{modelValue:o(t).transactionDirection,"onUpdate:modelValue":e[3]||(e[3]=l=>o(t).transactionDirection=l),placeholder:"\u8BF7\u9009\u62E9\u4EA4\u6613\u65B9\u5411",clearable:""},{default:u(()=>[(m(!0),N(h,null,U(o(M),l=>(m(),p(b,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),a(r,{span:8},{default:u(()=>[a(d,{label:"\u7269\u6599\u7F16\u7801",prop:"materialCode"},{default:u(()=>[a(n,{modelValue:o(t).materialCode,"onUpdate:modelValue":e[4]||(e[4]=l=>o(t).materialCode=l),placeholder:"\u8BF7\u8F93\u5165\u7269\u6599\u7F16\u7801"},null,8,["modelValue"])]),_:1})]),_:1}),a(r,{span:8},{default:u(()=>[a(d,{label:"\u7269\u6599\u540D\u79F0",prop:"materialName"},{default:u(()=>[a(n,{modelValue:o(t).materialName,"onUpdate:modelValue":e[5]||(e[5]=l=>o(t).materialName=l),placeholder:"\u8BF7\u8F93\u5165\u7269\u6599\u540D\u79F0"},null,8,["modelValue"])]),_:1})]),_:1}),a(r,{span:8},{default:u(()=>[a(d,{label:"\u7269\u6599\u7C7B\u578B",prop:"materialType"},{default:u(()=>[a(V,{modelValue:o(t).materialType,"onUpdate:modelValue":e[6]||(e[6]=l=>o(t).materialType=l),placeholder:"\u8BF7\u9009\u62E9\u7269\u6599\u7C7B\u578B",clearable:""},{default:u(()=>[(m(!0),N(h,null,U(o(P),l=>(m(),p(b,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),a(r,{span:8},{default:u(()=>[a(d,{label:"\u79FB\u52A8\u7C7B\u578B",prop:"moveType"},{default:u(()=>[a(V,{modelValue:o(t).moveType,"onUpdate:modelValue":e[7]||(e[7]=l=>o(t).moveType=l),placeholder:"\u8BF7\u9009\u62E9\u79FB\u52A8\u7C7B\u578B",clearable:""},{default:u(()=>[(m(!0),N(h,null,U(o(F),l=>(m(),p(b,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),a(r,{span:8},{default:u(()=>[a(d,{label:"\u79FB\u52A8\u6E90\u4ED3\u5E93\u540D\u79F0",prop:"fromWarehouseName"},{default:u(()=>[a(n,{modelValue:o(t).fromWarehouseName,"onUpdate:modelValue":e[8]||(e[8]=l=>o(t).fromWarehouseName=l),placeholder:"\u8BF7\u8F93\u5165\u79FB\u52A8\u6E90\u4ED3\u5E93\u540D\u79F0"},null,8,["modelValue"])]),_:1})]),_:1}),a(r,{span:8},{default:u(()=>[a(d,{label:"\u79FB\u52A8\u6E90\u4ED3\u4F4D\u540D\u79F0",prop:"fromLocationName"},{default:u(()=>[a(n,{modelValue:o(t).fromLocationName,"onUpdate:modelValue":e[9]||(e[9]=l=>o(t).fromLocationName=l),placeholder:"\u8BF7\u8F93\u5165\u79FB\u52A8\u6E90\u4ED3\u4F4D\u540D\u79F0"},null,8,["modelValue"])]),_:1})]),_:1}),a(r,{span:8},{default:u(()=>[a(d,{label:"\u79FB\u52A8\u5230\u4ED3\u5E93\u540D\u79F0",prop:"toWarehouseName"},{default:u(()=>[a(n,{modelValue:o(t).toWarehouseName,"onUpdate:modelValue":e[10]||(e[10]=l=>o(t).toWarehouseName=l),placeholder:"\u8BF7\u8F93\u5165\u79FB\u52A8\u5230\u4ED3\u5E93\u540D\u79F0"},null,8,["modelValue"])]),_:1})]),_:1}),a(r,{span:8},{default:u(()=>[a(d,{label:"\u79FB\u52A8\u5230\u4ED3\u4F4D\u540D\u79F0",prop:"toLocationName"},{default:u(()=>[a(n,{modelValue:o(t).toLocationName,"onUpdate:modelValue":e[11]||(e[11]=l=>o(t).toLocationName=l),placeholder:"\u8BF7\u8F93\u5165\u79FB\u52A8\u5230\u4ED3\u4F4D\u540D\u79F0"},null,8,["modelValue"])]),_:1})]),_:1}),a(r,{span:8},{default:u(()=>[a(d,{label:"\u79FB\u52A8\u65E5\u671F",prop:"moveDate"},{default:u(()=>[a(J,{modelValue:o(t).moveDate,"onUpdate:modelValue":e[12]||(e[12]=l=>o(t).moveDate=l),type:"date","value-format":"x",placeholder:"\u9009\u62E9\u79FB\u52A8\u65E5\u671F"},null,8,["modelValue"])]),_:1})]),_:1}),a(r,{span:8},{default:u(()=>[a(d,{label:"\u6765\u6E90\u5355\u53F7",prop:"sourceNo"},{default:u(()=>[a(n,{modelValue:o(t).sourceNo,"onUpdate:modelValue":e[13]||(e[13]=l=>o(t).sourceNo=l),placeholder:"\u8BF7\u8F93\u5165\u6765\u6E90\u5355\u53F7"},null,8,["modelValue"])]),_:1})]),_:1}),a(r,{span:8},{default:u(()=>[a(d,{label:"\u5E93\u5B58\u6279\u53F7",prop:"inventoryBatchNo"},{default:u(()=>[a(n,{modelValue:o(t).inventoryBatchNo,"onUpdate:modelValue":e[14]||(e[14]=l=>o(t).inventoryBatchNo=l),placeholder:"\u8BF7\u8F93\u5165\u5E93\u5B58\u6279\u53F7"},null,8,["modelValue"])]),_:1})]),_:1}),a(r,{span:8},{default:u(()=>[a(d,{label:"\u6570\u91CF",prop:"quantity"},{default:u(()=>[a(n,{modelValue:o(t).quantity,"onUpdate:modelValue":e[15]||(e[15]=l=>o(t).quantity=l),placeholder:"\u8BF7\u8F93\u5165\u6570\u91CF"},null,8,["modelValue"])]),_:1})]),_:1}),a(r,{span:8},{default:u(()=>[a(d,{label:"\u5355\u4F4D",prop:"quantityUnit"},{default:u(()=>[a(V,{modelValue:o(t).quantityUnit,"onUpdate:modelValue":e[16]||(e[16]=l=>o(t).quantityUnit=l),placeholder:"\u8BF7\u9009\u62E9\u5355\u4F4D",clearable:"",filterable:""},{default:u(()=>[(m(!0),N(h,null,U(o(W),l=>(m(),p(b,{key:l.value,label:l.label,value:l.label},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),a(r,{span:8},{default:u(()=>[a(d,{label:"\u57FA\u672C\u5355\u4F4D\u6570\u91CF",prop:"auxiliaryQuantity"},{default:u(()=>[a(n,{modelValue:o(t).auxiliaryQuantity,"onUpdate:modelValue":e[17]||(e[17]=l=>o(t).auxiliaryQuantity=l),placeholder:"\u8BF7\u8F93\u5165\u57FA\u672C\u5355\u4F4D\u6570\u91CF"},null,8,["modelValue"])]),_:1})]),_:1}),a(r,{span:8},{default:u(()=>[a(d,{label:"\u57FA\u672C\u5355\u4F4D",prop:"auxiliaryUnit"},{default:u(()=>[a(V,{modelValue:o(t).auxiliaryUnit,"onUpdate:modelValue":e[18]||(e[18]=l=>o(t).auxiliaryUnit=l),placeholder:"\u8BF7\u9009\u62E9\u57FA\u672C\u5355\u4F4D",clearable:"",filterable:""},{default:u(()=>[(m(!0),N(h,null,U(o(W),l=>(m(),p(b,{key:l.value,label:l.label,value:l.label},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),a(r,{span:8},{default:u(()=>[a(d,{label:"\u5907\u6CE8",prop:"remark"},{default:u(()=>[a(n,{modelValue:o(t).remark,"onUpdate:modelValue":e[19]||(e[19]=l=>o(t).remark=l),placeholder:"\u8BF7\u8F93\u5165\u5907\u6CE8"},null,8,["modelValue"])]),_:1})]),_:1}),a(r,{span:8},{default:u(()=>[a(d,{label:"\u51FA\u5165\u5E93\u524D\u5E93\u5B58\u6570\u91CF",prop:"beforeQuantity"},{default:u(()=>[a(n,{modelValue:o(t).beforeQuantity,"onUpdate:modelValue":e[20]||(e[20]=l=>o(t).beforeQuantity=l),placeholder:"\u8BF7\u8F93\u5165\u51FA\u5165\u5E93\u524D\u5E93\u5B58\u6570\u91CF"},null,8,["modelValue"])]),_:1})]),_:1}),a(r,{span:8},{default:u(()=>[a(d,{label:"\u51FA\u5165\u5E93\u540E\u5E93\u5B58\u6570\u91CF",prop:"afterQuantity"},{default:u(()=>[a(n,{modelValue:o(t).afterQuantity,"onUpdate:modelValue":e[21]||(e[21]=l=>o(t).afterQuantity=l),placeholder:"\u8BF7\u8F93\u5165\u51FA\u5165\u5E93\u540E\u5E93\u5B58\u6570\u91CF"},null,8,["modelValue"])]),_:1})]),_:1}),a(r,{span:8},{default:u(()=>[a(d,{label:"\u6210\u672C\u5BF9\u8C61\u7F16\u7801",prop:"costObjectCode"},{default:u(()=>[a(n,{modelValue:o(t).costObjectCode,"onUpdate:modelValue":e[22]||(e[22]=l=>o(t).costObjectCode=l),placeholder:"\u8BF7\u8F93\u5165\u6210\u672C\u5BF9\u8C61\u7F16\u7801"},null,8,["modelValue"])]),_:1})]),_:1}),a(r,{span:8},{default:u(()=>[a(d,{label:"\u6210\u672C\u5BF9\u8C61\u540D\u79F0",prop:"costObjectName"},{default:u(()=>[a(n,{modelValue:o(t).costObjectName,"onUpdate:modelValue":e[23]||(e[23]=l=>o(t).costObjectName=l),placeholder:"\u8BF7\u8F93\u5165\u6210\u672C\u5BF9\u8C61\u540D\u79F0"},null,8,["modelValue"])]),_:1})]),_:1}),a(r,{span:8},{default:u(()=>[a(d,{label:"\u8BB0\u8D26\u51ED\u8BC1\u53F7",prop:"accountingVoucherNumber"},{default:u(()=>[a(n,{modelValue:o(t).accountingVoucherNumber,"onUpdate:modelValue":e[24]||(e[24]=l=>o(t).accountingVoucherNumber=l),placeholder:"\u8BF7\u8F93\u5165\u8BB0\u8D26\u51ED\u8BC1\u53F7"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model","rules"])),[[le,o(f)]]),o(C)?(m(),p(ee,{key:0,modelValue:o(g),"onUpdate:modelValue":e[25]||(e[25]=l=>B(g)?g.value=l:null)},{default:u(()=>[a(X,{label:"\u5386\u53F2\u4EA4\u6613\u8BB0\u5F55",name:"history"},{default:u(()=>[a(ne,{"inventory-id":o(O)},null,8,["inventory-id"])]),_:1})]),_:1},8,["modelValue"])):Te("",!0)]),_:1},8,["title","modelValue"])}}});export{k as T,ge as _};
