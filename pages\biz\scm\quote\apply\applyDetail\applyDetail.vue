<template>
	<view class="detailPage">
		<uni-card :is-full="true" :is-shadow="true" v-if="nowPage === 'apply'">
			<template #title>
				<view class="card-header">
					<text class="card-title">申请信息</text>
					<text class="toggle-icon" @click="isCardExpanded = !isCardExpanded">
						{{ isCardExpanded ? '▲' : '▼' }}
					</text>
				</view>
			</template>
			<uv-form labelPosition="top" :model="formModel" v-if="isCardExpanded" labelAlign="center">
				<uv-form-item label="客户">
					<view class="picker-view-container">
						<!-- 使用SelectPicker组件作为客户选择器 -->
						<select-picker
							:options="customerList"
							:value="selectedCustomer"
							:title="'选择客户'"
							:labelField="'name'"
							:valueField="'id'"
							:placeholder="'请选择客户'"
							:enableLoadMore="true"
							:pageSize="10"
							@input="handleCustomerSelect"
							@loadMore="loadMoreCustomers"
							@search="searchCompanies"
							@resetSearch="resetCompanySearch"
						></select-picker>
						</view>
				</uv-form-item>
				<uv-form-item label="配方">
					<view class="picker-view-container">
						<!-- 完善配方选择器配置 -->
						<select-picker
							:options="formulaList"
							:value="selectedFormula"
							:title="'选择配方'"
							:labelField="'name'"
							:valueField="'id'"
							:placeholder="'请选择配方'"
							:enableLoadMore="true"
							:pageSize="10"
							@input="handleFormulaSelect"
							@loadMore="loadMoreFormulas"
							@search="searchFormulas"
							@resetSearch="resetFormulaSearch"
						></select-picker>
					</view>
				</uv-form-item>
				<uv-form-item label="生产工厂" labelWidth="80">
					<view class="picker-view-container">
						<picker 
							:range="producerList" 
							range-key="name"
							:value="selectedProducerIndex" 
							@change="handleProducerChange"
						>
							<view class="factory-picker-value">
								<text class="factory-text">{{applyDetail.producerName || '请选择生产工厂'}}</text>
								<text class="factory-arrow">▼</text>
							</view>
						</picker>
					</view>
				</uv-form-item>
				<uv-form-item label="产品规格" labelWidth="80">
					<view class="picker-view-container">
						<uni-data-picker 
							:localdata="mfgCostListCopy" 
							popup-title="选择产品规格"
							placeholder="请选择产品规格"							:preload="true"
							:step-searh="true"
							self-field="value"
							parent-field="value"
							collection-field="text"
							@change="handleSpecChange"
						>
							<template v-slot:default="{data, error, options}">
								<view class="picker-value">
									<text class="value-text">{{ specDisplayText }}</text>
									<text class="factory-arrow">▼</text>
								</view>
							</template>
						</uni-data-picker>
					</view>
				</uv-form-item>
				<uv-form-item label="需求">
					<view class="picker-view-container">
						<view class="picker-value">
							<input class="input-text" type="text" v-model="applyDetail.requirement" placeholder="请输入需求"/>
						</view>
					</view>
				</uv-form-item>
				<uv-form-item label="加工数量" labelWidth="80">
					<view class="picker-view-container">
						<view class="picker-value">
							<input class="input-text" type="number" v-model="applyDetail.amount" placeholder="请输入加工数量"/>
						</view>
					</view>
				</uv-form-item>
			</uv-form>
		</uni-card>
		<formula-detail 
			v-if="applyDetail.quoteFormula" 
			:formulaId="applyDetail.status < 4 ? applyDetail.formulaId : applyDetail.quoteFormulaId" 
			:isApply="true"
			:formulaDetail="applyDetail.quoteFormula"
			:isDisabled = "isDisabled"
			ref="formulaDetailRef"
		/>
		
		<!-- 底部安全间距 -->
		<view class="safe-area-inset-bottom"></view>
		
		<!-- 底部保存按钮区域 -->
		<view class="footer-actions safe-bottom">
			<!-- <button class="export-button" @click="exportApply">导出Excel</button> -->
			<button class="save-button" @click="saveApply">保存报价单</button>
		</view>
		
		<uv-back-top :scroll-top="scrollTop"></uv-back-top>
	</view>
</template>

<script>
import { getCompanyPageApi, getCompanyApi } from '../../../../../../api/scm/base/company';
import { getMfgCostApi, getMfgCostPageApi } from '../../../../../../api/scm/quote/mfgCost';
import { getFormulaPageApi, getFormulaApi } from '../../../../../../api/scm/rd/formula';
import { getDictDataPage } from '../../../../../../api/system/dict/dict.data';
import { getDictOptions } from '../../../../../../utils/dict';
import { createApplyApi, updateApplyApi } from '../../../../../../api/scm/quote/apply';
import SelectPicker from '../../../../../../components/SelectPicker/SelectPicker.vue';
import uniLoadMore from '../../../../../../uni_modules/uni-load-more/components/uni-load-more/uni-load-more.vue';
import FormulaDetail from '../../../rd/formula/FormulaDetail/index.vue'
	
	export default {
		components:{
			SelectPicker,
			uniLoadMore,
			FormulaDetail
		},
		data() {
			return {
				isCardExpanded: true, // 控制卡片是否展开
				applyDetail: {},
				formModel: {}, // 解决UvForm组件警告
				nowPage: '',
				customerList: [], // 客户列表
				selectedCustomer: null, // 选中的客户
				companyParams: { // 客户查询参数
					pageNo: 1,
					pageSize: 10,
					isCustomer: 1, // 确保始终是客户类型
					isProducer: 0,
					isSupplier: 0,
					name:'', // 公司名称搜索字段
				},
				formulaList: [], // 配方列表
				selectedFormula: null, // 选中的配方
				formulaParams: { // 配方查询参数
					pageNo: 1,
					pageSize: 10,
					name: '', // 配方名称搜索字段
				},
				producerList: [], // 生产工厂列表
				selectedProducerIndex: 0, // 选中的工厂索引
				producerParams: { // 生产工厂查询参数
					pageNo: 1,
					pageSize: 50, // 增大页大小获取更多工厂
					isProducer: 1, // 确保是生产工厂类型
					isCustomer: 0,
					isSupplier: 0,
				},
				mfgCostList:[], // 用于产品规格选择的数据源
				mfgCostListCopy: [], // 创建数据源的副本，避免直接修改prop
				materialStateOptions: [], // 物料状态字典
				prodSpecSubTypeOptions: [], // 产品规格亚型字典
				prodSpecOptions: [] ,// 产品规格字典
				specDisplayText: '', // 产品规格显示文本
				selectedSpecInfo: null ,// 完整的选中规格信息
				mfgCost:[],
				scrollTop:0,
				isDisabled:false
			}
		},
		watch: {
			// 监听applyDetail并更新formModel，避免直接修改props
			applyDetail: {
				handler(val) {
					this.formModel = JSON.parse(JSON.stringify(val));
					if( val.status < 4){
						this.isDisabled = true
					}
				},
				deep: true,
				immediate: true
			},
			// 监听选中的产品规格值变化
			selectedProdSpec: {
				handler(newVal) {
					if(newVal) {
						// 尝试找到完整的选中路径信息
						this.updateSpecDisplayText(newVal);
					}
				},
				immediate: true
			},
			// 监听mfgCostList变化，并更新副本
			mfgCostList: {
				handler(newVal) {
					if(newVal) {
						// 创建深拷贝
						this.mfgCostListCopy = JSON.parse(JSON.stringify(newVal));
					}
				},
				deep: true,
				immediate: true
			}
		},
		methods: {
			// 处理客户选择
			handleCustomerSelect(customer) {
				this.selectedCustomer = customer;
				
				// 防护措施：检查customer是否存在
				if(!customer){
					// 客户为空时，清空客户相关字段
					this.applyDetail = Object.assign({}, this.applyDetail, {
						customerCode: null,
						customerName: null,
					});
					return; // 提前返回，避免继续执行
				}
				
				// 确保customer对象有必要的属性
				const customerCode = customer.code || null;
				const customerName = customer.name || null;
				
				// 创建新对象进行更新，避免直接修改
				this.applyDetail = Object.assign({}, this.applyDetail, {
					customerCode: customerCode,
					customerName: customerName,
				});
			},
			onPageScroll(e) {
				this.scrollTop = e.scrollTop;
				// #ifdef APP-NVUE
				this.scrollTop = e.detail.scrollTop;
				// #endif
			},
			// 加载更多客户数据
			loadMoreCustomers(params) {
				const { page, pageSize, callback } = params;
				this.updateCompanyParams(page, pageSize);
				this.fetchCustomers(callback, false);
			},
			
			// 搜索公司
			searchCompanies(params) {
				const { keyword, page, pageSize, callback } = params;
				this.updateCompanyParams(page, pageSize, keyword);
				this.fetchCustomers(callback, page === 1);
			},
			
			// 重置公司搜索
			resetCompanySearch() {
				this.updateCompanyParams(1, this.companyParams.pageSize, '');
				this.fetchCustomers();
			},
			
			// 更新查询参数
			updateCompanyParams(page, pageSize, keyword = this.companyParams.name) {
				this.companyParams = Object.assign({}, this.companyParams, {
					pageNo: page,
					pageSize: pageSize || this.companyParams.pageSize,
					name: keyword,
					isCustomer: 1 // 确保始终是客户类型
				});
			},
			
			// 获取客户数据的通用方法
			async fetchCustomers(callback, isReset = true) {
				try {
					const response = await getCompanyPageApi(this.companyParams);
					
					if (response && response.code === 0 && response.data) {
						// 根据页码决定是替换还是追加数据
						if (isReset || this.companyParams.pageNo === 1) {
							this.customerList = response.data.list || [];
						} else {
							// 创建新数组，避免直接修改
							this.customerList = [...this.customerList, ...(response.data.list || [])];
						}
						
						// 如果有回调函数，调用回调
						if (callback) {
							const total = response.data.total || 0;
							const hasMore = this.customerList.length < total;
							
							callback({
								data: response.data.list || [],
								total: total,
								hasMore: hasMore
							});
						}
					} else if (callback) {
						callback({ data: [], total: 0, hasMore: false });
					}
				} catch (error) {
					if (callback) {
						callback({ data: [], total: 0, hasMore: false });
					}
				}
			},
			
			// 初始化客户列表
			initCustomerList() {
				this.updateCompanyParams(1, this.companyParams.pageSize, '');
				this.fetchCustomers();
			},
			
			// 处理配方选择
			async handleFormulaSelect(formula) {
				this.selectedFormula = formula;
				if(!formula){
					this.applyDetail = Object.assign({},this.applyDetail,{
						formulaId:null,
						productName:null,
						quoteFormula:null
					})
					return
				}
				// 创建新对象进行更新
				this.applyDetail = Object.assign({}, this.applyDetail, {
					formulaId: formula.id,
					productName: formula.name
				});
				
				try {
					uni.showLoading({ title: '加载配方详情...', mask: true });
					const formulaData = await this.getFormulaDetail(formula.id);
					if (formulaData) {
						const newApplyDetail = Object.assign({}, this.applyDetail);
						newApplyDetail.quoteFormula = formulaData;
						this.applyDetail = newApplyDetail;
					}
				} catch (error) {
					uni.showToast({ title: '配方详情获取失败', icon: 'none' });
				} finally {
					uni.hideLoading();
				}
			},
			
			// 获取配方详细信息
			async getFormulaDetail(id) {
				if (id) {
					const response = await getFormulaApi(id, true);
					if (response.code !== 0) {
						this.$method?.msgError?.('获取配方详情失败');
						return null;
					}
					return response.data;
				}
				return null;
			},
			
			// 加载更多配方数据
			loadMoreFormulas(params) {
				const { page, pageSize, callback } = params;
				this.updateFormulaParams(page, pageSize);
				this.fetchFormulas(callback, false);
			},
			
			// 搜索配方
			searchFormulas(params) {
				const { keyword, page, pageSize, callback } = params;
				this.updateFormulaParams(page, pageSize, keyword);
				this.fetchFormulas(callback, page === 1);
			},
			
			// 重置配方搜索
			resetFormulaSearch() {
				this.updateFormulaParams(1, this.formulaParams.pageSize, '');
				this.fetchFormulas();
			},
			
			// 更新配方查询参数
			updateFormulaParams(page, pageSize, keyword = this.formulaParams.name) {
				this.formulaParams = Object.assign({}, this.formulaParams, {
					pageNo: page,
					pageSize: pageSize || this.formulaParams.pageSize,
					name: keyword
				});
			},
			
			// 获取配方列表数据的通用方法
			async fetchFormulas(callback, isReset = true) {
				try {
					const response = await getFormulaPageApi(this.formulaParams);
					
					if (response && response.code === 0 && response.data) {
						// 根据页码决定是替换还是追加数据
						if (isReset || this.formulaParams.pageNo === 1) {
							this.formulaList = response.data.list || [];
						} else {
							// 创建新数组，避免直接修改
							this.formulaList = [...this.formulaList, ...(response.data.list || [])];
						}
						
						// 如果有回调函数，调用回调
						if (callback) {
							const total = response.data.total || 0;
							const hasMore = this.formulaList.length < total;
							
							callback({
								data: response.data.list || [],
								total: total,
								hasMore: hasMore
							});
						}
					} else if (callback) {
						callback({ data: [], total: 0, hasMore: false });
					}
				} catch (error) {
					if (callback) {
						callback({ data: [], total: 0, hasMore: false });
					}
				}
			},
			
			// 初始化配方列表
			initFormulaList() {
				this.updateFormulaParams(1, this.formulaParams.pageSize, '');
				this.fetchFormulas();
			},
			
			// 处理生产工厂选择变化
			handleProducerChange(e) {
				const index = e.detail.value;
				if (index >= 0 && index < this.producerList.length) {
					const selectedProducer = this.producerList[index];
					this.selectedProducerIndex = index;
					
					// 处理选择"暂不选择"选项的情况
					if (selectedProducer.name === '暂不选择') {
						// 清空生产工厂信息
						this.applyDetail = Object.assign({}, this.applyDetail, {
							producerId: null,
							producerName: null,
						});
						return;
					}
					
					// 创建新对象进行更新，避免直接修改
					this.applyDetail = Object.assign({}, this.applyDetail, {
						producerId: selectedProducer.id,
						producerName: selectedProducer.name,
					});
				}
			},
			
			// 获取生产工厂列表
			async fetchProducers() {
				try {
					uni.showLoading({ title: '加载生产工厂...', mask: true });
					const response = await getCompanyPageApi(this.producerParams);
					
					if (response && response.code === 0 && response.data) {
						// 添加"暂不选择"选项到生产工厂列表的开头
						const producerData = response.data.list || [];
						this.producerList = [
							{ id: null, name: '暂不选择' },
							...producerData
						];
						
						// 如果已有选中的生产工厂，找到对应索引
						if (this.applyDetail.producerId) {
							const index = this.producerList.findIndex(item => 
								item.id === this.applyDetail.producerId
							);
							if (index !== -1) {
								this.selectedProducerIndex = index;
							}
						}
					} else {
						uni.showToast({ title: '获取生产工厂失败', icon: 'none' });
					}
				} catch (error) {
					uni.showToast({ title: '获取生产工厂失败', icon: 'none' });
				} finally {
					uni.hideLoading();
				}
			},
			
			//获取成本数据列表 
			async initMfgCostSelect(){
				try {
					const response = await getMfgCostPageApi({
						pageNo:1,
						pageSize:100
					});

					if(response.code !== 0){
						this.$method?.msgError?.('获取生产成本数据错误');
						return;
					}

					const mfgCosts = response.data.list || []; // 获取成本数据，确保是数组
					this.mfgCost = mfgCosts
					
					// 创建临时数组
					const tempMfgCostList = [];

					// 获取字典数据并存储到组件的data属性中
					this.materialStateOptions = await getDictOptions('material_state') || [];
					this.prodSpecSubTypeOptions = await getDictOptions('prod_spec_sub_type') || [];
					this.prodSpecOptions = await getDictOptions('prod_spec') || [];
					
					// 设置初始显示文本
					if(this.applyDetail.spec){
						// 添加防护措施确保找到匹配项才设置文本
						const matchingSpec = this.prodSpecOptions.filter(item => item.value === this.applyDetail.spec);
						if(matchingSpec && matchingSpec.length > 0) {
							this.specDisplayText = matchingSpec[0].label;
						} else {
							this.specDisplayText = '请选择产品规格';
						}
					} else {
						this.specDisplayText = '请选择产品规格';
					}
					

					if(this.materialStateOptions.length > 0){
						this.materialStateOptions.forEach(state => {
							const materialStateObject = {
								text: state.label,
								value: state.value,
								children:[]
							};
							tempMfgCostList.push(materialStateObject);
						});
					}

					if(this.prodSpecSubTypeOptions.length > 0 && tempMfgCostList.length > 0){
						this.prodSpecSubTypeOptions.forEach(subType => {
							const prodSpecSubTypeObject = {
								text: subType.label,
								value: subType.value,
								children:[]
							};

							tempMfgCostList.forEach(stateItem => {
								if (stateItem.value === '2') { 
									stateItem.children.push(prodSpecSubTypeObject);
								}
							});
						});
					}
					if(this.prodSpecOptions.length > 0 && tempMfgCostList.length > 0){
						this.prodSpecOptions.forEach(item => {
							const prodSpecObject = {
								text:item.label,
								value:item.value,
							}
							if(/kg.*\(.*\)/.test(item.label)){
								tempMfgCostList.forEach(listItem => {
									if(listItem.value === '2'){
										listItem.children.forEach(subList => {
											subList.children.push(prodSpecObject)
										})
									}
								})
							}else if(/^\d+g$/.test(item.label) && /^\d+g$/.test(item.value)){
								tempMfgCostList.forEach(listItem => {
									if(listItem.value === '1'){
										listItem.children.push(prodSpecObject)
									}
								})
							}
						})
					}

					this.mfgCostList = tempMfgCostList; // 更新到组件的data属性
					// 更新副本，避免直接修改prop
					this.mfgCostListCopy = JSON.parse(JSON.stringify(tempMfgCostList));

				} catch (error) {
					this.$method?.msgError?.('初始化产品规格数据失败');
				}
			},
			
			// 初始化数据加载
			async initData() {
				await Promise.all([
					this.initCustomerList(),
					this.initFormulaList(),
					this.fetchProducers(),
					this.initMfgCostSelect() // 添加到并行初始化中
				]);
			},
			
			// 处理产品规格选择器变化
			async handleSpecChange(e) {
				const { value, detail } = e;
				
				// 处理规格被清空的情况
				if(!detail || !detail.value || !Array.isArray(detail.value) || detail.value.length === 0) {
					// 清空规格相关数据
					this.selectedSpecInfo = null;
					this.specDisplayText = '请选择产品规格';
					
					// 清空成本相关数据
					this.applyDetail = Object.assign({}, this.applyDetail, {
						totalPackageCost: 0,
						spec: null
					});
					
					// 如果配方表单已加载并存在，则同时更新配方表单中的封装成本
					if (this.applyDetail.quoteFormula) {
						this.applyDetail.quoteFormula.totalPackageCost = 0;
						
						// 强制更新配方表单的引用，确保子组件能感知变化
						this.applyDetail.quoteFormula = {...this.applyDetail.quoteFormula};
					}
					
					return;
				}
				
				// 注释或删除以下"暂不选择"选项的处理逻辑
				/*
				// 处理"暂不选择"选项
				if (detail && detail.value && detail.value[0] && detail.value[0].value === '0') {
					// 清空产品规格相关数据
					this.selectedSpecInfo = null;
					this.specDisplayText = '';
					
					// 清空相关数据
					this.applyDetail = Object.assign({}, this.applyDetail, {
						totalPackageCost: 0,
						spec: null
					});
					
					// 如果配方表单已加载并存在，则同时更新配方表单中的封装成本
					if (this.applyDetail.quoteFormula) {
						this.applyDetail.quoteFormula.totalPackageCost = 0;
						
						// 强制更新配方表单的引用，确保子组件能感知变化
						this.applyDetail.quoteFormula = {...this.applyDetail.quoteFormula};
					}
					
					return;
				}
				*/
				
				// 更新选中规格的完整信息
				this.selectedSpecInfo = detail;
				if(this.mfgCost.length !== 0 && detail && detail.value && Array.isArray(detail.value) && detail.value.length > 0){
					const selectedMfgCost = this.mfgCost.filter(item => {
						// 确保detail.value数组包含需要的元素
						if(!detail.value[0] || !detail.value[0].value) return false;
						
						// 获取spec值，兼容不同深度的选择
						const specValue = detail.value[2] && detail.value[2].value ? 
							detail.value[2].value : 
							(detail.value[1] && detail.value[1].value ? detail.value[1].value : null);
						
						if(!specValue) return false;
						
						const matchSpec = item.spec === specValue;
						
						// 使用字符串比较，物料状态为"2"时是粉末/固体，为"1"时是液体
						const stateValue = detail.value[0].value;
						const isSolid = stateValue === '2' || stateValue === 2;
						
						// 确保subType存在于detail.value[1]中
						const subTypeValue = detail.value[1] && detail.value[1].value ? detail.value[1].value : null;
						const matchSubType = isSolid ? (subTypeValue && item.subType === subTypeValue) : true;
						const matchType = isSolid ? (item.type === 'powder') : (item.type === 'liquid');
						
						return matchSpec && matchSubType && matchType;
					});
					
					if(selectedMfgCost && selectedMfgCost.length > 0 && selectedMfgCost[0] && selectedMfgCost[0].id){
						const costId = selectedMfgCost[0].id
						const response = await getMfgCostApi(costId, this.applyDetail.amount)
						if(response.code !== 0){
							uni.showToast({
								title:'获取成本数据失败',
								icon:'none'
							})
						} else {
							// 创建新对象进行更新，避免直接修改
							this.applyDetail = Object.assign({}, this.applyDetail, {
								totalPackageCost: response.data.totalMfgCost,
								spec: response.data.spec
							});
							
							// 如果配方表单已加载并存在，则同时更新配方表单中的封装成本
							if (this.applyDetail.quoteFormula) {
								this.applyDetail.quoteFormula.totalPackageCost = response.data.totalMfgCost;
								
								// 强制更新配方表单的引用，确保子组件能感知变化
								this.applyDetail.quoteFormula = {...this.applyDetail.quoteFormula};
							}
						}
					}
				}
				
				// 更新显示文本
				this.updateSpecDisplayText(detail);
			},
			
			// 更新显示文本
			updateSpecDisplayText(detail) {
				if(detail && detail.value && Array.isArray(detail.value) && detail.value.length > 0){
					// 检查每个层级的value对象是否存在并有text属性
					let text = '';
					if(detail.value[0] && detail.value[0].text) {
						text += detail.value[0].text;
						
						if(detail.value[1] && detail.value[1].text) {
							text += '/' + detail.value[1].text;
							
							if(detail.value[2] && detail.value[2].text) {
								text += '/' + detail.value[2].text;
							}
						}
					}
					this.specDisplayText = text || '请选择产品规格';
				} else {
					this.specDisplayText = '请选择产品规格';
				}
			},
			
			// 保存报价单
			async saveApply() {
				try {
					// 显示加载提示
					uni.showLoading({ title: '保存报价单...', mask: true });
					
					// 准备保存的数据
					const saveData = this.prepareSaveData();
					
					// 区分新增和更新
					let response;
					if (this.applyDetail.id) {
						// 更新报价单
						response = await updateApplyApi(JSON.stringify(saveData));
					} else {
						// 新增报价单
						response = await createApplyApi(JSON.stringify(saveData));
					}
					
					// 处理保存结果
					if (response && response.code === 0) {
						uni.showToast({ 
							title: '报价单保存成功', 
							icon: 'success',
							duration: 1500
						});
						
						// 更新本地数据，如果是新增则设置返回的ID
						if (!this.applyDetail.id && response.data) {
							this.applyDetail = Object.assign({}, this.applyDetail, {
								id: response.data.id
							});
						}
						
						// 等待提示显示后返回上一页并刷新列表
						setTimeout(() => {
							// 获取页面栈
							const pages = getCurrentPages();
							// 如果存在上一页
							if (pages.length > 1) {
								// 获取上一页实例
								const prevPage = pages[pages.length - 2];
								// 设置上一页需要刷新的标记或调用上一页的刷新方法
								if (prevPage && typeof prevPage.refreshList === 'function') {
									// 调用上一页的刷新方法
									prevPage.refreshList();
								} else if (prevPage) {
									// 设置刷新标记，上一页onShow时检测
									prevPage.$vm && (prevPage.$vm.needRefresh = true);
								}
							}
							
							// 返回上一页
							uni.navigateBack({
								delta: 1
							});
						}, 1500);
					} else {
						uni.showToast({ 
							title: response?.msg || '保存报价单失败', 
							icon: 'none',
							duration: 2000
						});
					}
				} catch (error) {
					uni.showToast({ 
						title: '保存报价单失败: ' + (error.message || '未知错误'), 
						icon: 'none',
						duration: 2000
					});
				} finally {
					uni.hideLoading();
				}
			},
			
			
			// 准备保存的数据
			prepareSaveData() {
				// 从子组件获取最新的配方数据
				let updatedFormulaData = null;
				if (this.$refs.formulaDetailRef && this.applyDetail.quoteFormula) {
					// 调用子组件的方法获取最新的配方数据
					updatedFormulaData = this.$refs.formulaDetailRef.getFormData();
				}
				
				// 确保数据中包含规格信息
				if (!this.applyDetail.spec) {
					// 规格为空时，确保相关成本数据也为0
					this.applyDetail.totalPackageCost = 0;
					
					// 如果有更新的配方数据，同步封装成本
					if (updatedFormulaData) {
						updatedFormulaData.totalPackageCost = 0;
					}
				}
				
				// 如果有更新的配方数据，进行数据同步
				if (updatedFormulaData) {
					// 同步NPK数据
					const npkArray = [];
					if (updatedFormulaData.npk) {
						// 将npk对象转换为applyDetail需要的数组格式
						if (updatedFormulaData.npk.N !== null && updatedFormulaData.npk.N !== undefined) {
							npkArray.push({
								element: "N",
								quantity: updatedFormulaData.npk.N,
								unit: updatedFormulaData.npk.n_unit || "%"
							});
						}
						
						if (updatedFormulaData.npk.P !== null && updatedFormulaData.npk.P !== undefined) {
							npkArray.push({
								element: "P",
								quantity: updatedFormulaData.npk.P,
								unit: updatedFormulaData.npk.p_unit || "%"
							});
						}
						
						if (updatedFormulaData.npk.K !== null && updatedFormulaData.npk.K !== undefined) {
							npkArray.push({
								element: "K",
								quantity: updatedFormulaData.npk.K,
								unit: updatedFormulaData.npk.k_unit || "%"
							});
						}
					}
					
					// 同步微量元素数据
					const microElements = [];
					if (updatedFormulaData.microElement && updatedFormulaData.microElement.length > 0) {
						updatedFormulaData.microElement.forEach(item => {
							if (item.element && item.quantity) {
								microElements.push({
									element: item.element,
									quantity: item.quantity,
									unit: item.unit || "g/L"
								});
							}
						});
					}
					
					// 同步其他原料数据
					const otherMaterials = [];
					if (updatedFormulaData.otherMaterial && updatedFormulaData.otherMaterial.length > 0) {
						updatedFormulaData.otherMaterial.forEach(item => {
							if (item.element && item.quantity) {
								otherMaterials.push({
									element: item.element,
									quantity: item.quantity,
									unit: item.unit || "g/kg"
								});
							}
						});
					}
					
					// 同步成本数据
					this.applyDetail.totalCost = parseFloat(updatedFormulaData.totalCost || 0);
					this.applyDetail.totalRawCost = parseFloat(updatedFormulaData.totalRawCost || 0);
					this.applyDetail.totalAuxiliaryCost = parseFloat(updatedFormulaData.totalAuxiliaryCost || 0);
					this.applyDetail.totalPackageCost = parseFloat(updatedFormulaData.totalPackageCost || 0);
					
					// 同步外观属性
					this.applyDetail.appearance = updatedFormulaData.appearance;
					
					// 更新配方数据
					this.applyDetail.npk = npkArray;
					this.applyDetail.microElements = microElements;
					this.applyDetail.otherMaterials = otherMaterials;
					
					// 更新产品名称
					if (updatedFormulaData.name && this.applyDetail.productName !== updatedFormulaData.name) {
						this.applyDetail.productName = updatedFormulaData.name;
					}
				}
				
				// 准备报价单保存数据
				const saveData = {
					...this.applyDetail,
					quoteFormula: updatedFormulaData || this.applyDetail.quoteFormula
				};
				
				return saveData;
			}
		},
		mounted() {
			this.initData();
		},
		onLoad: async function(option) {
			const eventChannel = this.getOpenerEventChannel();
			eventChannel.on('acceptDataFromOpener', async (data) => {
				if (data.data) {
					// 创建深拷贝避免直接引用
					this.applyDetail = JSON.parse(JSON.stringify(data.data));
					// 根据状态加载不同的配方信息
					if (this.applyDetail.status < 4) {
						if (this.applyDetail.formulaId) {
							const formulaData = await this.getFormulaDetail(this.applyDetail.formulaId);
							if (formulaData) {
								this.applyDetail = Object.assign({}, this.applyDetail, {
									quoteFormula: formulaData
								});
							}
						}
					} else {
						if (this.applyDetail.quoteFormulaId) {
							const formulaData = await this.getFormulaDetail(this.applyDetail.quoteFormulaId);
							if (formulaData) {
								this.applyDetail = Object.assign({}, this.applyDetail, {
									quoteFormula: formulaData
								});
							}
						}
					}
					
					// 设置选中的客户
					if (data.data.customerCode && data.data.customerName) {
						this.selectedCustomer = {
							id: data.data.customerId,
							code: data.data.customerCode,
							name: data.data.customerName
						};
					}
					
					// 设置选中的配方
					if (data.data.formulaId && data.data.productName) {
						this.selectedFormula = {
							id: data.data.formulaId,
							code: data.data.formulaCode,
							name: data.data.productName
						};
					}
				}
				
				if (data.type) {
					this.type = data.type;
				}
				if (data.nowPage) {
					this.nowPage = data.nowPage;
				}
				
				// 初始化数据
				this.initData();
			});
		}
	}
</script>

<style>
.detailPage {
	background-color: #f5f5f5;
	min-height: 100vh;
}

/* 选择器样式 */
.picker-view-container {
	width: 100%;
}

.picker-value {
	height: 40px;
	line-height: 40px;
	padding: 0 10px;
	background-color: #fff;
	border: 1px solid #eee;
	border-radius: 4px;
	width: 100%;
	box-sizing: border-box;
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.value-text {
	flex: 1;
	overflow: hidden;
	white-space: nowrap;
	text-overflow: ellipsis;
	padding-right: 5px;
}

/* 优化生产工厂选择器样式 */
.factory-picker-value {
	display: flex;
	align-items: center;
	justify-content: space-between;
	height: 40px;
	padding: 0 10px;
	background-color: #fff;
	border: 1px solid #eee;
	border-radius: 4px;
	box-sizing: border-box;
	width: 100%;
}

.factory-text {
	flex: 1;
	font-size: 14px;
	overflow: hidden;
	white-space: nowrap;
	text-overflow: ellipsis;
	padding-right: 5px;
}

.factory-arrow {
	font-size: 12px;
	color: #999;
	flex-shrink: 0;
}

/* 卡片头部样式 */
.card-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	width: 100%;
}

.card-title {
	font-size: 16px;
	font-weight: bold;
}

.toggle-icon {
	font-size: 14px;
	color: #666;
	padding: 5px 10px;
	cursor: pointer;
}

.input-text {
	flex: 1;
	height: 38px;
	font-size: 14px;
	border: none;
	background: transparent;
	outline: none;
	width: 100%;
}

/* 底部保存按钮区域样式 */
.footer-actions {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	padding: 10px;
	background-color: #fff;
	box-shadow: 0 -2px 5px rgba(0, 0, 0, 0.1);
	z-index: 999;
	display: flex;
	align-items: center;
	justify-content: space-evenly;
}

.save-button {
	width: 80%;
	height: 40px;
	line-height: 40px;
	background-color: #007aff;
	color: #fff;
	border-radius: 20px;
	font-size: 16px;
	text-align: center;
}
/* .export-button{
	width: 80%;
	height: 40px;
	line-height: 40px;
	background-color: #ff5500;
	color: #fff;
	border-radius: 20px;
	font-size: 16px;
	text-align: center;
} */

.safe-area-inset-bottom {
	height: 60px; 
	width: 100%;
}

</style>
