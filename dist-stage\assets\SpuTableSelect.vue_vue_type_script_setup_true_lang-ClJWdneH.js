import{p as ae,_ as le}from"./index-Byekp3Iv.js";import{_ as oe}from"./Dialog.vue_vue_type_style_index_0_lang-DhV_HFpU.js";import{_ as te}from"./ContentWrap.vue_vue_type_script_setup_true_lang-D3mNNG_R.js";import{_ as ue}from"./index.vue_vue_type_script_setup_true_lang-BeMNDf6p.js";import{k as ie,r as n,e as ne,y as b,m as k,u as o,h as C,_ as re,z as u,H as l,E as g,A as pe,Z as se,v as de,F as me}from"./form-create-B86qX0W_.js";import{h as ce,d as ve}from"./tree-COGD3qag.js";import{g as fe}from"./category-Tlk-MRkP.js";import{d as he}from"./spu-BqmQYIjx.js";import{f as ge,h as we,aj as ye,k as _e,a1 as Ve,F as be,ak as ke,Z as xe,_ as Ue,w as Te,u as Ce,al as Ie,bi as M}from"./form-designer-C0ARe9Dh.js";const Se=ie({name:"SpuTableSelect",__name:"SpuTableSelect",props:{multiple:ae.bool.def(!1)},emits:["change"],setup(I,{expose:Z,emit:q}){const S=n(0),c=n([]),x=n(!1),d=n(!1),i=n({pageNo:1,pageSize:10,tabType:0,name:"",categoryId:null,createTime:[]});Z({open:t=>{v.value=[],r.value={},s.value=!1,y.value=!1,t&&t.length>0&&(v.value=[...t],r.value=Object.fromEntries(t.map(e=>[e.id,!0]))),d.value=!0,N()}});const w=async()=>{x.value=!0;try{const t=await he(i.value);c.value=t.list,S.value=t.total,c.value.forEach(e=>r.value[e.id]=r.value[e.id]||!1),H()}finally{x.value=!1}},z=()=>{i.value.pageNo=1,w()},N=()=>{i.value={pageNo:1,pageSize:10,tabType:0,name:"",categoryId:null,createTime:[]},w()},s=n(!1),y=n(!1),v=n([]),r=n({}),_=n(),A=()=>{d.value=!1,D(M,[...v.value])},D=q,B=t=>{s.value=t,y.value=!1,c.value.forEach(e=>E(t,e,!1))},E=(t,e,T)=>{if(t)v.value.push(e),r.value[e.id]=!0;else{const m=G(e);m>-1&&(v.value.splice(m,1),r.value[e.id]=!1,s.value=!1)}T&&H()},G=t=>v.value.findIndex(e=>e.id===t.id),H=()=>{s.value=c.value.every(t=>r.value[t.id]),y.value=!s.value&&c.value.some(t=>r.value[t.id])},U=n(),Y=n();return ne(async()=>{await w(),U.value=await fe({}),Y.value=ce(U.value,"id","parentId")}),(t,e)=>{const T=_e,m=ye,K=Ve,O=be,F=le,V=ge,P=we,j=Te,f=Ue,R=Ce,J=Ie,L=xe,Q=ue,W=te,X=oe,$=ke;return k(),b(X,{modelValue:o(d),"onUpdate:modelValue":e[8]||(e[8]=a=>C(d)?d.value=a:null),appendToBody:!0,title:"\u9009\u62E9\u5546\u54C1",width:"70%"},re({default:u(()=>[l(W,null,{default:u(()=>[l(P,{ref:"queryFormRef",inline:!0,model:o(i),class:"-mb-15px","label-width":"68px"},{default:u(()=>[l(m,{label:"\u5546\u54C1\u540D\u79F0",prop:"name"},{default:u(()=>[l(T,{modelValue:o(i).name,"onUpdate:modelValue":e[0]||(e[0]=a=>o(i).name=a),class:"!w-240px",clearable:"",placeholder:"\u8BF7\u8F93\u5165\u5546\u54C1\u540D\u79F0",onKeyup:se(z,["enter"])},null,8,["modelValue"])]),_:1}),l(m,{label:"\u5546\u54C1\u5206\u7C7B",prop:"categoryId"},{default:u(()=>[l(K,{modelValue:o(i).categoryId,"onUpdate:modelValue":e[1]||(e[1]=a=>o(i).categoryId=a),data:o(Y),props:o(ve),"check-strictly":"",class:"!w-240px","node-key":"id",placeholder:"\u8BF7\u9009\u62E9\u5546\u54C1\u5206\u7C7B"},null,8,["modelValue","data","props"])]),_:1}),l(m,{label:"\u521B\u5EFA\u65F6\u95F4",prop:"createTime"},{default:u(()=>[l(O,{modelValue:o(i).createTime,"onUpdate:modelValue":e[2]||(e[2]=a=>o(i).createTime=a),"default-time":[new Date("1 00:00:00"),new Date("1 23:59:59")],class:"!w-240px","end-placeholder":"\u7ED3\u675F\u65E5\u671F","start-placeholder":"\u5F00\u59CB\u65E5\u671F",type:"daterange","value-format":"YYYY-MM-DD HH:mm:ss"},null,8,["modelValue","default-time"])]),_:1}),l(m,null,{default:u(()=>[l(V,{onClick:z},{default:u(()=>[l(F,{class:"mr-5px",icon:"ep:search"}),e[9]||(e[9]=g(" \u641C\u7D22 "))]),_:1}),l(V,{onClick:N},{default:u(()=>[l(F,{class:"mr-5px",icon:"ep:refresh"}),e[10]||(e[10]=g(" \u91CD\u7F6E "))]),_:1})]),_:1})]),_:1},8,["model"]),pe((k(),b(L,{data:o(c),"show-overflow-tooltip":""},{default:u(()=>[I.multiple?(k(),b(f,{key:0,width:"55"},{header:u(()=>[l(j,{modelValue:o(s),"onUpdate:modelValue":e[3]||(e[3]=a=>C(s)?s.value=a:null),indeterminate:o(y),onChange:B},null,8,["modelValue","indeterminate"])]),default:u(({row:a})=>[l(j,{modelValue:o(r)[a.id],"onUpdate:modelValue":p=>o(r)[a.id]=p,onChange:p=>E(p,a,!0)},null,8,["modelValue","onUpdate:modelValue","onChange"])]),_:1})):(k(),b(f,{key:1,label:"#",width:"55"},{default:u(({row:a})=>[l(R,{value:a.id,modelValue:o(_),"onUpdate:modelValue":e[4]||(e[4]=p=>C(_)?_.value=p:null),onChange:p=>{return D(M,h=a),d.value=!1,void(_.value=h.id);var h}},{default:u(()=>e[11]||(e[11]=[g(" \xA0 ")])),_:2},1032,["value","modelValue","onChange"])]),_:1})),l(f,{key:"id",align:"center",label:"\u5546\u54C1\u7F16\u53F7",prop:"id","min-width":"60"}),l(f,{label:"\u5546\u54C1\u56FE","min-width":"80"},{default:u(({row:a})=>[l(J,{src:a.picUrl,class:"h-30px w-30px","preview-src-list":[a.picUrl],"preview-teleported":""},null,8,["src","preview-src-list"])]),_:1}),l(f,{label:"\u5546\u54C1\u540D\u79F0","min-width":"200",prop:"name"}),l(f,{label:"\u5546\u54C1\u5206\u7C7B","min-width":"100",prop:"categoryId"},{default:u(({row:a})=>{var p,h;return[de("span",null,me((h=(p=o(U))==null?void 0:p.find(ee=>ee.id===a.categoryId))==null?void 0:h.name),1)]}),_:1})]),_:1},8,["data"])),[[$,o(x)]]),l(Q,{limit:o(i).pageSize,"onUpdate:limit":e[5]||(e[5]=a=>o(i).pageSize=a),page:o(i).pageNo,"onUpdate:page":e[6]||(e[6]=a=>o(i).pageNo=a),total:o(S),onPagination:w},null,8,["limit","page","total"])]),_:1})]),_:2},[I.multiple?{name:"footer",fn:u(()=>[l(V,{type:"primary",onClick:A},{default:u(()=>e[12]||(e[12]=[g("\u786E \u5B9A")])),_:1}),l(V,{onClick:e[7]||(e[7]=a=>d.value=!1)},{default:u(()=>e[13]||(e[13]=[g("\u53D6 \u6D88")])),_:1})]),key:"0"}:void 0]),1032,["modelValue"])}}});export{Se as _};
