import{_ as P}from"./ContentWrap.vue_vue_type_script_setup_true_lang-D3mNNG_R.js";import{d as C}from"./index-Byekp3Iv.js";import{_ as F}from"./Dialog.vue_vue_type_style_index_0_lang-DhV_HFpU.js";import{_ as z}from"./simple-process-designer-CVgyS9ud.js";import{g as B,a as h,N as R}from"./consts-CvaDWb4S.js";import{g as H}from"./index-D9Lef229.js";import{g as $}from"./index-C6Iy3vmH.js";import{h as j}from"./tree-COGD3qag.js";import{g as G}from"./index-CU_lrk1T.js";import{g as J}from"./index-DC6LIg_n.js";import{g as Q}from"./index-CL6LcTWy.js";import{g as W}from"./index-DP9Sf6UT.js";import{g as X}from"./index-D0XRAAM6.js";import{B as Y}from"./constants-C3gLHYOK.js";import{ak as Z,f as ee}from"./form-designer-C0ARe9Dh.js";import{k as A,i as se,r as a,p as t,e as ae,A as te,u as l,l as I,m as i,y as L,C as re,H as D,h as oe,z as f,v as le,G as me,$ as de,F as b,E as ie}from"./form-create-B86qX0W_.js";const pe={class:"overflow-auto"},ue=A({name:"SimpleProcessDesigner",__name:"SimpleProcessDesigner",props:{modelId:{type:String,required:!1},modelKey:{type:String,required:!1},modelName:{type:String,required:!1},startUserIds:{type:Array,required:!1},startDeptIds:{type:Array,required:!1}},emits:["success"],setup(N,{expose:c,emit:y}){const v=y,m=N,e=se("processData"),p=a(!1),u=a([]),_=a(20),S=a([]),E=a([]),w=a([]),g=a([]),k=a(),T=a([]);t("formFields",u),t("formType",_),t("roleList",S),t("postList",E),t("userList",w),t("deptList",g),t("userGroupList",T),t("deptTree",k),t("startUserIds",m.startUserIds),t("startDeptIds",m.startDeptIds),t("tasks",[]),t("processInstance",{}),C();const o=a();t("processNodeTree",o);const n=a(!1);let q=[];const U=async r=>{if(r)try{e.value=r,v("success",r)}catch{}};ae(async()=>{try{if(p.value=!0,m.modelId){const r=await H(m.modelId);if(r&&(_.value=r.formType,_.value===Y.NORMAL&&r.formId)){const s=await $(r.formId);u.value=s==null?void 0:s.fields}}S.value=await G(),E.value=await Q(),w.value=await W(),g.value=await J(),k.value=j(g.value,"id"),T.value=await X(),e.value?o.value=e==null?void 0:e.value:o.value||(o.value={name:"\u53D1\u8D77\u4EBA",type:h.START_USER_NODE,id:R.START_USER_NODE_ID,childNode:{id:R.END_EVENT_NODE_ID,name:"\u7ED3\u675F",type:h.END_EVENT_NODE}},U(o.value))}finally{p.value=!1}});const M=a();return c({}),(r,s)=>{const O=ee,V=F,x=Z;return te((i(),I("div",pe,[l(o)?(i(),L(z,{key:0,ref_key:"simpleProcessModelRef",ref:M,"flow-node":l(o),readonly:!1,onSave:U},null,8,["flow-node"])):re("",!0),D(V,{modelValue:l(n),"onUpdate:modelValue":s[1]||(s[1]=d=>oe(n)?n.value=d:null),title:"\u4FDD\u5B58\u5931\u8D25",width:"400",fullscreen:!1},{footer:f(()=>[D(O,{type:"primary",onClick:s[0]||(s[0]=d=>n.value=!1)},{default:f(()=>s[2]||(s[2]=[ie("\u77E5\u9053\u4E86")])),_:1})]),default:f(()=>[s[3]||(s[3]=le("div",{class:"mb-2"},"\u4EE5\u4E0B\u8282\u70B9\u5185\u5BB9\u4E0D\u5B8C\u5584\uFF0C\u8BF7\u4FEE\u6539\u540E\u4FDD\u5B58",-1)),(i(!0),I(me,null,de(l(q),(d,K)=>(i(),I("div",{class:"mb-3 b-rounded-1 bg-gray-100 p-2 line-height-normal",key:K},b(d.name)+" : "+b(l(B).get(d.type)),1))),128))]),_:1},8,["modelValue"])])),[[x,l(p)]])}}}),ne=A({name:"SimpleModelDesign",__name:"SimpleModelDesign",props:{modelId:{},modelKey:{},modelName:{},startUserIds:{},startDeptIds:{}},emits:["success"],setup(N,{emit:c}){const y=c,v=a(),m=e=>{e&&y("success",e)};return(e,p)=>{const u=P;return i(),L(u,{bodyStyle:{padding:"20px 16px"}},{default:f(()=>[D(l(ue),{"model-id":e.modelId,"model-key":e.modelKey,"model-name":e.modelName,onSuccess:m,"start-user-ids":e.startUserIds,"start-dept-ids":e.startDeptIds,ref_key:"designerRef",ref:v},null,8,["model-id","model-key","model-name","start-user-ids","start-dept-ids"])]),_:1})}}});export{ne as _};
