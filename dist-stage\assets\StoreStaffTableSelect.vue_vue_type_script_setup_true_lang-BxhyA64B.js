import{h as ee,D as F,_ as ae}from"./index-Byekp3Iv.js";import{_ as le}from"./Dialog.vue_vue_type_style_index_0_lang-DhV_HFpU.js";import{_ as te}from"./index.vue_vue_type_script_setup_true_lang-BeMNDf6p.js";import{_ as oe}from"./DictTag.vue_vue_type_script_lang-DdZ_pRVv.js";import{_ as ue}from"./ContentWrap.vue_vue_type_script_setup_true_lang-D3mNNG_R.js";import{k as se,r,P as re,y as U,m as V,z as o,H as a,u as t,Z as I,l as ne,G as ie,$ as pe,E as w,A as me,h as O}from"./form-create-B86qX0W_.js";import{d as de}from"./formatTime-HVkyL6Kg.js";import{a as fe}from"./index-DP9Sf6UT.js";import{_ as ce}from"./DeptTree.vue_vue_type_script_setup_true_lang-B7wvxYUk.js";import{i as ve,j as _e,h as he,aj as be,k as ge,x as ye,Q as Ve,F as we,f as ke,Z as xe,_ as Ue,w as Ce,ak as Se}from"./form-designer-C0ARe9Dh.js";const Te=se({__name:"StoreStaffTableSelect",emits:["change"],setup(Ne,{expose:Y,emit:A}){const i=r(!1),k=r(!1),h=r([]),p=r({}),m=r(!1),d=r(!0),C=r(0),f=r([]),s=re({pageNo:1,pageSize:10,username:void 0,mobile:void 0,status:void 0,deptId:void 0,roleId:5,createTime:[]}),S=r(),b=async()=>{d.value=!0;try{const u=await fe(s);f.value=u.list,C.value=u.total}finally{d.value=!1}},g=()=>{s.pageNo=1,b()},D=()=>{var u;(u=S.value)==null||u.resetFields(),g()},H=async u=>{s.deptId=u.id,await b()};Y({open:async()=>{m.value=!0,d.value=!0;try{await b()}finally{d.value=!1}}});const P=u=>{i.value=u,k.value=!1,f.value.forEach(e=>T(u,e,!1))},T=(u,e,c)=>{if(u)h.value.push(e),p.value[e.id]=!0;else{const v=E(e);v>-1&&(h.value.splice(v,1),p.value[e.id]=!1,i.value=!1)}c&&K()},E=u=>h.value.findIndex(e=>e.id===u.id),K=()=>{i.value=f.value.every(u=>p.value[u.id]),k.value=!i.value&&f.value.some(u=>p.value[u.id])},Z=()=>{m.value=!1,j("change",[...h.value])},j=A;return(u,e)=>{const c=ue,v=_e,N=ge,_=be,q=Ve,G=ye,Q=we,M=ae,y=ke,R=he,z=Ce,n=Ue,$=oe,B=xe,J=te,L=ve,W=le,X=Se;return V(),U(W,{title:"\u9009\u62E9\u5E97\u5458",modelValue:t(m),"onUpdate:modelValue":e[8]||(e[8]=l=>O(m)?m.value=l:null),width:"60%"},{footer:o(()=>[a(y,{type:"primary",onClick:Z},{default:o(()=>e[11]||(e[11]=[w("\u786E \u5B9A")])),_:1}),a(y,{onClick:e[7]||(e[7]=l=>m.value=!1)},{default:o(()=>e[12]||(e[12]=[w("\u53D6 \u6D88")])),_:1})]),default:o(()=>[a(L,{gutter:20},{default:o(()=>[a(v,{span:4,xs:24},{default:o(()=>[a(c,{class:"h-1/1"},{default:o(()=>[a(ce,{onNodeClick:H})]),_:1})]),_:1}),a(v,{span:20,xs:24},{default:o(()=>[a(c,null,{default:o(()=>[a(R,{class:"-mb-15px",model:t(s),ref_key:"queryFormRef",ref:S,inline:!0,"label-width":"68px"},{default:o(()=>[a(_,{label:"\u7528\u6237\u540D\u79F0",prop:"username"},{default:o(()=>[a(N,{modelValue:t(s).username,"onUpdate:modelValue":e[0]||(e[0]=l=>t(s).username=l),placeholder:"\u8BF7\u8F93\u5165\u7528\u6237\u540D\u79F0",clearable:"",onKeyup:I(g,["enter"]),class:"!w-240px"},null,8,["modelValue"])]),_:1}),a(_,{label:"\u624B\u673A\u53F7\u7801",prop:"mobile"},{default:o(()=>[a(N,{modelValue:t(s).mobile,"onUpdate:modelValue":e[1]||(e[1]=l=>t(s).mobile=l),placeholder:"\u8BF7\u8F93\u5165\u624B\u673A\u53F7\u7801",clearable:"",onKeyup:I(g,["enter"]),class:"!w-240px"},null,8,["modelValue"])]),_:1}),a(_,{label:"\u72B6\u6001",prop:"status"},{default:o(()=>[a(G,{modelValue:t(s).status,"onUpdate:modelValue":e[2]||(e[2]=l=>t(s).status=l),placeholder:"\u7528\u6237\u72B6\u6001",clearable:"",class:"!w-240px"},{default:o(()=>[(V(!0),ne(ie,null,pe(t(ee)(t(F).COMMON_STATUS),l=>(V(),U(q,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(_,{label:"\u521B\u5EFA\u65F6\u95F4",prop:"createTime"},{default:o(()=>[a(Q,{modelValue:t(s).createTime,"onUpdate:modelValue":e[3]||(e[3]=l=>t(s).createTime=l),"value-format":"YYYY-MM-DD HH:mm:ss",type:"datetimerange","start-placeholder":"\u5F00\u59CB\u65E5\u671F","end-placeholder":"\u7ED3\u675F\u65E5\u671F",class:"!w-240px"},null,8,["modelValue"])]),_:1}),a(_,null,{default:o(()=>[a(y,{onClick:g},{default:o(()=>[a(M,{icon:"ep:search"}),e[9]||(e[9]=w("\u641C\u7D22"))]),_:1}),a(y,{onClick:D},{default:o(()=>[a(M,{icon:"ep:refresh"}),e[10]||(e[10]=w("\u91CD\u7F6E"))]),_:1})]),_:1})]),_:1},8,["model"])]),_:1}),a(c,null,{default:o(()=>[me((V(),U(B,{data:t(f)},{default:o(()=>[a(n,{width:"55"},{header:o(()=>[a(z,{modelValue:t(i),"onUpdate:modelValue":e[4]||(e[4]=l=>O(i)?i.value=l:null),indeterminate:t(k),onChange:P},null,8,["modelValue","indeterminate"])]),default:o(({row:l})=>[a(z,{modelValue:t(p)[l.id],"onUpdate:modelValue":x=>t(p)[l.id]=x,onChange:x=>T(x,l,!0)},null,8,["modelValue","onUpdate:modelValue","onChange"])]),_:1}),a(n,{label:"\u7528\u6237\u7F16\u53F7",align:"center",key:"id",prop:"id"}),a(n,{label:"\u7528\u6237\u540D\u79F0",align:"center",prop:"username","show-overflow-tooltip":!0}),a(n,{label:"\u7528\u6237\u6635\u79F0",align:"center",prop:"nickname","show-overflow-tooltip":!0}),a(n,{label:"\u90E8\u95E8",align:"center",key:"deptName",prop:"deptName","show-overflow-tooltip":!0}),a(n,{label:"\u624B\u673A\u53F7\u7801",align:"center",prop:"mobile",width:"120"}),a(n,{label:"\u72B6\u6001",key:"status"},{default:o(l=>[a($,{type:t(F).COMMON_STATUS,value:l.row.status},null,8,["type","value"])]),_:1}),a(n,{label:"\u521B\u5EFA\u65F6\u95F4",align:"center",prop:"createTime",formatter:t(de),width:"180"},null,8,["formatter"])]),_:1},8,["data"])),[[X,t(d)]]),a(J,{total:t(C),page:t(s).pageNo,"onUpdate:page":e[5]||(e[5]=l=>t(s).pageNo=l),limit:t(s).pageSize,"onUpdate:limit":e[6]||(e[6]=l=>t(s).pageSize=l),onPagination:b},null,8,["total","page","limit"])]),_:1})]),_:1})]),_:1})]),_:1},8,["modelValue"])}}});export{Te as _};
